{"name": "@yqcloud/lcr", "routeName": "lcr", "description": "", "license": "ISC", "version": "1.39.0", "keywords": [], "contributors": ["yqcloud"], "main": "./lib/index.js", "files": ["lib"], "scripts": {"start": "zknow-front-boot start --config ./react/config.js", "dist": "zknow-front-boot dist --config ./react/config.js", "reinstall": "npm run clear:yq && npm run inst:yq", "lint-staged": "lint-staged", "lint-staged:es": "eslint"}, "peerDependencies": {"@yqcloud/apps-master": ">=1.39.0-develop.0 <1.39.0-develop-1", "@zknow/boot": ">=1.22.0-develop.0 < 1.22.0-develop-1"}, "dependencies": {"@hanyk/rc-viewer": "0.0.3", "@viz-js/viz": "^3.2.0", "dagre": "^0.8.5", "dayjs": "^1.11.5", "echarts": "^5.1.2", "echarts-for-react": "^3.0.2", "fast-xml-parser": "^4.2.7", "file-saver": "^2.0.5", "nanoid": "^2.1.11", "qrcode.react": "^1.0.1", "quill": "^1.3.7", "react-flow-renderer": "^10.3.17", "react-sortablejs": "^1.5.1", "sanitize-html": "^1.16.1", "sanitize.css": "^12.0.1", "sortablejs": "^1.10.1", "ua-parser-js": "^1.0.35", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"@yqcloud/apps-master": ">=1.39.0-develop.0 <1.39.0-develop-1", "@zknow/boot": ">=1.22.0-develop.0 < 1.22.0-develop-1"}, "lint-staged": {"react/**/*.{js,jsx}": ["npm run lint-staged:es"], "react/**/*.{scss,less}": "stylelint --syntax less"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}