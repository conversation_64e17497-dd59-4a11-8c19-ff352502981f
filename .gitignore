# maven ignore
target/
*.versionsBackup
.factorypath

# eclipse ignore
.settings/
.project
.classpath
*rebel.xml

# idea ignore
.idea/
*.ipr
*.iml
*.iws
webstorm.config.js

# temp ignore
*.log
*.cache
*.diff
*.patch
*.tmp

# Package Files #
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# system ignore
.DS_Store
Thumbs.db

# ide
.vscode/
.idea/

# dev properties
src/main/resources/application-dev.yml
src/main/resources/application-remote.yml
src/main/java/org/hzero/iam/config/HiamExtraDataManager.java

init-local-database-dev.sh
/customize/

# npm
/node_modules
/npm-debug.log
/yarn-error.log
/yarn-lock.json
/package-lock.json
/yarn.lock
/pnpm-lock.yaml

# folder
/dist
/lib
**/lib/dist/
**/lib/assets/
**/lib/fonts/
src/main/resources/application-default.yml
