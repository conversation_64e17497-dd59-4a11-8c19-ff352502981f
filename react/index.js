import React from 'react';
import { Route, Switch } from 'react-router-dom';
import { inject } from 'mobx-react';
import { nomatch } from '@yqcloud/apps-master';
import { asyncRouter, formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import './components/editor-register/editors';

import './index.less';

const Render = asyncRouter(() => import('./routes/render'));

export default inject('AppState')(formatterCollections({ code: ['lcr.components'] })(({ match }) => {
  return (
    <div className="yqcloud-lowcode-render">
      <StoreProvider>
        <Switch>
          <Route path={`${match.url}/render`} component={Render} />
          <Route path="*" component={nomatch} />
        </Switch>
      </StoreProvider>
    </div>
  );
}));
