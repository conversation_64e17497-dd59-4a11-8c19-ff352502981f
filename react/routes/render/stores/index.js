import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(observer(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
    } = props;
    const prefixCls = 'lcr-ticket-detail';

    const value = {
      ...props,
      prefixCls,
      tenantId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
