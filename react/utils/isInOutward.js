import { getEnv } from '@zknow/utils';

/**
 * TODO: 临时加，后面移动到 utils 项目里
 * @param others
 * @returns {*}
 */
function getPaths(others) {
  const injectOutward = getEnv('OUTWARD');
  if (injectOutward) {
    const paths = injectOutward.split(',').map(r => r.replace(/['"']/g, ''));
    if (others) {
      return paths.concat(others);
    }
    return paths;
  }

  return others;
}

/**
 * 判断是否为公开页面
 * @param pathname
 * @param others
 * @returns {boolean}
 */
export default function isInOutward(pathname, others) {
  if (!pathname) {
    return false;
  }
  const paths = getPaths(others);
  if (paths) {
    return paths.some(v => {
      if (v?.indexOf('#') === -1) {
        return pathname?.replace('#', '')?.startsWith(v);
      }
      return pathname.startsWith(v);
    });
  }
  return false;
}
