/* eslint-disable no-useless-escape */
import { omit } from 'lodash';
import moment from 'moment';
import { prompt, getEnv } from '@zknow/utils';
import generate from 'nanoid/generate';
import React from 'react';
import Quill from 'quill';
import { message } from 'choerodon-ui/pro';
import debounce from 'lodash/debounce';
import pickBy from 'lodash/pickBy';
import { updateRequestContent } from '@/service';

const QuillDeltaToHtmlConverter = require('quill-delta-to-html');

const key = 'ABBCDEFGHIJKLMNOPQRSTUVWXYZ';

/**
 * 判断当前页面是否在iframe中
 * @return {boolean} true 表示在 iframe 中，false 表示不在 iframe 中
 */
export function isInIframe() {
  try {
    return window.self !== window.top;
  } catch (e) {
    return true;
  }
}

function randomCode(size, prefix) {
  return `${prefix}_${generate(key, size)}`.toLowerCase();
}

// 生成随机字符串
function randomString(len) {
  len = len || 8;
  const $chars = 'abcdefhijkmnprstwxyz'; /* 默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1 */
  const maxPos = $chars.length;
  let pwd = '';
  for (let i = 0; i < len; i++) {
    pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
  }
  return pwd;
}

/**
 * 数据请求后的错误拦截
 * 不建议使用此错误处理方法
 * @param data
 * @param hasReturn
 */
function handlePromptError(data, hasReturn = true) {
  if (hasReturn && !data) return false;

  if (data && data.failed) {
    prompt(data.message);
    return false;
  }

  return true;
}

/**
 * 参数 长度低于2则前面加 0，否则不加
 * @param {string | number} str
 * @returns {string}
 */
function padZero(str) {
  return str.toString().padStart(2, '0');
}

/**
 * 格式化时间，转化为 YYYY-MM-DD hh:mm:ss
 * @param {Date} timestamp
 * @returns {string}
 */
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minutes = date.getMinutes();
  const seconds = date.getSeconds();

  return `${[year, month, day].map(padZero).join('-')} ${[hour, minutes, seconds].map(padZero).join(':')}`;
}

/**
 * 计算剩余时间
 * @param now 当前时间 时间戳
 * @param end 结束时间 时间戳
 * @returns {string}
 */
function getTimeLeft(now, end) {
  if (now >= end) {
    // eslint-disable-next-line no-chinese/no-chinese
    return '剩余 0 天';
  }
  const resTime = end - now;
  const days = Math.floor(resTime / (24 * 3600 * 1000));
  return `剩余 ${days} 天`;
}

/**
 * 将毫秒数转为时分秒格式
 * @param time 毫秒数
 */
function timeConvert(time) {
  if (!time || typeof time !== 'number') {
    return;
  }
  // 毫秒转为秒
  const now = time / 1000;
  const sec = Math.floor((now % 60) % 60);
  const min = Math.floor(now / 60) % 60;
  const hour = Math.floor(now / 3600);

  let result = `${sec}s`;
  if (hour > 0) {
    result = `${hour}h ${min}m ${sec}s`;
  } else if (hour <= 0 && min > 0) {
    result = `${min}m ${sec}s`;
  }

  return result;
}

function removeEndsChar(str, char) {
  if (typeof str !== 'string') return '';

  return str.endsWith(char) ? str.slice(0, -1) : str;
}

function verifyCode(value, text) {
  const reg = /^[A-Z0-9_]{1,50}$/;
  if (value && !reg.test(value)) {
    return text;
  }
  return true;
}

/* 获取url上面参数的值, 针对于hash路由 */
function getQueryString(name) {
  const url = window.location.hash;
  const theRequest = {};
  if (url.indexOf('?') !== -1) {
    const str = url.substr(url.indexOf('?') + 1);
    const strs = str.split('&');
    for (let i = 0; i < strs.length; i += 1) {
      theRequest[strs[i].split('=')[0]] = decodeURIComponent(strs[i].split('=')[1]);
      if (theRequest[name]) {
        return theRequest[name];
      }
    }
  }
}

/* 拷贝一份对对象 */
function deepClone(obj) {
  const copy = JSON.stringify(obj);
  const objClone = JSON.parse(copy);
  return objClone;
}

// 天/小时/分钟 转化成分钟类型
function daysToMinues(value) {
  const days = value?.days;
  const hours = value?.hours;
  const minutes = value?.minutes;
  const D2M = days ? days * 24 * 60 : 0;
  const H2M = hours ? hours * 60 : 0;
  const M2M = minutes || 0;
  return D2M + H2M + M2M;
}

// 分钟 转化为 天/小时/分钟
function durationToDays(duration, mode = 'hours') {
  let DAYS = 0;
  let HOURS = 0;
  let MINUTES = 0;
  if (mode === 'hours') {
    HOURS = Math.floor(duration / 60);
    MINUTES = duration % 60;
  } else if (mode === 'days') {
    DAYS = parseInt(duration / 60 / 24, 10);
    HOURS = parseInt((duration / 60) % 24, 10);
    MINUTES = parseInt(duration % 60, 10);
  }
  const data = { days: 0, hours: 0, minutes: 0 };
  if (DAYS > 0) {
    data.days = DAYS;
  }
  if (HOURS > 0) {
    data.hours = HOURS;
  }
  if (MINUTES > 0) {
    data.minutes = MINUTES;
  }
  return data;
}

/**
 * @description: 监听滚动到底部
 * @param {*} dom 监听dom
 * @param {*} offset 距离底部距离
 * @param {*} callback 回调
 * @return {*}
 */
const isScrollBottom = (dom, offset, callback) => {
  dom.addEventListener('scroll', e => {
    const { clientHeight, scrollHeight, scrollTop } = e.target;
    const isBottom = scrollTop + clientHeight + offset > scrollHeight;
    if (isBottom) {
      if (typeof callback === 'function') {
        callback();
      }
    }
  });
};

// 过滤富文本注入
function escapeHtml(html) {
  return html?.replace(/<\/script/g, '<\\/script')?.replace(/<!--/g, '<\\!--');
}

// 转换delta为html
function deltaToHtml(data) {
  let fieldValue = data;
  try {
    if (typeof data === 'string') {
      fieldValue = JSON.parse(data);
    }
  } catch (e) {
    fieldValue = data;
  }
  if (fieldValue) {
    if (typeof fieldValue === 'object' && fieldValue.ops) {
      fieldValue = fieldValue.ops;
    }
    const converter = new QuillDeltaToHtmlConverter(Array.from(fieldValue).slice(), {});
    const html = converter?.convert();
    return (
      <div dangerouslySetInnerHTML={{ __html: escapeHtml(html) }} />
    );
  }
  return data;
}

// delta to html string
function deltaToHtmlStr(delta) {
  let fieldValue = delta;
  try {
    if (typeof delta === 'string') {
      fieldValue = JSON.parse(delta);
    }
  } catch (e) {
    fieldValue = delta;
  }
  if (fieldValue) {
    if (typeof fieldValue === 'object' && fieldValue.ops) {
      fieldValue = fieldValue.ops;
    }
    const converter = new QuillDeltaToHtmlConverter(Array.from(fieldValue).slice(), {});
    const html = converter?.convert();
    return html;
  }
  return '';
}

function isJSON(str, type = 'object') {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str);
      // eslint-disable-next-line valid-typeof
      if (typeof obj === type && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  return '';
}

const emptyQuill = { ops: [{ insert: '\u200B' }] };

/**
 *
 * @param {*} htmlContent
 * @param {*} prevRichJson
 * @returns
 */
function createRichHtml(htmlContent, prevRichJson) {
  const {
    quillData = [],
    attachments = [],
    audios = [],
  } = prevRichJson || {};

  const richHtml = `${htmlContent}<p data-json='${JSON.stringify({ quillData, attachments, audios })}'></p>`;
  return richHtml;
}

/**
 *
 * @param {*} html richHtml <div data-json='{quillData, attachments, audios}'>${quillhtml}</div>
 * @returns {quillData, attachments, audios}
 */
function getRichJson(html) {
  if (!html) return null;
  // 兼容旧版quillData格式的数据
  if (isJSON(html)) {
    return JSON.parse(html);
  }
  if (typeof html === 'object') return html;
  // 兼容旧版html格式的数据，但没有存储quill源数据
  if (!html?.includes('<p data-json=')) {
    const quillData = htmlToDelta(html);
    return {
      quillData,
      attachments: [],
      audios: [],
    };
  }

  // eslint-disable-next-line no-useless-escape
  const setCookieMetaRegExp = /<p data-json=[\'](.*)[\'].*>/gi;
  const matches = [];
  while (setCookieMetaRegExp.exec(html)) {
    matches.push(RegExp.$1);
  }
  if (!matches.length) return null;
  try {
    return isJSON(matches[0]) ? JSON.parse(matches[0]) : null;
  } catch (error) {
    return null;
  }
}

/**
 * 将html字符串转成delta
 * @param {*} html
 * @returns delta json
 */
function htmlToDelta(html) {
  if (!html) {
    return emptyQuill;
  }

  const isExist = document.getElementById('__htmlToDelta');
  let div = null;
  if (isExist) {
    div = document.getElementById('__htmlToDelta');
    div.setAttribute('style', 'display:none');
    div.innerHTML = `<div id="__quillEditor">${html}</div>`;
  } else {
    div = document.createElement('div');
    div.setAttribute('id', '__htmlToDelta');
    div.setAttribute('style', 'display:none');
    div.innerHTML = `<div id="__quillEditor">${html}</div>`;
    document.body.appendChild(div);
  }
  const quill = new Quill('#__quillEditor', {
    theme: 'snow',
  });
  const delta = quill.getContents();
  // 不删了，否则性能有问题
  // document.getElementById('__htmlToDelta').remove();
  return delta;
}

/**
 *
 * @param {*} htmlStr <sometag>tag text<sometag1>tag1 text1</sometag></sometag>
 * @returns text => tag text tag1 text1
 */
function htmlToText(htmlStr) {
  let html = htmlStr;
  if (html) {
    html = html.replace(/\n|\r/g, ' ');
    html = html.replace(/<figure><img[^>]+__EMOJI_ALT__=(:[^:]+:)[^>]\/><\/figure>/gi, (match, alt) => { return `<emoji>${alt}</emoji>`; });
    html = html.replace(/\<\/p\>\s*\<emoji\>/gi, '');
    html = html.replace(/\<\/emoji\>\s*\<p\>/gi, '');
    html = html.replace(/\<\/p\>/gi, '\n');
    html = html.replace(/\<p\>/gi, '');
    html = html.replace(/\<br\s*?\/?\>/gi, '\n');
    html = html.replace(/([ \t ])+/g, ' ');
    html = html.replace(/\n /g, '\n');
    html = html.replace(/&nbsp;/, ' ');
    // eslint-disable-next-line eqeqeq
    if (html.charAt(0) == ' ') {
      html = html.substr(1);
    }
  } else {
    html = '';
  }
  return html;
}

// function htmlToText1(html) {
//   return convertHtmlToDelta(html);
// }

function checkMode(durationMode, type) {
  return durationMode.indexOf(type) !== -1;
}

/**
 * 将指定单位转化为秒钟
 * @param unit
 * @param durationUnit
 * @returns {*}
 */
function unitToSec(unit = 0, durationUnit = 'seconds') {
  switch (durationUnit) {
    case 'days':
      return Math.floor(unit * (3600 * 24));
    case 'hours':
      return Math.floor(unit * 3600);
    case 'minutes':
      return Math.floor(unit * 60);
    case 'seconds':
    default:
      return unit;
  }
}

/**
 * 将秒为单位数据转化到指定单位
 * @param sec
 * @param durationUnit
 * @returns {*}
 */
function secToUnit(sec = 0, durationUnit = 'seconds') {
  switch (durationUnit) {
    case 'days':
      return Math.floor(sec / (3600 * 24));
    case 'hours':
      return Math.floor(sec / 3600);
    case 'minutes':
      return Math.floor(sec / 60);
    case 'seconds':
    default:
      return sec;
  }
}

/**
 * 转换数据：时长对象长转化到指定单位
 * @param value
 * @param durationMode
 * @param durationUnit
 * @returns {*}
 */
function durToSec(value, durationMode, durationUnit) {
  const days = value?.days;
  const hours = value?.hours;
  const minutes = value?.minutes;
  const seconds = value?.seconds;
  // 未设置的type直接置为0
  const D2S = days && checkMode(durationMode, 'days') ? days * 24 * 60 * 60 : 0;
  const H2S = hours && checkMode(durationMode, 'hours') ? hours * 60 * 60 : 0;
  const M2S = minutes && checkMode(durationMode, 'minutes') ? minutes * 60 : 0;
  const sec = seconds && checkMode(durationMode, 'seconds') ? seconds : 0;
  return secToUnit(D2S + H2S + M2S + sec, durationUnit);
}

/**
 * 转化数据: 将指定单位数据转化为时长对象
 * @param duration
 * @param durationUnit
 * @returns {{hours: *, seconds: *, minutes: *, days: *}}
 */
function durToDays(duration, durationUnit = 'seconds', durationMode = ['days', 'hours', 'minutes', 'seconds']) {
  if (!duration) duration = 0;
  duration = unitToSec(duration, durationUnit);
  let DAYS = 0;
  let HOURS = 0;
  let MINUTES = 0;
  let SECONDS = 0;
  if (checkMode(durationMode, 'days')) {
    DAYS = Math.floor(duration / (3600 * 24));
    duration -= DAYS * 3600 * 24;
  }
  if (checkMode(durationMode, 'hours')) {
    HOURS = Math.floor(duration / 3600);
    duration -= HOURS * 3600;
  }
  if (checkMode(durationMode, 'minutes')) {
    MINUTES = Math.floor(duration / 60);
    duration -= MINUTES * 60;
  }
  if (checkMode(durationMode, 'seconds')) {
    SECONDS = Math.floor(duration);
  }
  return { days: DAYS, hours: HOURS, minutes: MINUTES, seconds: SECONDS };
}

/**
 * 单词转驼峰
 * @param str
 */
function wordToHump(str) {
  return str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase();
}

function getDurationUnitTextMapping(intl) {
  return {
    day: intl.formatMessage({ id: 'zknow.common.model.day', defaultMessage: '天' }),
    days: intl.formatMessage({ id: 'zknow.common.model.day', defaultMessage: '天' }),
    hour: intl.formatMessage({ id: 'zknow.common.model.hour', defaultMessage: '时' }),
    hours: intl.formatMessage({ id: 'zknow.common.model.hour', defaultMessage: '时' }),
    minute: intl.formatMessage({ id: 'zknow.common.model.minute', defaultMessage: '分' }),
    minutes: intl.formatMessage({ id: 'zknow.common.model.minute', defaultMessage: '分' }),
    second: intl.formatMessage({ id: 'zknow.common.model.second', defaultMessage: '秒' }),
    seconds: intl.formatMessage({ id: 'zknow.common.model.second', defaultMessage: '秒' }),
  };
}

function dealDuration({ value, intl, durationMode }) {
  const textMapping = getDurationUnitTextMapping(intl);
  if (!durationMode || durationMode.length === 0) {
    // 无时长显示配置
    const flag = Object.values(value).every((item) => item === 0);
    if (flag) {
      return `0 ${textMapping.seconds}`;
    }
    return (!value?.days ? '' : `${value.days}${textMapping.days}`)
      + (!value?.hours ? '' : `${value.hours}${textMapping.hours}`)
      + (!value?.minutes ? '' : `${value.minutes}${textMapping.minutes}`)
      + (!value?.seconds ? '' : `${value.seconds}${textMapping.seconds}`);
  }
  let duration = '';
  const order = ['day', 'hours', 'minutes', 'seconds'];
  durationMode.sort((a, b) => {
    const indexA = order.indexOf(a);
    const indexB = order.indexOf(b);
    return indexA - indexB;
  });
  durationMode.map((mode) => {
    duration += `${value[mode]}${textMapping[mode]}`;
  });
  return duration;
}

/**
 * @description: 解析自定义富文本插件
 * @param {*} data delta数据格式
 * @return {*} 处理完之后的数据
 */
function parseCustomQuillParse(data) {
  try {
    data.forEach(delta => {
      if (delta.insert && delta.insert['proc-user']) {
        delta.insert = delta.attributes?.innerHtml;
      }
      if (delta.insert && delta.insert['proc-knowledge']) {
        // delta.insert = delta.attributes?.innerHtml;
        delta.attributes.renderAsBlock = true;
        delta.insert = {
          'proc-knowledge': {
            id: delta.attributes?.atKnowledgeId,
            name: delta.attributes?.innerHtml,
          },
        };
      }
    });
  } catch (e) {
    //
  }
  return data;
}

/**
 * @description: 解析自定义富文本插件
 * @param {*} data delta数据格式
 * @return {*} 获取html
 */
function getHtml(data) {
  let htmlContent;
  try {
    if (Array.isArray(data)) {
      const jsonData = JSON.parse(JSON.stringify(data)
        ?.replace(
          new RegExp('YQ_FILE_HOST', 'gm'),
          `${getEnv('API_HOST', '')}/hfle/yqc/v1/0/files/download-by-key?fileKey=`
        )
        ?.replace(
          new RegExp('/hfle/v1', 'gm'),
          '/hfle/yqc/v1'
        ));
      const converter = new QuillDeltaToHtmlConverter(parseCustomQuillParse(jsonData), {
        customTag: () => {

        },
      });
      converter.renderCustomWith((customOp, contextOp) => {
        if (customOp.insert.type === 'proc-knowledge') {
          const val = customOp.insert.value;
          const url = `${window.location.origin}/#/itsm/portal/knowledge?tenantId=${getQueryString('tenantId')}&menu=knowledge&knowledgeId=${val.id}`;
          return `<a href="${url}" target="__blank" id="${val.id}">${val.name}</a>`;
        } else {
          return '';
        }
      });
      htmlContent = converter.convert();
    } else {
      htmlContent = data;
    }
  } catch (e) {
    htmlContent = data;
  }
  return htmlContent;
}

function simplifyField(field) {
  let filters = [
    'auditFlag',
    'conditionFlag',
    'customFlag',
    'nameFlag',
    'searchFlag',
    'syncedFlag',
    'persistableFlag',
    'tlFlag',
    'presetFlag',
    'queryFlag',

    'createdBy',
    'creationDate',
    'lastUpdateDate',
    'lastUpdatedBy',
    'domainId',
    'tenantId',
    'objectVersionNumber',
    '_token',

    'parentObjectId',
    'relationObject',
    'relationType',
    'relationFields',
    'calculatedFlag',
    'calculatedConfig',
    'defaultLabel',

    // tree
    'widgetConfig.childButtons',
    'widgetConfig.parentButtons',
    'widgetConfig.searchableFlag',
    'widgetConfig.showIconFlag',
    'widgetConfig.syncFlag',
    'widgetConfig.treeNodes',
  ];

  if (field.widgetType !== 'Table') {
    filters = filters.concat([
      'widgetConfig.actions',
      'widgetConfig.buttons',
      'widgetConfig.fields',
      'widgetConfig.lineButtons',
      'widgetConfig.columnViewFlag',
      'widgetConfig.autoQueryFlag',
      'widgetConfig.canExportFlag',
      'widgetConfig.pageSize',
      'widgetConfig.preciseQueryFlag',
      'widgetConfig.selection',
      'widgetConfig.tableFilterFlag',
      'widgetConfig.tableLinkFlag',
      'widgetConfig.tableLinkType',
      'widgetConfig.tableLinks',
    ]);
  }
  if (!['Table', 'TicketRelation', 'TicketRelationList'].includes(field.widgetType)) {
    filters = filters.concat([
      'widgetConfig.tableLinkViewSize',
      'widgetConfig.tableLinkViewType',
    ]);
  }
  if (field.widgetType !== 'VariableTable') {
    filters = filters.concat(['widgetConfig.variableFields']);
  }
  if (field.widgetType !== 'Custom' && field.widgetType !== 'TreeLov') {
    filters = filters.concat(['widgetConfig.customConfig']);
  }
  if (field.widgetType !== 'IntelligentSearch' && field.widgetType !== 'IntelligentRecommendation') {
    filters = filters.concat([
      'widgetConfig.searchJumpType',
      'widgetConfig.displayLines',
      'widgetConfig.searchInterval',
      'widgetConfig.whenSearching',
      'widgetConfig.whenNotSearched',
      'widgetConfig.intelligentJumpViewJSON',
      'widgetConfig.searchRange',
      'widgetConfig.problemSourceFields',
    ]);
  }
  if (field.widgetType !== 'Dynamic') {
    filters = filters.concat([
      'widgetConfig.dynamicDisplayContent',
      'widgetConfig.dynamicDisplayDataType',
      'widgetConfig.dynamicDisplayFields',
    ]);
  }
  if (field.widgetType !== 'AutoNumber') {
    filters = filters.concat([
      'widgetConfig.autoNumberFormat',
    ]);
  }
  if (field.widgetType !== 'ManyToMany') {
    filters = filters.concat([
      'intermediateObjectId',
      'intermediateField',
      'intermediateRelationField',
      'intermediateFieldName',
      'intermediateObjectName',
      'intermediateRelationFieldName',
    ]);
  }
  if (field.widgetType !== 'MasterDetail') {
    filters = filters.concat([
      'relationLovId',
      'relationLovName',
      'relationObjectCode',
      'widgetConfig.lovToView',
      'widgetConfig.mappingField',
      'widgetConfig.previewFlag',
    ]);
  }
  if (field.widgetType !== 'Alert') {
    filters = filters.concat([
      'widgetConfig.alertType',
      'widgetConfig.alertMessage',
    ]);
  }
  if (field.widgetType !== 'Chart') {
    filters = filters.concat([
      'widgetConfig.chartType',
      'widgetConfig.chartParams',
    ]);
  }
  if (field.widgetType !== 'Duration') {
    filters = filters.concat([
      'widgetConfig.durationUnit',
      'widgetConfig.durationMode',
    ]);
  }
  if (field.widgetType !== 'Upload' && field.widgetType !== 'Image') {
    filters = filters.concat([
      'widgetConfig.fileDragUpload',
      // 'widgetConfig.fileFormat',
      'widgetConfig.fileSizeLimit',
    ]);
  }
  if (field.widgetType !== 'TextArea') {
    filters = filters.concat([
      'widgetConfig.htmlFlag',
    ]);
  }
  if (field.widgetType !== 'Comment') {
    filters = filters.concat([
      'widgetConfig.isDefaultExpand',
      'widgetConfig.replyOpenCC',
    ]);
  }
  if (field.widgetType !== 'Rate') {
    filters = filters.concat([
      'widgetConfig.rateCount',
      'widgetConfig.rateCharacter',
    ]);
  }
  if (field.widgetType !== 'TicketSurveyDetail') {
    filters = filters.concat([
      'widgetConfig.respondent',
    ]);
  }
  if (field.widgetType !== 'TicketSurveyDetail') {
    filters = filters.concat([
      'widgetConfig.surveyViewFlag',
    ]);
  }
  if (field.widgetType !== 'Tag') {
    filters = filters.concat([
      'widgetConfig.tagAddMode',
      'widgetConfig.tagMultiFlag',
      'widgetConfig.tagQuickAdd',
      'widgetConfig.tagViewType',
    ]);
  }
  if (field.widgetType !== 'TicketHeader') {
    filters = filters.concat([
      'widgetConfig.ticketHeaderEditablePerson',
      'widgetConfig.ticketHeaderEditableRole',
      'widgetConfig.ticketHeaderFields',
      'widgetConfig.ticketHeaderFileField',
      'widgetConfig.ticketHeaderDescriptionField',
      'widgetConfig.ticketHeaderDisplayFields',
    ]);
  }
  if (field.widgetType !== 'TicketRelationList') {
    filters = filters.concat([
      'widgetConfig.ticketRelationListMode',
      'widgetConfig.ticketRelationListType',
      'widgetConfig.ticketRelationListFields',
      'widgetConfig.ticketRelationListPageSize',
      'widgetConfig.ticketRelationListJumpFlag',
    ]);
  }

  return omit(field, filters);
}

// quill空值
const quillNullContent = [
  '[{"insert":"\\n"}]',
  '[{"insert":" \n"}]',
  '[{"insert":"\n"}]',
  '[{"insert":""}]',
  '[]',
  '{}',
  '',
  null,
  'null',
  undefined,
  '<p>​</p>',
  undefined,
];

const dateTimeOption = [
  { meaning: '1970-11-30 14:30:45', value: 'YYYY-MM-DD HH:mm:ss' },
  { meaning: '19701130 14:30:45', value: 'YYYYMMDD HH:mm:ss' },
  { meaning: '1970-11-30', value: 'YYYY-MM-DD' },
  { meaning: '19701130', value: 'YYYYMMDD' },
  { meaning: '1970-11', value: 'YYYY-MM' },
  { meaning: '1970', value: 'YYYY' },
  { meaning: '14:30:45', value: 'HH:mm:ss' },
  { meaning: '14:30', value: 'HH:mm' },
];

const timeOption = [
  { meaning: '14:30:45', value: 'HH:mm:ss' },
  { meaning: '14:30', value: 'HH:mm' },
];

const dateOption = [
  { meaning: '1970-11-30', value: 'YYYY-MM-DD' },
  { meaning: '19701130', value: 'YYYYMMDD' },
  { meaning: '1970-11', value: 'YYYY-MM' },
  { meaning: '1970', value: 'YYYY' },
];

/**
 * 由于c7n的format与moment不一致，需要转换
 * @param format
 * @returns {*}
 */
export function getRealFormat(format) {
  return format
    ?.replace('yyyy', 'YYYY')
    ?.replace('mm', 'MM')
    ?.replace('dd', 'DD')
    ?.replace(':MM', ':mm');
}

/**
 * 判断富文本是否为空值
 * @param content // 富文本内容 String类型
 * @returns {*}
 */
export function getRichTextIsNull(content = '') {
  if (!content) return true;
  if (/* quill对象 */ isJSON(content)) {
    return quillNullContent.includes(content) && !content?.includes?.('"fileSize"');
    // 如果有"fileSize"，说明上传了附件也算是写入数据。
    // （需求出自INC00009916【邦普】pc富文本必填字段，上传附件后依旧校验文字必填）
  }
  if (/* JSON.stringify 一个String类型的数据 */ isJSON(content, 'string')) {
    if (/* quill对象, 传过来的是JSON.stringify之后的数据 */ isJSON(JSON.parse(content))) {
      return quillNullContent.includes(JSON.parse(content)) && !JSON.parse(content)?.includes?.('"fileSize"');
    }
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = JSON.parse(content).substring(0, JSON.parse(content).indexOf('<p data-json='));
    return quillNullContent.includes(data) && !JSON.parse(content)?.includes?.('"fileSize"');
  }
  if (/* 纯字符串 */content.indexOf('<p data-json=') > -1) {
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = content.substring(0, content.indexOf('<p data-json='));
    return quillNullContent.includes(data) && !content?.includes?.('"fileSize"');
  }
  return false;
}

function openViewById(viewId) {
  // 跳转视图配置详情页面
  if (viewId) {
    const search = window.location.href.split('?')[1];
    window.open(`${window.location.origin}#/lc/view/detail/${viewId}?${search}`);
  }
}

/**
 * 打开浏览器页签
 * @param url
 * @param searchStr
 */
function openPageByUrl(url, searchStr = '') {
  if (url) {
    const search = window.location.href.split('?')[1];
    window.open(`${window.location.origin}#${url}?${search}${searchStr}`);
  }
}

/**
 * 根据字段类型返回mock数据
 * @param type
 * @param intl
 * @param isDesign 是否设计模式
 */
function getMockDataByWidgetType(type, intl, isDesign, field) {
  switch (type) {
    case 'Input':
    case 'TextArea':
    case 'RichText':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.text', defaultMessage: '示例文本' });
    case 'AutoNumber':
      return '00001';
    case 'Radio':
    case 'CheckBox':
    case 'SelectBox':
      return intl.formatMessage({ id: 'lcr.common.desc.select', defaultMessage: '选择' });
    case 'Switch':
      return intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' });
    case 'Password':
      return '......';
    case 'EmailField':
      return '<EMAIL>';
    case 'Select':
    case 'MultipleSelect':
      return intl.formatMessage({ id: 'lcr.common.desc.drop.down.select', defaultMessage: '下拉选择' });
    case 'DateTime':
      return '2099-01-01 23:59:59';
    case 'Date':
      return '2099-01-01';
    case 'Time':
      return '23:59:59';
    case 'Range':
      if (isDesign) {
        return '2099-01-01 ~ 2099-12-31';
      }
      return {
        start: '2099-01-01',
        end: '2099-12-31',
      };
    case 'Duration':
      return '24';
    case 'ColorPicker':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.color', defaultMessage: '示例颜色' });
    case 'NumberField':
      return '1';
    case 'FloatNumber':
      return '1.1';
    case 'Currency':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.currency', defaultMessage: '示例货币' });
    case 'Url':
      return 'https://www.zknow.com';
    case 'Upload':
      if (field.get('widgetConfig.fileFormat') === 'multiple') {
        return JSON.stringify([
          { fileName: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }), fileKey: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }) },
          { fileName: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }), fileKey: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }) },
        ]);
      }
      return JSON.stringify([{ fileName: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }), fileKey: intl.formatMessage({ id: 'lcr.common.desc.sample.file', defaultMessage: '示例附件.docx' }) }]);
    case 'Image':
      // eslint-disable-next-line no-case-declarations
      const msg = intl.formatMessage({ id: 'lcr.common.desc.sample.image', defaultMessage: '示例图片.png' });
      return JSON.stringify(`${msg}`);
    case 'MasterDetail':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.text', defaultMessage: '示例文本' });
    case 'Rate':
      return '100';
    case 'Region':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.region', defaultMessage: '上海市-青浦区' });
    case 'IconPicker':
      return 'create';
    case 'Tag':
      return intl.formatMessage({ id: 'lcr.common.desc.sample.tag', defaultMessage: '示例标签' });
    default:
      return intl.formatMessage({ id: 'lcr.common.desc.sample.text', defaultMessage: '示例文本' });
  }
}

// 抛出校验错误信息
async function validateMessage(dataSet, intl) {
  const result = await dataSet?.current?.validate();
  if (!result) {
    const validationErrors = dataSet.getValidationErrors();
    if (validationErrors?.length) {
      const errors = validationErrors[0]?.errors;
      if (errors?.length) {
        errors.map(error => {
          if (error?.errors[0]?.injectionOptions?.label) {
            message.error(`${intl.formatMessage({ id: 'lcr.components.model.required.tip', defaultMessage: '必填字段为空' })}: ${error?.errors[0]?.injectionOptions?.label}`);
          }
          return error;
        });
      }
    }
  }
  return result;
}

const getImageType = (file) => {
  const imageMagicNumberMap = {
    47494638: 'gif',
    FFD8FFE0: 'jpg',
    '89504E47': 'png',
  };
  return new Promise((resolve, reject) => {
    try {
      const filereader = new FileReader();
      filereader.onloadend = (evt) => {
        if (evt.target.readyState === FileReader.DONE) {
          const uint = new Uint8Array(evt.target.result);
          const bytes = [];
          uint.forEach((byte) => {
            bytes.push(byte.toString(16));
          });
          const hex = bytes.join('').toUpperCase();
          let format;
          if (imageMagicNumberMap[hex]) {
            format = imageMagicNumberMap[hex];
          }
          resolve(format);
        }
      };
      const blob = file.slice(0, 4);
      filereader.readAsArrayBuffer(blob);
    } catch {
      reject();
    }
  });
};

export const getServiceItemDs = (id, pageRef) => {
  return pageRef?.current ? pageRef?.current?.get(id)?.current?.formDataSet : null;
};

export const validateServiceItemPage = async (dataRef, flag) => {
  if (!dataRef?.current) return flag ? [true, null] : true;
  const validates = Array.from(dataRef?.current).map(item => {
    const [id, pageRef] = item;
    return [pageRef?.current?.formDataSet?.validate(), getServiceItemDs(id, dataRef)];
  });
  const validateResult = await Promise.all(validates.map(item => item[0]));
  const falseIndex = validateResult.indexOf(false);
  if (flag) {
    return falseIndex === -1 ? [true, null] : [false, validates[falseIndex][1]];
  }
  return falseIndex === -1;
};

export const submitRequestContent = debounce(async ({ dataRef, tenantId }) => {
  if (dataRef?.current) {
    const validate = await validateServiceItemPage(dataRef);
    if (validate) {
      const requests = Array.from(dataRef?.current).reduce((pre, cur) => {
        const [id, pageRef] = cur;
        // 非服务请求的服务项视图提交忽略掉
        if (!pageRef?.current.requestItemFlag) return pre;
        const current = pageRef?.current?.formDataSet?.current?.toData() || {};
        const { price, quantity, __totalPrice, __dirty, ...rest } = pickBy(current, value => value !== null);
        pre.push(
          updateRequestContent({
            tenantId,
            id,
            data: {
              price,
              quantity,
              jsonData: JSON.stringify(rest),
            },
          }).then(res => {
            if (res && !res?.failed) {
              return pageRef?.current.serviceItemRefresh();
            }
          })
        );
        return pre;
      }, []);
      return Promise.all(requests);
    } else {
      return false;
    }
  }
}, 500);

const sec2str = (dr, intl, unit) => {
  const _unit = unit || ['days', 'hours', 'minutes', 'seconds'];
  return _unit.reduce((pre, _key) => {
    const value = moment.duration(dr * 1000)[_key]();
    if (value || pre) {
      pre += `${value}${intl.formatMessage({ id: `lcr.components.desc.${_key}` })}`;
    }
    return pre;
  }, '') || `0${intl.formatMessage({ id: `lcr.components.desc.${_unit?.[_unit.length - 1] || 'seconds'}` })}`;
};

export const sec2string = (dr, intl) => {
  return sec2str(dr, intl, ['days', 'hours', 'minutes']);
};

export const getEstimatedTime = (time, intl) => {
  const current = moment();
  const completionTime = moment(time);
  const completionDayOfWeek = completionTime.format('ddd');
  if (completionTime.isSame(current, 'day')) {
    return completionTime.format('HH:mm');
  }
  const tomorrow = moment().add(1, 'day').startOf('day');
  if (completionTime.isSame(tomorrow, 'day')) {
    return intl.locale === 'zh-CN'
      ? `明天${completionTime.format('HH:mm')}`
      : `${completionTime.format('HH:mm')} tomorrow`;
  }
  if (completionTime.isBetween(
    current.startOf('week').format(),
    current.endOf('week').format(),
    undefined, '[]'
  )) {
    return intl.locale === 'zh-CN'
      ? `本${completionDayOfWeek} ${completionTime.format('HH:mm')}`
      : `${completionTime.format('HH:mm')} on ${completionDayOfWeek}`;
  }
  const nextWeek = moment().add(1, 'weeks');
  if (completionTime.isBetween(
    nextWeek.startOf('week').format(),
    nextWeek.endOf('week').format(),
    undefined, '[]'
  )) {
    return intl.locale === 'zh-CN'
      ? `下${completionDayOfWeek} ${completionTime.format('HH:mm')}`
      : `${completionTime.format('HH:mm')} next ${completionDayOfWeek}`;
  }
  if (moment().isBefore(completionTime, 'year')) {
    return completionTime.format('YYYY-MM-DD HH:mm');
  }
  return completionTime.format('YYYY-MM-DD HH:mm');
};

/**
 * 将普通字符串转为HTML字符串
 * @param text
 * @returns {*}
 */
function transTextToHTML(text, outerElement) {
  try {
    const fragment = document.createElement(outerElement);
    fragment.innerText = text;
    return fragment.innerHTML;
  } catch (e) {
    return text;
  }
}

export const DATE_FIELDS = ['DateTime', 'Date', 'Time'];
export const OPTION_FIELDS = ['MultipleSelect', 'Select', 'SelectBox', 'Radio', 'Tag'];
export const MULTIPLE_FIELDS = ['MultipleSelect', 'SelectBox'];
export const BOOLEAN_FIELDS = ['Switch', 'CheckBox'];
export const FILE_FIELDS = ['Upload', 'Image'];
export const MAX_ROW = 5;

export {
  formatDate,
  getTimeLeft,
  timeConvert,
  handlePromptError,
  removeEndsChar,
  verifyCode,
  getQueryString,
  deepClone,
  daysToMinues,
  durationToDays,
  isScrollBottom,
  randomCode,
  escapeHtml,
  deltaToHtml,
  deltaToHtmlStr,
  isJSON,
  htmlToText,
  htmlToDelta,
  getRichJson,
  createRichHtml,
  durToSec,
  durToDays,
  unitToSec,
  dealDuration,
  randomString,
  parseCustomQuillParse,
  getHtml,
  simplifyField,
  quillNullContent,
  dateTimeOption,
  dateOption,
  timeOption,
  openViewById,
  openPageByUrl,
  getMockDataByWidgetType,
  validateMessage,
  getImageType,
  transTextToHTML,
};
