export const taskType = ['SC_TASK', 'TAS<PERSON>_SLA', 'SC_TASK_JOURNAL', 'PROBLEM_TASK_JOURNAL_AT_PERSON', 'PROBLEM_TASK_PARTICIPANT', 'CHANGE_TASK_ACT', 'CHANGE_TASK_ACT_ITEM', 'DATA_TASK', 'HPFM_GANTT_TASK', 'HPFM_GANTT_TASK_LINK', 'PROBLEM_TASK', 'CHANGE_TASK', 'PROBLEM_TASK_ACT', 'PROBLEM_TASK_ACT_ITEM', 'SC_DELIVERY_TASK_ASSIGNMENT', 'SC_TASK_ACT', 'SC_TASK_ACT_ITEM', 'WF_TASK_INSTANCE_ACT', 'WF_TASK_INSTANCE_ACT_ITEM', 'WF_TASK_INSTANCE_JOURNAL', 'WF_TASK_INSTANCE_JOURNAL_AT_PERSON', 'WF_TASK_INSTANCE_RANGE', 'WF_TASK_TRANSFER', 'WF_WORKFLOW_AGENT', 'PROBLEM_TASK_SLA', 'CHANGE_TASK_TASK_SLA', 'PROBLEM_TASK_TASK_SLA', 'CHANGE_TASK_SLA', 'CHANGE_TASK_ELEMENT', 'SC_TASK_JOURNAL_AT_PERSON', 'SC_ITEM_DELIVERY_PLAN_TASK', 'PROBLEM_TASK_ELEMENT', 'CHANGE_TASK_PARTICIPANT', 'SC_TASK_ELEMENT', 'CHANGE_TASK_JOURNAL', 'CHANGE_TASK_JOURNAL_AT_PERSON', 'PROBLEM_TASK_WORKING_TIME', 'PROBLEM_TASK_JOURNAL', 'WHITE3_SLA'];

export const taskIcon = 'icon-renwu';

export const commonType = {
  WF_TASK_INSTANCE: 'icon-shenpi',
  INCIDENT: 'icon-shijiandan',
  CHANGE: 'icon-biangengdan',
  PROBLEM: 'icon-wentidan',
  SC_REQUEST: 'icon-fuwuqingqiudan',
  SC_REQ_ITEM: 'icon-xuqiu',
  HANDOVER: 'icon-jiaojie',
};

export function getRandomIcon(code) {
  const len = code.length;
  const index = len % 10;
  return `icon-zidingyi${index}`;
}
