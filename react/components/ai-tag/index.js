import React from 'react';
import classnames from 'classnames';
import styles from './AiTag.module.less';
import AiImg from '@/assets/images/yan-ai-white-icon.svg';

export default function AiTag({ name, onClick, className = '' }) {
  const handleClick = () => {
    if (typeof onClick === 'function') {
      onClick();
    }
  };
  return (
    <div
      className={classnames(styles.wrap, className)}
      onClick={handleClick}
    >
      <img src={AiImg} alt="" className={styles.icon} />
      <span className={styles.name}>{name}</span>
    </div>
  );
}
