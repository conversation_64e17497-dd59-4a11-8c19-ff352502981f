@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.wrap {
  display: inline-flex;
  cursor: pointer;
  font-size: 12px;
  color: #fff;
  font-family: Ding<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>, Ding<PERSON><PERSON>;
  font-weight: normal;
  height: 24px;
  background: linear-gradient(135deg, #53ffc6, #439cff, #bb4bff);
  border-radius: 12px;
  padding: 0 8px 0 2px;
  vertical-align: middle;
  align-items: center;
  margin-bottom: 4px;
  margin-left: 12px;

  &:hover {
    background-size: 200% 200%;
    animation: flowColors 1s infinite;
  }

  .icon {
    width: 20px;
    height: 20px;
    margin: 2px 0;
  }

  .name {
    margin-left: 4px;
    white-space: nowrap;
    overflow: hidden;
  }
}

@keyframes flowColors {
  0% {
    background-position: 0 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}
