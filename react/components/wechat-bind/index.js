import React, { useEffect, useMemo } from 'react';
import { message, DataSet } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import useInterval from '../../hooks/use-interval';
import styles from './WechatBind.module.less';

function WechatBind({ personId, tenantId, intl }) {
  const openInfoDataSet = useMemo(() => new DataSet({
    autoQuery: true,
    selection: false,
    paging: false,
    dataKey: null,
    transport: {
      read: {
        url: '/iam/yqc/v1/user-open-account',
        method: 'get',
      },
    },
    fields: [
    ],
  }), []);
  const wechatBindInfoRecord = openInfoDataSet?.find(record => record.get('openAppCode') === 'wechat_open_account');
  const alreadyBindFlag = wechatBindInfoRecord?.get('creationDate');
  useEffect(() => {
    openInfoDataSet.query();
  }, []);
  useInterval(() => {
    openInfoDataSet.query();
    if (wechatBindInfoRecord?.get('creationDate')) {
      message.success(intl.formatMessage({ id: 'lcr.components.desc.operation.success', defaultMessage: '绑定成功' }));
    }
  }, alreadyBindFlag ? null : 1000);
  
  return (
    <div className={styles.wrapper}>
      <img style={{ width: '50%' }} src={`${window._env_.API_HOST}/iam/yqc/a/public/quickResponseCode?userId=${personId}&tenantId=${tenantId}`} alt="" />
      <div style={{ marginBottom: 40, textAlign: 'center' }}>{intl.formatMessage({ id: alreadyBindFlag ? 'wechat.bind.ok.tips' : 'wechat.bind.tips' })}</div>
    </div>
  );
}

export default observer(WechatBind);
