export default ({ tenantId, ticketId }) => {
  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    pageSize: 10,
    totalKey: 'totalElements',
    transport: {
      read: () => {
        return {
          url: ticketId ? `/itsm/v1/${tenantId}/global/tickets/upgrade/${ticketId}` : '',
          method: 'get',
        };
      },
    },
    fields: [
      { name: 'name', type: 'string' },
    ],
  };
};
