import React, { createContext, useMemo, useEffect, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import UpgradeOrderDataSet from './UpgradeOrderDataSet';
import { checkUpgradeOrder } from '@/service';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  (props) => {
    const {
      children,
      AppState: { currentMenuType: { tenantId } },
      context,
      record,
      HeaderStore: {
        getTenantConfig: { themeColor },
      },
    } = props;
    const formDataSet = context?.dsManager?.pageRef?.current?.formDataSet;
    const ticketId = context?.instanceId || formDataSet?.get(0)?.get('id');
    const objectVersionNumber = formDataSet?.get(0)?.get('object_version_number');
    const viewId = context?.dsManager?.viewId;
    const businessObjectCode = context?.viewDataSet?.get(0)?.get('businessObjectCode');
    const prefix = 'upgradeOrder';
    const [upgradeDisplay, setUpgradeDisplay] = useState(false);
    const upgradeOrderDataSet = useMemo(
      () => new DataSet(UpgradeOrderDataSet({ tenantId, ticketId })),
      [ticketId, objectVersionNumber],
    );
    useEffect(() => {
      if (upgradeDisplay) {
        upgradeOrderDataSet.query();
      }
    }, [upgradeDisplay, objectVersionNumber]);

    useEffect(() => {
      if (tenantId && ticketId && businessObjectCode) {
        checkCanDisplay();
      }
    }, [tenantId, ticketId, businessObjectCode]);

    const checkCanDisplay = async () => {
      const res = await checkUpgradeOrder({ tenantId, ticketId, businessObjectCode });
      if (res && !res.failed) {
        setUpgradeDisplay(res.udmUpgradeDisplayFlag);
      }
    };

    useEffect(() => {
      if (formDataSet) {
        // TODO：给表单 formDataSet 增加订阅，可以将需要在 formDataSet 变更后进行操作的 dataSet 放进订阅
        const subscribed = formDataSet.getState('subscribe') || {};
        formDataSet.setState('subscribe', { ...subscribed, upgradeOrderDataSet });
      }
    }, [upgradeOrderDataSet, formDataSet]);

    const value = {
      ...props,
      upgradeOrderDataSet,
      tenantId,
      record,
      context,
      prefix,
      themeColor,
      viewId,
      upgradeDisplay,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
