import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Spin } from 'choerodon-ui/pro';
import { Icon, StatusTag, YqAvatar, ExternalComponent } from '@zknow/components';
import classnames from 'classnames';
import moment from 'moment';
import RichTextPreview from '@/renderer/rich-text-preview';
import { getRichJson } from '@/renderer/utils/utils.js';
import FileItem from '@/renderer/ticket-header/components/file-item';
import Stores from './stores';
import styles from './UpgradeOrder.module.less';

const MainView = () => {
  const { context, tenantId, upgradeOrderDataSet, themeColor, upgradeDisplay, intl } = useContext(Stores);
  const { onJumpNewPage, pageRef } = context;

  const handleOpenDetail = (e, i) => {
    e.stopPropagation();
    onJumpNewPage({
      record: i,
      viewId: i.get('viewDetailId'),
      udmFlag: true,
    });
  };

  const renderCollapseItem = (i) => {
    // 参与人可能没有权限，需要控制跳转和展开
    const redirectDetailFlag = i.get('redirectDetailFlag');
    return (
      <div
        className={classnames({
          [styles.collapse]: true,
          [styles.collapseActive]: !i.getState('foldState') && redirectDetailFlag,
        })}
        onClick={() => {
          if (redirectDetailFlag) {
            i.setState('foldState', !i.getState('foldState'));
          }
        }}
      >
        <div className={styles.collapseLeft}>
          <Icon type="icon-document" theme="filled" size={16} fill={themeColor} style={{ marginRight: 4 }} />
          <div
            className={classnames(styles.link, {
              [styles.notAllow]: !redirectDetailFlag,
            })}
            onClick={(e) => {
              if (redirectDetailFlag) {
                handleOpenDetail(e, i);
              }
            }}
          >{i.get('number')}</div>
        </div>
        <div className={styles.collapseLeftTilte}>{i.get('shortDescription')}</div>
        <div className={styles.collapseRight}>
          {i.get('state') && <StatusTag className={styles.collapseRightState} color={i.get('stateColor') || '#2979ff'}>{i.get('state')}</StatusTag>}
          {i.get('priority') && <StatusTag className={styles.collapseRightPriority} color={i.get('priorityColor') || '#2979ff'} mode="icon">{i.get('priority')}</StatusTag>}
          {i.get('submitter') && <YqAvatar src={i.get('imageUrl')} size={22}>
            {i.get('submitter')}
          </YqAvatar>}
          {i.get('submitter') && <div className={styles.collapseRightName}>{i.get('submitter')}</div>}
          <div>{moment(i.get('lastUpdateDate')).format('YYYY-MM-DD HH:mm')}</div>
          {redirectDetailFlag && <Icon type={!i.getState('foldState') ? 'down' : 'right'} size="12" />}
        </div>
      </div>
    );
  };

  // 获取附加信息
  function getExtraInfo(r, fieldCode) {
    try {
      const content = r?.toData()[fieldCode || 'resolution'];
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  }

  // 渲染音频文件
  function renderAudioArea(r, name) {
    try {
      const descriptionExtraInfo = getExtraInfo(r, name);
      const { audios = [] } = descriptionExtraInfo || {};
      if (audios?.length === 0) return null;
      return <div className="reply-audio">{audios.map((i) => <ExternalComponent system={{ scope: 'itsm', module: 'YqAudio' }} fileInfo={i} />)}</div>;
    } catch {
      //
    }
  }

  // 渲染附件
  function renderAttachmentsArea(r, name) {
    const descriptionExtraInfo = getExtraInfo(r, name);
    const { attachments = [] } = descriptionExtraInfo || {};
    if (attachments?.length === 0) return null;
    return attachments?.map((i, index) => {
      return (
        <FileItem
          data={i}
          isLast={attachments.length - 1 === index}
          tenantId={tenantId}
          intl={intl}
          prefixCls="ticket-header-renderer-file"
        />
      );
    });
  }

  const renderCollapseContent = (i) => {
    const redirectDetailFlag = i.get('redirectDetailFlag');
    return (
      <div className={styles.collapseContent}>
        {i.get('description') && <div className={styles.collapseContentItem}>
          <div className={styles.collapseContentHeader}>
            <span />
            {intl.formatMessage({ id: 'zknow.common.model.description' })}
          </div>
          <div className={styles.collapseContentBox}>
            <RichTextPreview data={i.get('description')} ticketId={i.get('id')} />
            {renderAudioArea(i, 'description')}
            {renderAttachmentsArea(i, 'description')}
          </div>
        </div>}
        {i.get('resolution') && <div className={styles.collapseContentItem}>
          <div className={styles.collapseContentHeader}>
            <span />
            {intl.formatMessage({ id: 'lcr.renderer.desc.upgradeOrder.ticketSolution' })}
          </div>
          <div className={styles.collapseContentBox}>
            <RichTextPreview data={i.get('resolution')} />
            {renderAudioArea(i, 'resolution')}
            {renderAttachmentsArea(i, 'resolution')}
          </div>
        </div>}
        <div className={styles.collapseContentItem}>
          <div className={styles.collapseContentHeader}>
            <span />
            {intl.formatMessage({ id: 'lcr.renderer.desc.upgradeOrder.comment' })}
          </div>
          <div className={styles.collapseContentBox}>
            <ExternalComponent
              system={{
                scope: 'itsm',
                module: 'YQReply',
              }}
              tenantId={i.get('tenantId')}
              ticketId={i.get('id')}
              udmBusinessObjectId={i.get('businessObjectId')}
              udmViewId={i.get('viewDetailId')}
              udmTenantId={i.get('tenantId')}
              isUpgrade
              upgradeReadonly={!redirectDetailFlag}
              upgradeJournalList={i.get('journalList')}
              fallback={<span />}
              formDataSet={pageRef?.current?.formDataSet}
              businessObjectCode={i.get('businessObjectCode')}
            />
          </div>
        </div>
      </div>
    );
  };

  const rendererUpgradeOrderList = () => {
    if (!upgradeOrderDataSet.length) {
      return null;
    } else {
      return upgradeOrderDataSet.map(i => {
        const redirectDetailFlag = i.get('redirectDetailFlag');

        return (
          <>
            {renderCollapseItem(i)}
            {!i.getState('foldState') && redirectDetailFlag && renderCollapseContent(i)}
          </>
        );
      });
    }
  };

  const queryMoreData = async () => {
    await upgradeOrderDataSet.queryMore(upgradeOrderDataSet.currentPage + 1);
  };

  if (!upgradeDisplay || !upgradeOrderDataSet?.length) {
    return null;
  }

  return (
    <div className={styles.upgradeOrder}>
      <Spin dataSet={upgradeOrderDataSet}>
        <div className={styles.upgradeOrderHeader}>
          <span />
          {intl.formatMessage({ id: 'lcr.renderer.desc.upgradeOrder.title' })}
          <div className={styles.num}>{`(${upgradeOrderDataSet?.totalCount})`}</div>
        </div>
        <div className={styles.upgradeOrdercontent}>
          {rendererUpgradeOrderList()}
          {upgradeOrderDataSet.length !== upgradeOrderDataSet.totalCount && <div className={styles.seeMore} onClick={queryMoreData}>
            <Icon type="down" size="16" fill={themeColor} />
            {intl.formatMessage({ id: 'lcr.renderer.desc.upgradeOrder.seeMore' })}
          </div>}
        </div>
      </Spin>
    </div>
  );
};

export default observer(MainView);
