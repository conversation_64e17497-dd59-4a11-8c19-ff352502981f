@import '~choerodon-ui/lib/style/themes/default';

.upgradeOrder {
  margin: 16px 0;

  .upgradeOrderHeader {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 16px;

    > span {
      display: inline-block;
      width: 4px;
      background: @primary-color;
      height: 16px;
      margin-right: 8px;
      border-radius: 2px;
      position: relative;
    }

    > div {
      opacity: 0.65;
      margin-left: 8px;
    }
  }

  .upgradeOrdercontent {
    //max-height: 400px;
    //overflow-y: auto;

    .collapse {
      margin-top: 8px;
      width: 100%;
      height: 46px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid rgba(203, 210, 220, 0.5);
      display: flex;
      align-items: center;
      padding: 0 0 0 16px;
      cursor: pointer;

      &:hover {
        background: #f7f9fc;
      }

      .collapseRight {
        :global {
          .yq-cmp-status-tag {
            > div {
              max-width: 110px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .yq-cmp-status-tag-with-icon-text {
            max-width: 100px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .collapseLeft, .collapseRight {
        display: flex;
        align-items: center;

        > div {
          margin-right: 12px;
        }

        > span {
          margin-right: 12px;
        }
      }

      .collapseLeft {
        display: flex;

        .link {
          color: @primary-color;

          &:hover {
            text-decoration: underline;
          }
        }

        .notAllow {
          color: #12274d !important;
          text-decoration: none !important;
        }
      }

      .collapseLeftTilte {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .collapseRightName {
        max-width: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .collapseContent {
      max-height: 400px;
      overflow-y: auto;
      background: #f7f9fc;
      padding: 0 16px 16px;
      border: 1px solid rgba(203, 210, 220, 0.5);
      border-top: 0;

      .collapseContentItem {
        background-color: #fff;
        padding: 16px;
        margin-bottom: 12px;
        border-radius: 4px;
      }

      .collapseContentHeader {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-bottom: 8px;

        > span {
          display: inline-block;
          width: 4px;
          background: @primary-color;
          height: 16px;
          margin-right: 8px;
          border-radius: 2px;
          position: relative;
        }
      }
    }

    .collapseActive {
      background: #f7f9fc;
      border-bottom: 0;
    }

    .seeMore {
      display: flex;
      align-items: center;
      justify-content: center;
      color: @primary-color;
      cursor: pointer;

      > span {
        margin-right: 8px;
      }
    }
  }
}
