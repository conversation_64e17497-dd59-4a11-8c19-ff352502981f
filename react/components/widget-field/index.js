import React from 'react';
import { observer } from 'mobx-react-lite';
import '../editor-register/editors';
import { EditorRegister } from '@zknow/utils';

const WidgetField = observer((props) => {
  const { widgetType } = props;
  if (widgetType && EditorRegister.get(widgetType)) {
    return EditorRegister.get(widgetType).preview(props);
  }
  return null;
});

export default WidgetField;

/* externalize: WidgetField */
