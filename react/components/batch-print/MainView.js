/* eslint-disable no-chinese/no-chinese */
import React, { useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Select, Form, Output, SelectBox } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import BatchPrintHistory from '@/components/batch-print-history';
import Store from './stores';
import './index.less';

const MainView = () => {
  const context = useContext(Store);
  const { intl, intlPrefix, prefixCls, printDataSet, selectRecords, historyDataSet, templateOptionDs, tenantId } = context;
  const countLimit = 50;
  const limitFlag = selectRecords?.length > countLimit || templateOptionDs?.getState('listFlag');
  useEffect(() => {
    if (limitFlag) {
      printDataSet.current.set('printType', 'back');
    }
  }, [limitFlag]);
  useEffect(() => {
    const record = templateOptionDs?.find(r => r?.get('templateId') === printDataSet?.current?.get('templateId'));
    if (record) {
      templateOptionDs.setState('listFlag', record.get('listFlag'));
    }
  }, [printDataSet?.current?.get('templateId')]);
  const handleHistory = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.print.template.history', defaultMessage: '打印历史' }),
      children: (
        <BatchPrintHistory />
      ),
      drawer: true,
      style: { width: 800 },
      footer: null,
    });
  };
  return (
    <div className={`${prefixCls}-main`}>
      <div className={`${prefixCls}-info`}>
        <Icon type="Attention" theme="multi-color" size="20" fill={['#2979FF', '#2979FF', '#ffffff', '#2979FF']} />
        <div className={`${prefixCls}-info-content`}>
          <div>
            {intl.formatMessage({ id: 'lcr.components.desc.print.tempalte.info.prefix', defaultMessage: '数据条数超过50条、或模板中包含多行列表内容时，仅能进行后台打印。' })}  
          </div>
          <div>
            {intl.formatMessage({ id: 'lcr.components.desc.print.tempalte.info.suffix', defaultMessage: '进行后台打印后可到“打印历史”中进行查看生成的打印任务。' })}
          </div>
        </div>
      </div>
      <Form dataSet={printDataSet}>
        <Select name="templateId" />
        <Output label={intl.formatMessage({ id: 'lcr.components.desc.print.template.length', defaultMessage: '数据条数' })} value={`${selectRecords?.length}${intl.formatMessage({ id: 'lcr.components.desc.print.template.length.unit', defaultMessage: '条' })}`} className={`${prefixCls}-length`} />
        <Output
          name="printType"
          renderer={() => <>
            <SelectBox name="printType" disabled={limitFlag} />
            <span onClick={handleHistory} className={`${prefixCls}-history-button`}>{`${intl.formatMessage({ id: 'lcr.components.desc.print.template.history.enter', defaultMessage: '查看打印历史' })}>>`}</span>
          </>}
        />
      </Form>
    </div>
  );
};

export default observer(MainView);
