import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId },
        currentLanguage: language,
      },
      printDataSet,
      selectRecords,
    } = props;

    const intlPrefix = 'batch.print';
    const prefixCls = 'batch-print';

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      language,
      printDataSet,
      selectRecords,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
