import React from 'react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import FileSaver from 'file-saver';
import noop from 'lodash/noop';
import copy from 'copy-to-clipboard';
import R<PERSON><PERSON>iewer from '@hanyk/rc-viewer';
import { FileShower, Icon, YqPreview } from '@zknow/components';
import { getCookieToken } from '@zknow/utils';
import { Tooltip, Modal } from 'choerodon-ui/pro';
import { getFileIcon, getFileName, getFileType } from '../editor-register/utils';

import './index.less';

const modalKey = Modal.key();
const IMAGE_FORMAT_LIST = ['jpg', 'jpeg', 'png', 'gif'];

const FileInfo = observer((props) => {
  const { fileName, fileSize, handlePreview = noop } = props;
  const size = fileSize / 1024 > 1024
    ? `${Number(fileSize / 1024 / 1024).toFixed(1)}MB`
    : `${Number(fileSize / 1024).toFixed(1)}KB`;
  return (
    <div className="lc-file-default-info" onClick={handlePreview}>
      <Tooltip title={fileName} theme="dark">
        <div className="lc-file-default-name">{fileName}</div>
      </Tooltip>
      <div className="lc-file-default-name-size">{`(${size})`}</div>
    </div>
  );
});

function FileItem(props) {
  const { file, intl, onDelete } = props;
  const { fileKey, fileSize, md5, udmTenantId } = file || {};
  let imageViewRef;

  // 预览附件
  function previewFile(filekey, src) {
    const fileType = getFileType(filekey);
    if (IMAGE_FORMAT_LIST.includes(fileType)) {
      const { viewer } = imageViewRef;
      if (viewer) {
        viewer.element.firstChild.src = src;
        viewer.update();
        viewer.show();
      }
    } else {
      Modal.open({
        key: modalKey,
        title: intl.formatMessage({ id: 'lcr.components.desc.file.preview', defaultMessage: '附件预览' }),
        children: (
          <YqPreview fileKey={filekey} udmTenantId={udmTenantId} />
        ),
        destroyOnClose: true,
        fullScreen: true,
        footer: null,
      });
    }
  }

  // 下载附件
  function handleDownload(url) {
    let newUrl;
    const token = getCookieToken();
    if (url.includes('?')) {
      newUrl = `${url}&access_token=${token}`;
    } else {
      newUrl = `${url}?access_token=${token}`;
    }
    if (navigator?.userAgent?.includes('wxwork')) {
      copy(newUrl);
      Modal.info({
        title: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips', defaultMessage: '附件地址已复制到剪切板' }),
        children: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips.detail', defaultMessage: '由于企业微信限制，需要您粘贴附件地址到浏览器中下载附件。' }),
      });
    } else {
      FileSaver.saveAs(newUrl, getFileName(url));
    }
  }

  return (
    <div>
      <FileShower
        fileKey={fileKey}
      >
        {({ src }) => (
          src ? (
            <div
              tabIndex="-1"
              onClick={(e) => e.stopPropagation()}
              className="lc-file"
              style={{ position: 'relative' }}
            >
              <span style={{ width: '100%' }}>
                <Icon
                  type={getFileIcon(getFileType(fileKey)).iconType}
                  theme="filled"
                  fill={getFileIcon(getFileType(fileKey)).color}
                  size="16"
                  className="lc-file-link"
                />
                <span
                  style={{ width: '100%' }}
                >
                  <FileInfo
                    fileName={getFileName(fileKey)}
                    fileSize={fileSize}
                    handlePreview={() => previewFile(fileKey, src)}
                  />
                </span>
              </span>
              <span style={{ position: 'absolute', right: 8, top: 6, zIndex: 10, backgroundColor: '#f2f3f5' }}>
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' })} theme="dark">
                  <Icon
                    type="PreviewOpen"
                    size="14"
                    onClick={() => previewFile(fileKey, src)}
                    className="lc-file-icon"
                  />
                </Tooltip>
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })} theme="dark">
                  <Icon
                    type="Download"
                    size="14"
                    className="lc-file-icon"
                    onClick={() => handleDownload(src, intl)}
                  />
                </Tooltip>
                {onDelete ? (
                  <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })} theme="dark">
                    <Icon
                      type="delete"
                      size="14"
                      onClick={() => onDelete(md5 || fileKey)}
                      className="lc-file-icon"
                    />
                  </Tooltip>
                ) : null}
              </span>
            </div>
          ) : null
        )}
      </FileShower>
      <RcViewer
        ref={(ref) => {
          imageViewRef = ref;
        }}
        options={{
          toolbar: {
            zoomIn: { show: true },
            zoomOut: { show: true },
            oneToOne: { show: true },
            reset: { show: true },
            prev: { show: false },
            play: { show: true },
            next: { show: false },
            rotateLeft: { show: true },
            rotateRight: { show: true },
            flipHorizontal: { show: true },
            flipVertical: { show: true },
          },
        }}
        style={{ display: 'none' }}
      >
        <img alt="src" />
      </RcViewer>
    </div>
  );
}

export { FileInfo };

export default injectIntl(observer(FileItem));

/* externalize: FileItem */
