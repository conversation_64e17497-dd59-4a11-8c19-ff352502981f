@import '~choerodon-ui/lib/style/themes/default';

.lc-file {
  // 2022年8月23日 b2c去掉最大宽度。
  // max-width: 5.6rem;
  display: flex;
  justify-content: space-between;
  border-radius: 0.04rem;
  padding: 0 0.02rem;
  width: 100%;
  height: 28px;
  line-height: 0.32rem;
  margin-bottom: 0.05rem;
  border: 1px solid transparent;
  .c7n-progress-circle-path {
    stroke: @primary-color;
  }
  &:focus,
  &:active {
    background: @minor-color;
    border: 1px solid @primary-color;
  }
  &-default {
    width: 235px;
  }

  &-default-info {
    height: 0.28rem;
    :hover {
      cursor: pointer;
    }
  }

  &-info {
    height: 0.28rem;
    width: 100%;
  }

  &-default-name {
    display: inline-block;
    max-width: 2rem;
    height: 0.28rem;
    word-wrap: break-word;
    line-height: 0.28rem;
    font-size: 0.12rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(18, 39, 77, 0.85);
    &-size {
      display: inline-block;
      max-width: 0.8rem;
      height: 0.28rem;
      word-wrap: break-word;
      margin-left: 0.12rem;
      margin-right: 0.12rem;
      line-height: 0.28rem;
      font-size: 0.12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(18, 39, 77, 0.54);
    }
  }

  &-name {
    display: inline-block;
    width: calc(100% - 1.53rem);
    max-width: 5.45rem;
    height: 0.28rem;
    word-wrap: break-word;
    line-height: 0.28rem;
    font-size: 0.12rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: rgba(18, 39, 77, 0.85);
    &-size {
      display: inline-block;
      max-width: 2.28rem;
      height: 0.28rem;
      word-wrap: break-word;
      margin-left: 0.12rem;
      margin-right: 0.12rem;
      line-height: 0.28rem;
      font-size: 0.12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(18, 39, 77, 0.54);
    }
  }

  & > span {
    display: flex;
    align-items: center;
    & > span {
      display: flex;
      align-items: center;
      top: -0.02rem;
    }
  }

  &:hover {
    background: #f2f3f5;
    border: 1px solid #f2f3f5;

    .lc-file-icon {
      color: rgba(18, 39, 77, 0.65);
      display: inline-flex !important;
    }

    .cancel-upload {
      opacity: 1;
    }
  }

  .cancel-upload {
    opacity: 0;
    display: flex;
    position: absolute;
    right: -1px;

    &:hover {
      cursor: pointer;
    }
  }

  &-link {
    margin-right: 0.08rem;
  }
  &-icon.yqcloud-icon-park-wrapper {
    display: none !important;
    margin-right: 0.08rem;
    cursor: pointer;
  }
  &-icon:last-child {
    margin-right: 0;
  }
}
