import { Record } from 'choerodon-ui/dataset';

export default ({ intl, pageSize = 20 }) => {
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name' });

  return {
    autoQuery: false,
    pageSize,
    fields: [
      { name: 'id', type: 'string' },
      {
        name: 'name',
        type: 'string',
        label: nameLabel,
      },
    ],
    transport: {
      read: () => ({
        url: '/hpfm/v1/lovs/analysis/sql/data?lovCode=ITSM_TRANSFER_GROUP',
        method: 'GET',
      }),
    },
    events: {
      load: ({ dataSet }) => {
        const newRecord = new Record({ id: 'title', name: nameLabel }, dataSet, 'sync');
        newRecord.disabled = true;
        if (dataSet.length) { dataSet.unshift(newRecord); }
      },
    },
  };
};
