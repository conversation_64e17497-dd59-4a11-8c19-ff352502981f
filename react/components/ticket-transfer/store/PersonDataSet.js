import { Record } from 'choerodon-ui/dataset';

export default ({ intl, pageSize = 20, autoFillAssignGroup }) => {
  const emailLabel = intl.formatMessage({ id: 'zknow.common.model.email' });
  const nameLabel = intl.formatMessage({ id: 'zknow.common.model.name' });
  const workloadLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.workload' });
  const skillLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.skill' });
  const statusLabel = intl.formatMessage({ id: 'zknow.common.model.status' });

  return {
    autoQuery: false,
    pageSize,
    fields: [
      { name: 'id', type: 'string' },
      {
        name: 'realName',
        type: 'string',
        label: nameLabel,
      },
      {
        name: 'email',
        type: 'string',
        label: emailLabel,
      },

      {
        name: 'onHands',
        label: workloadLabel,
      },
      {
        name: 'skill',
        type: 'string',
        label: skillLabel,
      },
      {
        name: 'status',
        type: 'string',
        label: statusLabel,
      },
    ],
    transport: {
      read: () => ({
        url: `/hpfm/v1/lovs/analysis/sql/data?lovCode=${autoFillAssignGroup ? 'ITSM_TRANSFER_USER_GROUP' : 'ITSM_TRANSFER_USER'}`,
        method: 'GET',
      }),
    },
    events: {
      load: ({ dataSet }) => {
        const newRecord = new Record({ id: 'title', realName: nameLabel, email: emailLabel, onHands: workloadLabel, skill: skillLabel, status: statusLabel }, dataSet, 'sync');
        newRecord.disabled = true;
        if (dataSet.length) { dataSet.unshift(newRecord); }
      },
    },
  };
};
