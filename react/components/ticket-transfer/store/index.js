import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import TransferDataSet from './TransferDataSet';
import PersonDataSet from './PersonDataSet';
import GroupDataSet from './GroupDataSet';

const Store = createContext();
export default Store;

export const WF_TASK_INSTANCE = 'WF_TASK_INSTANCE';

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      dataSet: formDataSet,
      widgetConfig,
      pageRef,
      replyEnable,
    } = props;
    // 适配【审批单】，业务对象是【任务实例】时，隐藏处理组的相关操作
    //  注意：任务实例业务对象中没有 assignee_person_id 字段，处理人字段的 code 为 person_id
    const isWFTaskInstance = props?.pageRef?.current?.pageRecord?.get('businessObjectCode') === WF_TASK_INSTANCE;
    const personFieldName = isWFTaskInstance ? 'person_id' : 'assignee_person_id';
    const personNameFieldName = isWFTaskInstance ? 'person_id:real_name' : 'assignee_person_name';

    const prefixCls = 'ticket-transfer';
    // 转交组件可以配置【清除默认人】和【清除默认组】的配置，以防止转交时默认带上当前处理人和组
    const hasDefaultPerson = !widgetConfig?.clearDefaultPerson;
    const hasDefaultGroup = !widgetConfig?.clearDefaultGroup;
    const autoFillAssignGroup = widgetConfig?.autoFillAssignGroup;

    const transferGroupLovId = useMemo(() => {
      return formDataSet?.getState('transferInfo')?.transferGroupLovId;
    }, [formDataSet?.getState('transferInfo')?.transferGroupLovId]);

    const transferPersonLovId = useMemo(() => {
      return formDataSet?.getState('transferInfo')?.transferPersonLovId;
    }, [formDataSet?.getState('transferInfo')?.transferPersonLovId]);

    const transferTypeFlag = useMemo(() => {
      const typeFlag = formDataSet?.getState('transferInfo')?.transferTypeFlag;
      const groupId = formDataSet?.getState('transferInfo')?.transferGroupLovId;
      const userId = formDataSet?.getState('transferInfo')?.transferPersonLovId;
      if (isWFTaskInstance) {
        // 任务实例没有组配置
        return userId && typeFlag;
      } else {
        return groupId && userId && typeFlag;
      }
    }, [
      isWFTaskInstance,
      formDataSet?.getState('transferInfo')?.transferTypeFlag,
      formDataSet?.getState('transferInfo')?.transferGroupLovId,
      formDataSet?.getState('transferInfo')?.transferPersonLovId,
    ]);

    const transferDataSet = React.useMemo(() => new DataSet(TransferDataSet({
      intl,
      currentRecord: formDataSet?.current,
      widgetConfig,
      replyEnable,
      tenantId,
      ticketId: formDataSet?.current?.get('id'),
      transferGroupLovId,
      transferPersonLovId,
      transferTypeFlag,
      hasDefaultPerson,
      hasDefaultGroup,
      autoFillAssignGroup,
      isWFTaskInstance,
      personFieldName,
      personNameFieldName,
    })), [
      intl,
      formDataSet?.current,
      widgetConfig,
      replyEnable,
      formDataSet?.current?.get('id'),
      transferGroupLovId,
      transferPersonLovId,
      transferTypeFlag,
      hasDefaultPerson,
      hasDefaultGroup,
      autoFillAssignGroup,
      isWFTaskInstance,
      personFieldName,
      personNameFieldName,
    ]);

    const personDataSet = React.useMemo(() => new DataSet(PersonDataSet({
      intl, autoFillAssignGroup,
    })), [intl, autoFillAssignGroup]);

    const groupDataSet = React.useMemo(() => new DataSet(GroupDataSet({ intl })), [intl]);

    const noPagingPersonDataSet = React.useMemo(() => new DataSet(PersonDataSet({ intl })), [intl]);

    const noPagingGroupDataSet = React.useMemo(() => new DataSet(GroupDataSet({ intl })), [intl]);

    const value = {
      ...props,
      prefixCls,
      tenantId,
      intl,
      personDataSet,
      groupDataSet,
      transferDataSet,
      formDataSet,
      pageRef,
      replyEnable, // 业务对象是否开启回复
      noPagingPersonDataSet, // 不分页的人
      noPagingGroupDataSet, // 不分页组
      transferGroupLovId, // 动作上传过来的组值列表ID
      transferPersonLovId, // 动作上传过来的人值列表ID
      transferTypeFlag, // 当前动作是否是转交类型
      hasDefaultPerson,
      hasDefaultGroup,
      autoFillAssignGroup,
      isWFTaskInstance, // 业务对象是否为 任务实例
      personFieldName,
      personNameFieldName,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
