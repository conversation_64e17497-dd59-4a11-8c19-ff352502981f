/* eslint-disable no-chinese/no-chinese */
import omitBy from 'lodash/omitBy';
import { getQueryParams } from '@zknow/utils';
import compact from 'lodash/compact';
import { transformResponse, transformField } from '@/utils/lovConfig';
import { filterGroupByUserIdCondition, filterUserByGroupIdCondition, filterUserByGroup } from './conditions';

/* 拷贝一份对对象 */
function deepClone(obj) {
  const copy = JSON.stringify(obj);
  const objClone = JSON.parse(copy);
  return objClone;
}

function toFlag(boo) {
  return boo ? 0 : 1;
}

export default ({
  intl,
  currentRecord,
  widgetConfig,
  replyEnable,
  tenantId,
  ticketId,
  transferGroupLovId,
  transferPersonLovId,
  transferTypeFlag,
  hasDefaultPerson,
  hasDefaultGroup,
  autoFillAssignGroup,
  isWFTaskInstance,
  personFieldName,
}) => {
  const groupIDLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.group_id' });
  const personIDLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.person_id' });
  const transferDescLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.transfer_desc' });
  const customerHiddenLabel = intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.customer_hidden' });

  const defaultCondition = {
    condition: 'AND',
    filters: [
      {
        condition: 'AND',
        filter: 'is not',
        widgetType: 'Lov',
        componentType: 'Lov',
        field: 'id',
        fieldValue: ticketId,
      },
    ],
  };

  const lovTransformResponse = (originData) => {
    try {
      const jsonData = JSON.parse(originData);

      return {
        ...jsonData,
        content: jsonData?.content?.map(item => {
          return {
            ...item,
            primaryKey: item.id,
          };
        }) || [],
      };
    } catch (error) {
      return [];
    }
  };

  function getLovQueryAxiosConfig(props) {
    const { id, type } = props || {};

    return (lovCode, lovConfig = {}, { data, params }) => {
      lovConfig.method = 'POST';
      const extraConditions = [];
      let specCondition = null; // 选了中间表，人员的查询还需要单独过滤下组
      if (type === 'USER') {
        const user = deepClone(filterUserByGroupIdCondition);
        // NOTE 这里目前只能写死，要不把这个下掉吧，用户自己选择了值列表，就不要有互筛了
        if (lovConfig.idField === 'user_id') {
          user.filters.push({
            field: 'user_id', // NOTE 动作那边开放选择人员中间表，那么人员的值字段，不再是 id 而是 user_id
            filter: 'is in list',
            rcField: ['user_id'],
            condition: 'OR',
            fieldName: '主键(id)',
            expression: "/*\n** Script must return a value.\n** example:\n** var userName = $GetCurrentAttribute('iam_user_id:login_name')\n** return userName;\n*/\nreturn $Invoke('yqcloud-itsm', 'UserGroupAssignFilterInvoker', 'IAM_USER_GROUP-《group_id》');\n",
            filterUuid: 'df792d20-fd0f-451d-8cce-3b07b0234cb1',
            widgetType: 'NumberField',
            componentType: 'NumberField',
            fieldValueType: 'EXPRESSION',
          });
          specCondition = filterUserByGroup;
          specCondition.filters[0].fieldValue = id?.id || id;
        }
        user.filters.forEach(item => {
          item.expression = item.expression?.replace('《group_id》', id?.id || id);
        });
        extraConditions.push(user);
      } else if (type === 'GROUP') {
        const group = deepClone(filterGroupByUserIdCondition);
        // 如果有 user_id 那么一定是用了中间表，所以选 user_id 就好
        group.filters[0].expression = group.filters[0].expression?.replace('《user_id》', id?.user_id || id?.id || id);
        extraConditions.push(group);
      }

      return {
        url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
        method: 'POST',
        data: {
          conditions: compact([...extraConditions, specCondition, defaultCondition]),
          params: {
            ...getQueryParams(data),
            __page_params: data?.__page_params,
          },
        },
        params,
        transformResponse: lovTransformResponse,
      };
    };
  }

  const getLovConfig = () => {
    return {
      lovDefineAxiosConfig: lovCode => ({
        url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
        method: 'GET',
        transformResponse: data => {
          const tr = transformResponse(data, data?.name, (map, f) => transformField(map, f), intl, tenantId);
          // 为了保持交互的统一，强制开启输入，即使值列表不支持查询
          return { ...tr, editableFlag: 'Y' };
        },
      }),
      lovQueryAxiosConfig: getLovQueryAxiosConfig(),
    };
  };

  const personId = hasDefaultPerson ? currentRecord?.get(personFieldName) : undefined;
  const personName = hasDefaultPerson ? currentRecord?.get(`${personFieldName}:name`) || currentRecord?.get(`${personFieldName}:real_name`) : undefined;
  const personRealName = hasDefaultPerson ? currentRecord?.get(`${personFieldName}:real_name`) : undefined;
  const assignmentGroupId = hasDefaultGroup ? currentRecord?.get('assignment_group_id') : undefined;
  const groupName = hasDefaultGroup ? currentRecord?.get('assignment_group_id:name') : undefined;

  let defaultGroupData = transferTypeFlag ? {
    lov_assignment_group_id: assignmentGroupId,
    'lov_assignment_group_id:name': groupName,
  } : {
    normal_assignment_group_id: assignmentGroupId,
    'normal_assignment_group_id:name': groupName,
  };

  if (isWFTaskInstance) {
    defaultGroupData = null;
  }

  // 视图配置【默认对客户隐藏】，为 true，则需要给 customer_hidden 默认值为 0
  const defaultCustomerHidden = widgetConfig?.transferDescDefaultHidden;

  // 表单上的默认值
  const defaultData = transferTypeFlag ? [
    {
      lov_assignee_person_id: personId,
      'lov_assignee_person_id:real_name': personRealName,
      'lov_assignee_person_id:name': personName,
      ...defaultGroupData,
      customer_hidden: typeof defaultCustomerHidden === 'boolean' ? toFlag(defaultCustomerHidden) : 0,
    },
  ] : [
    {
      normal_assignee_person_id: personId,
      'normal_assignee_person_id:real_name': personRealName,
      ...defaultGroupData,
      customer_hidden: typeof defaultCustomerHidden === 'boolean' ? toFlag(defaultCustomerHidden) : 0,
    },
  ];

  let groupFields = transferTypeFlag ? [
    {
      name: 'lov_assignment_group_id',
      type: 'object',
      label: groupIDLabel,
      // textField: 'name',
      // valueField: 'id',
      required: widgetConfig?.assignGroupRequired === true,
      lovCode: transferGroupLovId,
      transformRequest: (value) => {
        return value?.id || null;
      },
      transformResponse: (value, data) => {
        if (data.lov_assignment_group_id) {
          return {
            id: value,
            name: data['lov_assignment_group_id:name'],
          };
        }
        return null;
      },
      ...getLovConfig(),
      dynamicProps: {
        lovQueryAxiosConfig: ({ record }) => {
          const userId = record?.get('lov_assignee_person_id');
          if (userId) {
            return getLovQueryAxiosConfig({ id: userId, type: 'GROUP' });
          }
          return getLovQueryAxiosConfig();
        },
      },
    },
    { name: 'lov_assignment_group_id:name', type: 'string', bind: 'lov_assignment_group_id.name' },
  ] : [
    {
      name: 'normal_assignment_group_id',
      type: 'object',
      lovCode: 'ITSM_TRANSFER_GROUP',
      label: groupIDLabel,
      textField: 'name',
      valueField: 'id',
      required: widgetConfig?.assignGroupRequired === true,
      transformRequest: (value) => {
        return value?.id || null;
      },
      transformResponse: (value, data) => {
        if (data.normal_assignment_group_id) {
          return {
            id: value,
            name: data['normal_assignment_group_id:name'],
          };
        }
        return null;
      },
      dynamicProps: {
        lovPara: ({ record }) => {
          const assignmentPerson = record?.get?.('normal_assignee_person_id');
          return { userId: assignmentPerson?.userId || assignmentPerson?.id };
        },
      },
    },
    { name: 'normal_assignment_group_id:name', type: 'string', bind: 'normal_assignment_group_id.name' },
  ];

  if (isWFTaskInstance) {
    groupFields = [];
  }

  const transferField = transferTypeFlag ? [
    ...groupFields,
    {
      name: 'lov_assignee_person_id',
      type: 'object',
      label: personIDLabel,
      // textField: 'real_name',
      // valueField: 'id',
      required: widgetConfig?.assignPersonRequired === true,
      lovCode: transferPersonLovId,
      transformRequest: (value) => {
        // 很烦。既能选中间表也可以选值列表
        return value?.user_id || value?.id || null;
      },
      transformResponse: (value, data) => {
        if (data.lov_assignee_person_id) {
          const pName = data['lov_assignee_person_id:real_name'] || data['lov_assignee_person_id:name'];
          return {
            id: value,
            name: pName,
            realName: pName,
            real_name: pName,
            'user_id:real_name': pName,
          };
        }
        return null;
      },
      ...getLovConfig(),
      dynamicProps: {
        lovQueryAxiosConfig: ({ record }) => {
          const groupId = record?.get('lov_assignment_group_id');
          if (groupId) {
            return getLovQueryAxiosConfig({ id: groupId, type: 'USER' });
          }
          return getLovQueryAxiosConfig();
        },
      },
    },
    { name: 'lov_assignee_person_id:name', type: 'string', bind: 'lov_assignee_person_id.name' },
    { name: 'lov_assignee_person_id:real_name', type: 'string', bind: 'lov_assignee_person_id.real_name' },
  ] : [
    ...groupFields,
    {
      name: 'normal_assignee_person_id',
      type: 'object',
      label: personIDLabel,
      lovCode: autoFillAssignGroup ? 'ITSM_TRANSFER_USER_GROUP' : 'ITSM_TRANSFER_USER',
      // valueField: 'id',
      textField: 'realName',
      required: widgetConfig?.assignPersonRequired === true,
      transformRequest: (value) => {
        // ITSM_TRANSFER_USER_GROUP 查出的数据人员 id 用的 userId
        return value?.userId || value?.id || null;
      },
      transformResponse: (value, data) => {
        if (data.normal_assignee_person_id) {
          return {
            id: value,
            name: data['normal_assignee_person_id:real_name'],
            realName: data['normal_assignee_person_id:real_name'],
          };
        }
        return null;
      },
      dynamicProps: {
        lovPara: ({ record }) => ({ groupId: record?.get?.('normal_assignment_group_id')?.id }),
      },
    },
    { name: 'normal_assignee_person_id:real_name', type: 'string', bind: 'normal_assignee_person_id.realName' },
  ];

  return {
    autoCreate: true,
    fields: [
      ...transferField,
      {
        name: 'transfer_desc',
        type: 'string',
        defaultValue: '',
        label: transferDescLabel,
        required: replyEnable && widgetConfig?.transferDescVisible === true && widgetConfig?.transferDescRequired === true,
      },
      {
        name: 'customer_hidden',
        type: 'number',
        trueValue: 0,
        falseValue: 1,
        label: customerHiddenLabel,
      },
      {
        name: '_temp_',
        ignore: 'always',
      },
    ],
    events: {
      update: ({ record, name, value }) => {
        if (['normal_assignment_group_id', 'lov_assignment_group_id'].includes(name) && !value) {
          record.set({
            [personFieldName]: undefined,
            [`${personFieldName}:real_name`]: undefined,
            [`${personFieldName}:name`]: undefined,
            normal_assignee_person_id: undefined,
            'normal_assignee_person_id:real_name': undefined,
            'normal_assignee_person_id:name': undefined,
            lov_assignee_person_id: undefined,
            'lov_assignee_person_id:real_name': undefined,
            'lov_assignee_person_id:name': undefined,
          });
        }
        if (!isWFTaskInstance && autoFillAssignGroup && ['normal_assignee_person_id', 'lov_assignee_person_id'].includes(name) && value) {
          const { userGroupId, user_group_id: ugi, groupName: gn } = value;
          const uGroupId = userGroupId || ugi;
          const uGroupName = gn || value['user_group_id:name'];

          // 如果人 lov 的列表项带着组的信息，并且视图里面配置了自动「填充处理组」功能
          // 则在选择了人后需要改变组
          if (uGroupId) {
            record.set({
              normal_assignment_group_id: { id: uGroupId, name: uGroupName },
              lov_assignment_group_id: { id: uGroupId, name: uGroupName },
            });
          }
        }
        currentRecord?.set(omitBy(record?.toJSONData(), (v, k) => `${k}`.startsWith('_')));
      },
      load: ({ dataSet }) => {
        // FIXME: 这里不得不增加一次 set，为了刷新该 dataSet 的 validate，需要更好的方法
        if (dataSet?.current) {
          dataSet.current.set('_temp_', Date.now());
        }
      },
    },
    data: defaultData,
  };
};
