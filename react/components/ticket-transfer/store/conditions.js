/* eslint-disable no-chinese/no-chinese */

// 根据人id筛选组，适用于组的lov
const filterGroupByUserIdCondition = {
  filters: [
    {
      field: 'id',
      filter: 'is in list',
      rcField: ['id'],
      condition: 'AND',
      fieldName: '主键(id)',
      expression: "/*\n** Script must return a value.\n** example:\n** var userName = $GetCurrentAttribute('iam_user_id:login_name')\n** return userName;\n*/\nreturn $Invoke('yqcloud-itsm', 'UserGroupAssignFilterInvoker', 'IAM_USER-《user_id》');\n",
      fieldCrumb: [{ id: '187592782519468032', code: 'id', name: '主键(id)', path: 'id', _token: 'MEgoYaVwNF5GDeElJj4FXHyXkSMQXRpSFEnAWKRPrjXmN+IAtimycjwweP6TPq89xwxx7cChRowLLK4Y27qABzcU/7I2FFtP4uz8X4/IITY=', parent: null, tlFlag: false, __dirty: false, isFirst: true, domainId: '0', editType: 'ALWAYS_EDIT', hideFlag: false, nameFlag: false, parentId: '0', tenantId: '0', auditFlag: false, createdBy: 10, maxLength: '19', minLength: null, queryFlag: true, customFlag: false, presetFlag: true, searchFlag: true, syncedFlag: true, widgetType: 'NumberField', description: null, placeHolder: null, creationDate: '2021-06-01 15:46:44', defaultLabel: null, defaultValue: null, relationType: null, requiredType: 'ALWAYS_NOT_REQUIRED', sysUserAgent: null, widgetConfig: {}, conditionFlag: true, lastUpdatedBy: 0, relationLovId: null, calculatedFlag: false, lastUpdateDate: '2021-08-11 11:47:45', parentObjectId: null, relationFields: null, relationObject: null, persistableFlag: true, relationLovName: null, businessObjectId: '187592773245861888', calculatedConfig: null, relationObjectId: null, businessObjectName: null, calculatedSortFlag: false, relationObjectCode: null, relationObjectName: null, intermediateFieldId: null, objectVersionNumber: 3, intermediateObjectId: null, calculatedSortFieldId: null, intermediateFieldName: null, calculatedSortObjectId: null, intermediateObjectName: null, calculatedSortFieldName: null, calculatedSortObjectName: null, calculatedSortExpressionId: null, intermediateRelationFieldId: null, calculatedSortExpressionName: null, intermediateRelationFieldName: null }],
      filterUuid: 'df792d20-fd0f-451d-8cce-3b07b0234cb2',
      widgetType: 'NumberField',
      componentType: 'NumberField',
      fieldValueType: 'EXPRESSION',
    },
  ],
  groupId: 'df792d20-fd0f-451d-8cce-3b07b0234cb2',
  condition: 'AND',
  groupName: 'filter.condition.groupTitle',
  tag: 'userAdd',
};

// 根据组id筛选人，适用于人的lov
const filterUserByGroupIdCondition = {
  filters: [
    {
      field: 'id',
      filter: 'is in list',
      rcField: ['id'],
      condition: 'AND',
      fieldName: '主键(id)',
      expression: "/*\n** Script must return a value.\n** example:\n** var userName = $GetCurrentAttribute('iam_user_id:login_name')\n** return userName;\n*/\nreturn $Invoke('yqcloud-itsm', 'UserGroupAssignFilterInvoker', 'IAM_USER_GROUP-《group_id》');\n",
      filterUuid: 'df792d20-fd0f-451d-8cce-3b07b0234cb1',
      widgetType: 'NumberField',
      componentType: 'NumberField',
      fieldValueType: 'EXPRESSION',
    },
  ],
  groupId: 'df792d20-fd0f-451d-8cce-3b07b0234cb1',
  condition: 'AND',
  groupName: 'filter.condition.groupTitle',
  tag: 'userAdd',
};

const filterUserByGroup = {
  condition: 'AND',
  filters: [
    {
      condition: 'AND',
      filter: 'is',
      widgetType: 'MasterDetail',
      componentType: 'MasterDetail',
      field: 'user_group_id',
      // fieldValue: '209106277303906304',
      fieldValueType: 'CONSTANT',
    },
  ],
};

export {
  filterGroupByUserIdCondition,
  filterUserByGroupIdCondition,
  filterUserByGroup,
};
