@import '~choerodon-ui/lib/style/themes/default';

.dropdown-wrap {
  height: 280px;
  min-width: 568px;
  overflow-y: scroll;

  ul.@{c7n-pro-prefix}-select-dropdown-menu {
    position: relative;
    display: table;
    width: 100%;
    padding-top: 0;
    // padding-bottom: 36px;
    li.@{c7n-pro-prefix}-select-dropdown-menu-item {
      display: table-row;
      line-height: 36px;
      padding: 10px 4px;

      .at-work i {
        width: 0.06rem;
        height: 0.06rem;
        display: inline-block;
        background-color: #75c940;
        border-radius: 50%;
        margin-top: -0.02rem;
        margin-right: 0.06rem;
        vertical-align: middle;
      }

      .on-leave i {
        width: 0.06rem;
        height: 0.06rem;
        display: inline-block;
        background-color: rgba(0, 0, 0, 0.25);
        border-radius: 50%;
        margin-top: -0.02rem;
        margin-right: 0.06rem;
        vertical-align: middle;
      }

      span.table-cell {
        display: table-cell;
        padding-right: 12px;
      }

      span.table-cell:first-child {
        padding-left: 12px;
      }

      span.table-cell:nth-child(3) {
        padding-right: 12px;
      }

      span.table-cell:last-child {
        padding-right: 12px;
      }
    }

    // 表格头
    li.@{c7n-pro-prefix}-select-dropdown-menu-item:first-child:not(.@{c7n-pro-prefix}-select-dropdown-menu-item-disabled) {
      display: table-row-group;
      font-size: 14px;
      font-weight: 500;
      color: #12274d;
      line-height: 36px;
      pointer-events: none;
      background-color: white;
      position: sticky;
      top: 0;
      z-index: 10;

      border-radius: 0;
      outline: #e5e6eb solid 1px;

      &:hover {
        background-color: white;
      }
    }

    //暂无数据
    li.@{c7n-pro-prefix}-select-dropdown-menu-item.@{c7n-pro-prefix}-select-dropdown-menu-item-disabled:first-child {
      text-align: center;
    }

    //分页组件
    li.@{c7n-pro-prefix}-select-dropdown-menu-item-more {
      display: table-caption;
      padding: 0 4px;
      position: absolute;
      bottom: 0;
      width: 100%;
      line-height: 36px;
      background-color: white;
      z-index: 10;
      border-top: 1px solid #e5e6eb;
    }
  }

  .multiple-select-style {
    background-color: transparent;

    & > label > .@{c7n-pro-prefix}-select {
      border: none;

      ul {
        flex-wrap: nowrap;

        .@{c7n-pro-prefix}-select-multiple-block {
          background-color: #f2f3f5;
          padding: 1px 8px;
          font-size: 12px;
        }
      }
    }

    .@{c7n-pro-prefix}-select-suffix {
      display: none;
    }
  }
}

.ticket-transfer-popup {
  .@{c7n-pro-prefix}-table-wrapper {
    width: 100% !important;
  }
}

.transfer-recommend-tag {
  margin-left: -98px;
  padding: 0 12px 0 3px;
  display: inline-flex;
  align-items: center;
  height: 30px;
  border-radius: 15px;
  background: rgba(203, 210, 220, 0.5);
  cursor: pointer;

  &-avt {
    margin-right: 4px;

    .c7n-avatar-string {
      font-size: 14px;
    }
  }

  &-text {
    font-size: 14px;
    color: #12274d;
  }
}
