import React, { useEffect, useState, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { CheckBox, Form, Select, Tooltip, Lov, Col } from 'choerodon-ui/pro';
import { Avatar } from 'choerodon-ui';
import { Icon } from '@zknow/components';
import Wysiwyg from '@/components/wysiwyg';
import { getRichJson, htmlToDelta, createRichHtml } from '@/utils';
import { getFileName } from '@/components/editor-register/utils';
import { getIsEnabledApps, getMemoTransfer } from '@/service';
import Store from './store';
import './index.less';

const POPUP_CLS = 'ticket-transfer-popup';

const TicketTransfer = () => {
  const {
    transferDataSet,
    personDataSet,
    groupDataSet,
    widgetConfig,
    pageRef,
    replyEnable,
    intl,
    noPagingPersonDataSet,
    noPagingGroupDataSet,
    formDataSet,
    tenantId,
    transferTypeFlag,
    hasDefaultPerson,
    hasDefaultGroup,
    autoFillAssignGroup,
    isWFTaskInstance,
    personFieldName,
    personNameFieldName,
  } = useContext(Store);
  const [initDone, setInitDone] = useState(false);
  const [isEnabledSkills, setIsEnabledSkills] = useState(false);
  const [memoInfo, setMemoInfo] = useState(null);

  /**
   * 2023-04-26
   * 第一次打开转交的表单的时候，不需要对组和人进行互筛，也就是如果没有配置清除，那么组和人都保留
   * 详：胡豹豹【已离职】
   */
  const unlock = () => {
    transferDataSet.setState('unlock', true);
    // 有过修改后，不再显示推荐
    setMemoInfo(null);
  };

  // 生产工单 INC00028318
  const inputChange = (event, name) => {
    if (!event?.target?.value) {
      transferDataSet?.current?.set({
        [name]: undefined,
        [`${name}:name`]: undefined,
        [`${name}:real_name`]: undefined,
      });
    }
  };

  useEffect(() => {
    // 动作中获取此实例 校验
    if (pageRef && pageRef.current) {
      pageRef.current.transferDataSet = transferDataSet;
      pageRef.current.isReply = true;
    }
  }, [transferDataSet]);

  useEffect(() => {
    (() => {
      getIsEnabledApps(tenantId, 'skill').then(res => {
        setIsEnabledSkills(res === true);
      }, () => {
        setIsEnabledSkills(false);
      });
      // 视图配置了【转交推荐】
      if (widgetConfig?.transferRecommend) {
        getMemoTransfer(tenantId).then(res => {
          if (res && !res.failed) {
            setMemoInfo(res);
          } else {
            setMemoInfo(null);
          }
        }, () => {
          setMemoInfo(null);
        });
      }
    })();
  }, []);

  useEffect(() => {
    if (transferDataSet?.current) {
      const personIdField = transferTypeFlag ? 'lov_assignee_person_id' : 'normal_assignee_person_id';
      const groupIdField = transferTypeFlag ? 'lov_assignment_group_id' : 'normal_assignment_group_id';
      const groupId = transferDataSet.current.get(groupIdField)?.id || transferDataSet.current.get(groupIdField);
      const groupName = transferDataSet.current.get(`${groupIdField}:name`);
      const personRecordData = transferDataSet.current.get(personIdField);
      const personId = personRecordData?.user_id || personRecordData?.userId || personRecordData?.id || personRecordData;
      const personName = personRecordData?.['user_id:real_name'] || transferDataSet.current.get(`${personIdField}:real_name`);
      const nextData = {
        [personFieldName]: personId,
        [`${personFieldName}:real_name`]: personName,
      };
      if (!isWFTaskInstance) {
        nextData.assignment_group_id = groupId;
        nextData['assignment_group_id:name'] = groupName;
      }
      transferDataSet.current.set(nextData);
    }
  }, [
    isWFTaskInstance,
    transferDataSet?.current?.get('normal_assignment_group_id'),
    transferDataSet?.current?.get('normal_assignee_person_id'),
    transferDataSet?.current?.get('lov_assignment_group_id'),
    transferDataSet?.current?.get('lov_assignee_person_id'),
    transferTypeFlag,
  ]);

  /** ********************
   *  FIXME：下面这段逻辑『L84~L171』有很大的问题
   *      会导致人和组在初始化的时候频繁重复查询（重复发了4次）
   *      有时间一定改掉
   ********************* */

  useEffect(() => {
    if (formDataSet?.current?.get('id')) {
      const fromAssigneePersionId = formDataSet.current.get(personFieldName) || undefined;
      const fromAssigneeGroupId = formDataSet.current.get('assignment_group_id') || undefined;

      const currentAssigneePersionId = transferDataSet?.current?.get('normal_assignee_person_id')?.id || transferDataSet?.current?.get('normal_assignee_person_id') || undefined;
      const currentAssigneeGroupId = transferDataSet?.current?.get('normal_assignment_group_id')?.id || transferDataSet?.current?.get('normal_assignment_group_id') || undefined;

      // 如果视图设置了清空处理人/处理组，这里需要判断
      if ((!hasDefaultPerson || fromAssigneePersionId === currentAssigneePersionId) && (!hasDefaultGroup || fromAssigneeGroupId === currentAssigneeGroupId)) {
        setInitDone(true);
      }
    }
  }, [hasDefaultGroup, hasDefaultPerson, formDataSet?.current?.get('id'), transferDataSet?.current?.get('normal_assignment_group_id'), transferDataSet?.current?.get('normal_assignee_person_id')]);

  // 当前处理人变动的时候
  useEffect(() => {
    if (!formDataSet?.current || !initDone || isWFTaskInstance) {
      return;
    }
    const assigneePersonId = transferDataSet?.current?.get('normal_assignee_person_id');
    handleChangePerson(assigneePersonId);
  }, [transferDataSet?.current?.get('normal_assignee_person_id'), initDone]);

  // 当前处理组变动的时候
  useEffect(() => {
    if (!formDataSet?.current || !initDone) {
      return;
    }
    const assignmentGroupId = isWFTaskInstance ? undefined : transferDataSet?.current?.get('normal_assignment_group_id');
    handleChangeGroup(assignmentGroupId);
  }, [transferDataSet?.current?.get('normal_assignment_group_id'), initDone]);

  // 人和组相互筛选
  const handleChangeGroup = async (value) => {
    const assignmentUser = transferDataSet?.current?.get('normal_assignee_person_id') || '';
    const assignmentUserId = assignmentUser?.userId || assignmentUser?.id || assignmentUser;
    if (!isWFTaskInstance) {
      const groupId = value?.id || value || '';
      personDataSet.setQueryParameter('groupId', groupId?.id || groupId);
      noPagingPersonDataSet.setQueryParameter('groupId', groupId?.id || groupId);
    }
    noPagingPersonDataSet.setQueryParameter('search_id', typeof assignmentUserId === 'string' ? assignmentUserId : assignmentUserId?.id || '');
    await personDataSet.query();
    await noPagingPersonDataSet.query();
    if (!noPagingPersonDataSet?.find((i) => i.get('id') === assignmentUserId) && transferDataSet?.getState('unlock')) {
      transferDataSet.current?.set('normal_assignee_person_id', null);
      transferDataSet.current?.set('normal_assignee_person_id:real_name', null);
    }
  };

  const handleChangePerson = async (value) => {
    if (isWFTaskInstance) {
      return false;
    }
    const assignmentGroup = transferDataSet?.current?.get('normal_assignment_group_id') || '';
    const assignmentGroupId = assignmentGroup?.id || assignmentGroup;
    const userId = value?.userId || value?.id || value || '';
    if (!value && assignmentGroupId) {
      // 清空了处理人 并且有 选择处理组
      personDataSet.setQueryParameter('groupId', assignmentGroupId?.id || assignmentGroupId);
      noPagingPersonDataSet.setQueryParameter('groupId', assignmentGroupId?.id || assignmentGroupId);
      await personDataSet.query();
      await noPagingPersonDataSet.query();
    }
    groupDataSet.setQueryParameter('userId', userId?.id || userId);
    await groupDataSet.query();

    if ((value?.userGroupId || value?.user_group_id) && autoFillAssignGroup) {
      // 如果有自动填充组的功能，需要跳过后续步骤
      return false;
    }
    noPagingGroupDataSet.setQueryParameter('userId', userId?.id || userId);
    noPagingGroupDataSet.setQueryParameter('search_id', typeof assignmentGroupId === 'string' ? assignmentGroupId : assignmentGroupId?.id || '');
    await noPagingGroupDataSet.query();
    // 如果当前处理组不在查询出来的处理组中，则清空值
    if (!noPagingGroupDataSet?.find((i) => i.get('id') === transferDataSet.current?.get('normal_assignment_group_id')?.id) && transferDataSet?.getState('unlock')) {
      transferDataSet.current?.set('normal_assignment_group_id', null);
      transferDataSet.current?.set('normal_assignment_group_id:name', null);
    }
  };

  /**
   * 留言字段变更
   * @param event
   * @param editor
   * @param file
   * @param record
   * @param name
   */
  const handleChange = (event, editor, file, record, name) => {
    const htmlData = editor.getData();
    //  这里是新格式数据保存到后端
    const prevRichJson = getRichJson(record?.get(name)) || {};
    prevRichJson.quillData = htmlToDelta(editor.getData());
    if (event === 'uploadFile' && file) {
      if (!prevRichJson.attachments) {
        prevRichJson.attachments = [];
      }
      prevRichJson.attachments.push({
        ...file,
        size: file?.fileSize,
        name: getFileName(file?.fileKey),
      });
    }
    if (event === 'deleteFile' && file) {
      if (!prevRichJson.attachments) {
        prevRichJson.attachments = [];
      }
      prevRichJson.attachments = prevRichJson.attachments.filter(
        fileItem => (file.md5 && fileItem.md5 !== file.md5) || (file.fileKey && fileItem.fileKey !== file.fileKey)
      );
    }
    const richHtml = createRichHtml(htmlData, prevRichJson);
    record?.set(name, richHtml);
  };

  /**
   * 转交推荐查询结果中组和人是否有值
   * @returns {Set<any>}
   */
  const computeRecommend = () => {
    const fromAssigneePersionId = formDataSet?.current?.get?.(personFieldName);
    const fromAssigneeGroupId = formDataSet?.current?.get?.('assignment_group_id');
    const memoGroupId = memoInfo?.assignment_group_id;
    const memoPersonId = memoInfo?.[personFieldName];
    const result = new Set();
    if (initDone) {
      if (!isWFTaskInstance && memoGroupId && memoGroupId !== transferDataSet.current?.get('assignment_group_id') && memoGroupId !== fromAssigneeGroupId) {
        result.add('group');
      }
      if (memoPersonId && memoPersonId !== transferDataSet.current?.get(personFieldName) && memoPersonId !== fromAssigneePersionId) {
        result.add('person');
      }
    }
    return result;
  };

  function handleFill() {
    const groupId = memoInfo?.assignment_group_id;
    const groupName = memoInfo?.assignment_group_name;
    const personId = memoInfo?.[personFieldName];
    const personName = memoInfo?.[isWFTaskInstance ? 'person_name' : 'assignee_person_name'];

    const nextTransfer = isWFTaskInstance ? {
      normal_assignee_person_id: { id: personId, realName: personName },
      lov_assignee_person_id: { id: personId, real_name: personName },
      [personFieldName]: personId,
      [`${personFieldName}:real_name`]: personName,
    } : {
      normal_assignment_group_id: groupId ? { id: groupId, name: groupName } : null,
      normal_assignee_person_id: personId ? { id: personId, realName: personName } : null,
      lov_assignment_group_id: groupId ? { id: groupId, name: groupName } : null,
      lov_assignee_person_id: personId ? { id: personId, real_name: personName } : null,

      assignment_group_id: groupId,
      'assignment_group_id:name': groupName,
      assignee_person_id: personId,
      'assignee_person_id:real_name': personName,
    };

    transferDataSet.current.set(nextTransfer);
  }

  function renderTag(type) {
    if (!widgetConfig?.transferRecommend) {
      return null;
    }
    const isGroup = type === 'group';
    return (
      <Col span={1}>
        <Tooltip title={isGroup ? intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.tips.group' }) : intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.tips.person' })}>
          <div
            className="transfer-recommend-tag"
            onClick={handleFill}
          >
            <span className="transfer-recommend-tag-avt">{isGroup
              ? <Avatar size={24} style={{ backgroundColor: '#2979ff' }}><Icon type="PeoplesTwo" size={14} /></Avatar>
              : <Avatar size={24} style={{ backgroundColor: '#7c35fb' }}>{memoInfo?.[personNameFieldName]?.slice(0, 1)}</Avatar>}</span>
            <span className="transfer-recommend-tag-text">{isGroup ? memoInfo?.assignment_group_name : memoInfo?.[personNameFieldName]}</span>
          </div>
        </Tooltip>
      </Col>
    );
  }

  function renderGroupRecommend() {
    const hasGroupRecommend = computeRecommend().has('group');
    if (!hasGroupRecommend || isWFTaskInstance) {
      return null;
    }
    return renderTag('group');
  }

  function renderPersonRecommend() {
    const hasPersonRecommend = computeRecommend().has('person');
    if (!hasPersonRecommend) {
      return null;
    }
    return renderTag();
  }

  // 转交动作类型
  function renderTransferActionPersonAndGroup() {
    const groupMethod = !widgetConfig?.assignGroupDisplayMethod || widgetConfig?.assignGroupDisplayMethod === 'MODAL' ? 'modal' : 'popup';
    const personMethod = !widgetConfig?.assignPersonDisplayMethod || widgetConfig?.assignPersonDisplayMethod === 'MODAL' ? 'modal' : 'popup';
    const hasGroupRecommend = computeRecommend().has('group');
    const hasPersonRecommend = computeRecommend().has('person');
    const commonFields = [
      <Lov
        colSpan={hasPersonRecommend ? 1 : 2}
        name="lov_assignee_person_id"
        onChange={unlock}
        onInput={e => inputChange(e, 'lov_assignee_person_id')}
        viewMode={personMethod}
        popupCls={POPUP_CLS}
        popupSearchMode="single"
      />,
      renderPersonRecommend(),
    ];

    return !isWFTaskInstance ? [
      <Lov
        colSpan={hasGroupRecommend ? 1 : 2}
        name="lov_assignment_group_id"
        onChange={unlock}
        onInput={e => inputChange(e, 'lov_assignment_group_id')}
        viewMode={groupMethod}
        popupCls={POPUP_CLS}
        popupSearchMode="single"
      />,
      renderGroupRecommend(),
      ...commonFields,
    ] : commonFields;
  }

  // 正常的动作
  function renderNormalPersonAndGroup() {
    const hasGroupRecommend = computeRecommend().has('group');
    const hasPersonRecommend = computeRecommend().has('person');

    const groupNode = !widgetConfig?.assignGroupDisplayMethod || widgetConfig?.assignGroupDisplayMethod === 'DROP_DOWN'
      ? (
        <Select
          colSpan={hasGroupRecommend ? 1 : 2}
          name="normal_assignment_group_id"
          searchMatcher="name"
          searchable
          popupCls="dropdown-wrap"
          options={groupDataSet}
          pagingOptionContent={<span>{intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.load.more' })}</span>}
          notFoundContent={<div>{intl.formatMessage({ id: 'zknow.common.model.noData' })}</div>}
          checkValueOnOptionsChange={false}
          onChange={unlock}
          onInput={e => inputChange(e, 'normal_assignment_group_id')}
          optionRenderer={({ record }) => {
            const { name } = record?.toData() || {};
            return <><span className="table-cell">{name}</span></>;
          }}
        />
      )
      : (
        <Lov
          colSpan={hasGroupRecommend ? 1 : 2}
          name="normal_assignment_group_id"
          viewMode={widgetConfig?.assignGroupDisplayMethod === 'DROP_DOWN_LIST' ? 'popup' : 'modal'}
          onChange={unlock}
          onInput={e => inputChange(e, 'normal_assignment_group_id')}
          popupCls={POPUP_CLS}
          popupSearchMode="single"
          searchFieldProps={{ multiple: true }}
        />
      );
    const personNode = !widgetConfig?.assignPersonDisplayMethod || widgetConfig?.assignPersonDisplayMethod === 'DROP_DOWN'
      ? (
        <Select
          colSpan={hasPersonRecommend ? 1 : 2}
          name="normal_assignee_person_id"
          searchMatcher="realName"
          searchable
          onChange={unlock}
          onInput={e => inputChange(e, 'normal_assignee_person_id')}
          popupCls="dropdown-wrap"
          options={personDataSet}
          pagingOptionContent={<span>{intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.load.more' })}</span>}
          notFoundContent={<div>{intl.formatMessage({ id: 'zknow.common.model.noData' })}</div>}
          optionRenderer={({ record }) => {
            const { id, realName, email, onHands, skill, status } = record?.toData() || {};
            return id === 'title' ? (
              <>
                <span className="table-cell">{realName}</span>
                <span className="table-cell"> {email}</span>
                <span className="table-cell"> {status}</span>
                <span className="table-cell"> {onHands}</span>
                {isEnabledSkills && widgetConfig?.skillVisible && <span className="table-cell"> {skill}</span>}
              </>
            ) : (
              <>
                <span className="table-cell">{realName}</span>
                <span className="table-cell table-cell-email">{email}</span>
                <span className={`table-cell ${Number(status) ? 'on-leave' : 'at-work'}`}>
                  <i />
                  {Number(status)
                    ? intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.status.on-leave' })
                    : intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.status.at-work' })}
                </span>
                <span className="table-cell"> {onHands ?? ''}</span>
                {
                  isEnabledSkills && widgetConfig?.skillVisible && (
                    <span className="table-cell">
                      <Tooltip title={skill || ''}>
                        <Select
                          className="multiple-select-style"
                          readOnly
                          value={(skill || '')?.split(',')}
                          multiple
                          maxTagCount={2}
                          maxTagTextLength={8}
                          maxTagPlaceholder={(restValues) => `+${restValues.length}`}
                        />
                      </Tooltip>
                    </span>
                  )
                }
              </>
            );
          }}
          checkValueOnOptionsChange={false}
        />
      )
      : (
        <Lov
          colSpan={hasPersonRecommend ? 1 : 2}
          name="normal_assignee_person_id"
          onChange={unlock}
          onInput={e => inputChange(e, 'normal_assignee_person_id')}
          viewMode={widgetConfig?.assignPersonDisplayMethod === 'DROP_DOWN_LIST' ? 'popup' : 'modal'}
          popupSearchMode="single"
          popupCls={POPUP_CLS}
        />
      );

    const commonFields = [
      personNode,
      renderPersonRecommend(),
    ];

    return isWFTaskInstance ? commonFields : [
      groupNode,
      renderGroupRecommend(),
      ...commonFields,
    ];
  }

  return (
    <Form
      columns={computeRecommend().size ? 2 : 1}
      dataSet={transferDataSet}
      style={{ paddingTop: 16, paddingBottom: 16 }}
    >
      {transferTypeFlag ? renderTransferActionPersonAndGroup() : renderNormalPersonAndGroup()}
      {widgetConfig?.transferDescVisible === true && replyEnable && [
        <Wysiwyg
          colSpan={2}
          help={intl.formatMessage({ id: 'lcr.renderer.ticketTransfer.tips.desc' })}
          name="transfer_desc"
          // 针对附件、录音的数据
          htmlData={transferDataSet?.current?.get('transfer_desc')}
          onChange={(e, editor, file) => handleChange(e, editor, file, transferDataSet?.current, 'transfer_desc')}
          uploadFlag
        />,
        <CheckBox
          colSpan={2}
          name="customer_hidden"
        />,
      ]}
    </Form>
  );
};

export default observer(TicketTransfer);
