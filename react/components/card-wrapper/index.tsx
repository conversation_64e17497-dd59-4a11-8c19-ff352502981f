import React from 'react';
import { Icon } from '@zknow/components';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import styles from './CardWrapper.module.less';

type CardWrapperProps = {
  title?: string,
  description?: string,
  onClickEdit?: () => void,
  children: React.ReactNode;
  icon?: string,
  headerColor?: string,
  className?: string,
  headerClassName?: string,
};

const DEFAULT_ICON = 'write';

export default observer(({
  onClickEdit,
  title,
  description,
  children,
  icon,
  headerColor,
  className,
  headerClassName,
}: CardWrapperProps) => {
  function handleClick() {
    if (onClickEdit) {
      onClickEdit();
    }
  }

  return (
    <div className={classnames(styles.wrapper, className)}>
      <header
        className={classnames(styles.header, headerClassName)}
        style={{ background: headerColor }}
      >
        <span className={styles.title}>{title}<span className={styles.description}>{description}</span></span>
        {onClickEdit ? <div className={styles.icon} onClick={handleClick}><Icon type={icon || DEFAULT_ICON} /></div> : null}
      </header>
      <main className={styles.main}>
        {children}
      </main>
    </div>
  );
});
