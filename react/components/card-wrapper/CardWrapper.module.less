.wrapper {
  background: #fff;
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.65);
  overflow: hidden;
  &:hover {
    .icon {
      display: block !important;
    }
  }
  .header {
    position: relative;
    background: rgba(98, 198, 255, 0.12);
    height: 46px;
    padding: 12px 16px;
    .title {
      display: flex;
      white-space: nowrap;
      height: 22px;
      line-height: 22px;
      font-size: 16px;
      font-weight: 500;
      color: #12274d;
      margin-right: 20px;
    }
    .description {
      overflow: hidden;
      white-space: nowrap;
      width: 100%;
      display: inline-block;
      text-overflow: ellipsis;
      height: 22px;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.65);
      line-height: 22px;
      font-size: 14px;
      margin-left: 12px;
    }
    .icon {
      display: none;
      position: absolute;
      height: 42px;
      width: 42px;
      top: 1px;
      right: 3px;
      padding: 13px;
      cursor: pointer;
    }
  }
  .main {
    padding: 16px;
    background-color: #fff;
  }
}
