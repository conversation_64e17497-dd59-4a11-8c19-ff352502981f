@import "~choerodon-ui/lib/style/themes/default";
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.lc-components-segmented-control {
  position: relative;

  display: flex;
  align-items: center;
  align-items: center;
  box-sizing: border-box;
  height: 32px;
  padding: 3px;

  background: @minor-color !important;
  border-radius: 4px;
  &-active {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 0;

    height: 24px;
    margin: 4px 3px;

    background: white;
    border-radius: 4px;

    transition: width 0.1s ease-out, transform 0.4s cubic-bezier(0.32, 1.25, 0.64, 1);
  }

  &-option {
    z-index: 1;

    display: inline-block;
    margin: 0 3px;
    // padding: 2px 6px;

    font-size: 14px;
    color: rgba(18, 39, 77, 0.85);
    white-space: nowrap;

    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    transition: color 0.4s cubic-bezier(0, 0, 0, 1);
    &-inline {
      padding: 2px 6px;
      height: 25px;
      display: flex;
      align-items: center;
      &.active {
        color: @primary-color;
        background-color: #fff;
        border-radius: 4px;
      }
    }
  }
}
