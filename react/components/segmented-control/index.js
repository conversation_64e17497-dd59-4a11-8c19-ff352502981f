import React, { useState, useEffect } from 'react';
import classNames from 'classnames';
import { Tooltip } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import './index.less';

const Option = ({ prefix, setActive, data, onChange, active, defaultValue, mode }) => {
  const [ref, setRef] = useState(null);
  const isActive = data.value === (active?.value || defaultValue);
  useEffect(() => {
    if (isActive && ref) {
      handleClick();
    }
  }, [ref]);
  const handleClick = () => {
    setActive({ value: data?.value, ref });
    onChange(data?.value);
  };
  let children = null;
  if (mode === 'icon') {
    children = (
      <Tooltip title={data.label}>
        <Icon type={data.icon} />
      </Tooltip>
    );
  } else {
    children = data.label;
  }
  return (
    <div
      ref={setRef}
      className={classNames(`${prefix}-option`, { active: isActive })}
      onClick={handleClick}
    >
      <div className={classNames(`${prefix}-option-inline`, { active: isActive })}>{children}</div>
    </div>
  );
};

export default function SegmentedControl({
  options,
  defaultValue: _defaultValue,
  className = '',
  onChange = () => {},
  mode = 'text', // 分为icon跟text两个类型，默认是text
}) {
  const [active, setActive] = useState(null);
  if (!Array.isArray(options)) {
    return null;
  }
  const defaultValue = _defaultValue || options[0].value;
  const prefix = 'lc-components-segmented-control';
  return (
    <div
      className={classNames(prefix, className)}
      // style={{ opacity: active?.ref?.offsetWidth ? 1 : 0 }}
    >
      <div
        className={`${prefix}-active`}
        // style={{
        //   width: active?.ref?.offsetWidth || 0,
        //   transform: `translate(${active?.ref?.offsetLeft - 6 || 0}px, 0px)`,
        // }}
      />

      {options.map((option) => {
        return (
          <Option
            key={option.value}
            prefix={prefix}
            data={option}
            active={active}
            defaultValue={defaultValue}
            setActive={setActive}
            onChange={onChange}
            mode={mode}
          />
        );
      })}
    </div>
  );
}

/* externalize: SegmentedControl */
