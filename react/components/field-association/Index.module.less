@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';
.association {
    position: absolute;
    z-index: 999;
    background: #FFFFFF;
    box-shadow: 0 2px 8px 0 rgba(129, 137, 153, 0.32);
    border-radius: 2px;
    width: 86%;
    padding: 10px 0;
    .head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 16px;
        .title {
            font-weight: bold;
        }
        .refresh {
            color: @primary-color;
            cursor: pointer;
            display: flex;
            align-items: center;
            .refreshIcon {
                margin-right: 4px;
            }
        }
    }
    .content {
        .item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            padding: 12px 16px;
            &:hover {
                background-color: #f3f5f8;
            }
            .left, .right {
                display: flex;
                align-items: center;
            }
            .left {
                font-size: 14px;
                .description {
                    margin-left: 8px;
                    :global {
                        color: @primary-color;
                    }
                }
            }
            .right {
                font-size: 12px;
                color: @primary-color;
                background-color: @yq-primary-color-10;
                border-radius: 11px 16px 16px 11px;
                padding: 4px 10px;
                display: flex;
                align-items: center;
                .text {
                    margin-left: 4px;
                }
            }
        }
    }
}
.qa {
    max-width: 500px;
    min-width: 200px;
    max-height: 500px;
    // overflow: auto;
    .question {
        border-bottom: 1px solid #e8e8e8;
    }
}