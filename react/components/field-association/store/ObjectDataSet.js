const SearchDataSet = ({ tenantId }) => {
  return {
    autoCreate: true,
    autoQuery: true,
    paging: false,
    dataKey: 'content',
    pageSize: 20,
    transport: {
      read: () => {
        return {
          url: `search/v1/${tenantId}/yq_search/query_subTypes?searchChannel=GLOBAL_SEARCH&type=TICKETS`,
          method: 'get',
        };
      },
    },
  };
};

export default SearchDataSet;
