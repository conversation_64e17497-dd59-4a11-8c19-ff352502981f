const SearchDataSet = ({ tenantId, fieldValue, businessObjectCode }) => {
  return {
    autoCreate: true,
    autoQuery: true,
    paging: false,
    dataKey: 'content',
    pageSize: 20,
    transport: {
      read: () => {
        return {
          url: fieldValue ? `/search/v1/${tenantId}/yq_search?searchChannel=SUBMIT_SERVICE_ITEM&page=0&size=20` : '',
          method: 'post',
          data: {
            jumpAddressType: 'SERVICE_PORTAL',
            queryText: fieldValue,
            serviceCatalogFlag: false,
            type: ['KNOWLEDGE_CENTER', 'TICKETS', 'qa'],
          },
          transformResponse: (data) => {
            try {
              const res = JSON.parse(data);
              const arr = res?.content?.filter(i => i?.similarity >= 20 && i?.similarity !== 'NaN');
              return arr;
            } catch (error) {
              return data;
            }
          },
        };
      },
    },
  };
};

export default SearchDataSet;
