import React, { createContext, useEffect, useMemo, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import SearchDataSet from './SearchDataSet';
import ObjectDataSet from './ObjectDataSet';
import * as sea from 'node:sea';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'lcr.components' })(injectIntl(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId } },
      name,
      fieldValue,
      businessObjectCode,
    } = props;
    const [displayFlag, setDisplayFlag] = useState(props.displayFlag);

    const searchDataSet = useMemo(
      () => new DataSet(SearchDataSet({ tenantId, fieldValue, businessObjectCode })),
      [tenantId, fieldValue, businessObjectCode],
    );

    // 不得已写一些**代码，因为最初的实现不好再动
    //  为了兼容生产的单子 INC00029907
    useEffect(() => {
      if (!fieldValue) {
        searchDataSet.loadData([]);
        searchDataSet.totalCount = 0;
        setDisplayFlag(false);
      }
    }, [fieldValue]);

    useEffect(() => {
      if (fieldValue) {
        setDisplayFlag(props.displayFlag);
      }
    }, [props.displayFlag, fieldValue]);

    const objectDataSet = useMemo(
      () => new DataSet(ObjectDataSet({ tenantId })),
      [tenantId]
    );

    const value = {
      ...props,
      intl,
      searchDataSet,
      tenantId,
      objectDataSet,
      displayFlag,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
