import React, { useContext, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import queryString from 'query-string';
import { Icon, ExternalComponent } from '@zknow/components';
import { Tooltip } from 'choerodon-ui/pro';
import { getQueryString } from '@/utils';
import styles from './Index.module.less';
import Store, { StoreProvider } from './store';

const ticketIconMap = {
  incident: 'icon-shijiandan',
  service_request: 'icon-fuwuqingqiudan',
  problem: 'icon-wentidan',
  problem_task: 'icon-wentidan',
  change: 'icon-biangengdan',
  change_task: 'icon-biangengdan',
  cart_order: 'icon-zidingyi2',
  demand: 'icon-xuqiu',
  handover: 'icon-jiaojie',
  training: 'icon-zidingyi7',
  complaint: 'icon-zidingyi4',
  inspection: 'icon-xunjian',
  login_account: 'icon-zidingyi3',
  deploy: 'icon-fabu',
  sub_task: 'icon-renwu',
};

const MainView = observer(() => {
  const { displayFlag, searchDataSet, businessObjectCode, objectDataSet, tenantId, intl } = useContext(Store);
  const [recommdData, setRecommdData] = useState([]);
  const [startNum, setStartNum] = useState(0);
  const [holding, setHolding] = useState(false);

  useEffect(() => {
    handlePage();
  }, [searchDataSet?.totalCount]);

  const handlePage = () => {
    const data = searchDataSet?.toData() || [];
    if (data?.length) {
      // 20条数据循环
      const newData = data.slice(startNum, Math.min(data?.length, startNum + 5));
      setRecommdData(newData);
      if (Math.min(data?.length, startNum + 5) === data?.length) {
        setStartNum(0);
      } else {
        setStartNum(startNum + 5);
      }
    }
  };

  const rendererItemIcon = (i) => {
    const icon = ticketIconMap[i?.type] || 'icon-shijiandan';
    switch (i?.type) {
      case 'knowledge':
        return <ExternalComponent
          system={{
            scope: 'knowledge',
            module: 'knowledge-icon',
          }}
          fileType={i?.fileType}
          size="16"
        />;
      case 'qa':
        return <Icon type="icon-wendaku" size="16" fill="#7e6ff7" />;
      default:
        return <Icon type={icon} size="16" />;
    }
  };

  const handleJump = (i) => {
    if (i?.type === 'knowledge') {
      const search = '';
      const baseUrl = `${window.location.origin}/#/itsm/portal/knowledge?portal=true`;
      const baseSearch = `&tenantId=${getQueryString('tenantId')}&solutionId=${getQueryString('solutionId')}`;
      if (i?.fileType !== 'FOLDER') {
        const sourceModule = 'TICKETS';
        const sourceId = queryString.parse(window.location.href.split('?').pop())?.ticketId || '';
        let url = `${baseUrl}&menu=knowledge&knowledgeId=${i.id}${baseSearch}&sourceModule=${sourceModule}&sourceFunction=GLOBAL_SEARCH&sourceId=${sourceId}`;
        if (search) {
          url += `&search=${search}`;
        }
        window.open(url);
      } else {
        window.open(`${baseUrl}&menu=folder&folderId=${i?.id}${baseSearch}`);
      }
    } else if (i?.type === 'qa') {
      //
    } else {
      //
      const data = objectDataSet.find(l => {
        return l?.get('code') === i?.type;
      });
      const ticketId = i.ticketId || i?.id;
      const viewId = data?.get('ticketTypeVO.portalViewId');
      if (viewId && ticketId) {
        window.open(`${window.location.origin}/#/itsm/portal/service_list?tenantId=${tenantId}&viewId=${viewId}&ticketId=${ticketId}&solutionId=${getQueryString('solutionId') || ''}`);
      }
    }
  };

  const handleMoveMouse = (i) => {
    setHolding(i);
  };

  const rendererItem = (i) => {
    const similarity = Math.floor((i?.similarity || 0));
    return <div className={styles.item} onClick={() => handleJump(i)}>
      <div className={styles.left}>
        {rendererItemIcon(i)}
        <div className={styles.description} dangerouslySetInnerHTML={{ __html: i?.title || i?.question }} />
      </div>
      <div className={styles.right}>
        <Icon type="cube-three" />
        <div className={styles.text}>{intl.formatMessage({ id: 'lcr.components.fieldAssociation.match', defaultMessage: '匹配度:' })}{similarity}</div>
      </div>
    </div>;
  };

  const rendererQa = (i) => {
    return <div className={styles.qa}>
      <div className={styles.question} dangerouslySetInnerHTML={{ __html: i?.question }} />
      <div className={styles.answer} dangerouslySetInnerHTML={{ __html: i?.answer }} />
    </div>;
  };

  if (recommdData?.length > 0 && (displayFlag || holding)) {
    return <div className={styles.association} onMouseEnter={() => handleMoveMouse(true)} onMouseLeave={() => handleMoveMouse(false)}>
      <div className={styles.head}>
        <div className={styles.title}>{intl.formatMessage({ id: 'lcr.components.fieldAssociation.intelligentRecommendation', defaultMessage: '相关推荐' })}</div>
        {searchDataSet?.totalCount > 5 && <div className={styles.refresh} onClick={handlePage}><Icon type="refresh" className={styles.refreshIcon} />{intl.formatMessage({ id: 'lcr.components.fieldAssociation.next', defaultMessage: '换一批' })}</div>}
      </div>
      <div className={styles.content}>
        {recommdData?.map(i => {
          const dom = rendererItem(i);
          if (i?.type === 'qa') {
            return <Tooltip title={rendererQa(i)} theme="light" placement="topLeft">{dom}</Tooltip>;
          } else {
            return dom;
          }
        })}
      </div>
    </div>;
  }
});

export default (props) => {
  return <StoreProvider {...props}>
    <MainView />
  </StoreProvider>;
};
