@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.lc-low-handlePrint {

  img {
    width: 100%;
  }
}

.lc-form-page-loader {
  background: #fff;
  min-height: 100%;

  // 调查的题干需要换行
  &-survey {
    .lc-form-page-loader-content {
      .lc-form-page-loader-column {
        .c7n-pro-form-wrapper {
          .c7n-pro-field-label {
            width: 100%;
            white-space: normal;
            height: auto;
          }
        }
      }
    }
  }

  &-spin {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.c7n-spin-spinning {
      display: block !important;
    }

    & + .c7n-spin-blur {
      opacity: .5 !important;
      user-select: none !important;
      pointer-events: none !important;
    }
  }

  & > .c7n-spin-nested-loading {
    height: 100%;

    .c7n-spin-container {
      height: 100%;
    }
  }

  .@{c7n-pro-prefix}-field-wrapper {
    padding: 0.04rem 0.16rem 0.04rem 0.16rem;
  }

  .@{c7n-pro-prefix}-field-output {
    padding: 0.01rem 0.09rem 0.01rem 0.16rem;
  }

  .page-head {
    padding: 0.08rem 0.16rem;
  }

  &-content {
    overflow: hidden !important;

    .lc-title {
      margin-left: 0.16rem;
    }
  }

  &-column {
    width: 100%;
    display: flex;
    flex-direction: column;
    // fix: 修复ckeditor表格显示问题，先注释一段时间看看
    // overflow: hidden;
  }

  &-label {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 0.2rem);

    &-richText {
      color: rgba(18, 39, 77, 0.65);
      white-space: pre-line;
      margin-top: 4px;

      .ql-container {
        border: none !important;
      }

      .ql-editor {
        padding: 0;
        line-height: 22px;
      }

      p {
        display: none;
      }
    }
  }

  &-title {
    width: 100%;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    color: #12274d;
    line-height: 28px;

    &-relative {
      position: relative;
    }

    &-ticket {
      display: flex;
      align-items: center;
      position: absolute;
      top: -30px;
      right: 0;
      font-size: 14px;
      font-weight: 400;
    }

    &-shortdesc {
      margin-left: 6px;
    }
  }

  &-description {
    width: 100%;
    text-align: center;
    margin-top: 8px;
  }

  .preview-item {
    margin-bottom: 0;
  }

  &.READONLY {
    .lc-form-page-loader-content {
      padding: 0.08rem 0 0.24rem;
    }
  }

  &.MODIFY {
    .lc-form-page-loader-content {
      padding: 0;
    }
  }

  &.CREATE {
    height: 100%;

    .page-head {
      border: none;
      background-color: #fff;
      padding: 0;
      height: 0.32rem;
      margin: 0.16rem;

      .page-head > div:last-child {
        right: 0;
      }
    }

    .lc-form-page-loader-content {
      padding: 0 0 0.2rem 0;
      overflow: auto !important;
      height: calc(100% - 0.32rem);

      &-inner {
        height: 100%;
        padding: 0;
      }
    }
  }

  &.PREVIEW {
    padding: 0.24rem;
    margin-top: 0;
    box-shadow: rgba(38, 38, 38, 0.1) 0 0.01rem 0.05rem 0;
    border-radius: 0.04rem;

    .page-head {
      padding: 0;
      height: 0.48rem;
      line-height: 0.32rem;
      min-height: 0.32rem;
      background-color: #fff;
      border: none;

      & > div:last-child {
        right: 0;
      }
    }

    .lc-form-page-loader-content {
      padding: 0.24rem 0;
    }
  }

  &.view-type-TABLE {
    &.hasAnnouncement {
      .lc-page-loader-table {
        height: calc(100vh - 0.48rem - 0.37rem) !important;
      }
    }

    .lc-page-loader-table {
      height: calc(100vh - 0.48rem) !important;
    }
  }

  &.view-type-UPDATE,
  &.view-type-INSERT {
    height: 100%;

    .lc-form-page-loader-content {
      height: calc(100% - 0.48rem);
      overflow: auto !important;

      &.lc-form-page-loader-hiddenHeader {
        height: 100%;
      }

      &-inner {
        height: 100%;
        padding: 0;
      }
    }
  }

  &.view-type-UPDATE {
    .lc-form-page-loader-content {
      padding-top: 16px;

      // 嵌套视图无padding
      .lc-form-page-loader-content {
        padding-top: 0;
      }

      .@{c7n-pro-prefix}-field-wrapper {
        padding: 0.04rem 0.9rem 0.04rem 0.16rem;

        img {
          width: 100% !important;
        }
      }
    }
  }

  &.view-type-MODAL {
    .lc-form-page-loader-content {
      padding: 0 !important;
    }
  }

  &.ticket-header {
    .page-head {
      background: #fff;

      .yqcloud-icon-park-wrapper.additional-btn {
        justify-content: center;
        align-items: center;

        color: @primary-color;

        background-color: @primary-x;
        border: 1px solid @primary-1;

        transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;

        svg {
          width: 20px !important;
          height: 20px !important;
        }

        &:hover {
          color: #fff;

          background-color: @primary-color;
        }
      }
    }

    .lc-form-page-loader-content {
      padding-top: 0;

      .@{c7n-pro-prefix}-field-wrapper {
        padding: 0.04rem 0.16rem;
      }
    }
  }

  &.paddingHorizontal {
    padding: 0 0.16rem;
  }

  .lc-schedule-info-wrapper {
    display: flex;

    > span:first-child {
      width: calc(100% - 32px);
    }

    .ck-editor-wrapper {
      width: 100%;
    }
  }

  .lc-more-info {
    &-wrapper {
      position: relative;

      .@{c7n-pro-prefix}-select-lov {
        width: 100%;
      }

      &.hasQuickAdd {
        width: calc(100% - 0.4rem);
      }

      &.hasInfoView {
        width: calc(100% - 0.4rem);
      }

      &.hasQuickAdd.hasInfoView {
        width: calc(100% - 0.8rem);
      }
    }

    &-icon {
      top: 0;
      width: 0.32rem;
      height: 0.32rem;
      position: absolute;
      right: -0.4rem;
      border: 0.01rem solid @yq-border-2;
      border-radius: 0.04rem;
      vertical-align: middle;
      margin-left: 0.08rem;
      cursor: pointer;
      color: #595959;
      font-size: 0.16rem;
      padding: 0.07rem;

      &.has-info {
        right: -0.8rem;
      }
    }
  }

  .survey-pagination {
    text-align: center;
    padding: 12px;
    padding-top: 24px;
    display: flex;
    align-items: center;
    justify-content: center;

    &-page-info {
      font-size: 14px;
      font-weight: 400;
      color: #12274d;
      line-height: 22px;

      > span {
        display: inline-block;
        width: 32px;
        height: 24px;
        background: #fff;
        border-radius: 4px;
        margin: 0 0.1rem;
      }

      > span.current-page {
        border: 1px solid #d7e2ec;

        input {
          border: none;
          height: 20px;
          padding: 0;
          text-align: center;
        }
      }
    }

    .pagination-icon {
      cursor: pointer;
    }

    .left-icon {
      &-disabled {
        cursor: not-allowed;
        color: #babfc9;
      }
    }

    .right-icon {
      &-disabled {
        cursor: not-allowed;
        color: #babfc9;
      }
    }
  }

  // 子任务面包屑
  &-subBreadCrumb {
    &-item {
      color: rgba(18, 39, 77, 0.65);
      cursor: pointer;

      &:hover {
        color: rgba(18, 39, 77, 0.65);
        text-decoration: underline;
      }
    }
  }
}

.lc-form-preview {
  &-popover {
    .c7n-popover-arrow {
      display: none;
    }

    .c7n-popover-title {
      padding: 12px 16px;
      line-height: 24px;
      color: #12274d;
      font-weight: 500;
      font-size: 14px;
    }

    .c7n-popover-inner-content {
      padding: 12px 0 12px 0;
    }
  }

  &-content {
    height: 230px; // 由于需要计算位置，这里只能写成固定值
    overflow: auto;

    .lc-page-info-preview-title {
      min-width: 1.77rem;
      // min-height: 0.32rem;
      margin: 0;
      font-weight: 500;
      font-size: unset;
      border-bottom: 0.01rem solid rgba(203, 210, 220, 0.5);
      padding: 0 0.16rem 0.12rem 0.16rem;
      line-height: 0.22rem;
      color: #12274d;
    }
  }
}

.@{c7n-pro-prefix}-modal-body {
  .lc-form-page-loader-content {
    // 弹窗视图无边距
    padding-top: 0 !important;

    .lc-title {
      // 弹窗视图不缩进
      margin-left: 0 !important;
    }

    // 弹窗form无边距
    .@{c7n-pro-prefix}-field-wrapper {
      padding: .04rem 0 .04rem .16rem !important;
    }

    .@{c7n-pro-prefix}-field-label {
      padding: 0;
    }
  }
}
