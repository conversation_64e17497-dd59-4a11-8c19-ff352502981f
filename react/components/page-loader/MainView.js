/* eslint-disable no-console */
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Spin } from 'choerodon-ui';
import classnames from 'classnames';
import { getEnv } from '@zknow/utils';
import axios from 'axios';
import QRCode from 'qrcode.react';
import { Header, nomatch as NoMatch, Permission } from '@yqcloud/apps-master';
import { Icon, TRANSLATE_FIELDS, ClickText } from '@zknow/components';
import { message, Modal, Button, TextField, Tooltip, Form, Select } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import queryString from 'query-string';
import { ModalTitle, Empty } from '@zknow/components';
import DownloadModal from '@/components/download-modal';
import PageLoader from '@/components/page-loader/index';
import WechatBind from '@/components/wechat-bind';
import BatchImport from '@/components/batch-import';
import { calculateHide, ModalAction, validateMessage, handleBack, calculateFieldDefaultValue } from './utils';
import Store from './stores';
import Preview from './Preview';
import { openViewById } from '@/utils';
import NotFound from '@/assets/images/not-found.svg';
import TranslateLoading from './components/translation-loading';
import useServiceSetting from '../../hooks/useServiceSetting';
import useTranslate from '../../hooks/useTranslate';
import { isInIframe } from '@/utils';

const modalKey = Modal.key();
const modalPrintKey = Modal.key();
const HiddenParent = () => {
  const ref = useRef();
  useEffect(() => {
    try {
      // 无权限隐藏菜单
      if (ref.current.parentElement?.parentElement?.role === 'menuitem') {
        ref.current.parentElement.parentElement.hidden = true;
      } else {
        ref.current.parentElement.hidden = true;
      }
    } catch {
      /** */
    }
  }, []);
  return <div ref={ref} />;
};

export default observer(() => {
  const rootSectionRef = useRef(null);
  const context = useContext(Store);
  const {
    history, lastViewId, mode, viewDataSet, buttonDataSet, sectionDataSet, parentKey, openType,
    actionDataSet, dsManager, styles, classNames, surveyFlag, showHeaderFlag, viewId, HeaderStore,
    modal, ticketFlag, formDataSet, intl, tenantId, instanceId, parentDataSet, person, personId,
    pageableFlag, isLastPage = () => { }, pageViewFlag, lastInstance, openBy, modalButtonFlag = true,
    surveyTitleFlag = true, hiddenFooter, scheduleFlag, scPageRef, autoCalculateFlag, calculateData, DS_QUERY_IDENTIFICATION,
    infoFlag = false, mainStore, AppState, gptTenantFlag, tabMenuDataSet, onJumpNewPage, subTaskParentDataSet, defaultType,
    templateOptionDs, buttonPrintDataSet, scItemViewFlag, surveyDataRecord, pageDOMRefs,
  } = context;
  const name = viewDataSet.current?.get('name');
  const description = viewDataSet.current?.get('description');
  const id = viewDataSet.current?.get('id');
  const viewType = viewDataSet.current?.get('viewType');
  const viewCode = viewDataSet.current?.get('code');
  const jsonData = viewDataSet.current?.get('jsonData');
  const hamburgerButton = viewDataSet.current?.get('jsonData.hamburgerButton');
  const sections = viewDataSet.current?.get('jsonData.sections');
  const buttons = viewDataSet.current?.get('jsonData.buttons');
  const targetDataSet = dsManager.get(viewId || id);
  const ds = scItemViewFlag ? targetDataSet : (formDataSet || targetDataSet);
  const modalStyle = useMemo(() => ({ width: 520 }), []);
  const attachmentModalStyle = useMemo(() => ({ width: 800 }), []);
  const bodyStyle = useMemo(() => ({ padding: 0 }), []);
  const [surveyCurrentPage, setSurveyCurrentPage] = useState(1);
  const [surveyPageSize, setSurveyPageSize] = useState(1); // 分页调查的section数量大小
  const pageRef = useRef();
  const [actionSetId, setActionSetId] = useState(false); // 标识是否配置动作
  const boId = viewDataSet?.current?.get('businessObjectId');
  const serviceSetting = useServiceSetting(boId, '', tenantId, AppState);
  const handleTranslate = useTranslate({ intl, mainStore, tenantId, formDataSet: ds, setShowPopover: () => { }, businessObjectId: boId, ticketId: instanceId });
  const translatingRef = useRef(false);
  const isUDMTicket = queryString.parse(history.location.search)?.udmTenantId;

  useEffect(() => {
    pageDOMRefs.current = rootSectionRef;
  }, []);

  let openModal;

  useEffect(() => {
    if (autoCalculateFlag && ds?.current && calculateData) {
      const fiedCodeMap = dsManager.getFieldCodeMap(viewId || id);
      const hasUDMDefault = ds.getState('udmUpgradeDefault');
      Object.keys(calculateData).forEach(key => {
        const config = fiedCodeMap && fiedCodeMap.get(key);
        let defaultValue = config ? calculateFieldDefaultValue({
          widgetType: config.widgetType,
          code: config.code,
          widgetConfig: config.widgetConfig,
        }, calculateData) : calculateData[key];
        if (hasUDMDefault && ds.current.get(key)) {
          // 保证UDM的默认值会覆盖视图的默认值
          defaultValue = ds.current.get(key);
        }
        if (defaultValue) {
          if (config?.widgetType === 'MasterDetail') {
            const { relationLovNameFieldCode = 'name' } = config?.widgetConfig || {};
            const nameField = `${key}:${relationLovNameFieldCode}`;
            if (typeof defaultValue === 'object' && !calculateData[nameField]) {
              ds.current.init(nameField, defaultValue[relationLovNameFieldCode]);
              // 如果当前是多对一字段，处理下级字段
              Object.keys(calculateData[key]).forEach(subKey => {
                const subFieldKey = `${key}:${subKey}`;
                // 视图中有当前字段，并且不为名称字段（不影响名称字段逻辑）
                if (ds.getField(subFieldKey) && nameField !== subFieldKey) {
                  ds.current.init(subFieldKey, calculateData[key][subKey]);
                }
              });
            }
            ds.current.init(key, defaultValue);
          } else if (config?.widgetType === 'Time' && defaultValue.length === 8) {
            // 如果后端默认值仅返回了时间格式，ds中转换为moment会报错Invalid date
            ds.current.init(key, `1970-01-01 ${defaultValue}`);
          } else {
            ds.current.init(key, defaultValue);
          }
        }
      });
    }
  }, [autoCalculateFlag, ds?.current, calculateData]);

  useEffect(() => {
    async function getActions() {
      // 视图中配置了动作集
      const hasUIAction = buttons?.find(btn => btn.tag === 'ButtonAction');
      // 平台视图不支持动作集
      const isPlatform = viewDataSet.current?.get('tenantId') === '0';
      // 显示header才显示动作
      const showHeader = calculateShowHeader();
      if (hasUIAction && viewCode && instanceId && showHeader && !isPlatform) {
        try {
          const res = await axios.get(`/lc/v1/${tenantId}/lc_actions/apply_list/${viewCode}?id=${instanceId}`);
          if (!res.failed) {
            if (res.length) {
              // 查出的动作来自同一个动作集，随便取一个即可
              setActionSetId(res[0].actionSetId);
            }
          }
        } catch (e) {
          // 动作接口异常
        }
      }
    }
    getActions();
  }, [viewCode, buttons]);

  function getHamBtnConfig() {
    if (isUDMTicket) {
      // 上下游页面，由于跨租户，能实现的功能有限
      return {
        defaultAction: ['REFRESH', 'CHECK'],
        buttonFlag: true,
      };
    }
    if (hamburgerButton?.length) {
      return {
        defaultAction: hamburgerButton[0]?.defaultActions?.split(','),
        buttonFlag: hamburgerButton[0]?.displayFlag,
      };
    }
    // 新建不显示刷新&查看记录
    if (!instanceId) {
      return {
        defaultAction: 'COPY,CONFIG'.split(','),
        buttonFlag: true,
      };
    }
    // 默认配置
    return {
      defaultAction: 'REFRESH,CHECK,COPY,CONFIG,NEWTAB,ACTION'.split(','),
      buttonFlag: true,
    };
  }

  useEffect(() => {
    return () => {
      if (dsManager) {
        dsManager.clear();
      }
    };
  }, []);

  useEffect(() => {
    if (modal) {
      const modalProps = {};
      if (name) {
        // 右侧弹出的详情视图，需要显示更多按钮
        if (openType === 'RIGHT' && ds?.current && instanceId) {
          modalProps.title = (
            <ModalTitle
              title={name}
              dataSet={ds}
              actionsList={getActionList(ds, ds?.current)}
              {...getHamBtnConfig()}
            />
          );
        } else {
          // 其他情况只更新名称
          modalProps.title = name;
        }
      }
      if (buttonDataSet?.treeData?.length && modalButtonFlag) {
        modalProps.footer = buttonDataSet.treeData.filter(v => !v.get('areaId')).map(renderPreview);
      }
      if (openBy !== 'Button') {
        const modalAction = new ModalAction();
        !modal.props.onOk && modal.handleOk(modalAction.handleOk({ viewMode: mode, dataSet: ds, parentDataSet }));
        !modal.props.onCancel && modal.handleCancel(modalAction.handleClose({
          viewMode: mode,
          dataSet: ds,
          intl,
          parentDataSet,
          viewRecord: viewDataSet.current,
        }));
        if (!modal.props.onClose) {
          modalProps.onClose = modalAction.handleClose({ viewMode: mode, dataSet: ds, intl, parentDataSet, viewRecord: viewDataSet.current });
        }
      }
      !hiddenFooter && modal.update(modalProps);
    }
  }, [buttonDataSet?.treeData?.length, name, ds?.current, lastInstance?.index]);

  useEffect(() => {
    querySubBreakCrums();
  }, [boId, ds?.current?.get('id'), subTaskParentDataSet]);

  // 视图中存在RenderSubTaskBreadCrumbs__Flag自定义组件，查询父级单据信息
  const querySubBreakCrums = async () => {
    try {
      if (sections && JSON.stringify(sections).includes('RenderSubTaskBreadCrumbs__Flag') && subTaskParentDataSet && boId && ds?.current?.get('id')) {
        subTaskParentDataSet.transport.read.url = `/itsm/v1/${tenantId}/subtasks/${boId}/${ds?.current?.get('id')}/crumb?workbenchType=${defaultType || 'SUPPORTER_WORKBENCH'}`;
        await subTaskParentDataSet.query();
        const { shortDescription, ticketId } = subTaskParentDataSet?.current?.toData() || {};
        subTaskParentDataSet?.current?.set('short_description', shortDescription);
        subTaskParentDataSet?.current?.set('id', ticketId);
      }
    } catch (error) {
      //
    }
  };

  function renderPreview(record) {
    const funcConfig = { person, personId, tenantId };
    const hideFlag = calculateHide(record, ds?.current, mode, parentKey, funcConfig);
    if (hideFlag) {
      return null;
    }
    return (
      <Preview
        key={record.key}
        record={record}
        dataSet={ds}
        modal={modal}
        context={context}
        hideFlag={hideFlag}
        instanceId={instanceId}
      />
    );
  }

  function renderPreviewBtn(record) {
    return (
      <Preview
        key={record.key}
        record={record}
        dataSet={ds}
        modal={modal}
        context={context}
        instanceId={instanceId}
      />
    );
  }

  function getBackPath() {
    let list = [];
    try {
      list = JSON.parse(sessionStorage.getItem('lastViews'));
    } catch (e) {
      list = [];
    }
    if (list?.length > 1) {
      return `${list[list.length - 2].split('#')[1]}&${history.location?.search}`;
    } else {
      return false;
    }
  }

  /**
   * 打开视图
   * @param view
   * @param dataSet
   * @param current
   * @param viewMode
   * @param defaultData
   * @returns {boolean}
   */
  async function openView(view = {}, dataSet, current, viewMode = 'MODIFY', defaultData) {
    const { viewSize, openViewType, openViewId, viewName, btnName, btnType } = view;
    const viewModalStyle = { width: Number(viewSize) };
    if (openViewType === 'NEW') {
      history.push(`/lc/engine/${openViewId}${current.get('id') ? `/${current.get('id')}` : ''}${id ? `/${id}` : ''}${history.location?.search}`);
    } else if (openViewType === 'RIGHT' || openViewType === 'MIDDLE') {
      if (openModal) {
        await openModal.close();
      }
      openModal = Modal.open({
        title: openViewType === 'MIDDLE' ? (viewName || btnName) : '',
        children: (
          <PageLoader
            instanceId={btnType === 'CREATE' ? undefined : current.get('id')}
            viewId={openViewId}
            pageRef={pageRef}
            mode={viewMode}
            openType={openViewType}
            defaultData={defaultData}
          />
        ),
        key: modalKey,
        drawer: openViewType === 'RIGHT',
        style: viewModalStyle,
        destroyOnClose: true,
        onOk: async () => {
          if (viewMode === 'READONLY') {
            openModal = false;
            return true;
          }
          if (await validateMessage(pageRef.current?.formDataSet, intl)) {
            await pageRef.current?.formDataSet?.submit();
            dataSet.query();
            openModal = false;
            return true;
          }
          return false;
        },
        onCancel: () => {
          openModal = false;
          if (viewMode === 'READONLY') {
            return true;
          }
          pageRef.current?.formDataSet?.reset();
        },
        okButton: viewMode !== 'READONLY',
        cancelText: viewMode === 'READONLY' ? intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }) : undefined,
      });
    }
    return true;
  }

  // 跳转动作页面
  async function openActionByCode() {
    if (actionSetId) {
      const search = window.location.href.split('?')[1];
      const solutionId = queryString?.parse?.(search)?.solutionId;
      window.open(`${window.location.origin}#/lc/ui_action/${actionSetId}?tenantId=${tenantId}&solutionId=${solutionId}`);
    }
  }

  function openBindWechat() {
    Modal.open({
      // title: intl.formatMessage({ id: 'bindWechat' }),
      children: (
        <WechatBind personId={personId} intl={intl} tenantId={tenantId} />
      ),
      footer: null,
    });
  }
  /**
   * 渲染表格行按钮
   * @param dataSet
   * @param record
   * @returns {*}
   */
  const getActionList = (dataSet, record) => {
    if (isUDMTicket) {
      return [];
    }
    const actions = [];
    actionDataSet?.map((actionRecord) => {
      const icon = actionRecord.get('icon');
      const btnName = actionRecord.get('name');
      const btnId = actionRecord.get('id');
      const btnType = actionRecord.get('type');
      const action = actionRecord.get('action');
      const scope = actionRecord.get('scope');
      const module = actionRecord.get('module');
      const confirmFlag = actionRecord.get('confirmFlag');
      const confirmText = actionRecord.get('confirmText');
      const okText = actionRecord.get('okText');
      const cancelText = actionRecord.get('cancelText');
      const openViewId = actionRecord.get('viewId');
      const viewName = actionRecord.get('viewName');
      const openViewType = actionRecord.get('openType');
      const viewSize = actionRecord.get('viewSize') || 800;
      const updateFieldCode = actionRecord.get('updateFieldCode');
      const trueValueText = actionRecord.get('trueValueText');
      const falseValueText = actionRecord.get('falseValueText');
      const trueValueIcon = actionRecord.get('trueValueIcon');
      const falseValueIcon = actionRecord.get('falseValueIcon');
      const qrType = actionRecord.get('qrType');
      const qrFields = actionRecord.get('qrFields');
      const qrViewType = actionRecord.get('qrViewType');
      const qrViewId = actionRecord.get('qrViewId');
      const areaId = actionRecord.get('areaId');
      // 打印配置
      const printRelatedKey = actionRecord.get('widgetConfig.printRelatedKey');
      const printRelatedObjectId = actionRecord.get('widgetConfig.printRelatedObjectId');

      const funcConfig = { person, personId, tenantId };
      const hideFlag = calculateHide(actionRecord, dataSet?.current, mode, parentKey, funcConfig);
      if (hideFlag) {
        return null;
      }

      if ((scope && module) || ['GenerateKnowledge'].includes(actionRecord.get('tag'))) {
        actions.push({
          name,
          areaId,
          onClick: () => { },
          element: (
            <Preview
              record={actionRecord}
              tableLineRecord={record}
              dataSet={ds}
              feature="table-action" // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
              context={context}
            />
          ),
        });
        return actionRecord;
      }

      const view = {
        viewSize,
        openViewType,
        openViewId,
        viewName,
        btnName,
        btnType,
      };

      const loadExpression = async () => {
        if (mode === 'PREVIEW') {
          return null;
        }
        if (btnType !== 'CREATE' && btnType !== 'COPY') {
          openView(view, dataSet, record);
          return true;
        }
        const fieldMap = {
          _parentId: instanceId, // 计算默认值将父级id传入
        };
        const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${openViewId}/${openViewId}/calculate`, JSON.stringify(fieldMap));
        if (result && !result.failed) {
          const obj = btnType === 'COPY' ? { ...result, ...record.toData() } : result;
          if (btnType === 'COPY') {
            delete obj.id;
            obj.createFromCopy = true;
          }
          openView(view, dataSet, record, 'MODIFY', obj);
        } else {
          const obj = btnType === 'COPY' ? record.toData() : null;
          if (btnType === 'COPY') {
            delete obj.id;
            obj.createFromCopy = true;
          }
          openView(view, dataSet, record, 'MODIFY', obj);
        }
      };

      function executionAction() {
        if (action === 'OPEN_VIEW' && openViewId) {
          loadExpression();
        }
        return true;
      }

      async function handleSubmit() {
        const recordValidate = await validateMessage(dataSet, intl, scPageRef);
        if (!recordValidate) {
          return false;
        }
        // 提交前触发submit事件，dataSet默认submit中修改数据不会被提交
        if (dataSet.props?.events?.submit) {
          await dataSet.props?.events?.submit({ dataSet, data: dataSet.current?.toData() });
        }
        try {
          const resData = await dataSet.submit();
          if (!resData?.failed) {
            if (resData?.content[0]?.id && instanceId === 'new') {
              history.push({
                pathname: `/lc/engine/${lastViewId}`,
                search: history.location?.search,
              });
            }
            if (parentDataSet) {
              parentDataSet?.query();
            } else {
              dataSet.query();
            }
            if (modal) {
              modal.close();
            }
            return true;
          } else {
            message.error(resData?.message);
            return false;
          }
        } catch (e) {
          return false;
        }
      }

      async function handleDelete() {
        await dataSet.delete(dataSet.current, false);
      }

      async function handleUpdate() {
        record?.set(updateFieldCode, !record.get(updateFieldCode));
        await dataSet?.submit();
        dataSet.query();
      }

      const handleExpression = async () => {
        const fieldMap = dataSet?.current?.toData() || dataSet?.get(0)?.toData() || {};
        const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${id}/executeButton/${btnId}`, JSON.stringify(fieldMap));
        if (!result.failed) {
          message.success(result?.message || intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
          // NODE 表达式执行后触发 DataSet 钩子
          if (dataSet.props?.events?.submitSuccess) {
            await dataSet.props?.events?.submitSuccess({ dataSet, data: dataSet.current?.toData() });
          }
          dataSet.query();
        } else {
          message.error(result?.message || intl.formatMessage({ id: 'lcr.components.desc.expression.execute.failed', defaultMessage: '提交失败' }));
        }
      };

      function preConfirm(executionFun) {
        if (confirmFlag) {
          Modal.confirm({
            title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
            children: (
              <div>{confirmText}</div>
            ),
            okText,
            cancelText,
          }).then((button) => {
            if (button === 'ok') {
              executionFun();
            }
          });
        } else {
          executionFun();
        }
      }

      function handleQRCodeDownload() {
        const canvas = document.getElementById('lc-qrCode');
        const img = new Image();
        img.src = canvas.toDataURL('image/png');
        img.onload = () => {
          const link = document.createElement('a');
          link.href = img.src;
          link.download = `${intl.formatMessage({ id: 'lcr.components.desc.qr.code', defaultMessage: '二维码' })}.png`;
          const event = new MouseEvent('click');
          link.dispatchEvent(event);
        };
      }

      async function handleQRCode() {
        const currentViewId = viewDataSet?.current?.get('id');
        const businessObjectId = viewDataSet?.current?.get('businessObjectId');
        const realViewId = qrViewType === 'PC' ? currentViewId : qrViewId;
        let qrValue = `${getEnv('API_HOST')}/lc/v1/${tenantId}/views/scanQRCode/${realViewId}${instanceId ? `?dataId=${instanceId}` : ''}`;
        if (instanceId && businessObjectId && qrType === 'RETRIEVE_DATA') {
          let fields = [];
          try {
            const fieldList = JSON.parse(qrFields);
            fields = fieldList.map(f => f.path);
          } catch (e) {
            fields = [];
          }
          try {
            const result = await axios.post(`/lc/v1/${tenantId}/business_objects/data/${businessObjectId}/${instanceId}`, fields);
            if (!result.failed) {
              qrValue = JSON.stringify({
                ...result,
                viewId: realViewId,
              });
            } else {
              message.error(intl.formatMessage({ id: 'lcr.components.desc.execute.failed', defaultMessage: '查询数据失败' }));
              return false;
            }
          } catch (e) {
            return false;
          }
        }
        Modal.open({
          title: viewName || name,
          children: (
            <div style={{ textAlign: 'center' }}>
              <QRCode
                value={qrValue}
                size={255}
                fgColor="#000"
                includeMargin
                id="lc-qrCode"
              />
            </div>
          ),
          key: modalKey,
          style: modalStyle,
          bodyStyle,
          destroyOnClose: true,
          okText: intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' }),
          onOk: handleQRCodeDownload,
        });
      }

      async function handleImport() {
        const businessObjectId = viewDataSet.current?.get('businessObjectId');
        Modal.open({
          title: intl.formatMessage({ id: 'lcr.components.desc.batch.import', defaultMessage: '批量导入' }),
          children: <BatchImport businessObjectId={businessObjectId} />,
          key: modalKey,
          style: modalStyle,
          bodyStyle: { padding: 0 },
          destroyOnClose: true,
          okCancel: false,
          okText: intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }),
        });
      }

      async function handleAttachment() {
        const json = viewDataSet?.current?.get('jsonData');
        const dsFieldList = json?.datasets?.find(dataset => dataset.id === id)?.fields || [];
        const attachments = [];
        const formRecord = dataSet?.current;
        if (formRecord && dsFieldList) {
          dsFieldList.map(file => {
            if (['Upload', 'Image'].includes(file.widgetType)) {
              attachments.push(formRecord.get(file.code));
            }
            return file;
          });
        }
        Modal.open({
          title: intl.formatMessage({ id: 'lcr.components.desc.attachment', defaultMessage: '下载附件' }),
          children: (
            <DownloadModal attachments={attachments} fileName={`${name}_${instanceId}`} />
          ),
          key: modalKey,
          style: attachmentModalStyle,
          bodyStyle,
          destroyOnClose: true,
        });
      }

      async function handlePrint() {
        const currentMenu = tabMenuDataSet?.find?.(r => r.getState('current'));
        let dataId = currentMenu?.get?.('id') || instanceId || tabMenuDataSet?.toData?.()?.[0]?.id;
        const { search } = history?.location || {};
        if (!dataId) {
          try {
            dataId = queryString?.parse?.(search)?.ticketId;
          } catch (e) {
            message.error(e);
          }
        }
        templateOptionDs.setQueryParameter('printRelatedKey', printRelatedKey);
        templateOptionDs.setQueryParameter('printRelatedObjectId', printRelatedObjectId);
        await templateOptionDs.query();
        if (buttonPrintDataSet.current) buttonPrintDataSet.current.set('templateId', templateOptionDs.toData()?.[0]?.templateId);
        openModal = Modal.open({
          title: viewName || name,
          children: (
            <Form dataSet={buttonPrintDataSet}>
              <Select name="templateId" />
            </Form>
          ),
          key: `${modalPrintKey}`,
          destroyOnClose: true,
          onOk: async () => {
            const val = await buttonPrintDataSet.validate();
            if (val) {
              const data = buttonPrintDataSet.current.toData();
              const res = await axios.get(`/report/v1/${tenantId}/report-print/template/pdf`, {
                params: {
                  businessObjectId: viewDataSet.current.get('businessObjectId'),
                  templateId: data.templateId,
                  dataId,
                  printRelatedKey,
                  printRelatedObjectId,
                },
                responseType: 'blob',
              });
              if (!res?.failed) {
                const blob = new Blob([res], { type: 'application/pdf' });
                const link = window.URL.createObjectURL(blob);
                // 创建一个隐藏的iframe用于加载PDF
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none'; // 确保iframe不会显示在页面上
                iframe.src = link;
                document.body.appendChild(iframe);

                // 加载完成后自动打印
                iframe.onload = function () {
                  iframe.contentWindow.print();
                };
              } else {
                message.error(`${intl.formatMessage({ id: 'lcr.components.desc.print.template.api.error', defaultMessage: '接口错误数据: {message}' }, { message: res?.message })}`);
              }
              return true;
            }
            return false;
          },
        });
      }

      async function handleClick() {
        if (mode === 'PREVIEW') {
          return null;
        }
        switch (btnType) {
          case 'SUBMIT':
            await preConfirm(handleSubmit);
            break;
          case 'DELETE':
            await preConfirm(handleDelete);
            break;
          case 'UPDATE':
            await preConfirm(handleUpdate);
            break;
          case 'CREATE':
            if (viewId) {
              await preConfirm(executionAction);
            } else {
              await preConfirm(handleSubmit);
            }
            break;
          case 'ADD':
          case 'CUSTOM':
          case 'OPEN':
            await preConfirm(executionAction);
            break;
          case 'COPY':
            await preConfirm(executionAction);
            break;
          case 'EXPRESSION':
            await preConfirm(handleExpression);
            break;
          case 'WORKFLOW':
            await preConfirm(handleExpression);
            break;
          case 'QRCode':
            await handleQRCode();
            break;
          case 'Attachment':
            await handleAttachment();
            break;
          case 'IMPORT':
            await handleImport();
            break;
          case 'PRINT':
            await preConfirm(handlePrint);
            break;
          default:
            break;
        }
      }

      function getName() {
        if (btnType === 'UPDATE' && updateFieldCode) {
          return record?.get(updateFieldCode) ? trueValueText : falseValueText;
        }
        return btnName;
      }

      function getIcon() {
        if (btnType === 'UPDATE' && updateFieldCode) {
          return record?.get(updateFieldCode) ? trueValueIcon : falseValueIcon;
        }
        return icon;
      }

      actions.push({
        name: getName(),
        icon: getIcon(),
        onClick: handleClick,
        key: btnId,
        areaId,
      });

      return actionRecord;
    });
    // 跳转视图配置按钮
    if (getHamBtnConfig()?.defaultAction?.includes('CONFIG')) {
      actions.unshift({
        icon: 'SettingWeb',
        element: (
          <Permission
            service={[
              'yqcloud-lowcode.view.create',
              'yqcloud-lowcode.view.page',
              'yqcloud-lowcode.view.update',
            ]}
            noAccessChildren={<HiddenParent />}
          >
            <div onClick={() => openViewById(viewId)}>
              {intl.formatMessage({ id: 'lcr.components.desc.view.config', defaultMessage: '视图配置' })}
            </div>
          </Permission>
        ),
        key: `openViewConfig-${viewId}`,
      });
    }
    // 跳转动作配置按钮
    if (getHamBtnConfig()?.defaultAction?.includes('ACTION') && actionSetId) {
      actions.unshift({
        icon: 'SettingWeb',
        element: (
          <Permission
            service={[
              'yqcloud-lowcode.action-set.create',
              'yqcloud-lowcode.action-set.page',
              'yqcloud-lowcode.action-set.update',
            ]}
            noAccessChildren={<HiddenParent />}
          >
            <div onClick={() => openActionByCode()}>
              {intl.formatMessage({ id: 'lcr.components.desc.action.config', defaultMessage: '动作配置' })}
            </div>
          </Permission>
        ),
        key: `openActionConfig-${viewCode}`,
      });
    }
    if (HeaderStore.getTenantConfig.qrcodeDetailViewFlag && getHamBtnConfig()?.defaultAction?.includes('WECHAT')) {
      actions.unshift({
        icon: 'ScanningTwo',
        element: (
          <div onClick={() => openBindWechat()}>
            {intl.formatMessage({ id: 'lcr.components.desc.wechat.bind.message', defaultMessage: '微信接收消息通知' })}
          </div>
        ),
        key: `openViewConfig-${viewId}`,
      });
    }
    return actions;
  };

  // 跳转门户单据详情
  const handleOpenSurveyTicket = () => {
    if (surveyDataRecord?.get('refId') && surveyDataRecord?.get('viewId')) {
      window.open(`${window.location.origin}/#/itsm/portal/service_list?ticketId=${surveyDataRecord?.get('refId')}&extraInstanceId=${surveyDataRecord?.get('refId')}&viewId=${surveyDataRecord?.get('viewId')}`);
    }
  };

  const renderHeader = () => {
    if (surveyFlag) {
      return surveyTitleFlag ? (
        <div>
          <div className={classnames('lc-form-page-loader-title', { 'lc-form-page-loader-title-relative': surveyDataRecord?.get('refId') })}>
            {name}
            {/* 调查需要展示关联单据 */}
            {surveyDataRecord?.get('refId') && surveyDataRecord?.get('viewId') && (
              <div className="lc-form-page-loader-title-ticket">{intl.formatMessage({
                id: 'lcr.components.desc.page.loader.related',
                defaultMessage: '关联服务单据：',
              })}<ClickText onClick={handleOpenSurveyTicket}>{surveyDataRecord?.get('refNumber') || '-'}</ClickText><span className="lc-form-page-loader-title-shortdesc">{surveyDataRecord?.get('relationShortDescription') || ''}</span>
              </div>
            )}
          </div>
          <div className="lc-form-page-loader-description">
            {description}
          </div>
        </div>
      ) : null;
    }
    // 定时任务服务项不需要头部按钮
    if (scheduleFlag) {
      return null;
    }
    if (infoFlag) {
      return (
        <div className="lc-page-info-preview-title">
          {name}
        </div>
      );
    }
    // 子任务详情需要显示面包屑
    let pageName = name;
    try {
      const currentMenu = tabMenuDataSet?.find?.(r => r.getState('current'));
      // 视图中存在RenderSubTaskBreadCrumbs__Flag自定义组件，header需要显示子任务父级面包屑
      if (sections && JSON.stringify(sections).includes('RenderSubTaskBreadCrumbs__Flag') && subTaskParentDataSet?.current) {
        const { shortDescription, number } = subTaskParentDataSet?.current?.toData() || {};
        const tooltipTitle = number && shortDescription ? `${number} ${shortDescription}` : number || shortDescription || '';
        pageName = (<div className="lc-form-page-loader-subBreadCrumb">
          <Tooltip title={`${tooltipTitle}`}>
            <span className="lc-form-page-loader-subBreadCrumb-item" onClick={() => onJumpNewPage({ record: subTaskParentDataSet?.current, viewId: currentMenu?.get('__subParentViewId') || subTaskParentDataSet?.current?.get('detailViewId') })}>{intl.formatMessage({ id: 'lcr.components.desc.renderer.sub.tasks.parent', defaultMessage: '父级单据详情' })}</span>
            /
          </Tooltip>
          {name}
        </div>);
      }
    } catch (error) {
      //
    }

    return (
      <Header
        backPath={getBackPath() && lastViewId}
        dataSet={ds}
        onBackPathBtnClick={() => handleBack(lastViewId, history)}
        actionsList={getActionList(ds, ds?.current)}
        onRefresh={() => {
          ds.query();
          // FIXME: 不要将视图渲染和实际业务逻辑绑定起来（如下），而是增加订阅模式（暂无）
          // TODO: 临时做了个简易订阅，局限性比较大
          const subscribed = ds.getState('subscribe') || {};
          const subscribedDs = Object.keys(subscribed);
          // eslint-disable-next-line no-restricted-syntax
          for (const sDs of subscribedDs) {
            if (subscribed[sDs]) {
              subscribed[sDs]?.query?.();
            }
          }

          // 点击刷新按钮后，刷新动态记录，单据头等
          ds?.current?.setState(
            'approvalHistoryDynamicRefreshCount',
            ds?.current?.getState('approvalHistoryDynamicRefreshCount') + 1 || new Date().getTime(),
          );
        }}
        {...getHamBtnConfig()}
      >
        <h1>{pageName}</h1>
        <div>
          {buttonDataSet
            .treeData
            .filter(r => !r.get('areaId'))
            .map(renderPreviewBtn)}
        </div>
      </Header>
    );
  };

  const calculateShowHeader = () => {
    // 主动传参控制显示
    if (showHeaderFlag === false) {
      return false;
    }
    // TODO: 特殊判断，隐藏单据类型视图预览的Header
    if (jsonData && JSON.stringify(jsonData).indexOf('TicketHeaderRenderer') !== -1) {
      return false;
    }
    // 弹窗中视图不显示
    if (openType && openType !== 'NEW') {
      return false;
    }
    // 表格视图默认不显示
    if (viewType === 'TABLE') {
      return false;
    }
    // 主动传参控制显示
    if (showHeaderFlag !== undefined) {
      return showHeaderFlag;
    }
    // 更新、报表视图显示
    if (['UPDATE', 'REPORT'].includes(viewType)) {
      return true;
    }
    // 新增、预览、禁用模式显示
    if (['CREATE', 'PREVIEW', 'DISABLED'].includes(mode)) {
      return true;
    }
    return false;
  };

  const calculateHasPadding = () => {
    return jsonData && JSON.stringify(jsonData).includes('TicketHeaderRenderer');
  };

  useEffect(() => {
    if (pageableFlag) {
      const isLast = surveyCurrentPage === sectionDataSet?.treeData?.length;
      isLastPage(isLast);
    }
  }, [surveyCurrentPage, sectionDataSet?.treeData?.length]);

  function renderSurveyPagination() {
    if (!pageableFlag) return null;
    if (sectionDataSet.treeData.length > 1) {
      return (
        <div className="survey-pagination">
          <Button
            funcType="flat"
            disabled={surveyCurrentPage === 1}
            onClick={() => {
              if (surveyCurrentPage === 1) return;
              setSurveyCurrentPage(surveyCurrentPage - 1);
            }}
          >
            <Icon type="left" />
          </Button>
          <span className="survey-pagination-page-info">
            {/* 添加页面跳转 */}
            <TextField
              className="current-page"
              value={surveyCurrentPage}
              onChange={val => {
                let newPage = Number(val);
                if (Number.isNaN(val)) {
                  return;
                } else if (val > surveyPageSize) {
                  newPage = surveyPageSize;
                } else if (val < 0) {
                  newPage = 1;
                }
                setSurveyCurrentPage(newPage);
              }}
            />/<span>{surveyPageSize}</span>
          </span>
          <Button
            funcType="flat"
            disabled={surveyCurrentPage === surveyPageSize}
            onClick={() => {
              if (surveyCurrentPage === surveyPageSize) return;
              setSurveyCurrentPage(surveyCurrentPage + 1);
            }}
          >
            <Icon type="right" />
          </Button>
        </div>
      );
    }
    return null;
  }

  // 渲染调查页面
  function renderSurveyPage() {
    const surveyRecordList = [];
    let hiddenCount = 0; // 当前页前隐藏的数量
    sectionDataSet.treeData.map((i, index) => {
      const funcConfig = { person, personId, tenantId };
      const hideFlag = calculateHide(i, ds?.current, mode, parentKey, funcConfig);
      if (hideFlag) {
        if (index + 1 <= surveyCurrentPage) {
          hiddenCount += 1;
        }
        return i;
      } else {
        surveyRecordList.push(i);
      }
      return i;
    });
    if (surveyRecordList?.length !== surveyPageSize) {
      setSurveyCurrentPage(surveyCurrentPage - hiddenCount);
      setSurveyPageSize(surveyRecordList?.length);
    }
    if (!surveyRecordList[surveyCurrentPage - 1]) return null;
    return renderPreview(surveyRecordList[surveyCurrentPage - 1]);
  }

  function getSpinning() {
    if (viewType === 'TABLE') {
      return false;
    }
    return ds?.status ? ds?.status !== 'ready' : false;
  }

  // NOTE: 增加了 DS_QUERY_IDENTIFICATION 自定义状态，用来保持 status 和 id 拿到数据的同步性
  // 防止出现由于mobx重渲染导致的数据不同步而进入下面的条分支
  const noFormDataSetId = formDataSet ? formDataSet?.status === 'ready' && !formDataSet?.current?.get('id') : false;
  const noDsManagerId = formDataSet ? false : targetDataSet?.status === 'ready' && !targetDataSet?.getState(DS_QUERY_IDENTIFICATION);

  // 设置自动翻译
  useEffect(() => {
    // 自动翻译需要进到视图的时候立即触发，可能会出现视图还未加载完成，翻译弹窗已经关闭的情况
    // FIXME 等待翻译的时候会遮盖视图，导致不好的用户体验
    if (!translatingRef.current && !mainStore.getTranslateManualCancel && viewDataSet?.current?.get?.('viewType') === 'UPDATE' && mode !== 'PREVIEW' && !(noFormDataSetId || noDsManagerId) && !(isInIframe())) {
      if (gptTenantFlag && serviceSetting?.ticketTranslationFlag && serviceSetting?.ticketTranslationSettingVO?.autoTranslateFlag) {
        const { autoLanguage, autoType, fields } = serviceSetting?.ticketTranslationSettingVO || {};
        const targetLang = autoLanguage && autoType === 'CUSTOMER' ? autoLanguage : AppState?.currentLanguage;
        ds?.setState(TRANSLATE_FIELDS, (fields || []).map(({ code }) => code));
        handleTranslate(targetLang || 'zh_CN');
        translatingRef.current = true;
      }
    }
  }, [viewDataSet?.current?.get?.('viewType'), mode, noFormDataSetId, noDsManagerId, gptTenantFlag, serviceSetting, AppState?.currentLanguage, handleTranslate, ds]);

  // 设置子table没有spinning
  useEffect(() => {
    if (!(surveyFlag && sectionDataSet?.current) && ds) {
      ds.setState('childrenSpinning', false);
    }
  }, [ds, surveyFlag, sectionDataSet?.current]);

  if (['UPDATE'].includes(viewDataSet?.current?.get('viewType'))
    && mode !== 'PREVIEW'
    && (noDsManagerId || noFormDataSetId)
  ) {
    // 如果是单据页 并且获取不到当前 formDataSet 的 id 视为无权限
    const actions = modal ? [{
      text: intl.formatMessage({ id: 'zknow.common.button.back', defaultMessage: '返回' }),
      onClick: () => {
        modal?.close();
      },
    }] : [
      {
        text: intl.formatMessage({ id: 'lcr.components.desc.return.portal', defaultMessage: '返回门户' }),
        onClick: () => {
          window.location.href = window.location.origin;
        },
      },
    ];
    return (
      <Empty
        type="permission"
        description={intl.formatMessage({ id: 'lcr.components.desc.empty.page.notfound', defaultMessage: '抱歉，您访问的页面迷路了' })}
        style={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexDirection: 'column',
        }}
        innerStyle={{ width: '170px', height: '189px' }}
        actions={actions}
        picture={NotFound}
      />
    );
  }

  if (surveyFlag && sectionDataSet?.current) {
    return (
      <div
        style={styles || {}}
        className={classnames('lc-form-page-loader lc-form-page-loader-survey', mode, classNames, {
          [`view-type-${viewType}`]: viewType,
          'ticket-header': ticketFlag,
          modal: openType === 'RIGHT',
          hasAnnouncement: !HeaderStore.getAnnouncementClosed,
          // TODO: 临时方案，新的单据结构不加 padding, 旧的单据结构加 padding
          paddingHorizontal: calculateHasPadding(),
        })}
      >
        <Spin className="lc-form-page-loader-spin" spinning={getSpinning()}>
          {calculateShowHeader() ? renderHeader() : ''}
          <div
            // className={classnames('lc-form-page-loader-content')}
            className={classnames('lc-form-page-loader-content', `lc-form-page-loader-anchor-${viewId}-${instanceId}`, {
              'lc-form-page-loader-content-inner': pageViewFlag,
              'lc-form-page-loader-content-root': isRootSection(),
              'lc-form-page-loader-hiddenHeader': !calculateShowHeader(),
            })}
          >
            {
              pageableFlag
                ? renderSurveyPage()
                : sectionDataSet.treeData?.map(renderPreview)
            }
          </div>
        </Spin>
        {renderSurveyPagination()}
      </div>
    );
  }

  function isRootSection() {
    // parentId为null yPosition为0判断为视图根节点
    return !sectionDataSet?.current?.get('parentId') && sectionDataSet?.current?.get('yPosition') === 0;
  }

  if (sectionDataSet?.current) {
    return (
      <div
        ref={rootSectionRef}
        style={styles || {}}
        data-pageId={`${viewId}-${instanceId}`}
        data-viewId={viewId}
        className={classnames('lc-form-page-loader', mode, classNames, {
          [`view-type-${viewType}`]: viewType,
          'ticket-header': ticketFlag,
          modal: openType === 'RIGHT',
          hasAnnouncement: !HeaderStore.getAnnouncementClosed,
          // TODO: 临时方案，新的单据结构不加 padding, 旧的单据结构加 padding
          paddingHorizontal: calculateHasPadding(),
        })}
      >
        <Spin className="lc-form-page-loader-spin" spinning={getSpinning()}>
          {calculateShowHeader() ? renderHeader() : ''}
          <div
            className={classnames('lc-form-page-loader-content', `lc-form-page-loader-anchor-${viewId}-${instanceId}`, {
              'lc-form-page-loader-content-inner': pageViewFlag,
              'lc-form-page-loader-content-root': isRootSection(),
              'lc-form-page-loader-hiddenHeader': !calculateShowHeader(),
            })}
          >
            {sectionDataSet.treeData?.map(renderPreview)}
          </div>
        </Spin>
        {!(isInIframe()) && mainStore.getTranslateLoading && <TranslateLoading />}
      </div>
    );
  }

  if (viewDataSet.status !== 'ready') {
    return (
      <div style={{ height: '100%' }} />
    );
  }
  return <NoMatch />;
});
