@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.moreInfo {
  position: relative;
  :global {
    .@{c7n-pro-prefix}-select-lov {
      width: 100%;
    }
  }

  &.hasQuickAdd {
    width: calc(100% - 0.4rem);
  }

  &.hasInfoView {
    width: calc(100% - 0.4rem);
  }

  &.hasQuickAdd.hasInfoView {
    width: calc(100% - 0.8rem);
  }
  
}
.moreInfoIcon {
  top: 0;
  width: 0.32rem;
  height: 0.32rem;
  position: absolute;
  right: -0.4rem;
  border: 0.01rem solid @yq-border-2;
  border-radius: 0.04rem;
  vertical-align: middle;
  margin-left: 0.08rem;
  cursor: pointer;
  color: #595959;
  font-size: 0.16rem;
  padding: 0.07rem;
  &.hasInfo {
    right: -0.8rem;
  }

}
.moreIconReadOnly {
  width: 16px;
  height: 16px;
  border: none;
  padding: 0;
}
