import React, { useContext, useMemo, useRef, useState, useEffect } from 'react';
import { Form, Modal, RichText } from 'choerodon-ui/pro';
import { Popover } from 'choerodon-ui';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { Icon, YqCodeMirror } from '@zknow/components';
import { EditorRegister } from '@zknow/utils';
import { ACTION, calculateConditions, FOCUS_FIELD_TYPE, calculateHide, calculateFieldDefaultValue } from './utils';
import PageLoader from './index';
import Preview from './Preview';
import Store from './stores';

import styles from './PageLoader.module.less';

const modalAddKey = Modal.key();
const { RichTextViewer } = RichText;

export default observer((props) => {
  const context = props?.context || useContext(Store);
  const {
    dsManager, viewId, viewDataSet, mode: _mode, surveyFlag, numberFlag, tenantId,
    formDataSet, parentKey, intl, mainStore, person, personId, autoFocusDisabled, scheduleFlag,
    scheduleItembusinessObjectId, scItemViewFlag, AppState,
  } = context;
  const mode = props?.mode || _mode;
  const pageRef = useRef();
  const modalAddStyle = useMemo(() => ({ width: 800 }), []);
  const id = viewDataSet.current?.get('id');
  const viewType = viewDataSet.current?.get('viewType');
  const { layout, flex = 1, groupIndex = 0 } = props;
  const { children } = layout;
  // 有字段优先渲染字段，没有再渲染其他组件。不可同时渲染在同一个Section中
  const fields = layout.getCascadeRecords('fields')?.filter(record => record.get('xPosition') === groupIndex);
  const records = (fields?.length ? fields : children)?.filter(record => record.get('xPosition') === groupIndex);
  const ds = scItemViewFlag ? dsManager.get(viewId || id) : (formDataSet || dsManager.get(viewId || id));
  const sectionCode = layout.get('variableSetFlag') && layout.get('code');
  const sectionDisplayMode = layout?.get('displayMode');
  const fullParentKey = `${parentKey || ''}${parentKey && sectionCode ? '.' : ''}${sectionCode || ''}`;
  const [overflowDisplay, setOverflowDisplay] = useState(false);
  const formFlag = fields?.length > 0 && records.filter(field => field.get('tag') !== 'Field').length === 0;

  function renderPreview(record) {
    const funcConfig = { person, personId, tenantId };
    const hideFlag = calculateHide(record, ds?.current, mode, fullParentKey, funcConfig);
    if (hideFlag) {
      return null;
    }
    return (
      <Preview
        key={record.key}
        record={record}
        dataSet={ds}
        context={context}
        setOverflowDisplay={setOverflowDisplay}
        sectionDisplayMode={sectionDisplayMode}
        labelWidth={getLabelWidth()}
        currentTabRef={props?.currentTabRef}
      />
    );
  }

  /**
   * 打开信息视图
   * @param view
   * @param instanceId
   * @returns {boolean}
   */
  function openView(view = {}, instanceId) {
    const { viewId: infoViewId, viewName, name } = view;
    if (infoViewId && instanceId) {
      return (
        <Popover
          placement="rightTop"
          popupClassName="lc-form-preview-popover"
          content={
            <div className="lc-form-preview-content">
              <PageLoader
                instanceId={instanceId}
                viewId={infoViewId}
                pageRef={pageRef}
                mode="OUTPUT"
                parentDataSet={ds}
                showHeaderFlag
                infoFlag
              />
            </div>
          }
          overlayStyle={{ width: 624 }}
          // title={viewName || name}
          trigger="click"
          autoAdjustOverflow
          arrowPointAtCenter
        >
          <Icon
            type="info"
            onClick={() => { }}
            className={classnames(styles.moreInfoIcon, {
              [styles.moreIconReadOnly]: mode === 'OUTPUT',
            })}
          />
        </Popover>
      );
    }
    return null;
  }

  /**
   * 打开快速新建视图
   * @param addViewId
   * @param addViewName
   * @returns {boolean}
   */
  async function openAddView(addViewId, addViewName, record) {
    Modal.open({
      title: addViewName,
      children: (
        <PageLoader
          viewId={addViewId}
          mode="CREATE"
          pageRef={pageRef}
          showHeaderFlag={false}
        />
      ),
      key: modalAddKey,
      drawer: false,
      style: modalAddStyle,
      destroyOnClose: true,
      onOk: async () => {
        if (await pageRef.current?.formDataSet?.validate()) {
          const res = await pageRef.current?.formDataSet?.submit();
          if (res && res.failed) {
            return false;
          } else {
            const newItem = res.content[0];
            const name = fullParentKey ? `${fullParentKey}.${record?.get('code')}` : record?.get('code');
            ds.current?.set(name, newItem);
            return true;
          }
        }
        return false;
      },
      onCancel: () => {
        pageRef.current?.formDataSet?.reset();
      },
    });
    return true;
  }

  function renderField(record) {
    const widgetType = record.get('widgetType');
    const help = record.get('help');
    const placeHolder = record.get('placeHolder');
    const name = record.get('name');
    const title = record.get('title');
    const description = record.get('description');
    const code = record.get('code');
    const fieldId = record.get('id');
    const visibleType = record.get('widgetConfig.visibleType');
    const verticalFlag = record.get('widgetConfig.verticalFlag');
    const visibleAction = record.get('widgetConfig.visibleAction');
    // 只有当类型为条件时，判断条件才生效
    const visibleCondition = visibleType === 'CONDITION' ? record.get('widgetConfig.visibleCondition') : null;
    const informationViewId = record.get('widgetConfig.informationViewId');
    const informationViewName = record.get('widgetConfig.informationViewName');
    const relationLovAddViewId = record.get('widgetConfig.relationLovAddViewId');
    const relationLovAddViewName = record.get('widgetConfig.relationLovAddViewName');

    const policiesData = viewDataSet.current?.getState('policiesData');
    const sortAutomaticallyFlag = viewDataSet.current?.get('sortAutomaticallyFlag');
    let hideFieldFlag = visibleType === 'ALWAYS_NOT_VISIBLE';
    // 根据UI规则控制字段显示隐藏
    const fieldAction = policiesData?.actionMap[code];
    if (fieldAction || visibleType === 'CONDITION') {
      const funcConfig = { person, personId, tenantId };
      // 隐藏条件判断
      const hideIds = fieldAction && fieldAction[ACTION.HIDE];
      if (hideIds || (visibleAction === ACTION.HIDE)) {
        hideFieldFlag = calculateConditions(fullParentKey, policiesData.conditionMap, hideIds, ds?.current, visibleCondition, funcConfig, code, true);
      }
      // 显示条件判断
      const showIds = fieldAction && fieldAction[ACTION.SHOW];
      if (showIds || (visibleAction === ACTION.SHOW && visibleCondition?.length)) {
        hideFieldFlag = !calculateConditions(fullParentKey, policiesData.conditionMap, showIds, ds?.current, visibleCondition, funcConfig, code, true);
      }
    }
    if (hideFieldFlag) {
      return null;
    }
    const field = EditorRegister.get(widgetType);
    // 默认焦点
    if (FOCUS_FIELD_TYPE.includes(widgetType) && !mainStore.getFocus) {
      mainStore.setFocus(code);
    }
    // 字段前增加编号
    let number = '';
    if (numberFlag && sortAutomaticallyFlag) {
      const fieldMap = mainStore.getMap;
      if (fieldMap[fieldId]) {
        number = fieldMap[fieldId];
      } else {
        number = `${mainStore.getNumber}. `;
        mainStore.setMap({
          ...fieldMap,
          [fieldId]: number,
        });
        mainStore.setNumber();
      }
    }
    let label = `${number}${surveyFlag ? (title || name) : name}`;
    // 对于调查定义，配置了详述，需要显示在label下面
    if (surveyFlag && description) {
      label = (
        <span className="lc-form-page-loader-label">
          <div>{label}</div>
          <RichTextViewer className="lc-form-page-loader-label-richText" deltaOps={description} />
        </span>
      );
    }
    const readOnly = mode === 'OUTPUT';
    const formField = field?.preview({
      record,
      formDs: ds,
      viewDataSet,
      disabled: mode === 'DISABLED' || mode === 'READONLY' || undefined, // 如果mode为只读，则表单字段全部只读
      help,
      vertical: verticalFlag,
      placeholder: placeHolder,
      label,
      parentKey: fullParentKey,
      intl,
      autoFocus: mainStore.getFocus === code && !autoFocusDisabled,
      tenantId,
      context,
      pageRef,
      surveyFlag,
      readOnly,
      AppState,
    });

    if (widgetType === 'MasterDetail' && (informationViewId || relationLovAddViewId)) {
      const fieldName = fullParentKey ? `${fullParentKey}.${record?.get('code')}` : record?.get('code');
      const fieldValue = ds?.current?.get(fieldName)?.id || ds?.current?.get(fieldName);
      const view = {
        viewSize: 800,
        openType: 'MIDDLE',
        viewId: informationViewId,
        viewName: informationViewName,
        name: intl.formatMessage({ id: 'lcr.components.desc.preview.view', defaultMessage: '预览视图' }),
      };

      return (
        <span
          name={fieldName}
          className={classnames(styles.moreInfo, {
            [styles.hasQuickAdd]: relationLovAddViewId,
            [styles.hasInfoView]: informationViewId && fieldValue,
          })}
        >
          {formField}
          {openView(view, fieldValue)}
          {relationLovAddViewId ? (
            <Icon
              type="Plus"
              className={classnames(styles.moreInfoIcon, {
                [styles.hasInfo]: informationViewId && fieldValue,
              })}
              onClick={() => openAddView(relationLovAddViewId, relationLovAddViewName, record)}
            />
          ) : null}
        </span>
      );
    }
    // 定时任务类型 服务型视图每个字段都支持js表达式。
    if (scheduleFlag) {
      const fieldName = fullParentKey ? `${fullParentKey}.${record?.get('code')}` : record?.get('code');
      return (
        <div
          name={fieldName}
          className="lc-schedule-info-wrapper"
        >
          {formField}
          <YqCodeMirror
            name={`${fieldName}:quartzExpression`}
            mode="button"
            record={ds?.current}
            viewId={viewDataSet?.current?.get('id')}
            businessObjectId={scheduleItembusinessObjectId}
            readOnly={mode === 'DISABLED'}
          />
        </div>
      );
    }
    return formField;
  }

  function getLabelWidth() {
    const result = layout?.get('labelWidth') || 100;
    // eslint-disable-next-line no-nested-ternary
    return result === 'auto' ? { minWidth: 100, maxWidth: 140 } : result === 'responsive' ? 'auto' : Number(result);
    // return result === 'auto'
    //   ? [100, 120, 140]
    //   : Number(result);
  }

  return (
    <div
      className="lc-form-page-loader-column"
      style={{
        flex,
        overflow: overflowDisplay || '',
      }}
    >
      {formFlag
        ? (
          <Form
            key={layout.get('id')}
            labelWidth={getLabelWidth()}
            labelLayout={surveyFlag ? 'vertical' : 'horizontal'}
            record={ds?.current}
            excludeUseColonTagList={['button', 'Button']}
          >
            {records?.map(renderField)}
          </Form>
        )
        : records?.map(renderPreview)}
    </div>
  );
});
