export default ({ intl, tenantId, type, viewId, permissionOptionDs, personId, publicSurveyFlag }) => {
  const url = `lc/v1/${type === 'site' ? '' : `${tenantId}/`}formFilters`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const icon = intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' });
  const defaultFilter = intl.formatMessage({ id: 'lcr.components.model.default.table.filter', defaultMessage: '默认筛选器' });
  const visibleRange = intl.formatMessage({ id: 'lcr.components.model.visible.range', defaultMessage: '可见范围' });
  const permissionType = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const specificGroup = intl.formatMessage({ id: 'lcr.components.model.specific.group', defaultMessage: '特定组' });

  return {
    autoQuery: false,
    paging: false,
    selection: true,
    primaryKey: 'id',
    transport: {
      create: ({ data: [data] }) => ({
        url: `${url}/createBySelf`,
        method: 'post',
        data: {
          ...data,
          userId: data.type === 'SELF' ? personId : undefined,
          groupId: data.type === 'SELF' ? undefined : data.groupId,
        },
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: name, required: true, isIntl: true },
      { name: 'icon', type: 'string', label: icon, required: true },
      { name: 'defaultFlag', type: 'boolean', label: defaultFilter },
      { name: 'condition', type: 'string' },
      { name: 'orderBy', type: 'string' },
      { name: 'visibleRange', type: 'string', label: visibleRange },
      { name: 'viewId', type: 'string', defaultValue: viewId },
      {
        name: 'type',
        type: 'string',
        options: permissionOptionDs,
        label: permissionType,
        defaultValue: 'SELF',
      },
      publicSurveyFlag ? { name: 'groupId', type: 'object', label: specificGroup } : {
        name: 'groupId',
        type: 'object',
        label: specificGroup,
        lovCode: 'MY_GROUP',
        textField: 'name',
        valueField: 'code',
        lovPara: {
          self: personId,
        },
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (!value) {
            return undefined;
          }
          return {
            code: value,
            name: data.groupName,
          };
        },
      },
    ],
  };
};
