export default ({ tenantId, viewId, dataSetId }) => {
  const url = `/lc/v1/engine/${tenantId}/dataset/${viewId}/${dataSetId}`;
  return {
    autoQuery: false,
    selection: false,
    autoLocateFirst: true,
    paging: false,
    transport: {
      read: ({ data }) => ({
        url: `${url}/query`,
        method: 'post',
        data,
      }),
      create: ({ data }) => ({
        url: `${url}/submit`,
        method: 'post',
        data,
      }),
      update: ({ data }) => ({
        url: `${url}/submit`,
        method: 'post',
        data,
      }),
      destroy: ({ data }) => ({
        url: `${url}/submit`,
        method: 'post',
        data,
      }),
    },
  };
};
