export default ({ tenantId, type, viewId }) => {
  const url = `lc/v1/${type === 'site' ? '' : `${tenantId}/`}column`;

  return {
    autoQuery: false,
    paging: false,
    pageSize: 10,
    selection: true,
    primaryKey: 'id',
    fields: [
      {
        name: 'widgetConfig',
        type: 'string',
        transformResponse: (value, data) => {
          if (value) {
            try {
              return JSON.parse(value);
            } catch (e) {
              return value;
            }
          }
          return value;
        },
      },
    ],
    transport: {
      // read: {
      //   url: `${url}/view/list/${viewId}`,
      //   method: 'get',
      // },
    },
  };
};
