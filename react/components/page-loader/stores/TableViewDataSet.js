export default ({ intl, tenantId, type, viewId, columnDataSet, permissionOptionDs, personId, publicSurveyFlag }) => {
  const url = `lc/v1/${type === 'site' ? '' : `${tenantId}/`}formPersonalColumns`;
  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const field = intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' });
  const defaultView = intl.formatMessage({ id: 'lcr.components.model.default.view', defaultMessage: '默认视图' });
  const permissionType = intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' });
  const specificGroup = intl.formatMessage({ id: 'lcr.components.model.specific.group', defaultMessage: '特定组' });

  return {
    autoQuery: false,
    paging: true,
    pageSize: 999,
    selection: true,
    primaryKey: 'id',
    transport: {
      // read: {
      //   url: `${url}/queryBySelf/${viewId}`,
      //   method: 'get',
      // },
      create: ({ data: [data] }) => ({
        url: `${url}/createBySelf`,
        method: 'post',
        data: {
          ...data,
          code: data.basicId ? null : data.code,
          userId: data.type === 'SELF' ? personId : undefined,
          groupId: data.type === 'SELF' ? undefined : data.groupId,
        },
      }),
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'name', type: 'string', label: name, required: true, isIntl: true },
      { name: 'defaultFlag', type: 'boolean', label: defaultView },
      { name: 'columns', type: 'string', label: field },
      { name: 'viewId', type: 'string', defaultValue: viewId },
      { name: 'businessObjectId', type: 'string' },
      {
        name: 'jsonData',
        type: 'string',
        options: columnDataSet,
        textField: 'label',
        valueField: 'objectFieldPath',
        transformRequest: (value, record) => JSON.stringify(value),
        transformResponse: (value, record) => {
          try {
            return JSON.parse(value);
          } catch (e) {
            return [];
          }
        },
      },
      {
        name: 'type',
        type: 'string',
        options: permissionOptionDs,
        label: permissionType,
        defaultValue: 'SELF',
      },
      publicSurveyFlag ? { name: 'groupId', type: 'object', label: specificGroup } : {
        name: 'groupId',
        type: 'object',
        label: specificGroup,
        lovCode: 'MY_GROUP',
        textField: 'name',
        valueField: 'code',
        lovPara: {
          self: personId,
        },
        transformRequest: (value) => {
          return value?.id;
        },
        transformResponse: (value, data) => {
          if (!value) {
            return undefined;
          }
          return {
            code: value,
            name: data.groupName,
          };
        },
      },
    ],
  };
};
