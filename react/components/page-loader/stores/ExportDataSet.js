import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, tenantId, type, viewId, personId, tableFilterDataSet, tableViewDataSet }) => {
  const url = `lc/v1/${type === 'site' ? '' : `${tenantId}/`}formFilters`;
  // const help = intl.formatMessage({ id: 'lcr.components.model.export.help.background.tips', defaultMessage: '导出数据超出1000条\导出计算字段时，由于数据计算量较大，仅能进行后台导出。' });
  const methodsOptionDs = new DataSet({
    paging: false,
    data: [
      { meaning: intl.formatMessage({ id: 'lcr.components.model.direct.export', defaultMessage: '直接导出' }), value: false },
      { meaning: intl.formatMessage({ id: 'lcr.components.model.background.export', defaultMessage: '后台导出' }), value: true },
      // {
      //   meaning: (<span style={{ display: 'flex', alignItems: 'center' }}>{intl.formatMessage({ id: 'lcr.components.model.background.export', defaultMessage: '后台导出' })}
      //     <Tooltip title={help}>
      //       <IconPro className="help" type="help_outline" />
      //     </Tooltip>
      //   </span>),
      //   value: true },
    ],
  });
  const exportMethod = intl.formatMessage({ id: 'lcr.components.model.export.method', defaultMessage: '导出方式' });
  const exportTotalCount = intl.formatMessage({ id: 'lcr.components.model.export.total.count', defaultMessage: '数据条数' });
  const filter = intl.formatMessage({ id: 'lcr.components.model.filter', defaultMessage: '筛选' });
  const listView = intl.formatMessage({ id: 'lcr.components.model.list.view', defaultMessage: '列表视图' });

  return {
    autoQuery: false,
    paging: false,
    selection: true,
    primaryKey: 'id',
    transport: {
      update: ({ data: [data] }) => ({
        url,
        method: 'put',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    fields: [
      { name: 'condition', type: 'string' },
      { name: 'type', type: 'string' },
      { name: 'orderBy', type: 'string' },
      { name: 'objectId', type: 'string' },
      {
        name: 'basicFilterId',
        type: 'string',
        valueField: 'id',
        textField: 'name',
        label: filter,
        options: tableFilterDataSet,
      },
      {
        name: 'basicViewId',
        type: 'string',
        valueField: 'id',
        textField: 'name',
        label: listView,
        options: tableViewDataSet,
      },
      {
        name: 'totalCount',
        type: 'string',
        label: exportTotalCount,
        ignore: 'always',
      },
      {
        name: 'asyncFlag',
        type: 'boolean',
        label: exportMethod,
        defaultValue: false,
        dynamicProps: {
          options: ({ record }) => {
            return new DataSet({
              paging: false,
              data: [
                { meaning: record?.getState('fromImportFlag') ? intl.formatMessage({ id: 'lcr.components.export.direct.download', defaultMessage: '直接下载' }) : intl.formatMessage({ id: 'lcr.components.export.direct.export', defaultMessage: '直接导出' }), value: false },
                { meaning: record?.getState('fromImportFlag') ? intl.formatMessage({ id: 'lcr.components.export.background.download', defaultMessage: '后台下载' }) : intl.formatMessage({ id: 'lcr.components.export.background.export', defaultMessage: '后台导出' }), value: true },
              ],
            });
          },
        },
        computedProps: {
          disabled: ({ record }) => {
            return record.get('totalCount') > 1000 || record.getState('calculatedFields')?.length > 0;
          },
        },
      },
      {
        name: 'fields',
        multiple: true,
      },
    ],
    events: {
      update: ({ record, name, value }) => {
        if (name === 'totalCount') {
          if (value > 1000 || record.getState('calculatedFields')?.length > 0) {
            record.set('asyncFlag', true);
          } else {
            record.set('asyncFlag', false);
          }
        }
      },
    },
  };
};
