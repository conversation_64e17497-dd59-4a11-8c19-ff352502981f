import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, tableFilterDataSet, tableViewDataSet }) => {
  const newMethod = intl.formatMessage({ id: 'lcr.components.model.new.method', defaultMessage: '新建方式' });
  const filter = intl.formatMessage({ id: 'lcr.components.model.filter', defaultMessage: '筛选' });
  const listView = intl.formatMessage({ id: 'lcr.components.model.list.view', defaultMessage: '列表视图' });

  const filterOptionDs = new DataSet({
    paging: false,
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'NEW' },
      { meaning: intl.formatMessage({ id: 'lcr.components.model.basic.filter.create', defaultMessage: '基于已有筛选器新建' }), value: 'BASIC' },
    ],
  });

  const viewOptionDs = new DataSet({
    paging: false,
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'NEW' },
      { meaning: intl.formatMessage({ id: 'lcr.components.model.basic.view.create', defaultMessage: '基于已有列表视图新建' }), value: 'BASIC' },
    ],
  });

  return {
    fields: [
      { name: 'type', type: 'string' },
      {
        name: 'newMethod',
        type: 'string',
        label: newMethod,
        defaultValue: 'NEW',
        dynamicProps: {
          options: ({ record }) => (record.get('type') === 'FILTER' ? filterOptionDs : viewOptionDs),
        },
      },
      {
        name: 'basicId',
        type: 'string',
        valueField: 'id',
        textField: 'name',
        dynamicProps: {
          label: ({ record }) => (record.get('type') === 'FILTER' ? filter : listView),
          options: ({ record }) => (record.get('type') === 'FILTER' ? tableFilterDataSet : tableViewDataSet),
          required: ({ record }) => record.get('newMethod') !== 'NEW',
        },
      },
    ],
  };
};
