import {
  getDefaultSection,
  transformCondition,
  transformRule,
  transformJsonData,
} from '../utils';

export default (props) => {
  const {
    tenantId, viewId, viewCode, variableFlag, surveyFlag, publicSurveyFlag, sectionDataSet,
    buttonDataSet, dsDataSet, dsManager, viewData, pageRef, parentKey, actionDataSet,
    tableFilterDataSet, tableViewDataSet, columnDataSet, AppState, viewJsonData,
    setParentPageDsFieldMap, scItemViewFlag, scItemViewType, scItemId, udmFlag, viewDataSetCreated,
  } = props;
  const url = `/lc/v1/${tenantId}/views`;

  function getQueryUrl() {
    // 普通对象视图展示服务项视图
    if (scItemViewFlag) {
      return `${url}/formServiceItem/${scItemId}?type=${scItemViewType}`;
    }
    // 公开调查调用公开接口
    if (publicSurveyFlag) {
      return `/asmt/v1/${tenantId}/assessments/${viewId}/website`;
    }
    if (surveyFlag) {
      return `/asmt/v1/${tenantId}/assessments/${viewId}`;
    }
    return `${url}/form/${variableFlag ? 'variable/' : ''}${viewId || `code/${viewCode}`}${variableFlag ? '?objectType=ITEM' : ''}`;
  }

  return {
    dataToJSON: 'normal',
    autoQuery: !viewData && !viewJsonData && (viewId || viewCode || scItemViewFlag),
    selection: false,
    paging: false,
    data: viewData || viewJsonData,
    autoQueryAfterSubmit: false,
    transport: {
      read: () => ({
        url: getQueryUrl(),
        method: 'get',
        transformResponse: (data) => {
          const originData = JSON.parse(data);
          if (!originData.jsonData) {
            return {
              ...originData,
              jsonData: {
                buttons: [],
                sections: [getDefaultSection()],
                datasets: [{
                  id: originData.id,
                  businessObjectId: originData.businessObjectId,
                  tag: 'Form',
                  fields: [],
                  filters: [],
                }],
              },
            };
          }
          const jsonData = {
            ...originData,
            jsonData: transformJsonData(JSON.parse(originData.jsonData || '{}')),
          };
          AppState.setCustomConfig(`view-${tenantId}-${viewId || `${scItemId + scItemViewType}`}-${viewCode}`, JSON.stringify([jsonData]));
          return jsonData;
        },
      }),
    },
    children: {
      'jsonData.sections': sectionDataSet,
      'jsonData.buttons': buttonDataSet,
      'jsonData.datasets': dsDataSet,
      'jsonData.actions': actionDataSet,
      columns: columnDataSet,
      personalColumns: tableViewDataSet,
    },
    events: {
      load({ dataSet: { current } }) {
        if (current) {
          const jsonData = current.get('jsonData');
          const policies = current.get('policies');
          const rules = current.get('validationRules');
          const viewType = current.get('viewType');
          const columns = current.get('columns');
          // 表格视图加载筛选器和视图
          if (current.get('viewType') === 'TABLE') {
            // columnDataSet.query();
            // tableViewDataSet.query();
            tableFilterDataSet.loadData(current.get('filters') || []);
          }
          const policiesData = transformCondition(policies, parentKey);
          const rulesData = transformRule(rules, parentKey);
          current.setState('policiesData', policiesData);
          current.setState('rulesData', rulesData);
          dsManager.init(dsDataSet, jsonData, current.get('id'), policiesData, rulesData, viewType, columns);
          if (pageRef?.current) {
            pageRef.current.pageData = {
              id: current?.get('id'),
              businessObjectId: current?.get('businessObjectId'),
            };
            pageRef.current.pageRecord = current;
          } else if (pageRef) {
            pageRef.current = {
              pageData: {
                id: current?.get('id'),
                businessObjectId: current?.get('businessObjectId'),
              },
              pageRecord: current,
            };
          }
          if (setParentPageDsFieldMap) {
            // 页面视图需要将字段返给主视图，供UIAction使用
            const formDs = jsonData.datasets.find(ds => ds.tag === 'Form');
            if (formDs) {
              setParentPageDsFieldMap(current.get('id'), formDs.fields || []);
            }
          }

          if (typeof viewDataSetCreated === 'function') {
            viewDataSetCreated(current);
          }
        }
      },
    },
  };
};
