import moment from 'moment';
import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId, viewId }) => {
  const url = `/data/v1/${tenantId}/dataTasks`;
  const file = intl.formatMessage({ id: 'lcr.components.model.download.history.file', defaultMessage: '文件' });
  const size = intl.formatMessage({ id: 'lcr.components.model.download.history.size', defaultMessage: '文件大小' });
  const createdBy = intl.formatMessage({ id: 'zknow.common.model.createdBy', defaultMessage: '创建人' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const createdDate = intl.formatMessage({ id: 'lcr.components.model.download.history.export.date', defaultMessage: '导出时间' });
  const percent = intl.formatMessage({ id: 'lcr.components.model.download.history.export.percent', defaultMessage: '导出进度' });
  const momentFormatStart = 'YYYY-MM-DD 00:00:00';
  const momentFormatEnd = 'YYYY-MM-DD 23:59:59';
  return {
    selection: false,
    transport: {
      read: ({ data }) => ({
        url: `${url}?viewId=${viewId}`,
        method: 'get',
        data: getQueryParams(data),
      }),
    },
    fields: [
      {
        name: 'file',
        type: 'string',
        label: file,
      },
      {
        name: 'fileSize',
        type: 'string',
        label: size,
      },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        lookupCode: 'DATA_TASK_STATUS',
      },
      {
        name: 'percent',
        type: 'string',
        label: percent,
      },
      {
        name: 'creationDate',
        type: 'string',
        label: createdDate,
      },
    ],
    queryFields: [
      {
        name: 'file',
        type: 'string',
        label: file,
      },
      // {
      //   name: 'fileSize',
      //   type: 'string',
      //   label: size,
      // },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        lookupCode: 'DATA_TASK_STATUS',
      },
      {
        name: 'creationDate',
        type: 'date',
        range: ['start', 'end'],
        format: 'YYYY-MM-DD',
        label: createdDate,
      },
    ],
    events: {
      query: ({ params }) => {
        if (params.search_creationDate) {
          if (params.search_creationDate.start) {
            params.search_startCreationDate = moment(params.search_creationDate.start).format(momentFormatStart);
          }
          params.search_endCreationDate = moment(params.search_creationDate.end).format(momentFormatEnd);
          delete params.search_creationDate;
        }
      },
    },
  };
};
