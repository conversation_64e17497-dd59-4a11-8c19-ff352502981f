import { DataSet } from 'choerodon-ui/pro';

export const TemplateOptionDs = ({ tenantId, viewDataSet, dataId }) => {
  return {
    paging: false,
    autoQuery: false, // 每次点击下拉都会查询，这里关闭自动查询
    transport: {
      read: ({ dataSet }) => {
        if (!viewDataSet.current) return;
        return {
          url: `report/v1/${tenantId}/report-print/template/available?businessObjectId=${viewDataSet.current.get('businessObjectId')}&dataId=${dataId || dataSet.getState('selectedDataIds')}`,
          method: 'get',
          transformResponse: (resp) => {
            let newData = [];
            try {
              newData = JSON.parse(resp);
            } catch (e) {
              newData = [];
            }
            return newData;
          },
        };
      },
    },
    fields: [
      { name: 'listFlag',
        transformResponse: (value, object) => /<yq-template-table[\s\S]*?<\/yq-template-table>/.test(object.content),
      },
    ],
  };
};

export default ({ intl, templateOptionDs }) => {
  const printTemplateLabel = intl.formatMessage({ id: 'lcr.components.model.ticket.transfer.print.template', defaultMessage: '打印模板' });
  const printTypeLabel = intl.formatMessage({ id: 'lcr.components.model.print.template.type', defaultMessage: '打印方式' });
  return {
    autoCreate: true,
    fields: [
      {
        required: true,
        name: 'templateId',
        type: 'string',
        valueField: 'templateId',
        textField: 'templateName',
        label: printTemplateLabel,
        options: templateOptionDs,
      },
      {
        name: 'printType',
        type: 'string',
        defaultValue: 'derict',
        options: new DataSet({ 
          data: [{ meaning: intl.formatMessage({ id: 'lcr.components.model.print.template.type.derict', defaultMessage: '直接打印' }), value: 'derict' }, { meaning: intl.formatMessage({ id: 'lcr.components.model.print.template.type.back', defaultMessage: '后台打印' }), value: 'back' }],
          paging: false,
        }),
        label: printTypeLabel,
      },
    ],
  };
};
