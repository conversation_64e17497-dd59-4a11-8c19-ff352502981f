/* eslint-disable no-useless-escape */
/* eslint-disable camelcase */
import { DataSet, message } from 'choerodon-ui/pro';
import moment from 'moment';
import difference from 'lodash/difference';
import merge from 'lodash/merge';
import isObject from 'lodash/isObject';
import { getEnv, getQueryParams } from '@zknow/utils';
import axios from 'axios';
import { toJS } from 'mobx';
import { SelectTag } from '@zknow/components';
import {
  ACTION, OPERATOR, calculateConditions, getFieldMap, getExpressionPage,
  getCascadeMap, RULE_TYPE, calculateExpression, executeExpression,
  getWidgetData, safeBoolean,
} from '../utils';
import { durToSec, durToDays, deltaToHtmlStr, getRichTextIsNull, submitRequestContent } from '@/utils';
import { getPageViewId } from '@/service';
import { transformResponse } from '../lovConfig';

const { TagDataSet } = SelectTag;

const MAP = Symbol('map');

const NO_SEARCH_PREFIX = ['__condition', '__orderBy'];

const DATE_FIELDS = ['DateTime', 'Date', 'Time'];
const OPTION_FIELDS = ['MultipleSelect', 'Select', 'SelectBox', 'Radio', 'Tag', 'Cascader'];
const MULTIPLE_FIELDS = ['MultipleSelect', 'SelectBox'];
const BOOLEAN_FIELDS = ['Switch', 'CheckBox'];

// 创建时间和更新时间还是会用到的
export const FILTER_FIELD_CODES = [
  'id',
  'created_by',
  // 'creation_date',
  // 'last_update_date',
  'last_updated_by',
  'tenant_id',
  '__dirty',
  'domain_id',
  'domain_path',
];

const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

const RICH_TEXT_NULL_LIST = [
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\\n\"}]',
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\n\"}]',
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\"}]',
  '[]',
  'null',
  '\u200b',
  null,
];

const DATE_TYPE_MAP = {
  DateTime: 'dateTime',
  Date: 'date',
  Time: 'time',
};

const WIDGET_TYPE_MAP = {
  ...DATE_TYPE_MAP,
  Switch: 'boolean',
  CheckBox: 'boolean',
  EmailField: 'email',
  Url: 'string', // url类型的正则无法覆盖
  MasterDetail: 'object',
  Region: 'object',
  NumberField: 'number',
  FloatNumber: 'number',
  Rate: 'number',
  VariableSet: 'object',
  MobileField: 'number',
  Like: 'boolean',
  Input: 'string',
  Currency: 'currency',
};

const mobileReg = /^1\d{10}$/;
const urlReg = /^(?:(?:https?|HTTPS?|ftp|FTP):\/\/)?(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-zA-Z\u00a1-\uffff0-9]-*)*[a-zA-Z\u00a1-\uffff0-9]+)(?:\.(?:[a-zA-Z\u00a1-\uffff0-9]-*)*[a-zA-Z\u00a1-\uffff0-9]+)*)(?::\d{2,5})?(?:[\/?#]\S*)?$/;

export const SEARCH_TYPE_MAP = {
  ...DATE_TYPE_MAP,
  MasterDetail: 'object',
};

function getTypeByWidgetType(widgetType) {
  return WIDGET_TYPE_MAP[widgetType] || 'auto';
}

function getFieldLimit(valueType, value, expression, fieldCode, dateLimitCode, fieldProps) {
  const { record, funcConfig, widgetType } = fieldProps;
  const dateFlag = DATE_FIELDS.includes(widgetType);

  if (valueType === 'FIXED_VALUE' && value !== undefined && value !== '') {
    if (dateFlag) {
      return value ? moment(value) : undefined;
    }
    return value;
  } else if (valueType === 'VARIABLE' && expression) {
    if (dateFlag) {
      if (expression) {
        const date = executeExpression({ expression }, record, funcConfig);
        return moment.isDate(date) ? moment(date) : undefined;
      }
    }
    return executeExpression({ expression }, record, funcConfig);
  } else if (valueType === 'FIELD' && fieldCode && record.get(fieldCode)) {
    return fieldCode;
  }
  return undefined;
}

/**
 * 处理字段默认值
 * @param fieldDto
 * @param expressionDefaultValue
 */
function handleExpressionDefaultValue(fieldDto, expressionDefaultValue) {
  // 执行表达式默认值
  if (expressionDefaultValue) {
    const { widgetType, code } = fieldDto || {};
    const { relationLovNameFieldCode = 'name', idField = 'id', defaultValueType } = fieldDto?.widgetConfig || {};
    if (expressionDefaultValue[code]) {
      // 对于多对一字段，需要将名称字段也设置默认值
      if (widgetType === 'MasterDetail' && expressionDefaultValue[code]) {
        if (typeof expressionDefaultValue[code] === 'object') {
          // 如果默认值为对象，如 { idField: 1, nameField: 'a' }
          fieldDto.defaultValue = expressionDefaultValue[code][idField];
          fieldDto.widgetConfig.defaultValueName = expressionDefaultValue[code][relationLovNameFieldCode];
        } else {
          // 如果默认值为id，则尝试从默认值获取名称字段fieldCode:nameFieldCode
          fieldDto.defaultValue = expressionDefaultValue[code];
          if (expressionDefaultValue[`${code}:${relationLovNameFieldCode}`]) {
            fieldDto.widgetConfig.defaultValueName = expressionDefaultValue[`${code}:${relationLovNameFieldCode}`];
          }
        }
      } else if (MULTIPLE_FIELDS.includes(widgetType)) {
        // 对于多选字段，转换为逗号分割的字符串
        const value = expressionDefaultValue[code];
        try {
          const jsonValue = JSON.parse(value);
          if (Array.isArray(jsonValue)) {
            fieldDto.defaultValue = jsonValue.join(',');
          } else {
            fieldDto.defaultValue = value;
          }
        } catch (e) {
          fieldDto.defaultValue = value;
        }
      } else {
        fieldDto.defaultValue = expressionDefaultValue[code];
      }
    } else if (widgetType === 'Input' && code.includes(':') && !expressionDefaultValue[code]) {
      // 多对一字段默认值为对象时，需要给名称字段也赋一下默认值，用于展示
      // 尝试对名称字段进行截断，从默认值中获取对应的多对一字段，如：
      // user_id: { idField: 1, nameField: 'a' } => user_id:nameField = user_id.nameField
      const fieldCodeList = code.split(':');
      const nameFieldCode = fieldCodeList.pop();
      const originFieldCode = fieldCodeList.join(':');
      if (expressionDefaultValue[originFieldCode] && expressionDefaultValue[originFieldCode][nameFieldCode]) {
        fieldDto.defaultValue = expressionDefaultValue[originFieldCode][nameFieldCode];
      }
    }
  }
}

/**
 * 根据字段类型，转换为DataSet Field
 * @param props
 * @returns fields
 */
export function transformField(props) {
  const {
    field, viewId, tenantId, parentKey, policiesData, rulesData, ignoreFields = [],
    intl, expressionDefaultValue, funcConfig, fieldCodeMap, isTable, tableInlineEdit, publicSurveyFlag,
  } = props;
  // 执行js
  handleExpressionDefaultValue(field, expressionDefaultValue);
  const {
    name, code, widgetType, defaultValue, maxLength, requiredType,
    editType, relationLovId, tlFlag, relationObjectId, width, placeHolder, help,
  } = field;
  const {
    dataSource, options = [], lookupCode, currentTimeFlag, format, relationLovNameFieldCode = 'name',
    defaultValueName, requiredAction, requiredCondition, editAction, editCondition, variableFilter,
    decimalLength, integerLength, condition, relationLovBusinessObjectId, regularRule, regularRuleMessage,
    durationMode, durationUnit, maxExpression, minExpression, minValue, minValueType,
    maxValue, maxValueType, startFieldCode, endFieldCode, minFieldCode, maxFieldCode,
    tagGroupId, tagMultiFlag, tagViewType, autoTransform, showFullPath, cascadeFields = [],
    onlyLeafFlag = false, cascadeFlag, cascadeParentField, queryLimitFlag, displayMethod
  } = field.widgetConfig || {};
  const fieldCode = parentKey ? `${parentKey}.${code}` : code;

  // 业务对象校验规则
  // TODO: 可以把这个函数拆出去
  const validator = async (value, validateFieldCode, record) => {
    if ((value || value === 0) && rulesData && (value !== record.getPristineValue(validateFieldCode))) {
      // 唯一性校验
      const uniqueRule = rulesData?.uniqueMap[fieldCode];
      if (uniqueRule) {
        const { unique, errorMessage } = uniqueRule;
        const uniqueMapList = [];
        unique.map(items => {
          let validateFlag = true;
          const uniqueMap = {};
          items.map(item => {
            const itemValue = record.get(item);
            // 当字段未填时，不进行校验
            if (itemValue === undefined) {
              validateFlag = false;
            }
            uniqueMap[item] = itemValue?.id || itemValue;
            return item;
          });
          if (validateFlag) {
            uniqueMapList.push(uniqueMap);
          }
          return items;
        });
        if (uniqueMapList.length) {
          const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/validate`, {
            unique: uniqueMapList,
          });
          if (res?.failed || !res?.length || res?.find(item => item === false) === false) {
            if (Object.keys(uniqueMapList[0])?.length === 1) {
              message.error(errorMessage);
            }
            return errorMessage;
          }
        }
      }
      // 正则校验
      const regexpRule = rulesData?.regexpMap[fieldCode];
      if (regexpRule) {
        const { regexp, errorMessage } = regexpRule;
        let regexpStr = regexp;
        if (regexpStr?.startsWith('/')) {
          regexpStr = regexpStr.substr(1);
        }
        if (regexpStr?.endsWith('/')) {
          regexpStr = regexpStr.substr(0, regexpStr.length - 1);
        }
        const fieldRegexp = new RegExp(regexpStr);
        if (!fieldRegexp.test(value)) {
          message.error(errorMessage);
          return errorMessage;
        }
      }
      // 长度校验
      const maxLengthRule = rulesData?.maxLengthMap[fieldCode];
      if (maxLengthRule) {
        const { maxLength: validateMaxLength, errorMessage } = maxLengthRule;
        if (validateMaxLength && value.length > validateMaxLength) {
          message.error(errorMessage);
          return errorMessage;
        }
      }
      // 等于校验
      const equalsRule = rulesData?.equalsMap[fieldCode];
      if (equalsRule) {
        const { ruleType, value: validateValue, connectField, errorMessage } = equalsRule;
        if (ruleType === RULE_TYPE.FIXED && value !== validateValue) {
          message.error(errorMessage);
          return errorMessage;
        }
        if (ruleType === RULE_TYPE.ASSOCIATION) {
          if (DATE_FIELDS.includes(widgetType)) {
            if (!value?.isSame(record?.get(connectField))) {
              message.error(errorMessage);
              return errorMessage;
            }
          } else if (value !== record.get(connectField)) {
            message.error(errorMessage);
            return errorMessage;
          }
        }
      }
      // 范围校验-最大值
      const maxRangeRule = rulesData?.maxRangeMap[fieldCode];
      if (maxRangeRule) {
        const { ruleType, value: validateValue, connectField, errorMessage } = maxRangeRule;
        if (ruleType === RULE_TYPE.FIXED && value > validateValue) {
          message.error(errorMessage);
          return errorMessage;
        }
        if (ruleType === RULE_TYPE.ASSOCIATION) {
          if (DATE_FIELDS.includes(widgetType)) {
            if (record?.get(connectField) && value?.isAfter(record?.get(connectField))) {
              message.error(errorMessage);
              return errorMessage;
            }
          } else if (value > record.get(connectField)) {
            message.error(errorMessage);
            return errorMessage;
          }
        }
      }
      // 范围校验-最小值
      const minRangeRule = rulesData?.minRangeMap[fieldCode];
      if (minRangeRule) {
        const { ruleType, value: validateValue, connectField, errorMessage } = minRangeRule;
        if (ruleType === RULE_TYPE.FIXED && value < validateValue) {
          message.error(errorMessage);
          return errorMessage;
        }
        if (ruleType === RULE_TYPE.ASSOCIATION) {
          if (DATE_FIELDS.includes(widgetType)) {
            if (record?.get(connectField) && value?.isBefore(record?.get(connectField))) {
              message.error(errorMessage);
              return errorMessage;
            }
          } else if (value < record.get(connectField)) {
            message.error(errorMessage);
            return errorMessage;
          }
        }
      }
      return true;
    }
    return true;
  };

  // 默认配置
  const presetProps = {
    name: fieldCode,
    label: name,
    type: getTypeByWidgetType(widgetType),
    defaultValue,
    isIntl: !!tlFlag && fieldCode.indexOf(':') === -1,
    idField: 'id',
    parentField: 'parentId',
    placeholder: placeHolder,
    help,
    dynamicProps: {
      required: !tableInlineEdit ? false : ({ dataSet, record, name: fieldName }) => {
        let newField = dataSet?.getState?.('fieldMap')?.get?.(field.id) || field;
        const sameFieldId = dataSet.getState(`sameFieldId${fieldName}`);
        const sameFieldCode = dataSet.getState(`sameFieldCode${fieldName}`);
        if (sameFieldId && sameFieldCode && fieldName === sameFieldCode) {
          newField = dataSet?.getState?.('fieldMap')?.get?.(sameFieldId);
        }
        let required = newField?.requiredType === 'ALWAYS_REQUIRED';
        const _requiredAction = newField?.widgetConfig?.requiredAction;
        const _requiredCondition = newField?.widgetConfig?.requiredCondition;
        // 增加字段UI规则判断
        const fieldAction = policiesData?.actionMap[fieldName];
        // 有UI规则 或者 配置了条件
        if (fieldAction || newField?.requiredType === 'CONDITION') {
          const requiredIds = fieldAction && fieldAction[ACTION.REQUIRED];
          // 必填校验
          if (requiredIds || (newField?.requiredType === 'CONDITION' && _requiredAction === ACTION.REQUIRED)) {
            required = calculateConditions(
              parentKey,
              policiesData.conditionMap,
              requiredIds,
              record,
              newField?.requiredType === 'CONDITION' ? _requiredCondition : null,
              funcConfig,
              '',
              true,
            );
          }
          const notRequiredIds = fieldAction && fieldAction[ACTION.Not_REQUIRED];
          if (notRequiredIds || (newField?.requiredType === 'CONDITION' && _requiredAction === ACTION.Not_REQUIRED && _requiredCondition?.length)) {
            required = !calculateConditions(
              parentKey,
              policiesData.conditionMap,
              notRequiredIds,
              record,
              newField?.requiredType === 'CONDITION' ? _requiredCondition : null,
              funcConfig,
              '',
              true,
            );
          }
        }
        return required;
      },
      disabled: ({ dataSet, record, name: fieldName }) => {
        const newField = dataSet?.getState?.('fieldMap')?.get?.(field.id) || field;
        let disabled = newField.editType === 'ALWAYS_NOT_EDIT';
        const _editAction = newField?.widgetConfig?.editAction;
        const _editCondition = newField?.widgetConfig?.editCondition;
        // 增加字段UI规则判断
        const fieldAction = policiesData?.actionMap[fieldName];
        if (fieldAction || newField.editType === 'CONDITION') {
          const readOnlyIds = fieldAction && fieldAction[ACTION.READONLY];
          if (readOnlyIds || (newField.editType === 'CONDITION' && _editAction === ACTION.READONLY)) {
            disabled = calculateConditions(
              parentKey,
              policiesData?.conditionMap,
              readOnlyIds,
              record,
              newField.editType === 'CONDITION' ? _editCondition : null,
              funcConfig,
              '',
              true,
            );
          }
          const editableIds = fieldAction && fieldAction[ACTION.EDITABLE];
          if (editableIds || (newField.editType === 'CONDITION' && _editAction === ACTION.EDITABLE && _editCondition?.length)) {
            disabled = !calculateConditions(
              parentKey,
              policiesData?.conditionMap,
              editableIds,
              record,
              newField.editType === 'CONDITION' ? _editCondition : null,
              funcConfig,
              '',
              true,
            );
          }
        }
        return disabled;
      },
      max: ({ dataSet, record, name: fieldName }) => {
        const max = getFieldLimit(maxValueType, maxValue, maxExpression, maxFieldCode, endFieldCode, {
          dataSet,
          record,
          fieldName,
          widgetType,
          funcConfig,
        });
        return max === 0 ? 0 : (max || undefined);
      },
      min: ({ dataSet, record, name: fieldName }) => {
        const min = getFieldLimit(minValueType, minValue, minExpression, minFieldCode, startFieldCode, {
          dataSet,
          record,
          fieldName,
          widgetType,
          funcConfig,
        });
        return min === 0 ? 0 : (min || undefined);
      },
    },
    validator,
    defaultValidator: validator,
    width,
    ignore: ignoreFields?.includes(fieldCode) ? 'always' : undefined,
  };

  if (maxLength) {
    presetProps.maxLength = maxLength;
  }
  // 数据源为选项集
  if (dataSource === 'optionSet' && options.length) {
    presetProps.options = new DataSet({ paging: false, data: options });
  }
  // 数据源为快码
  if (OPTION_FIELDS.includes(widgetType) && dataSource === 'lookup') {
    presetProps.lookupCode = lookupCode;
    if (showFullPath) {
      presetProps.textField = 'path';
    }
    presetProps.lookupUrl = (luCode) => {
      if (cascadeFlag && !defaultValue && cascadeParentField) {
        return `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryValuesByParentCode`;
      }
      if (isTable || !condition || condition?.length === 0) {
        return `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryByCode/all?lookupTypeCode=${luCode}`;
      }
      return `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryCodeWithCondition?allFlag=true&lookupTypeCode=${luCode}`;
    };
    presetProps.lookupAxiosConfig = ({ record }) => {
      let url = `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryCodeWithCondition?allFlag=true&lookupTypeCode=${lookupCode}`;
      let method = 'POST';
      if (cascadeFlag && !defaultValue && cascadeParentField && record?.get(cascadeParentField)) {
        url = `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryValuesByParentCode`;
        method = 'GET';
      } else if (isTable || !condition || condition?.length === 0) {
        url = `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryByCode/all?lookupTypeCode=${lookupCode}`;
        method = 'GET';
      }
      const params = {};
      if (cascadeFlag && !defaultValue && cascadeParentField) {
        params.parentCode = record?.get(cascadeParentField);
        if (!url.includes('lookupTypeCode')) {
          params.lookupTypeCode = lookupCode;
        }
      }

      return {
        url,
        method,
        data: isTable ? undefined : {
          conditions: condition || [],
          params: record?.toData() || {},
        },
        params,
        transformResponse: [
          (data) => {
            let originData = {};
            if (typeof data === 'string') {
              try {
                originData = JSON.parse(data);
              } catch (e) {
                return data;
              }
            } else {
              originData = data;
            }
            if (originData.content) {
              return originData;
            }
            return {
              content: originData.map && originData.map(_d => ({
                ..._d,
                meaning: _d.value,
                value: _d.code,
              })),
            };
          },
        ],
      };
    };
    // 如果有公开调查Id就用公开接口
    if (publicSurveyFlag) {
      presetProps.lookupUrl = (luCode) => `/asmt/v1/${tenantId === 0 ? '' : `${tenantId}/`}assessments/query/${viewId}?lookUpCode=${luCode}`;
      presetProps.lookupAxiosConfig = () => ({
        method: 'GET',
        transformResponse: [
          (data) => {
            let originData = {};
            if (typeof data === 'string') {
              try {
                originData = JSON.parse(data);
              } catch (e) {
                return data;
              }
            } else {
              originData = data;
            }
            if (originData.content) {
              return originData;
            }
            return {
              content: originData.map && originData.map(_d => ({
                ..._d,
                meaning: _d.value,
                value: _d.code,
              })),
            };
          },
        ],
      });
    }
  }

  // 数据源为标签组
  if (dataSource === 'tagGroup' && tagGroupId) {
    presetProps.valueField = 'code';
    presetProps.textField = 'name';
    presetProps.cascadeMap = {};
    // TODO: 写死标签业务对象id
    presetProps.options = new DataSet(TagDataSet({ tenantId, tagGroupId, autoQuery: isTable, conditions: condition, businessObjectId: '297420777941364736' }));
  }

  // 根据字段类型，个性化配置
  const realFormat = format
    ?.replace('yyyy', 'YYYY')
    ?.replace('mm', 'MM')
    ?.replace('dd', 'DD')
    ?.replace(':MM', ':mm') || DEFAULT_DATE_FORMAT[widgetType];

  const fieldCodeList = code?.split(':') || [];
  fieldCodeList.pop();
  const originFieldCode = fieldCodeList.join(':');
  switch (widgetType) {
    case 'MasterDetail':
      presetProps.lovDefineAxiosConfig = lovCode => ({
        url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
        method: 'GET',
        transformResponse: data => transformResponse(
          data,
          data?.name,
          (map, f) => transformField(
            {
              fieldMap: map,
              field: f,
              viewId,
              tenantId,
              intl,
            },
          ),
          intl,
          tenantId,
        ),
      });
      presetProps.lovQueryAxiosConfig = (lovCode, lovConfig = {}, { data, params }) => {
        lovConfig.method = 'POST';
        const { parentIdField, treeFlag, idField } = lovConfig || {};
        let searchFlag = false;
        const queryParams = getQueryParams(data, ['current_params', '__page_params', parentIdField]);
        Object.keys(queryParams).forEach((v) => {
          if (v.indexOf('search_') !== -1) {
            searchFlag = true;
          }
        });
        if (treeFlag === 'Y' && parentIdField) {
          if (data[parentIdField]) {
            params.size = 999;
            params.page = 0;
          } else if (!searchFlag) {
            queryParams[parentIdField] = '0';
          }
        }
        const data_params = {
          ...(data.current_params || {}),
          ...queryParams,
          ...(params || {}),
        };
        delete data_params.current_params;

        let dealParams = params;
        if (onlyLeafFlag) {
          dealParams = {
            ...params,
            treeFlag: true,
          };
        }

        return {
          url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
          method: 'POST',
          data: {
            params: data_params,
            conditions: condition || [],
          },
          params: dealParams,
          transformResponse: (originData) => {
            try {
              const jsonData = JSON.parse(originData);
              return {
                ...jsonData,
                content: jsonData?.content?.map(item => {
                  // 由于富文本字段展示在值列表中需要转换，导致值列表赋值时，会导致样式丢失
                  // 这里缓存一下原始字段值
                  const backup = {};
                  Object.keys(item).map(key => {
                    if (
                      !FILTER_FIELD_CODES.includes(key)
                      && item[key]
                      && typeof item[key] === 'string'
                      && (item[key].includes('<p>') || item[key].includes('<table>') || item[key].includes('</figure>'))
                    ) {
                      backup[`${key}-backup`] = item[key];
                    }
                    return key;
                  });
                  if (searchFlag) {
                    // 搜索时，树形结构打平显示
                    return {
                      ...backup,
                      ...item,
                      isLeaf: true,
                      [parentIdField]: null,
                      id: item[idField] || item.id,
                      primaryKey: item.id,
                    };
                  }
                  return {
                    ...backup,
                    ...item,
                    id: item[idField] || item.id,
                    primaryKey: item.id,
                  };
                }) || [],
              };
            } catch (error) {
              return [];
            }
          },
        };
      };
      presetProps.lovCode = relationLovId;
      // TODO: 如果多对一字段未设置relationLovId，需要设置textField才能正常显示
      presetProps.textField = relationLovNameFieldCode;
      presetProps.transformRequest = (value, record) => value?.id || (typeof value === 'string' ? value : null) || null;
      presetProps.transformResponse = (value, data) => {
        if (value) {
          // 当值列表的业务对象为中间表时，需要特殊处理
          // 即配置的nameFieldCode为user_id:name。但实际接口返回为name
          let nameField = relationLovNameFieldCode;
          if (relationLovBusinessObjectId !== relationObjectId) {
            const nameFieldList = nameField?.split(':');
            if (nameFieldList?.length > 1) {
              nameFieldList.shift();
              nameField = nameFieldList.join(':');
            }
          }
          const nameFieldCode = `${code}:${nameField}`;
          // 对于变量视图，数据是从json中获取，nameFieldCode需要取原始值
          // 即配置的nameFieldCode为user_id:name。但实际json中的数据也为user_id:name
          const originNameFieldCode = `${code}:${relationLovNameFieldCode}`;
          if (typeof value === 'object') {
            return value;
          }
          if (value === '0') {
            if (code === 'domain_id') {
              data[nameFieldCode] = intl.formatMessage({ id: 'lcr.components.model.global', defaultMessage: ' 全局' });
            } else {
              data[nameFieldCode] = intl.formatMessage({ id: 'lcr.components.model.none', defaultMessage: '无' });
            }
          }
          // 对于变量视图中的变量集，后端返回的是一个string，需要转换为Json才能正常解析
          if (code.includes('.')) {
            const variableSetCode = code.split('.')[0];
            const parentFieldConfig = fieldCodeMap.get(variableSetCode);
            const variableData = parentKey ? data[parentKey] : data;
            // 判断父级字段是VariableSet类型
            if (parentFieldConfig?.widgetType === 'VariableSet' && variableData[variableSetCode]) {
              const realFieldCode = nameFieldCode?.replace(`${variableSetCode}.`, '');
              try {
                if (typeof variableData[variableSetCode] === 'string') {
                  const newData = JSON.parse(variableData[variableSetCode]);
                  return {
                    id: value,
                    [relationLovNameFieldCode]: newData[realFieldCode],
                  };
                } else {
                  return {
                    id: value,
                    [relationLovNameFieldCode]: variableData[variableSetCode][realFieldCode],
                  };
                }
              } catch (e) {
                // message.error(e);
              }
            }
          }
          return {
            id: value,
            [relationLovNameFieldCode]: parentKey && data[parentKey] ? (data[parentKey][nameFieldCode] || data[parentKey][originNameFieldCode]) : data[nameFieldCode],
          };
        }
        return undefined;
      };
      if (defaultValue) {
        presetProps.defaultValue = {
          id: defaultValue,
          [relationLovNameFieldCode]: defaultValueName,
        };
      }
      if (condition?.length || variableFilter?.length) {
        presetProps.dynamicProps.lovPara = ({ record }) => {
          const variableParams = {};
          variableFilter?.map(({ variable, relatedFieldCode }) => {
            variableParams[variable] = record.get(relatedFieldCode)?.id || record.get(relatedFieldCode);
            return variable;
          });
          return {
            __page_params: variableParams,
            current_params: record?.toData() || {},
          };
        };
      } else {
        presetProps.lovPara = {
          __page_params: {},
          current_params: {},
        };
      }
      presetProps.validator = (value, fieldName, record) => {
        let result = true;
        if (record?.getField(fieldName)?.required && intl) {
          const errorMessage = intl.formatMessage({ id: 'lcr.components.model.field.required.tip', defaultMessage: '请输入{name}' }, { name: field.name });
          const data = typeof value === 'string' ? value : value?.id;
          if (data) {
            result = true;
          } else {
            message.error(errorMessage);
            return errorMessage;
          }
        }
        if (presetProps.defaultValidator) {
          result = presetProps.defaultValidator(value, fieldName, record);
        }
        return result;
      };
      presetProps.optionsProps = (dsProps) => {
        return {
          ...dsProps,
          autoLocateFirst: !onlyLeafFlag,
          record: {
            dynamicProps: {
              disabled: (record) => (onlyLeafFlag ? record.get('readonlyFlag') : false),
            },
          },
          events: {
            query: ({ dataSet, params, data}) => {
              console.log('============data', params, data?.params);
              if (queryLimitFlag) {
                const queryParam = Object.keys(data?.params || {}).filter(key => key.startsWith('search_'));
                dataSet.pageSize = 50;
                if (queryParam.length > 0) {
                  return true;
                } else {
                  return false;
                }
              } else {
                return true;
              }
            },
          },
        };
      };
      break;
    case 'DateTime':
    case 'Date':
    case 'Time':
      // 由于choerodon-ui的日期格式比较特殊，需要单独处理
      if (realFormat) {
        presetProps.format = realFormat;
        presetProps.transformRequest = (value, record) => {
          return value ? moment(value).format(DEFAULT_DATE_FORMAT[widgetType]) : value;
        };
      }
      if (currentTimeFlag) {
        presetProps.defaultValue = moment();
        if (realFormat) {
          if (realFormat === 'HH:mm:ss' || realFormat === 'HH:mm') {
            presetProps.defaultValue = moment().format(`YYYY-MM-DD ${realFormat}`);
          } else {
            presetProps.defaultValue = moment().format(realFormat);
          }
        }
      }
      break;
    case 'Switch':
    case 'CheckBox':
      presetProps.defaultValue = defaultValue === 'true';
      presetProps.transformResponse = (value, data) => {
        return value && (value === 'true' || value === true);
      };
      break;
    case 'MultipleSelect':
    case 'SelectBox':
      presetProps.dynamicProps.multiple = ({ record }) => {
        if (!record.editing && !record.get('id') && isTable) {
          // 表格搜索行默认不设置multiple属性
          return undefined;
        }
        return record.editing ? undefined : ',';
      };
      presetProps.transformResponse = (value) => {
        try {
          const jsonValue = JSON.parse(value || '[]');
          // eslint-disable-next-line no-nested-ternary
          return Array.isArray(jsonValue) ? jsonValue.join(',') : typeof jsonValue !== 'string' ? jsonValue?.toString() : jsonValue;
        } catch (e) {
          return value;
        }
      };
      presetProps.transformRequest = (value) => {
        if (value && Array.isArray(value)) {
          return value.join(',');
        }
        return value;
      };
      break;
    case 'Tag':
      presetProps.multiple = tagMultiFlag ? ',' : false;
      if (tagMultiFlag) {
        presetProps.transformResponse = (value, record) => {
          try {
            return value && value.split(',');
          } catch (e) {
            // eslint-disable-next-line no-console
            console.log(e);
          }
        };
      }
      break;
    case 'RichText':
      presetProps.transformRequest = (value, record) => {
        if (typeof value === 'string') {
          return value;
        } else {
          return JSON.stringify(value);
        }
      };
      presetProps.transformResponse = (value, record) => {
        try {
          // 替换富文本中的图片url
          const apiHost = getEnv('API_HOST', '');
          let newValue;
          try {
            // 兼容老数据，需要将YQ_FILE_HOST替换为真实的附件地址
            newValue = value?.replace(new RegExp('YQ_FILE_HOST', 'gm'), `${apiHost}/hfle/yqc/v1/0/files/download-by-key?fileKey=`);
            newValue = newValue?.replace(new RegExp('/hfle/v1', 'gm'), '/hfle/yqc/v1');
          } catch (e) {
            // eslint-disable-next-line no-console
            newValue = value;
          }
          // 兼容旧富文本的编辑器格式 [{'insert': '123'}]
          // 尝试转json
          return value && JSON.parse(newValue);
        } catch (e) {
          // eslint-disable-next-line no-console
          // 如果json转换失败，则默认为新版本的富文本格式（html）
          return value;
        }
      };
      presetProps.validator = (value, fieldName, record) => {
        let result = true;
        if (record?.getField(fieldName)?.required && intl) {
          const errorMessage = field.name;
          if (typeof value === 'string') {
            if (value && !getRichTextIsNull(value)) {
              result = true;
            } else {
              // message.error(errorMessage);
              return errorMessage;
            }
          } else if (!value || RICH_TEXT_NULL_LIST.includes(JSON.stringify(value))) {
            // message.error(errorMessage);
            return errorMessage;
          }
        }
        if (presetProps.defaultValidator) {
          result = presetProps.defaultValidator(value, fieldName, record);
        }
        return result;
      };
      break;
    case 'Image':
    case 'Upload':
      presetProps.validator = (value, fieldName, record) => {
        let result = true;
        if (record?.getField(fieldName)?.required && intl) {
          if (!value) {
            return intl.formatMessage({ id: 'lcr.components.model.field.required.tip', defaultMessage: '请输入{name}' }, { name: field.name });
          }
        }
        if (presetProps.defaultValidator) {
          result = presetProps.defaultValidator(value, fieldName, record);
        }
        return result;
      };
      break;
    case 'NumberField':
      presetProps.max = presetProps.max || 2147483647;
      presetProps.min = presetProps.min || -2147483648;
      break;
    case 'FloatNumber':
      presetProps.validator = (value, fieldName, record) => {
        let result = true;
        if (integerLength && value >= 10 ** integerLength) {
          return intl.formatMessage({ id: 'lcr.components.model.number.validator.failed', defaultMessage: '输入的数字应小于' }) + (10 ** integerLength);
        }
        if (presetProps.defaultValidator) {
          result = presetProps.defaultValidator(value, fieldName, record);
        }
        return result;
      };
      presetProps.precision = decimalLength ? Number(decimalLength) : undefined;
      presetProps.formatterOptions = decimalLength ? { options: { minimumFractionDigits: Number(decimalLength) } } : undefined;
      break;
    case 'Duration':
      presetProps.transformRequest = (value) => durToSec(value, durationMode, durationUnit);
      presetProps.transformResponse = (res) => durToDays(res, durationUnit, durationMode);
      break;
    case 'VariableSet':
      presetProps.transformRequest = (value, record) => {
        // 变量集类型字段后端需要映射，所以对于m2o字段需要转换为id
        // ds在提交前会处理父级字段，再处理子级字段，例如
        // { variable_set_a: { c: 1 }, 'variable_set_a.c': 1 }
        // 会先调用variable_set_a的transformRequest，再调用variable_set_a.c的
        // 导致variable_set_a中的c字段没有正常转义
        // 这里需要对字段进行转义处理
        const vsValue = value;
        if (typeof value === 'object') {
          Object.keys(value).map(vsField => {
            let variableSetCode = fieldCode;
            // 变量视图下的变量集，查询字段配置时，需要去掉变量视图前缀
            if (fieldCode.includes('.')) {
              variableSetCode = fieldCode.split('.')[1];
            }
            const vsFieldConfig = fieldCodeMap.get(`${variableSetCode}.${vsField}`);
            if (value[vsField] && vsFieldConfig?.widgetType === 'MasterDetail') {
              const nameFieldCode = vsFieldConfig?.widgetConfig.relationLovNameFieldCode || 'name';
              const valueFieldCode = vsFieldConfig?.widgetConfig.relationLovValueFieldCode || 'id';
              vsValue[vsField] = value[vsField][valueFieldCode] || value[vsField];
              if (!vsValue[`${vsField}:${nameFieldCode}`] && vsFieldConfig.defaultValue) {
                vsValue[`${vsField}:${nameFieldCode}`] = vsFieldConfig?.widgetConfig?.defaultValueName;
              }
            } else if (DATE_FIELDS.includes(vsFieldConfig?.widgetType)) {
              const vsFieldFormat = vsFieldConfig?.widgetConfig?.format
                ?.replace('yyyy', 'YYYY')
                ?.replace('mm', 'MM')
                ?.replace('dd', 'DD')
                ?.replace(':MM', ':mm') || DEFAULT_DATE_FORMAT[widgetType];
              if (value[vsField]) {
                vsValue[vsField] = moment(value[vsField]).format(vsFieldFormat);
              }
            }
            return vsField;
          });
        }
        return JSON.stringify(vsValue);
      };
      presetProps.transformResponse = (value, data) => {
        try {
          if (value && typeof value === 'object') {
            return value;
          }
          return JSON.parse(value || '{}');
        } catch (e) {
          return {};
        }
      };
      break;
    // case 'Url':
    //   presetProps.pattern = urlReg;
    //   presetProps.defaultValidationMessages = {
    //     typeMismatch: intl.formatMessage({ id: 'lcr.components.model.url.validator', defaultMessage: '请输入有效网址' }),
    //   };
    //   break;
    case 'TextArea':
    case 'Input':
      presetProps.format = autoTransform?.toLowerCase();
      presetProps.validator = (value, fieldName, record) => {
        let result = true;
        if (regularRule && value) {
          let regexpStr = regularRule;
          if (regexpStr?.startsWith('/')) {
            regexpStr = regexpStr.substr(1);
          }
          if (regexpStr?.endsWith('/')) {
            regexpStr = regexpStr.substr(0, regexpStr.length - 1);
          }
          const fieldRegexp = new RegExp(regexpStr);
          if (!fieldRegexp.test(value)) {
            return regularRuleMessage || intl.formatMessage({ id: 'lcr.components.model.field.regular.error', defaultMessage: '字段正则校验失败' });
          }
        }
        if (presetProps.defaultValidator) {
          result = presetProps.defaultValidator(value, fieldName, record);
        }
        return result;
      };
      // 多对一字段默认值为对象时，需要给名称字段也赋一下默认值，用于展示
      // 尝试对名称字段进行截断，从默认值中获取对应的多对一字段，如：
      // user_id: { idField: 1, nameField: 'a' } => user_id:nameField = user_id.nameField
      // eslint-disable-next-line no-case-declarations
      const originField = originFieldCode && fieldCodeMap?.get(originFieldCode);
      if (originField && code === `${originFieldCode}:${originField?.widgetConfig?.relationLovNameFieldCode}` && originField.defaultValue) {
        presetProps.defaultValue = fieldCodeMap.get(originFieldCode).widgetConfig.defaultValueName;
      }
      // if (originFieldCode && fieldCodeMap?.get(originFieldCode) && fieldCodeMap?.get(originFieldCode).defaultValue) {
      //   presetProps.defaultValue = fieldCodeMap.get(originFieldCode).widgetConfig.defaultValueName;
      // }
      break;
    case 'Range':
      presetProps.range = ['start', 'end'];
      presetProps.transformResponse = (res, data) => {
        return {
          start: data[startFieldCode],
          end: data[endFieldCode],
        };
      };
      break;
    case 'Cascader':
      presetProps.defaultValue = typeof defaultValue === 'string' && defaultValue ? JSON.parse(defaultValue) : defaultValue;
      presetProps.transformResponse = (value, data) => {
        const _data = cascadeFields.map(item => data[item.value]).filter(item => item);
        return _data?.length ? _data : undefined;
      };
      break;
    case 'MobileField':
      presetProps.numberGrouping = false;
      // 如果字段编码为xx:xx，则不校验手机号码
      presetProps.pattern = fieldCode.includes(':') ? null : mobileReg;
      presetProps.defaultValidationMessages = {
        patternMismatch: intl.formatMessage({ id: 'lcr.components.model.mobile.pattern', defaultMessage: '请输入正确的手机号' }),
      };
      break;
    case 'Like':
      // 需要兼容旧数据，例如点赞值为 'true' 的情况
      presetProps.transformResponse = (res) => safeBoolean(res);
      break;
    default:
      break;
  }
  return presetProps;
}

/**
 * 转换字段，处理变量视图字段
 * @param props
 * @returns {*}
 */
function transformFields(props) {
  const {
    id, dsConfig, jsonData, fieldMap, viewId, tenantId, parentKey, policiesData, rulesData,
    intl, expressionDefaultValue, funcConfig, fieldCodeMap, publicSurveyFlag, ignoreFields,
  } = props;
  const widgetData = getWidgetData(jsonData, id);
  const tableInlineEdit = dsConfig.get('tag') === 'Table' ? widgetData?.widgetConfig?.inlineFlag : true;
  const fieldRulesData = dsConfig.get('tag') === 'Form' || tableInlineEdit ? rulesData : false;
  const fields = [...fieldMap.values()].reduce((array, field) => {
    const presetProps = transformField(
      {
        fieldMap,
        field,
        viewId,
        tenantId,
        parentKey,
        policiesData,
        rulesData: fieldRulesData,
        intl,
        expressionDefaultValue,
        funcConfig,
        fieldCodeMap,
        isTable: dsConfig.get('tag') === 'Table',
        tableInlineEdit,
        publicSurveyFlag,
        ignoreFields,
      },
    );
    if (field?.widgetType === 'MasterDetail') {
      const { code, widgetConfig } = field;
      const { relationLovNameFieldCode = 'name' } = widgetConfig || {};
      const fieldCode = parentKey ? `${parentKey}.${code}` : code;
      const nameFieldCode = `${fieldCode}:${relationLovNameFieldCode}`;
      // 多对一字段的名称字段不存在，需要加一下 (这里应该并不需要了)
      if (!array.find(_field => _field.name === nameFieldCode)) {
        array.push({
          name: nameFieldCode,
          type: 'string',
        });
      }
    }

    const fieldCode = parentKey ? `${parentKey}.${field.code}` : field.code;
    // 防止多对一字段插入的name字段 将 手动拖入的name字段配置覆盖
    if (!array.find(_field => _field.name === fieldCode)) {
      array.push(presetProps);
    } else if (field?.widgetConfig) {
      // 如果有字段配置，则允许将之前的配置覆盖
      array.push(presetProps);
    }
    return array;
  }, []);
  return fields;
}

/**
 * DataSet搜索字段
 * @param dsConfig
 * @param fieldMap
 * @param fieldCodeMap
 * @param intl
 * @param tenantId
 * @returns {[]}
 */
function transformQueryFields({ dsConfig, queryFieldMap, intl, tenantId, viewId }) {
  const enableOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: 'true' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: 'false' },
    ],
  });
  const queryFields = [];
  if (dsConfig.get('tag') === 'Table' || dsConfig.get('tag') === 'VariableTable') {
    dsConfig.get('fuzzySearch').map((queryField) => {
      const { name, code, widgetType, filterFlag, fuzzyFlag, quickFlag, condition } = queryField;
      if (!name || (quickFlag === undefined && fuzzyFlag)) {
        return queryField;
      }
      const {
        relationLovId,
        widgetConfig,
      } = queryFieldMap.get(code) || {};
      const {
        dataSource, lookupCode, options, tagGroupId,
        relationLovNameFieldCode = 'name', relationLovValueFieldCode = 'id',
      } = widgetConfig || {};
      if (filterFlag) {
        const presetProps = {
          name: code,
          type: SEARCH_TYPE_MAP[widgetType] || 'string',
          label: name,
        };
        if (BOOLEAN_FIELDS.includes(widgetType)) {
          presetProps.options = enableOptionDs;
        }
        if (OPTION_FIELDS.includes(widgetType)) {
          presetProps.multiple = ','; // 搜索支持多选
          if (dataSource === 'lookup') {
            presetProps.lookupCode = lookupCode;
            // 由于快码前端缓存是根据url做标识的，这里加上search是为了区别表格行上的数据展示
            presetProps.lookupUrl = (luCode) => {
              if (!condition || condition?.length === 0) {
                return `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryByCode/all?lookupTypeCode=${luCode}&search=true`;
              }
              return `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryCodeWithCondition?allFlag=true&lookupTypeCode=${luCode}&search=true`;
            };
            // 筛选过滤已失效选项集
            presetProps.lookupAxiosConfig = ({ record }) => {
              return {
                method: (!condition || condition?.length === 0) ? 'GET' : 'POST',
                data: (!condition || condition?.length === 0) ? undefined : {
                  conditions: condition,
                  params: record?.toData() || {},
                },
                transformResponse: (data) => {
                  let originData = {};
                  if (typeof data === 'string') {
                    try {
                      originData = JSON.parse(data);
                    } catch (e) {
                      return data;
                    }
                  } else {
                    originData = data;
                  }
                  if (originData.content) {
                    return {
                      ...originData,
                      content: originData.content?.filter(_d => _d.enabledFlag),
                    };
                  }
                  return {
                    content: originData.map && originData.filter(_d => _d.enabledFlag)
                      .map(_d => ({
                        ..._d,
                        meaning: _d.value,
                        value: _d.code,
                      })),
                  };
                },
              };
            };
          } else if (dataSource === 'optionSet') {
            presetProps.options = new DataSet({ paging: false, data: options || [] });
          } else if (dataSource === 'tagGroup') {
            presetProps.valueField = 'code';
            presetProps.textField = 'name';
            presetProps.noCache = true;
            presetProps.options = new DataSet(TagDataSet({ tenantId, tagGroupId, autoQuery: true, businessObjectId: '297420777941364736' }));
            presetProps.multiple = ',';
          }
        }
        if (DATE_FIELDS.includes(widgetType)) {
          presetProps.range = ['start', 'end'];
          presetProps.placeholder = [
            `${name} ${intl.formatMessage({ id: 'zknow.common.desc.from', defaultMessage: '从' })}`,
            `${intl.formatMessage({ id: 'zknow.common.desc.to', defaultMessage: '至' })}`,
          ];
        }
        // 支持多对一字段搜索
        if (widgetType === 'MasterDetail' && relationLovId) {
          presetProps.multiple = ','; // 搜索支持多选
          presetProps.multiple = true;
          presetProps.lovCode = relationLovId;
          presetProps.valueField = relationLovValueFieldCode;
          presetProps.textField = relationLovNameFieldCode;
          presetProps.lovDefineAxiosConfig = lovCode => ({
            url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
            method: 'GET',
            transformResponse: data => transformResponse(
              data,
              data?.name,
              (map, f) => transformField(
                {
                  fieldMap: map,
                  field: f,
                  viewId,
                  tenantId,
                  intl,
                },
              ),
              intl,
              tenantId,
            ),
          });
          presetProps.lovQueryAxiosConfig = (lovCode, lovConfig = {}, { data, params }) => {
            lovConfig.method = 'POST';
            const { parentIdField, treeFlag, idField } = lovConfig || {};
            let searchFlag = false;
            const queryParams = getQueryParams(data, ['current_params', '__page_params', parentIdField]);

            Object.keys(queryParams).forEach((v) => {
              if (v.indexOf('search_') !== -1) {
                searchFlag = true;
              }
            });

            if (treeFlag === 'Y' && parentIdField) {
              if (data[parentIdField]) {
                params.size = 999;
              } else if (!searchFlag) {
                queryParams[parentIdField] = '0';
              }
            }

            const data_params = {
              ...queryParams,
              ...(data.current_params || {}),
              ...(params || {}),
            };
            delete data_params.current_params;

            return {
              url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
              method: 'POST',
              data: {
                params: data_params,
                conditions: condition || [],
              },
              params,
              transformResponse: (originData) => {
                try {
                  const jsonData = JSON.parse(originData);
                  return {
                    ...jsonData,
                    content: jsonData?.content?.map(item => {
                      if (searchFlag) {
                        // 搜索时，树形结构打平显示
                        return {
                          ...item,
                          isLeaf: true,
                          [parentIdField]: null,
                          id: item[idField] || item.id,
                          primaryKey: item.id,
                        };
                      }
                      return {
                        ...item,
                        id: item[idField] || item.id,
                        primaryKey: item.id,
                      };
                    }) || [],
                  };
                } catch (error) {
                  return [];
                }
              },
            };
          };
          presetProps.transformRequest = (value) => {
            return value?.map(v => v.id)?.join(',');
          };
        }
        queryFields.push(presetProps);
      }
      return queryField;
    });
  }
  return queryFields;
}

/**
 * DataSet查询配置
 * @param props
 * @returns {{read: (function({data?: *}): {method: string, transformRequest: (function(*): string), url: string, transformResponse: transformResponse}), create: (function({data: *}): {method: string, data: *, url: string}), update: (function({data: *}): {method: string, data: *, url: string}), destroy: (function({data: *}): {method: string, data: *, url: string})}}
 */
function getTransportConfig(props) {
  const {
    datasetId, viewId, tenantId, instanceId, parentField, relatedFieldCode, viewType, dsConfig, requestItemConfig,
    fieldCodeMap, tableAutoQuery, queryParams, bodyParams, disableACL, scPageRef, DS_QUERY_IDENTIFICATION,
    variablePage, infoFlag, parentDataSet,
  } = props;
  // NOTE: 全平台提单需要替换接口
  let openSupportTenantFlag;
  try {
    openSupportTenantFlag = sessionStorage.getItem('OPEN_SUPPORT_TENANT_FLAG');
  } catch (e) {
    openSupportTenantFlag = '';
  }
  const OPEN_SUPPORT_TENANT_FLAG = openSupportTenantFlag === 'true';
  const url = `/lc/v1/engine/${tenantId}/dataset/${viewId}/${datasetId}`;
  let getUrl = OPEN_SUPPORT_TENANT_FLAG
    ? `/itsm/v1/support_center/engine/${tenantId}/dataset/${viewId}/${datasetId}`
    : `/lc/v1/engine/${tenantId}/dataset/${viewId}/${datasetId}`;
  // 变量视图目前支持嵌套业务对象类型的表格，但接口不支持需要特殊处理
  let variableParams = {};
  if (!viewType && tableAutoQuery && requestItemConfig) {
    variableParams = {
      field: dsConfig.get('relatedFieldCode'),
      value: requestItemConfig.instanceId,
    };
    getUrl = `/lc/v1/engine/${tenantId}/dataset/variable/${dsConfig.get('businessObjectId')}`;
  }
  const paging = parentField ? 'server' : !(viewId === datasetId && instanceId);
  return {
    read: (conf) => {
      const { data: _data, dataSet: _ds, params: dsParams } = conf;
      const EXTRA_NO_PREFIX = ['filter_id', 'filter_code', 'precise_query', `search_${relatedFieldCode}`, parentField];
      const data = getQueryParams(_data, [...NO_SEARCH_PREFIX, ...EXTRA_NO_PREFIX]);
      let searchFlag = false;
      Object.keys(data).forEach((v) => {
        if (v.indexOf('search_') !== -1) {
          searchFlag = true;
        }
        if (data[v] && (data[v].start || data[v].end)) {
          const { widgetType } = fieldCodeMap.get(v.replace('search_', '')) || {};
          const queryFormat = DEFAULT_DATE_FORMAT[widgetType] || 'YYYY-MM-DD HH:mm:ss';
          const startQueryDate = moment(data[v].start || 0)?.format(queryFormat);
          const endQueryDate = moment(data[v].end)?.format(queryFormat);
          data[v.replace('search_', 'search_start_')] = startQueryDate;
          data[v.replace('search_', 'search_end_')] = endQueryDate;
          delete data[v];
        }
      });
      if (data && relatedFieldCode) {
        data[`search_${relatedFieldCode}`] = instanceId;
      }
      // if (typeof queryParams === 'object') {
      //   data.params = {
      //     ...(data.params || {}),
      //     ...queryParams,
      //   };
      // }
      const parentFieldValue = parentField && !searchFlag ? (data[parentField] || '0') : undefined;

      // 处理表格查询参数
      const params = [];
      const { filter_id, filter_code, precise_query } = data;
      if (filter_id) {
        params.push(`filter_id=${filter_id}`);
      }
      if (filter_code) {
        params.push(`filter_code=${filter_code}`);
      }
      if (precise_query) {
        params.push(`precise_query=${precise_query}`);
      }

      if (disableACL) {
        params.push('disableACL=true');
      }

      // PI42 添加参数
      params.push(`viewType=${viewType}`);

      // 对于默认不查询的表格，当清空筛选时，需要清空当前数据
      const tableQueryParams = {};
      if (!searchFlag && tableAutoQuery !== false && !tableAutoQuery && datasetId !== viewId) {
        tableQueryParams.id = '-1';
      }
      let queryUrl = disableACL && !paging && !variablePage ? `${getUrl}/draft/query` : `${getUrl}/query`;
      // 表格树形查询子节点时，默认不分页查询所有
      if (data[parentField]) {
        queryUrl += '?page=0&size=999';
      }
      if (params?.length > 0) {
        queryUrl += `${data[parentField] ? '&' : '?'}${params.join('&')}`;
      }
      // 表格查询，如果有parent_id，则加入页面查询条件
      if (instanceId && data) {
        if (data.__page_params) {
          data.__page_params.__parent_id = instanceId;
        } else {
          data.__page_params = {
            __parent_id: instanceId,
          };
        }
      }
      // 表格配置了多关联对象--子任务树形，兼容'[]'脏数据
      if (dsConfig.get('selectMultipleObject') && dsConfig.get('selectMultipleObject') !== '[]' && _ds?.getState('businessObjectCode')) {
        const selectMultipleObjectData = JSON.parse(dsConfig.get('selectMultipleObject'));
        const querySubObjectMap = selectMultipleObjectData.map(i => ({
          subObjectId: i.businessObjectId,
          subObjectCode: i.businessObjectCode.toLocaleLowerCase(),
          mainObjectCode: _ds?.getState('businessObjectCode')?.toLocaleLowerCase(),
        }));
        data.__querySubObjectMap = querySubObjectMap;
      }
      delete data?.filter_id;

      const pureParams = paging !== false ? {
        ...dsParams,
        ...queryParams,
        ...variableParams,
      } : {
        ...queryParams,
        ...variableParams,
      };

      // NOTE: 生产单据 INC00024540
      //   理论上，sort 为 undefined 也是可以的，ds 在请求的时候，会自动过滤
      //   但是现在出现了这样的请求 /lc/v1/engine/.../dataset/.../c0...d1093a/query?page=0&size=20&sort=undefined
      //   导致页面无法继续请求加载，显示了页面丢失
      if (pureParams.sort === undefined || pureParams.sort === 'undefined') {
        delete pureParams.sort;
      } else if (pureParams.sort) {
        // 优先级排序字段转换
        pureParams.sort = pureParams.sort.replace(/priority_id(:[^,]+)?/g, 'priority_id:rank_num');
      }

      return {
        url: queryUrl,
        method: 'post',
        params: pureParams,
        data: {
          ...bodyParams,
          ...data,
        },
        transformResponse: (resData) => {
          const originData = JSON.parse(resData);
          _ds.setState(DS_QUERY_IDENTIFICATION, originData?.content?.[0]?.id);
          if (datasetId === viewId && instanceId) {
            // return originData?.content?.length ? originData.content[0] : {};
            return originData;
          }
          // 树形表格搜索时数据打平显示, 新加的子任务树形不打平,兼容'[]'脏数据
          if (parentField && searchFlag && (!dsConfig.get('selectMultipleObject') || dsConfig.get('selectMultipleObject') === '[]')) {
            if (originData?.content?.length) {
              return {
                ...originData,
                content: originData?.content?.map(lineData => ({
                  ...lineData,
                  [parentField]: undefined,
                  isLeaf: true,
                })),
              };
            }
          }
          return originData;
        },
        transformRequest: (reqData) => {
          let id = (
            datasetId === viewId
            && instanceId !== 'new'
          )
            ? (instanceId || undefined)
            : undefined;
          if (!id && infoFlag && parentDataSet) {
            id = parentDataSet.current?.get('id');
          }
          id && _ds.setState(`_refresh_${id}`, Date.now());
          // 子任务树形表格 __mainObjectDataId为父级id
          if (reqData.search___mainObjectDataId && reqData.search___mainObjectDataId !== '0') {
            reqData.__mainObjectDataId = reqData.search___mainObjectDataId;
            delete reqData.search___mainObjectDataId;
          }
          // 子任务树形 查询时不需要传【parentField】
          const parentQueryParams = parentField !== '__mainObjectDataId' ? { [parentField]: parentFieldValue } : {};
          return JSON.stringify({
            ...reqData,
            id,
            ...parentQueryParams,
            ...tableQueryParams,
          });
        },
      };
    },
    create: ({ data }) => ({
      url: `${url}/submit`,
      method: 'post',
      data,
    }),
    update: ({ data }) => ({
      url: `${url}/submit`,
      method: 'post',
      transformRequest: (_data) => {
        // 提交前 插入服务项视图 组件的提交
        submitRequestContent({
          dataRef: scPageRef,
          tenantId,
        });
        return JSON.stringify(data);
      },
    }),
    destroy: ({ data }) => ({
      url: `${url}/submit`,
      method: 'post',
      data,
    }),
  };
}

export default class DataSetManager {
  [MAP] = new Map();

  constructor({
    mode,
    viewId,
    tenantId,
    instanceId,
    viewCode,
    pageRef,
    scPageRef,
    events,
    defaultInstanceData,
    formDataSet,
    parentKey,
    intl,
    defaultData,
    personId,
    person,
    expressionDefaultValue,
    publicSurveyFlag,
    formDataSetCreated,
    tableParentFieldCode,
    queryParams,
    bodyParams,
    disableACL,
    ignoreFields,
    scItemViewFlag,
    DS_QUERY_IDENTIFICATION,
    getUIConfig,
    requestItemConfig,
    mainStore,
    variablePage,
    infoFlag,
    parentDataSet,
  }) {
    this.mode = mode; // MODIFY,READONLY,PREVIEW
    this.viewId = viewId;
    this.viewCode = viewCode;
    this.tenantId = tenantId;
    this.instanceId = instanceId; // 要查询数据的id
    this.pageRef = pageRef;
    this.scPageRef = scPageRef; // 请求内容中的数据
    this.events = events; // dataSet的events
    this.defaultInstanceData = defaultInstanceData; // 默认数据，传入后不会再请求接口
    this.defaultData = defaultData; // 默认值，新建时默认为字段赋值
    this.expressionDefaultValue = expressionDefaultValue; // 表达式默认值
    this.formDataSet = formDataSet; // 变量视图会指定formDataSet，指代父级的formDataSet
    this.parentKey = parentKey; // 变量视图会指定parentKey，指代与父级关联的字段
    this.intl = intl;
    this.funcConfig = { personId, person, tenantId };
    this.publicSurveyFlag = publicSurveyFlag;
    this.formDataSetCreated = formDataSetCreated;
    this.tableParentFieldCode = tableParentFieldCode;
    this.queryParams = queryParams;
    this.bodyParams = bodyParams;
    this.disableACL = disableACL;
    this.ignoreFields = ignoreFields;
    this.scItemViewFlag = scItemViewFlag;
    this.DS_QUERY_IDENTIFICATION = DS_QUERY_IDENTIFICATION;
    this.getUIConfig = getUIConfig;
    this.otherPoliciesData = {};
    this.requestItemConfig = requestItemConfig;
    this.mainStore = mainStore;
    this.variablePage = variablePage;
    this.infoFlag = infoFlag;
    this.parentDataSet = parentDataSet;
  }

  /**
   * 初始化dataSet
   * @param dataSource ds配置
   * @param jsonData 页面配置
   * @param viewId 页面id
   * @param policiesData UI规则配置
   * @param rulesData 校验规则配置
   * @param viewType
   * @param columns
   */
  init(dataSource, jsonData, viewId, policiesData, rulesData, viewType, columns = []) {
    const map = this[MAP];
    map.clear();
    const calculateAutoQuery = (dsConfig) => {
      // 新版服务项变量视图，默认查询
      if (dsConfig.get('tag') === 'Form' && this.scItemViewFlag) {
        return true;
      }
      // 预览表单视图不做查询
      if (dsConfig.get('tag') === 'Form' && this.mode === 'PREVIEW') {
        return false;
      }

      // 表格视图不需要默认查询，会在表格中根据视图和筛选配置进行请求
      if (viewType === 'TABLE') {
        return false;
      }
      // 新增类型视图默认不查询，无论是表单还是表格
      // 排除服务请求视图组件(scItemViewFlag:常规视图)
      // 排除表单中嵌入的变量视图
      if (viewType === 'INSERT' && !this.scItemViewFlag && !this.instanceId) {
        return false;
      }

      // if ((this.requestItemConfig || this.scItemViewFlag) && dsConfig.get('tag') === 'Table') {
      //   return true;
      // }

      // 服务请求视图组件(requestItemConfig:变量视图)
      // 非表格视图中的表格自动查询
      if ((viewType || this.requestItemConfig) && this.mode !== 'PREVIEW' && dsConfig.get('tag') === 'Table') {
        const tableAutoQuery = dsConfig.get('autoQuery');
        return tableAutoQuery === false || tableAutoQuery;
      }
      // 更新类型视图默认查询，无论是否有主键id
      if (viewType === 'UPDATE' && this.instanceId !== 'new') {
        return true;
      }
      // 未给定初始化数据，且传入主键id，则默认查询
      if (this.instanceId && this.instanceId !== 'new' && !this.defaultInstanceData) {
        return true;
      }
      if (this.infoFlag && this.mode === 'OUTPUT') {
        return true;
      }
      return false;
    };

    const calculateAutoCreate = (dsConfig) => {
      if (viewType === 'TABLE') {
        // 表格视图不需要默认新建记录
        return false;
      }
      if (dsConfig.get('tag') === 'Form' && this.mode === 'PREVIEW') {
        // 预览表单视图自动创建
        return true;
      }
      if (viewType === 'UPDATE') {
        // 更新不需要默认新建记录
        return false;
      }
      if (dsConfig.get('tag') === 'Table') {
        return false;
      }
      if (this.defaultInstanceData) {
        return false;
      }
      if (this.instanceId && this.instanceId !== 'new') {
        return false;
      }
      return true;
    };

    // 记录跟随页面刷新ds
    let followPageRefresh = {};
    const expressionPages = getExpressionPage(jsonData);
    dataSource.forEach((dsConfig) => {
      const id = dsConfig.get('id');
      // 表格和详情自动查询
      const autoQuery = calculateAutoQuery(dsConfig);
      // 表格和详情自动创建
      const autoCreate = calculateAutoCreate(dsConfig);
      const fields = dsConfig.get('fields') || [];
      const queryFields = dsConfig.get('fuzzySearch') || [];
      const fieldMap = getFieldMap(fields, 'id', columns);
      const queryFieldMap = getFieldMap(queryFields, 'code', columns);
      const fieldCodeMap = getFieldMap(fields, 'code', columns);
      const cascadeMap = getCascadeMap(fields);
      const mergeMap = (map1, map2) => {
        const mergedMap = new Map();
        map1.forEach((value, key) => {
          mergedMap.set(key, value);
        });

        map2.forEach((value, key) => {
          mergedMap.set(key, value);
        });
        return mergedMap;
      };
      // 存在页面视图或者变量集时，传入父级ds，需要把子级字段加入父级ds
      if (this.formDataSet && (this.parentKey || dsConfig.get('tag') === 'Form') && !this.scItemViewFlag) {
        const fieldConfigList = transformFields({
          id,
          dsConfig,
          jsonData,
          fieldMap,
          viewId: this.viewId || viewId,
          tenantId: this.tenantId,
          parentKey: this.parentKey,
          policiesData,
          rulesData,
          intl: this.intl,
          expressionDefaultValue: this.expressionDefaultValue,
          funcConfig: this.funcConfig,
          fieldCodeMap,
          publicSurveyFlag: false,
          ignoreFields: this.ignoreFields,
        });
        this.formDataSet.setState('otherPoliciesData', policiesData);
        fieldConfigList.map(fieldConfig => {
          const fieldCode = fieldConfig.name?.replace(this.parentKey ? `${this.parentKey}.` : '', '');
          const widgetType = fieldCodeMap.get(fieldCode)?.widgetType;
          this.formDataSet.addField(fieldConfig.name, fieldConfig);
          if (this.formDataSet.getField(fieldConfig.name)) {
            this.formDataSet.setState(`sameFieldId${fieldCode}`, fieldCodeMap.get(fieldCode)?.id);
            this.formDataSet.setState(`sameFieldCode${fieldCode}`, fieldCode);
          }
          this.formDataSet.setState('fieldMap', mergeMap(fieldMap, (this.formDataSet?.getState('fieldMap') || new Map())));
          this.formDataSet.setState('fieldCodeMap', fieldCodeMap);
          const fieldRecord = this.formDataSet.getField(fieldConfig.name);
          const record = this.formDataSet.current;
          let newValue = record?.get(fieldConfig.name);
          // 对于变量视图，多选数据会被多次处理导致变成嵌套数组
          if (MULTIPLE_FIELDS.includes(widgetType)) {
            if (newValue && Array.isArray(newValue.slice())) {
              newValue = newValue.join(',');
            }
          }
          if (fieldRecord?.get('transformResponse')) {
            newValue = fieldRecord?.get('transformResponse')(
              newValue,
              record?.toData(),
            );
          }
          record?.init(fieldConfig.name, newValue);
          return fieldConfig;
        });
        if (!this.variablePage) {
          return true;
        }
      }

      const ds = dsConfig.get('tag') === 'Form' && viewType === 'TABLE'
        ? new DataSet({ autoQuery: false })
        : this.initDataSet(
          id,
          dsConfig,
          jsonData,
          fieldMap,
          expressionPages,
          queryFieldMap,
          fieldCodeMap,
          autoQuery,
          autoCreate,
          viewId,
          viewType,
          policiesData,
          rulesData,
          cascadeMap,
          fields,
        );
      map.set(id, [ds, dsConfig, fieldMap, fieldCodeMap]);
      if (this.pageRef) {
        if (viewType === 'TABLE' && dsConfig.get('tag') !== 'Form') {
          // 表格类型视图，主dataSet为表格dataSet
          if (this.pageRef.current) {
            this.pageRef.current.tableDataSet = ds;
          } else {
            this.pageRef.current = { tableDataSet: ds };
          }
        }
        if (dsConfig.get('tag') === 'Table' && viewType !== 'TABLE') {
          // form中的表格
          if (this.pageRef.current) {
            if (Array.isArray(this.pageRef.current.formTableDsList)) {
              this.pageRef.current.formTableDsList.push(ds);
            } else {
              this.pageRef.current.formTableDsList = [ds];
            }
          } else {
            this.pageRef.current = { formTableDsList: [ds] };
          }
          // 记录跟随页面刷新ds
          if (dsConfig.get('followPageRefreshFlag')) {
            followPageRefresh = {
              ...followPageRefresh,
              [id]: ds,
            };
          }
        }
        if (dsConfig.get('tag') === 'Form') {
          // 非表格视图，主dataSet为表单的dataSet
          if (this.formDataSetCreated) {
            this.formDataSetCreated(ds);
          }
          if (this.pageRef.current) {
            this.pageRef.current.formDataSet = ds;
          } else {
            this.pageRef.current = { formDataSet: ds };
          }
          // 记录新版服务项视图，跟随主视图刷新
          if (this.scItemViewFlag) {
            followPageRefresh = {
              ...followPageRefresh,
              [id]: ds,
            };
          }
          if (this.defaultInstanceData) {
            ds.loadData(this.defaultInstanceData);
          }
        }
      }
    });
    // 记录跟随页面刷新ds
    const formDs = this.formDataSet || (map.get(viewId) && map.get(viewId)[0]);
    if (formDs) {
      const lastFollowPageRefresh = formDs.getState('followPageRefreshDs') || {};
      formDs.setState('followPageRefreshDs', {
        ...lastFollowPageRefresh,
        ...followPageRefresh,
      });
    }
  }

  initDataSet(id, dsConfig, jsonData, fieldMap, expressionPages, queryFieldMap, fieldCodeMap, autoQuery, autoCreate, viewId, viewType, policiesData, rulesData, cascadeMap) {
    const fields = transformFields({
      id,
      dsConfig,
      jsonData,
      fieldMap,
      viewId: this.viewId || viewId,
      tenantId: this.tenantId,
      parentKey: false,
      policiesData,
      rulesData,
      intl: this.intl,
      expressionDefaultValue: this.expressionDefaultValue,
      funcConfig: this.funcConfig,
      fieldCodeMap,
      publicSurveyFlag: this.publicSurveyFlag,
      ignoreFields: this.ignoreFields,
    });
    const queryFields = transformQueryFields({
      dsConfig,
      queryFieldMap,
      intl: this.intl,
      tenantId: this.tenantId,
      viewId,
    });
    const idField = dsConfig.get('idField') || 'id';
    // association子任务树形，固定字段为__mainObjectDataId
    const parentField = dsConfig.get('treeDisplayType') === 'association' ? '__mainObjectDataId' : dsConfig.get('parentFieldCode');
    const relatedFieldCode = dsConfig.get('relatedFieldCode') || this.tableParentFieldCode;
    const tableAutoQuery = dsConfig.get('autoQuery');
    const transport = this.mode === 'PREVIEW' ? undefined : getTransportConfig({
      datasetId: id,
      viewId: this.viewId || viewId,
      tenantId: this.tenantId,
      instanceId: this.instanceId,
      parentField,
      relatedFieldCode,
      fieldCodeMap,
      tableAutoQuery,
      queryParams: this.queryParams,
      bodyParams: this.bodyParams,
      disableACL: this.disableACL,
      scPageRef: this.scPageRef,
      DS_QUERY_IDENTIFICATION: this.DS_QUERY_IDENTIFICATION,
      viewType,
      dsConfig,
      requestItemConfig: this.requestItemConfig,
      variablePage: this.variablePage,
      infoFlag: this.infoFlag,
      parentDataSet: this.parentDataSet,
    });
    const selection = ['false', 'none'].includes(dsConfig.get('selection')) ? false : (dsConfig.get('selection') || false);
    const paging = parentField ? 'server' : !(this.viewId === id && this.instanceId);
    const pageSize = dsConfig.get('pageSize') || 20;
    const events = {};
    // 过滤掉create event，会影响ds初始化
    if (dsConfig.get('tag') === 'Form' && this.events) {
      Object.keys(this.events).map(eventKey => {
        if (this.events[eventKey] && eventKey !== 'create') {
          events[eventKey] = this.events[eventKey];
        }
        return eventKey;
      });
    }
    // 表格ds支持load event
    if (dsConfig.get('tag') === 'Table' && this.events?.load) {
      events.load = this.events.load;
    }

    // 跟随刷新变量
    const allDs = this[MAP];
    const formViewId = this.viewId || viewId;
    const parentFormDs = this.formDataSet;
    return new DataSet({
      dataToJSON: 'dirty',
      autoQuery,
      autoCreate,
      validateBeforeQuery: false,
      autoLocateFirst: viewType !== 'TABLE',
      paging,
      pageSize,
      selection,
      queryFields,
      fields,
      transport,
      id,
      modifiedCheck: false,
      parentField,
      idField,
      primaryKey: 'id',
      events: {
        ...events,
        update: async ({ dataSet, record, name, value, oldValue }) => {
          if (events.update) {
            events.update({ dataSet, record, name, value, oldValue });
          }
          let fieldConfig = fieldCodeMap.get(name);
          // 新版服务项视图字段修改，同步更新主视图，用于后续保存
          if (this.scItemViewFlag && this.formDataSet?.current) {
            if (!this.formDataSet.getField(name)) {
              this.formDataSet.addField(name, {});
              this.formDataSet.current?.init(name, oldValue);
            }
            // 注意这里，字段设置了 transformRequest 的话，这一步会丢失
            //   例如 MultipleSelect，保存值应该是 'xxxx,xxx,xxx' 格式，在这一步会被又赋值为 ['xxxx', 'xxxx', 'xxx'] 格式
            let _value = value;
            if (fieldConfig?.widgetType === 'MultipleSelect') {
              _value = Array.isArray(value) ? value.join(',') : value;
            }
            this.formDataSet?.current.set(name, _value);
          }
          // 字段更新时，把关联子集字段设置为undefined (只读不触发更新)
          if (['MODIFY', 'CREATE', 'PREVIEW'].includes(this.mode)) {
            if (cascadeMap.has(name)) {
              cascadeMap.get(name).forEach((item) => {
                record.set(item, null);
              });
            }
          }
          const isVariablePage = name.startsWith('sc_req_item_id:item_id:variable_view_id:_variable');
          // 变量视图字段配置需要从子视图设置的state获取
          const variableFieldCodeMap = dataSet.getState('fieldCodeMap');
          if (!fieldConfig && isVariablePage) {
            const realName = name.split('.')[1];
            if (variableFieldCodeMap && realName) {
              fieldConfig = variableFieldCodeMap.get(realName);
            }
          }
          if (value && typeof value === 'object' && fieldConfig?.widgetType === 'MasterDetail') {
            const includeFields = [];
            // 将MasterDetail字段其他属性也设置到dataSet，用于LOV属性的级联带出
            Object.keys(value).forEach((fieldKey) => {
              if (!FILTER_FIELD_CODES.includes(fieldKey)) {
                const otherFieldCode = `${name}:${fieldKey}`;
                const relatedFieldConfig = fieldCodeMap.get(otherFieldCode);
                includeFields.push(relatedFieldConfig?.code);
                if (relatedFieldConfig?.widgetType === 'RichText') {
                  const richText = value[`${fieldKey}-backup`];
                  try {
                    const richTextValue = JSON.parse(richText);
                    record.set(otherFieldCode, deltaToHtmlStr(richTextValue));
                  } catch (e) {
                    record.set(otherFieldCode, richText);
                  }
                } else {
                  record.set(otherFieldCode, value[fieldKey]);
                }
              }
            });
            const allFieldCode = Array.from(fieldCodeMap.keys());
            const differenceArr = difference(allFieldCode, includeFields);
            differenceArr.forEach(v => {
              if (v.startsWith(`${name}:`)) {
                record.set(v, undefined);
              }
            });
          }
          if (fieldConfig?.widgetType === 'MasterDetail' && ['MODIFY', 'CREATE', 'PREVIEW'].includes(this.mode)) {
            // 计算变量集或者变量视图字段前缀
            let filedPrefix = '';
            if (fieldConfig.variableSetFlag || isVariablePage) {
              filedPrefix = name?.split('.')[0];
            }
            // 判断m2o字段是否进行了赋值
            const mappingConfigList = fieldConfig?.widgetConfig?.mappingField || [];
            mappingConfigList.map((mapping) => {
              const { mappingFieldCode: _mappingFieldCode, sourceFieldCode } = mapping || {};
              // 变量集中字段需要拼接上变量集code
              const mappingFieldCode = filedPrefix ? `${filedPrefix}.${_mappingFieldCode}` : _mappingFieldCode;
              let mappingField = fieldCodeMap.get(mappingFieldCode);
              // 变量视图字段配置需要从子视图设置的state获取
              if (!mappingField && isVariablePage) {
                mappingField = variableFieldCodeMap.get(_mappingFieldCode);
              }
              if (mappingFieldCode && sourceFieldCode) {
                if (value && (value[sourceFieldCode] !== undefined && value[sourceFieldCode] !== null)) {
                  // 如果被映射的字段是m2o，则默认尝试去当前值列表中查找名称字段，进行赋值操作
                  if (mappingField && mappingField?.widgetType === 'MasterDetail') {
                    const { relationLovNameFieldCode = 'name' } = mappingField?.widgetConfig || {};
                    record.set(mappingFieldCode, {
                      id: value[sourceFieldCode],
                      [relationLovNameFieldCode]: value[`${sourceFieldCode}:${relationLovNameFieldCode}`] || value[relationLovNameFieldCode],
                    });
                  } else if (mappingField && mappingField?.widgetType === 'RichText') {
                    const richText = value[`${sourceFieldCode}-backup`];
                    try {
                      const richTextValue = JSON.parse(richText);
                      record.set(mappingFieldCode, deltaToHtmlStr(richTextValue));
                    } catch (e) {
                      record.set(mappingFieldCode, richText);
                    }
                  } else {
                    record.set(mappingFieldCode, value[sourceFieldCode]);
                  }
                } else {
                  record.set(mappingFieldCode, undefined);
                }
              }
              return mapping;
            });
          }
          // UI规则，设置值
          const allPoliciesData = merge({}, toJS(dataSet.getState('otherPoliciesData')), policiesData);
          if (allPoliciesData) {
            const { fieldConditionMap, settingMap, conditionMap } = allPoliciesData;
            if (fieldConditionMap && fieldConditionMap[name]) {
              const fieldConditionIds = fieldConditionMap[name];
              fieldConditionIds?.map(conditionId => {
                const result = calculateConditions(
                  this.parentKey,
                  conditionMap,
                  [conditionId],
                  record,
                  false,
                  this.funcConfig,
                  name,
                );
                const settings = settingMap[conditionId];
                if (result && settings) {
                  settings.map(settingItem => {
                    const {
                      field, filter, fieldValue, widgetType, fieldLovValue,
                      executeType, expression,
                    } = settingItem;
                    if (executeType !== 'EXPRESSION') {
                      if (OPERATOR.IS_ASSIGN_VARIABLE === filter) { // 根据其他字段取值
                        record.set(field, fieldValue && record.get(fieldValue));
                      } else if (OPERATOR.NULL === filter) { // 设置为null
                        record.set(field, undefined);
                      } else if (widgetType === 'MasterDetail') { // 默认设置固定值
                        let lovValue;
                        try {
                          lovValue = JSON.parse(fieldLovValue);
                          const currentFieldConfig = fieldCodeMap.get(field);
                          const businessObjectId = currentFieldConfig?.businessObjectId;
                          const relationLovBusinessObjectId = currentFieldConfig?.widgetConfig?.relationLovBusinessObjectId;
                          const isSame = businessObjectId === relationLovBusinessObjectId;
                          const relationLovNameFieldCode = currentFieldConfig?.widgetConfig?.relationLovNameFieldCode;
                          if (!isSame) {
                            lovValue = {
                              ...lovValue,
                              [relationLovNameFieldCode]: relationLovNameFieldCode?.indexOf(':') !== -1 ? lovValue[relationLovNameFieldCode.match(/:(.*)/)[1]] : lovValue[relationLovNameFieldCode],
                            };
                          }
                        } catch (e) {
                          lovValue = false;
                        }
                        record.set(field, lovValue || fieldValue);
                      } else {
                        record.set(field, fieldValue);
                      }
                    } else {
                      calculateExpression(expression, record, this.tenantId);
                    }
                    return settingItem;
                  });
                }
                return conditionId;
              });
            }
          }
          if (expressionPages.length > 0 && !this.variablePage) {
            const pageConfig = expressionPages.find((item) => item.dependentField === name);
            if (pageConfig && pageConfig.defaultViewType === 'VARIABLE' && this.instanceId === record?.get('id')) {
              const pageView = await getPageViewId({ tenantId: this.tenantId, expression: pageConfig.pageExpression, params: record?.toData() });
              if (pageView?.id || pageView?.url) {
                this.mainStore.setPageCompViewData(pageConfig.id, {
                  id: pageView?.id,
                  url: pageView?.url,
                });
              }
            }
          }
        },
        query: (props) => {
          let queryResult = true;
          if (events.query) {
            queryResult = events.query(props);
          }
          const { dataSet, params, data } = props;
          // 格式化日期类型字段搜索值
          Object.keys(data)?.map((queryField) => {
            const queryFieldConfig = dataSet?.queryDataSet?.getField(queryField?.replace('search_', ''));
            let queryFieldType = queryFieldConfig?.props?.type;
            queryFieldType = queryFieldType?.replace(queryFieldType[0], queryFieldType[0].toUpperCase());
            const queryFieldTypeFormat = DEFAULT_DATE_FORMAT[queryFieldType];
            if (queryFieldTypeFormat) {
              data[queryField] = moment(data[queryField]).format(queryFieldTypeFormat);
            }
            return queryField;
          });

          // ---1、表单刷新同步刷新设置为跟随刷新的表格---
          // ---2、表格刷新也要同步刷新设置为跟随刷新的表格---
          if (dataSet && !dataSet.getState('followPageRefreshFlag') && dataSet.getState('hasQueryFlag')) {
            const formDs = parentFormDs || (allDs.get(formViewId) && allDs.get(formViewId)[0]);
            const childDsMap = formDs?.getState('followPageRefreshDs');
            if (childDsMap) {
              Object.keys(childDsMap).map(childKey => {
                if (childDsMap[childKey].id !== dataSet.id) {
                  // 需要标记控制，防止死循环
                  childDsMap[childKey]?.setState('followPageRefreshFlag', true);
                  childDsMap[childKey]?.query();
                }
                return childKey;
              });
            }
          }
          // 如果首次加载，则不触发同步刷新
          dataSet.setState('hasQueryFlag', true);
          if (dataSet.getState('followPageRefreshFlag')) {
            dataSet.setState('followPageRefreshFlag', false);
          }
          if (!this.variablePage) {
            this.mainStore.setPageCompViewData({});
          }
          return queryResult;
        },
        indexChange: ({ dataSet, record, previous }) => {
          if (events.indexChange) {
            events.indexChange({ dataSet, record, previous });
          }

          const hasDraft = dataSet?.getState('__isDraft');
          // autoCreate && !autoQuery: 表示详情页面新建，例如：事件单详情页面，新建行
          // !autoCreate && autoQuery: 表示详情页面查询默认值
          // viewType: INSERT、autoCreate: true、autoQuery: false
          // const currentDsDefaultData = ((autoCreate && !autoQuery) || (!autoCreate && autoQuery)) ? this.defaultData : undefined;
          const currentDsDefaultData = viewType === 'INSERT' && dsConfig.get('tag') === 'Table' && !autoCreate && !autoQuery ? undefined : this.defaultData;

          if (viewType !== 'TABLE' && currentDsDefaultData && record && !hasDraft) {
            Object.keys(currentDsDefaultData).forEach((fieldCode) => {
              const fieldConfig = fieldCodeMap?.get(fieldCode);
              const value = currentDsDefaultData[fieldCode];
              if (['VariableSet', 'RichText'].includes(fieldConfig?.widgetType)) {
                try {
                  const jsonValue = JSON.parse(value);
                  record.init(fieldCode, jsonValue);
                } catch (e) {
                  record.init(fieldCode, value);
                }
              } else if (MULTIPLE_FIELDS.includes(fieldConfig?.widgetType)) {
                try {
                  const jsonValue = JSON.parse(value);
                  if (Array.isArray(jsonValue)) {
                    record.init(fieldCode, jsonValue.join(','));
                  } else {
                    record.init(fieldCode, value);
                  }
                } catch (e) {
                  record.init(fieldCode, value);
                }
              } else if (fieldConfig?.widgetType === 'MasterDetail') {
                const relationLovNameFieldCode = fieldConfig?.widgetConfig?.relationLovNameFieldCode || 'name';
                const originNameFieldCode = `${fieldCode}:${relationLovNameFieldCode}`;
                if (value && !value?.id && relationLovNameFieldCode) {
                  let nameField = relationLovNameFieldCode;
                  const nameFieldList = nameField?.split(':');
                  if (nameFieldList?.length > 1) {
                    nameFieldList.shift();
                    nameField = nameFieldList.join(':');
                  }
                  const nameFieldCode = `${fieldCode}:${nameField}`;
                  record.init(fieldCode, {
                    id: value,
                    [relationLovNameFieldCode]: this.defaultData[nameFieldCode] || this.defaultData[originNameFieldCode] || fieldConfig?.widgetConfig?.defaultValueName,
                  });
                } else {
                  // 将MasterDetail字段的其他属性也设置到dataSet，用于LOV属性的级联带出
                  isObject(value) && Object.keys(value).forEach((fieldKey) => {
                    if (!FILTER_FIELD_CODES.includes(fieldKey)) {
                      const otherFieldCode = `${fieldCode}:${fieldKey}`;
                      record.init(otherFieldCode, value[fieldKey]);
                    }
                  });
                  record.init(fieldCode, value);
                }
              } else if (BOOLEAN_FIELDS.includes(fieldConfig?.widgetType)) {
                // 判断后端默认值返回的为'true'，需要转换
                record.init(fieldCode, typeof value === 'string' ? value === 'true' : value);
              } else if (DATE_FIELDS.includes(fieldConfig?.widgetType)) {
                // 当有默认值时，ds会将Time类型的默认转换只有HH:mm:ss，但是后端Time是带年月日的
                if (fieldConfig.widgetType === 'Time') {
                  record.init(fieldCode, value ? moment(`1970-01-01 ${value}`) : value);
                } else {
                  record.init(fieldCode, value ? moment(value) : value);
                }
              } else if (fieldConfig?.widgetType === 'Duration') {
                const { durationUnit, durationMode } = fieldConfig?.widgetConfig || {};
                record.init(fieldCode, durToDays(value, durationUnit, durationMode));
              } else if (currentDsDefaultData.createFromCopy) {
                fieldCodeMap?.get(fieldCode) && record.init(fieldCode, value);
              } else {
                record.init(fieldCode, value);
                if (OPTION_FIELDS.includes(fieldConfig?.widgetType) && fieldConfig?.widgetConfig?.lookupCode) {
                  const field = record.getField(fieldCode);
                  field.fetchLookup();
                }
              }
            });
          }

          // 视图中存在页面视图组件，并且页面视图组件的viewId是通过脚本进行获取的情况
          if (this.variablePage && this.formDataSet) {
            const allFieldCode = Array.from(fieldCodeMap.keys());
            allFieldCode.forEach(fieldCode => {
              this.formDataSet.current?.init(fieldCode, dataSet.current.get(fieldCode));
            });
          }

          if (expressionPages.length > 0 && !this.variablePage && this.instanceId === record?.get('id')) {
            expressionPages.forEach(async (item) => {
              if (item.defaultViewType === 'VARIABLE') {
                const pageView = await getPageViewId({ tenantId: this.tenantId, expression: item.pageExpression, params: record?.toData() }); // executeExpression({ expression: item.pageExpression }, record, this.funcConfig);
                if (pageView?.id || pageView?.url) {
                  this.mainStore.setPageCompViewData(item.id, {
                    id: pageView?.id,
                    url: pageView?.url,
                  });
                }
              }
            });
          }
        },
        validate: ({ dataSet }) => {
          // 部分场景下无需校验
          if (dataSet?.getState('noCheck')) {
            return true;
          }
          let dynamicResult = true;
          dataSet.props.fields.forEach(item => {
            if (
              item?.dynamicProps?.required
              && item?.dynamicProps?.required({ name: item.name, record: dataSet.current, dataSet })
              && dataSet.current
            ) {
              const value = dataSet.current.get(item.name);
              if (
                [undefined, null, ''].includes(value)
                || (typeof value === 'string' && (getRichTextIsNull(value) || RICH_TEXT_NULL_LIST.includes(JSON.stringify(value))))
              ) {
                // message.error(`${this.intl.formatMessage({ id: 'lcr.components.model.required.tip', defaultMessage: '必填字段为空' })}: ${item.label}`);
                dynamicResult = false;
                return false;
              }
            }
            return item;
          });
          return dynamicResult;
        },
      },
    }, { getConfig: this.getUIConfig });
  }

  get(id) {
    const ds = this[MAP].get(id);
    if (ds) {
      return ds[0];
    }
  }

  getConfig(id) {
    const ds = this[MAP].get(id);
    if (ds) {
      return ds[1];
    }
  }

  getFieldConfig(id, name) {
    const ds = this[MAP].get(id);
    if (ds) {
      return ds[2].get(name);
    }
  }

  getFieldCodeMap(id) {
    const ds = this[MAP].get(id);
    if (ds) {
      return ds[3];
    }
  }

  keys() {
    return this[MAP].keys();
  }

  values() {
    return this[MAP].values();
  }

  clear() {
    this[MAP].clear();
    this.mode = null;
    this.viewId = null;
    this.viewCode = null;
    this.tenantId = null;
    this.instanceId = null; // 要查询数据的id
    this.pageRef = null;
    this.scPageRef = null;
    this.events = null; // dataSet的events
    this.defaultInstanceData = null; // 默认数据，传入后不会再请求接口
    this.defaultData = null; // 默认值，新建时默认为字段赋值
    this.expressionDefaultValue = null; // 表达式默认值
    this.formDataSet = null; // 变量视图会指定formDataSet，指代父级的formDataSet
    this.parentKey = null; // 变量视图会指定parentKey，指代与父级关联的字段
    this.intl = null;
    this.funcConfig = null;
    this.publicSurveyFlag = null;
    this.formDataSetCreated = null;
    this.tableParentFieldCode = null;
    this.getConfig = null;
  }
}
