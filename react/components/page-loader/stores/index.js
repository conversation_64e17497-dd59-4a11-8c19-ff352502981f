import React, { createContext, useMemo, useState, useRef, useEffect, useContext } from 'react';
import queryString from 'query-string';
import { DataSet } from 'choerodon-ui/pro';
import ConfigContext from 'choerodon-ui/lib/config-provider/ConfigContext';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import ViewDataSet from './ViewDataSet';
import ButtonDataSet from './ButtonDataSet';
import SectionDataSet from './SectionDataSet';
import FieldDataSet from './FieldDataSet';
import FilterDataSet from './FilterDataSet';
import DataSetManager from './DataSetManager';
import DsDataSet from './DsDataSet';
import TableColumnDataSet from './TableColumnDataSet';
import TableFilterDataSet from './TableFilterDataSet';
import TableViewDataSet from './TableViewDataSet';
import CreateDataSet from './CreateDataSet';
import ExportDataSet from './ExportDataSet';
import DownloadHistoryDataSet from './DownloadHistoryDataSet';
import ConfigDataSet from './ConfigDataSet';
import SubTaskParentDataSet from './SubTaskParentDataSet';
import useStore from './useStore';
import ButtonPrintDataSet, { TemplateOptionDs } from './ButtonPrintDataSet';
import { transformJsonData, transformPostData } from '../utils';

const DS_QUERY_IDENTIFICATION = 'DataSetQueryFinished';
const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore', 'MenuStore')(observer(
  (props) => {
    const {
      intl,
      history,
      children,
      AppState: {
        customConfig,
        currentMenuType: { organizationId, type, tenantNum },
        userInfo: { personId, person },
      },
      HeaderStore: {
        getTenantConfig: { themeColor, enableChatGptFlag, gptTenantFlag },
      },
      AppState,
      viewId,
      viewCode,
      pageRef,
      events,
      lastViewId, // 上一个页面id，用于返回
      jumpViewId, // 创建后需要跳转的页面
      instanceId, // 主业务对象实例id
      defaultInstanceData,
      defaultData, // 默认值，在indexChange更新，包含详情页面也会生效
      expressionDefaultValue, // 表达式默认值，会覆盖视图配置中的默认值，只会在新建时生效
      parentDataSet, // 上级dataSet，用于新增后查询父级列表
      viewData,
      variableFlag,
      mode = 'READONLY', // READONLY-只读 MODIFY-修改 CREATE-新增 PREVIEW-预览 DISABLED-禁用  为了兼容之前的数据 增加OUTPUT为仅有文本的只读模式
      openType, // 视图打开方式
      formDataSet, // 父视图的dataSet，目前主要用于变量视图
      parentKey, // 父视图关联字段key，目前主要用于变量视图
      numberFlag, // 是否显示编号
      surveyFlag, // 调查模式
      publicSurveyFlag, // 公开调查的Id
      autoFocus, // 是否已经设置字段焦点
      showHeaderFlag, // 是否显示顶部标题，CREATE, PREVIEW, DISABLED默认会显示标题
      ticketFlag, // 是否单据页面
      onJumpNewPage, // 跳转新页面的钩子
      tableFilterId, // 针对表格类型的视图，支持传入过滤器。传入时，禁用表格自带过滤器
      tableFilterCode, // 针对表格类型的视图，支持传入筛选器
      tableSuffixes, // 表格按钮自定义区域
      modal, // 如果视图为弹窗，则可以获取弹窗实例
      pageContext, // 父级视图的配置
      autoFocusDisabled, // 停用自动聚焦，用在门户上
      formDataSetCreated, // 当dataSet初始化完成时触发
      viewDataSetCreated, // 当视图dataSet加载完成时触发
      setParentPageDsFieldMap, // 用于汇总页面视图字段
      tableParentFieldCode, // 表格视图支持传入父级查询字段，值默认取instanceId
      MenuStore,
      portalProps, // 门户引用视图属性
      modalButtonFlag = true, // 弹窗是否显示配置的按钮
      surveyTitleFlag = true, // 调查是否显示标题
      hiddenFooter = false, // 在弹窗中的话是否隐藏footer按钮
      tabMenuDataSet,
      queryParams = {}, // 查询参数
      bodyParams = {},
      quickTicketConfig = {}, // 快速提单按钮配置
      surveyHasAnchorComponentParentViewId, // 调查组件嵌入到设计器中，父级设计器的viewId
      ignoreFields = [], // 忽略提交字段
      scItemViewFlag, // 是否新版服务项视图: 业务对象
      requestItemConfig, // 服务请求视图组件父级视图配置: 变量
      scItemViewType, // 视图类型：CREATE_PC/QUERY_PC
      scItemId, // 服务项id
      autoCalculateFlag, // 默认调用计算默认值接口【新特性，需要整体调整】
      calculateCallback, // 计算默认值接口完成回调
      udmFlag, // 共享服务项提单
      submissionChannel, // 提单渠道（现复制提单需要这个参数）
      rowActionFlag, // 表格视图小I显示控制表头
      defaultType, // 工作台类型：后台、门户
      variablePage = false, // 页面视图组件的视图类型是否是脚本
      infoFlag = false, // 小i预览
    } = props;

    const pageDOMRefs = useRef({}); // 视图渲染过程中所需的一些需要传递的 DOM 信息

    const { getConfig } = useContext(ConfigContext);

    const { search } = history?.location || {};
    const disableACL = props.disableACL || queryString?.parse?.(search)?.disableACL; // 是否取消权限控制查询，当前阶段运用与草稿
    const scPageRef = useRef(); // RequestContentRef 用于服务项视图的提交
    const [calculateData, setCalculateData] = useState(null);

    const renderViewData = useMemo(() => {
      if (viewData) {
        const data = transformPostData(viewData, false);
        return [{
          ...data,
          jsonData: transformJsonData(data?.jsonData),
        }];
      }
      return null;
    }, [viewData]);

    // 新建查询表单默认值
    useEffect(() => {
      async function calculateDefaultValue() {
        const fieldMap = {};
        const url = `lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/${variableFlag ? 'calculateVariableView' : 'calculate'}`;
        const result = await axios.post(url, JSON.stringify(fieldMap));
        if (calculateCallback) {
          calculateCallback(result);
        }
        if (result && !result.failed) {
          setCalculateData(result);
        }
      }
      if (autoCalculateFlag) {
        calculateDefaultValue();
      }
    }, [autoCalculateFlag, udmFlag]);

    const intlPrefix = 'lc.page.loader';
    const prefixCls = 'lc-page-loader';
    const tenantId = type === 'site' ? 0 : organizationId;
    const mainStore = useStore(autoFocus);

    // 缓存页面视图字段，用于UIAction
    function handlePageDsFieldMap(dsId, dsFields) {
      if (dsId && dsFields) {
        if (pageRef?.current?.pageRecord) {
          pageRef.current.pageRecord.setState({
            pageDsFieldMap: {
              ...(pageRef?.current?.pageRecord?.pageDsFieldMap || {}),
              [dsId]: dsFields,
            },
          });
        }
      }
    }

    const permissionOptionDs = new DataSet({
      paging: false,
      data: [
        { meaning: intl.formatMessage({ id: 'lcr.components.desc.self', defaultMessage: '本人' }), value: 'SELF' },
        { meaning: intl.formatMessage({ id: 'lcr.components.model.specific.group', defaultMessage: '特定组' }), value: 'SPECIFIC_GROUP' },
      ],
    });

    const dsManager = useMemo(
      () => new DataSetManager({
        mode,
        viewId,
        tenantId,
        instanceId,
        viewCode,
        pageRef,
        scPageRef,
        events,
        defaultInstanceData,
        defaultData,
        formDataSet,
        parentKey,
        intl,
        personId,
        person,
        expressionDefaultValue,
        publicSurveyFlag,
        formDataSetCreated,
        tableParentFieldCode,
        queryParams,
        bodyParams,
        disableACL,
        ignoreFields,
        scItemViewFlag,
        DS_QUERY_IDENTIFICATION,
        getUIConfig: getConfig,
        requestItemConfig,
        mainStore,
        variablePage,
        infoFlag,
        parentDataSet,
      }),
      [viewId, viewCode, tenantId, instanceId, expressionDefaultValue, disableACL, udmFlag, infoFlag, parentDataSet],
    );
    const columnDataSet = useMemo(
      () => new DataSet(TableColumnDataSet({ tenantId, type, viewId })),
      [viewId],
    );

    const tableFilterDataSet = useMemo(
      () => new DataSet(TableFilterDataSet({
        intl,
        tenantId,
        type,
        viewId,
        permissionOptionDs,
        personId,
        publicSurveyFlag,
      })),
      [viewId, publicSurveyFlag],
    );

    const tableViewDataSet = useMemo(
      () => new DataSet(TableViewDataSet({
        intl,
        tenantId,
        type,
        viewId,
        columnDataSet,
        permissionOptionDs,
        personId,
        publicSurveyFlag,
      })),
      [viewId, columnDataSet, publicSurveyFlag],
    );

    const createDataSet = useMemo(
      () => new DataSet(CreateDataSet({ intl, tableFilterDataSet, tableViewDataSet })),
      [tableFilterDataSet, tableViewDataSet],
    );

    const tableButtonDataSet = useMemo(
      () => new DataSet(ButtonDataSet()),
      [],
    );

    const tableActionDataSet = useMemo(
      () => new DataSet(ButtonDataSet()),
      [],
    );

    const tableFieldDataSet = useMemo(
      () => new DataSet(FilterDataSet()),
      [],
    );

    const variableTableFieldDataSet = useMemo(
      () => new DataSet(FilterDataSet()),
      [],
    );

    const tableLineButtonDataSet = useMemo(
      () => new DataSet(ButtonDataSet()),
      [],
    );

    const fieldDataSet = useMemo(
      () => new DataSet(FieldDataSet({
        tableButtonDataSet,
        tableFieldDataSet,
        tableLineButtonDataSet,
        tableActionDataSet,
        variableTableFieldDataSet,
      })),
      [tableButtonDataSet, tableFieldDataSet, tableLineButtonDataSet, tableActionDataSet, variableTableFieldDataSet],
    );

    const sectionDataSet = useMemo(
      () => new DataSet(SectionDataSet({ fieldDataSet })),
      [],
    );

    const buttonDataSet = useMemo(
      () => new DataSet(ButtonDataSet()),
      [],
    );

    const actionDataSet = useMemo(
      () => new DataSet(ButtonDataSet()),
      [],
    );

    const dsFieldDataSet = useMemo(
      () => new DataSet(FilterDataSet()),
      [],
    );

    const dsFilterDataSet = useMemo(
      () => new DataSet(FilterDataSet()),
      [],
    );

    const dsDataSet = useMemo(
      () => new DataSet(DsDataSet({ dsFieldDataSet, dsFilterDataSet })),
      [dsFieldDataSet, dsFilterDataSet],
    );

    // 从AppState中获取视图缓存
    let viewJsonData = null;
    try {
      const cacheViewData = customConfig[`view-${tenantId}-${viewId || `${scItemId + scItemViewType}`}-${viewCode}`];
      viewJsonData = cacheViewData && JSON.parse(cacheViewData);
    } catch (e) {
      viewJsonData = null;
    }

    const viewDataSet = useMemo(
      () => new DataSet(ViewDataSet({
        tenantId,
        viewId,
        viewCode,
        variableFlag,
        surveyFlag,
        publicSurveyFlag,
        sectionDataSet,
        buttonDataSet,
        actionDataSet,
        dsDataSet,
        dsManager,
        viewData: renderViewData,
        pageRef,
        viewDataSetCreated,
        parentKey,
        tableFilterDataSet,
        tableViewDataSet,
        columnDataSet,
        viewJsonData,
        AppState,
        setParentPageDsFieldMap,
        scItemViewFlag,
        scItemViewType,
        scItemId,
        udmFlag,
      })),
      [
        sectionDataSet,
        buttonDataSet,
        dsDataSet,
        dsManager,
        viewData,
        variableFlag,
        tableFilterDataSet,
        tableViewDataSet,
        columnDataSet,
        udmFlag,
      ],
    );

    const exportDataSet = useMemo(
      () => new DataSet(ExportDataSet({ intl, tableFilterDataSet, tableViewDataSet })),
      [tableFilterDataSet, tableViewDataSet],
    );

    const downloadHistoryDataSet = useMemo(
      () => new DataSet(DownloadHistoryDataSet({ intl, tenantId, viewId })),
      [viewId],
    );

    const configDataSet = useMemo(
      () => new DataSet(ConfigDataSet({ tenantId })),
      [],
    );
    const currentMenu = tabMenuDataSet?.find?.(r => r.getState('current'));
    let dataId = currentMenu?.get?.('id') || instanceId || tabMenuDataSet?.toData?.()?.[0]?.id;
    if (!dataId) {
      try {
        dataId = queryString?.parse?.(search)?.ticketId;
      } catch (e) {
        // console.error(e);
      }
    }
    const templateOptionDs = useMemo(
      () => new DataSet(TemplateOptionDs({ intl, tenantId, viewDataSet, dataId })),
      [viewDataSet.length, dataId],
    );
    const buttonPrintDataSet = useMemo(
      () => new DataSet(ButtonPrintDataSet({ intl, templateOptionDs })),
      [viewDataSet.length, dataId],
    );
    // 子任务面包屑
    const subTaskParentDataSet = useMemo(() => new DataSet(SubTaskParentDataSet()), []);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      tenantNum,
      mode,
      viewId: viewDataSet.current?.get('id') || viewId,
      lastViewId,
      viewDataSet,
      buttonDataSet,
      templateOptionDs,
      buttonPrintDataSet,
      actionDataSet,
      sectionDataSet,
      dsManager,
      instanceId,
      formDataSet,
      parentKey,
      mainStore,
      columnDataSet,
      tableFilterDataSet,
      tableViewDataSet,
      createDataSet,
      exportDataSet,
      downloadHistoryDataSet,
      person,
      personId,
      handlePageDsFieldMap,
      MenuStore,
      portalProps,
      configDataSet,
      themeColor,
      disableACL,
      surveyHasAnchorComponentParentViewId,
      scPageRef,
      calculateData,
      submissionChannel,
      DS_QUERY_IDENTIFICATION,
      enableChatGptFlag,
      gptTenantFlag,
      subTaskParentDataSet,
      variablePage,
      pageDOMRefs,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
