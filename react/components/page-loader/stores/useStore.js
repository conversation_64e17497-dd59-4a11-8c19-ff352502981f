import { useLocalStore } from 'mobx-react-lite';

export default function useStore(autoFocus) {
  return useLocalStore(() => ({
    currentNumber: 1,
    setNumber() {
      this.currentNumber = this.currentNumber + 1;
    },
    get getNumber() {
      return this.currentNumber;
    },

    fieldMap: {},
    setMap(data) {
      this.fieldMap = data;
    },
    get getMap() {
      return this.fieldMap;
    },

    autoFocus,
    setFocus(data) {
      this.autoFocus = data;
    },
    get getFocus() {
      return this.autoFocus;
    },
    /* ** 工单翻译 start ** */
    translateLoading: false,
    get getTranslateLoading() {
      return this.translateLoading;
    },
    setTranslateLoading(data) {
      this.translateLoading = data;
    },
    translateManualCancel: false,
    get getTranslateManualCancel() {
      return this.translateManualCancel;
    },
    setTranslateManualCancel(data) {
      this.translateManualCancel = data;
    },
    /* ** 工单翻译 end ** */

    /**
     * 页面组件视图ID
     */
    pageCompViewData: new Map(),
    get getPageCompViewData() {
      return this.pageCompViewData;
    },
    setPageCompViewData(key, value) {
      this.pageCompViewData.set(key, value);
    },
  }));
}
