import React from 'react';
import { v4 as uuidv4 } from 'uuid';
import { toJS } from 'mobx';
import moment from 'moment';
import axios from 'axios';
import { Modal, message } from 'choerodon-ui/pro';
import qs from 'qs';
import { UAParser } from 'ua-parser-js';
import { getRichTextIsNull, simplifyField } from '../../utils';
import { validateServiceItemPage } from '@/utils';

if (!window.__yqUserAgent__) {
  window.__yqUserAgent__ = UAParser(window.navigator.userAgent);
}

export const getCurrentDate = () => {
  const date = new Date();
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? (`0${m}`) : m;
  let d = date.getDate();
  d = d < 10 ? (`0${d}`) : d;
  let h = date.getHours();
  h = h < 10 ? (`0${h}`) : h;
  let minute = date.getMinutes();
  minute = minute < 10 ? (`0${minute}`) : minute;
  let second = date.getSeconds();
  second = second < 10 ? (`0${second}`) : second;
  return `${y}_${m}_${d}-${h}_${minute}_${second}`;
};

export function getQueryFields({ fuzzySearch = [], searchType, advancedSearch = [] }, transform) {
  if (searchType === 'advanced') {
    return advancedSearch
      .sort(({ hidden }, { hidden: hidden2 }) => Number(hidden) - Number(hidden2))
      .map(({ id }) => transform(id));
  } else if (searchType === 'fuzzy') {
    return fuzzySearch.map(({ id }) => transform(id));
  }
  return [];
}

/**
 * 表格视图，根据columns配置补全表格ds
 * @param fields
 * @param code
 * @param columns
 * @returns {Map<unknown, unknown>}
 */
export function getFieldMap(fields, code = 'id', columns) {
  const fieldMap = [];
  fields.map(field => {
    const columnField = columns?.find(v => v.objectFieldPath === field.code);
    // 改为了所有添加进数据源的字段都是可编辑的字段，添加转换
    let newField = {
      ...field,
      editable: true,
    };
    if (columnField) {
      newField = {
        ...newField,
        ...columnField,
        requiredType: field.requiredType || columnField.requiredType,
        name: columnField.label,
        editable: true,
        code: columnField.objectFieldPath,
        id: columnField.objectFieldPath, // objectFieldId会重复，改为objectFieldPath
        widgetConfig: {
          ...JSON.parse(columnField?.widgetConfig),
          ...field.widgetConfig,
        } || {},
      };
    }
    fieldMap.push([field[code] || field.code, newField]);
    return field;
  });
  /**
   * 兼容错误数据
   * 为了修复当表格视图的json中，datasets下无fields，默认从columns不全字段，其他情况默认取fields
   */
  if (columns?.length && fields?.length === 0) {
    columns.map(column => {
      const field = {
        ...column,
        name: column.label,
        editable: true,
        code: column.objectFieldPath,
        id: column.objectFieldPath, // objectFieldId会重复，改为objectFieldPath
        widgetConfig: {},
      };
      // 防止字段重复
      if (new Map(fieldMap).get(column[code] || column.code)) {
        return column;
      }
      try {
        field.widgetConfig = JSON.parse(column?.widgetConfig) || {};
      } catch (e) {
        // console.log(column?.widgetConfig);
      }
      fieldMap.push([field[code] || field.code, field]);
      return column;
    });
  }
  return new Map(fieldMap);
}

// 页面视图
export function getExpressionPage(jsonData) {
  const pageWidgetConfigList = [];

  jsonData.sections.forEach(section => {
    section.fields.forEach(field => {
      if (field.widgetType === 'Page') {
        pageWidgetConfigList.push({
          ...field.widgetConfig,
          id: field.id,
        });
      }
    });
  });

  return pageWidgetConfigList;
}

function setParentMap(key, value, parentMap) {
  const relatedList = parentMap.get(key);
  if (relatedList) {
    parentMap.set(key, [...relatedList, value]);
  } else {
    parentMap.set(key, [value]);
  }
}

export function getCascadeMap(fields, code = 'code') {
  const parentMap = new Map();
  fields.map(field => {
    // 多对一字段级联关系
    const variableFilter = field?.widgetConfig?.variableFilter;
    if (field.id && variableFilter?.length) {
      variableFilter.map((filter) => {
        setParentMap(filter.relatedFieldCode, field[code], parentMap);
        return filter;
      });
    }
    // 下拉单选 选项集级联关系
    const cascadeParentField = field?.widgetConfig?.cascadeParentField;
    if (cascadeParentField) {
      setParentMap(cascadeParentField, field[code], parentMap);
    }
    return field;
  });
  return parentMap;
}

export function getQueryFieldMap({ fuzzySearch = [], searchType, advancedSearch = [] }) {
  const fields = searchType === 'advanced' ? advancedSearch : fuzzySearch;
  return getFieldMap(fields);
}

/**
 * 判断color是否为亮色
 * @param color
 * @returns {boolean}
 */
export function isLight(color) {
  return (
    0.213 * parseInt(`0x${color.slice(1, 3)}`, 16)
    + 0.715 * parseInt(`0x${color.slice(3, 5)}`, 16)
    + 0.072 * parseInt(`0x${color.slice(5, 7)}`, 16)
    > 255 / 2
  );
}

/**
 * 根据背景的获取字体颜色
 * @param color
 * @returns {string}
 */
export function getFontColor(color) {
  const colorHexReg = /^#?([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$/;
  if (colorHexReg.test(color)) {
    return isLight(color) ? '#000' : '#FFF';
  }
  return '#000';
}

// let iframe;

/**
 * 渲染表格title
 * @param dataSet
 * @param name
 * @returns {*}
 */
export function renderTableTitle(label, dataSet, name) {
  const header = label || dataSet.getField(name)?.get('label') || undefined;
  return <span title={header}>{header}</span>;
}

export function getFileSuffix(fileName) {
  return fileName.replace(/.+\./, '').toLowerCase();
}

export function getDefaultSection() {
  return {
    id: uuidv4(),
    name: 'Section',
    layout: '5:5',
    mode: 'REDUCED', // 标准STANDARD/精简REDUCED
    hiddenFlag: true, // 区域显示 不可折叠/可折叠
    activeFlag: true, // 默认方式 打开/收起
    xPosition: 0,
    yPosition: 0,
    fields: [],
    sections: [],
    parentId: null,
    tag: 'Section',
  };
}

export const TABLE_BUTTON_TYPES = ['IMPORT', 'EXPORT', 'EXPORT_HISTORY', 'REFRESH', 'CONFIG'];

export const EDIT_BUTTON_TYPES = ['CREATE', 'DELETE', 'SUBMIT', 'UPDATE', 'ADD', 'REMOVE', 'IMPORT', 'INLINECREATE', 'INLINEEDIT'];

export const RELATION = {
  AND: 'AND',
  OR: 'OR',
};

export const ACTION = {
  SHOW: 'SHOW',
  HIDE: 'HIDE',
  READONLY: 'READONLY',
  EDITABLE: 'EDITABLE',
  Not_REQUIRED: 'Not_REQUIRED',
  REQUIRED: 'REQUIRED',
  SETTINGS: 'SETTINGS',
};

export const OPERATOR = {
  IS_ASSIGN_VARIABLE: 'is assign variable',
  IS: 'is',
  IS_NOT: 'is not',
  NULL: 'is null',
  NOT_NULL: 'is not null',
  IN_LIST: 'is in list',
  NOT_IN_LIST: 'is not in list',
  IS_NOT_EMPTY: 'is not empty',
  IS_EMPTY: 'is empty',

  IS_EARLY_THAN: 'is early than',
  IS_LATER_THAN: 'is later than',
  IS_NOT_EARLY_THAN: 'is not early than',
  IS_NOT_LATER_THAN: 'is not later than',

  STARTS_WITH: 'starts with',
  ENDS_WITH: 'ends with',
  EXISTS: 'exists',
  DOES_NOT_EXISTS: 'does not exist',
  CONTAINS: 'contains',
  DOES_NOT_CONTTAIN: 'does not contain',

  BETWEEN: 'between',
  IS_GREATER_THAN: 'is greater than',
  IS_SMALLER_THAN: 'is smaller than',
  IS_NOT_GREATER_THAN: 'is not greater than',
  IS_NOT_SMALLER_THAN: 'is not smaller than',

  IS_LIKE: 'is like',
  IS_STEP: 'is step',
  IS_NOTHING: 'is Nothing',

  IS_UPDATE: 'is update',
  IS_VARIABLE: 'is variable',
  IS_CURRENT_USER: 'is current user',

  UPDATE: 'update',

  RECURSIVE_IN: 'recursive in',
  RECURSIVE_NOT_IN: 'recursive not in',
};

function compareTime(time, curTime) {
  if (!curTime) return false;
  const cur = new Date(curTime);
  if (typeof time === 'string' && time?.indexOf(',') !== -1) {
    const tArr = time.split(',').map((item) => new Date(item));
    return cur > tArr[0] && cur < tArr[1];
  }
  const t = new Date(time);
  // 当前时间比time早，返回true
  return cur < t;
}

function compareText(type, text, curText) {
  if (!curText) return false;
  const index = curText.indexOf(text);
  if (index === -1) return false;
  const endIndex = index + text.length;
  return (index === 0 && type === 'start') || (endIndex === curText.length && type === 'end') || (index !== -1 && type === 'contains');
}

const INPUT_FIELDS = [
  'Input',
  'TextArea',
  'Password',
  'Email',
  'NumberField',
  'FloatNumber',
  'Currency',
  'Url',
  'Duration',
];

const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};
const MOMENT_FIELDS = [
  'DateTime',
  'Date',
  'Time',
];

const MULTIPLE_FIELDS = ['MultipleSelect', 'SelectBox'];
const BOOLEAN_FIELDS = ['Switch', 'CheckBox'];

const FUNC_BASE = {
  GetValue: 'function getValue(code){return record && record.get(code);}\n',
  SetValue: 'function setValue(code, value) { record && record.set(code, value); }\n',
  GetCurrentAttribute: 'function getCurrentAttribute(code){return record && record.get(code);}\n',
  GetCurrentPersonId: 'function getCurrentPersonId(){ return personId; }\n',
  GetCurrentPerson: 'function getCurrentPerson(){ return person; }\n',
  GetCurrentTime: 'function getCurrentTime(code){return new Date();}\n',
  GetCurrentDate: 'function getCurrentDate() { return moment().format(\'YYYY-MM-DD\'); }\n',
  GetCurrentDateTime: 'function getCurrentDateTime() { return moment().format(\'YYYY-MM-DD HH:mm:ss\'); }\n',
  GetCurrentTenant: 'function getCurrentTenant(){ return tenantConfig; }\n',
  GetCurrentTenantId: 'function getCurrentTenantId(){ return tenantConfig && tenantConfig[\'tenantId\']; }\n',
  GetUserAgent: `function getUserAgent() {
    var deviceType = window.__yqUserAgent__.device.type || 'PC';
    return {
      ua: window.navigator.userAgent,
      browserType: window.__yqUserAgent__.browser.name,
      browserVersion: 'v'+window.__yqUserAgent__.browser.version,
      osType: window.__yqUserAgent__.os.name,
      osVersion: 'v'+window.__yqUserAgent__.os.version,
      deviceType: deviceType.replace(new RegExp('(\\b\\w|\\s\\w)', 'g'), m => m.toUpperCase()),
    };
  }\n`,
  Invoke: [
    'function invoke(service, component, key) {return record?.get(realField);}\n',
    `function invoke(service, component, key) {
      if (service && component && key) {
        return {
          service,
          component,
          key,
        }
      }
      return '';
   }`,
    `function invoke(service, component, key) {
    if (service && component && key) {
       var url = \`/lc/v1/engine/\${_tenantId_}/dataset/invoke?service=\${service}&component=\${component}&key=\${key}\`;
       return axios.post(url, record && record.toData() || {}).then(function(data) { return data; });
    }
    return '';
 }`,
  ],
};

function firstToLowerCase(str) {
  return str.replace(/^./, match => match.toLowerCase());
}

// 设计器中执行的js
function transformDesignExpression(expression) {
  let transformedExpression = expression;
  Object.keys(FUNC_BASE).forEach(funcName => {
    const replaceRegex = new RegExp(`\\$${funcName}`, 'g');
    transformedExpression = transformedExpression.replace(replaceRegex, firstToLowerCase(funcName));
  });
  return transformedExpression;
}

export function executeExpression(fieldDto, record, funcConfig, realField) {
  const { expression } = fieldDto;
  const { tenantId } = funcConfig;
  const tenantConfig = window.__yqcloudStores__?.AppState.currentMenuType || {};
  try {
    const config = `
        var personId = "${funcConfig?.personId}";
        var person = ${JSON.stringify(funcConfig?.person || {})};
      `;
    const functionExpression = Object.keys(FUNC_BASE).reduce((pre, cur) => {
      if (expression.includes(cur)) {
        const funcBody = FUNC_BASE[cur];
        pre += Array.isArray(funcBody) ? funcBody[0] : funcBody;
      }
      return pre;
    }, config);
    // eslint-disable-next-line no-new-func
    const func = new Function(
      'record', 'realField', 'tenantConfig', 'moment',
      functionExpression + transformDesignExpression(expression)
    );
    const defaultValue = func(record, realField, tenantConfig, moment);
    return defaultValue;
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('error==>', e);
    return null;
  }
}

export async function executeExpression1(fieldDto, record, funcConfig) {
  const { expression } = fieldDto;
  const { tenantId } = funcConfig;
  const tenantConfig = window.__yqcloudStores__?.AppState.currentMenuType || {};
  try {
    const config = `
        var personId = "${funcConfig?.personId}";
        var person = ${JSON.stringify(funcConfig?.person || {})};
      `;
    const functionExpression = Object.keys(FUNC_BASE).reduce((pre, cur) => {
      if (expression.includes(cur)) {
        const funcBody = FUNC_BASE[cur];
        pre += Array.isArray(funcBody) ? funcBody[1] : funcBody;
      }
      return pre;
    }, config);

    // eslint-disable-next-line no-new-func
    const func = new Function(
      'axios', 'record', 'tenantConfig', 'moment',
      functionExpression + transformDesignExpression(expression)
    );
    let defaultValue;
    if (expression.includes('Invoke')) {
      const jsonInvoke = func();
      const response = await axios.post(`/lc/v1/engine/${tenantId}/dataset/jsonInvoke?service=${jsonInvoke?.service}&component=${jsonInvoke?.component}&key=${jsonInvoke?.key}`, record.toData());
      defaultValue = response?.data;
    } else {
      defaultValue = func(axios, record, tenantConfig, moment);
    }
    return defaultValue;
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('error==>', e);
    return null;
  }
}

function operatorIs(curValue, fieldValue, widgetType) {
  // 临时： CheckBox在Conditon中的配置类型是Select
  if (['MultipleSelect', 'Select', 'SelectBox', 'Tag'].includes(widgetType)) {
    const value = fieldValue?.split(',');
    return value?.every(item => {
      if (typeof curValue?.split === 'function') {
        return curValue && curValue?.split(',').indexOf(item) !== -1;
      }
      if (typeof curValue?.find === 'function') {
        return curValue && curValue?.find(option => option === item);
      }
      return curValue === fieldValue;
    });
  }
  if (widgetType === 'RichText' && curValue.length) {
    return fieldValue === curValue[0]?.insert?.replace('\n', '');
  }
  return curValue === fieldValue;
}

function operatorRecursive(curValue, fieldValue, widgetType, record, field) {
  if (['Select', 'SelectBox', 'MultipleSelect', 'Radio'].includes(widgetType)) {
    const data = record.getField(field)?.options?.toData();
    const currentData = data?.find(v => v.code === fieldValue);
    if (currentData) {
      const codes = data.filter(v => v.parentId === currentData.id).map(v => v.code);
      return [...codes, currentData.code].includes(curValue);
    }
    return false;
  }
  return false;
}

/**
 * 输入多个条件组，计算条件关系
 * @param parentKey 选填父级key，后边与filters[i].field相拼
 * @param conditions 必填数组条件组
 * @param record 必填当前数据record
 * @param funcConfig 表达式需求的上下文数据如 funcConfig.personId; funcConfig.person
 * @param fieldCode 变更字段编码
 * */
export async function calculateCondition1(parentKey, conditions, record, funcConfig, fieldCode, notVisibleChange) {
  let conditionFlag = true;
  if (conditions?.length) {
    for (let cIndex = 0; cIndex < conditions.length; cIndex++) {
      const { condition, filters } = conditions[cIndex];
      let filterFlag = true;
      if (filters && filters.length) {
        for (let fIndex = 0; fIndex < filters.length; fIndex++) {
          // 默认条件符合
          let newFilterFlag = true;
          const {
            condition: filterCondition, field, filter, fieldValue: originFieldValue,
            fieldLovValue, widgetType, fieldValueType,
          } = filters[fIndex];
          let currentValue; //
          let actualValue; // （新）
          let fieldValue = originFieldValue;
          // 对于多对一字段，fieldValue值可能丢失（目前还没定位到问题），先从fieldLovValue取值
          if (!originFieldValue && widgetType === 'MasterDetail') {
            try {
              const lovValue = JSON.parse(fieldLovValue);
              if (Array.isArray(lovValue)) {
                fieldValue = lovValue?.map(v => v?.id)?.join(',');
              } else {
                fieldValue = lovValue?.id || '';
              }
            } catch (e) {
              fieldValue = '';
            }
          }
          const realField = parentKey ? `${parentKey}.${field}` : field;
          if (INPUT_FIELDS.includes(widgetType)) {
            // 'Input','TextArea','Password','Email','NumberField','FloatNumber','Currency','Url','Duration',
            // 对于可输入类型的组件，不能在onChange触发时就立即进行条件判断，先取原始值
            // getPristineValue(fieldName) 根据字段名获取字段的原始值。
            currentValue = notVisibleChange ? record.get(realField) : record.getPristineValue(realField);
            actualValue = record.get(realField);
          } else if (widgetType === 'RichText') {
            // 'RichText' 富文本特殊处理
            currentValue = record.getPristineValue(realField);
            actualValue = record.get(realField);
            // NOTE: 第一版本富文本富文本是Quill，数据是object类型的。 第二版富文本是ckeditor，存的是html字符串
            const isNullCurrentValue = getRichTextIsNull(JSON.stringify(currentValue));
            const isNullActualValue = getRichTextIsNull(JSON.stringify(actualValue));
            // 判断quill富文本是否为空
            if (isNullCurrentValue) {
              currentValue = '';
            }
            if (isNullActualValue) {
              actualValue = '';
            }
          } else if (MOMENT_FIELDS.includes(widgetType)) {
            // 'DateTime','Date','Time', // 时间相关
            const realFormat = DEFAULT_DATE_FORMAT[widgetType];
            currentValue = record?.get(realField) && moment(record?.get(realField)).format(realFormat);
            actualValue = currentValue;
          } else if (MULTIPLE_FIELDS.includes(widgetType) && Array.isArray(toJS(record.get(realField)))) {
            // 多选值如果是数组，转为以逗号分割的字符串
            currentValue = toJS(record.get(realField)).join(',');
            actualValue = currentValue;
          } else if (widgetType === 'MasterDetail' && record.get(realField)) {
            // 判断多对一字段值为对象的情况
            if (typeof record.get(realField) === 'object') {
              currentValue = record.get(realField)?.id;
            }
            if (typeof record.get(realField) === 'string') {
              currentValue = record.get(realField);
            }
            actualValue = currentValue;
          } else {
            currentValue = record.get(realField);
            actualValue = currentValue;
          }
          if (fieldValueType === 'EXPRESSION') {
            // js表达式取值
            // eslint-disable-next-line no-await-in-loop
            fieldValue = await executeExpression1(filters[fIndex], record, funcConfig, realField);
          }
          if (fieldValueType === 'VARIABLE') {
            fieldValue = toJS(record.get(originFieldValue));
          }
          // 判断条件
          // // start switch
          switch (filter) { // 根据条件判断
            case OPERATOR.IS: // 判断是否相等
              newFilterFlag = operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.IS_NOT: // 判断是否不相等
              newFilterFlag = !operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.NULL: // 判断为空
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.NOT_NULL: // 判断不为空
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IN_LIST: // 判断是否是数组
              // eslint-disable-next-line no-nested-ternary
              newFilterFlag = (typeof fieldValue === 'string') ? fieldValue?.split(',')?.includes(currentValue) : Array.isArray(fieldValue) ? fieldValue.includes(currentValue) : false;
              break;
            case OPERATOR.NOT_IN_LIST:
              // eslint-disable-next-line no-nested-ternary
              newFilterFlag = !((typeof fieldValue === 'string') ? fieldValue?.split(',')?.includes(currentValue) : Array.isArray(fieldValue) ? fieldValue.includes(currentValue) : false);
              break;
            case OPERATOR.IS_EMPTY:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_NOT_EMPTY:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IS_EARLY_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_LATER_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_EARLY_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_LATER_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.STARTS_WITH:
              newFilterFlag = compareText('start', fieldValue, currentValue);
              break;
            case OPERATOR.ENDS_WITH:
              newFilterFlag = compareText('end', fieldValue, currentValue);
              break;
            case OPERATOR.CONTAINS:
              newFilterFlag = compareText('contains', fieldValue, currentValue);
              break;
            case OPERATOR.DOES_NOT_CONTTAIN:
              newFilterFlag = !compareText('contains', fieldValue, currentValue);
              break;
            case OPERATOR.BETWEEN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_GREATER_THAN:
              newFilterFlag = Number(fieldValue) < Number(currentValue);
              break;
            case OPERATOR.IS_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) > Number(currentValue);
              break;
            case OPERATOR.IS_NOT_GREATER_THAN:
              newFilterFlag = Number(fieldValue) >= Number(currentValue);
              break;
            case OPERATOR.IS_NOT_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) <= Number(currentValue);
              break;
            case OPERATOR.IS_CURRENT_USER:
              newFilterFlag = currentValue === funcConfig.personId;
              break;
            case OPERATOR.EXISTS:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.DOES_NOT_EXISTS:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_LIKE:
              newFilterFlag = ['true', true].includes(actualValue);
              break;
            case OPERATOR.IS_STEP:
              newFilterFlag = ['false', false].includes(actualValue);
              break;
            case OPERATOR.IS_NOTHING:
              newFilterFlag = [undefined, '', null].includes(actualValue);
              break;
            case OPERATOR.UPDATE:
              newFilterFlag = realField === fieldCode;
              break;
            case OPERATOR.RECURSIVE_IN:
              newFilterFlag = operatorRecursive(currentValue, fieldValue, widgetType, record, realField);
              break;
            case OPERATOR.RECURSIVE_NOT_IN:
              newFilterFlag = !operatorRecursive(currentValue, fieldValue, widgetType, record, realField);
              break;
            default:
              break;
          }
          // // end switch
          // switch 运算后 => newFilterFlag
          // // start filters[i].conditio
          if (RELATION.AND === filterCondition) {
            // 如果 filters[i].condition === RELATION.AND
            filterFlag = filterFlag && newFilterFlag;
          } else {
            // 如果 filters[i].condition === RELATION.OR
            filterFlag = filterFlag || newFilterFlag;
          }
        }
      }
      if (RELATION.AND === condition) {
        conditionFlag = conditionFlag && filterFlag;
      } else {
        // 当只有一组条件是，且条件是或，已这组条件计算结果为准
        if (conditions?.length === 1) {
          conditionFlag = false;
        }
        conditionFlag = conditionFlag || filterFlag;
      }
    }
  }
  return conditionFlag;
}
export function calculateCondition(parentKey, conditions, record, funcConfig, fieldCode, notVisibleChange) {
  let conditionFlag = true;
  if (conditions?.length) {
    conditions.map(conditionItem => {
      const { condition, filters } = conditionItem;
      let filterFlag = true;
      if (filters && filters.length) {
        filters.map(filterItem => {
          // 默认条件符合
          let newFilterFlag = true;
          const {
            condition: filterCondition, field, filter, fieldValue: originFieldValue,
            fieldLovValue, widgetType, fieldValueType,
          } = filterItem;
          let currentValue; //
          let actualValue; // （新）
          let fieldValue = originFieldValue;
          // 对于多对一字段，fieldValue值可能丢失（目前还没定位到问题），先从fieldLovValue取值
          if (!originFieldValue && widgetType === 'MasterDetail') {
            try {
              const lovValue = JSON.parse(fieldLovValue);
              if (Array.isArray(lovValue)) {
                fieldValue = lovValue?.map(v => v?.id)?.join(',');
              } else {
                fieldValue = lovValue?.id || '';
              }
            } catch (e) {
              fieldValue = '';
            }
          }
          const realField = parentKey ? `${parentKey}.${field}` : field;
          if (INPUT_FIELDS.includes(widgetType)) {
            // 'Input','TextArea','Password','Email','NumberField','FloatNumber','Currency','Url','Duration',
            // 对于可输入类型的组件，不能在onChange触发时就立即进行条件判断，先取原始值
            // 原因：当A字段配置的条件是控制自己本身的显隐，触发change时，会导致A字段隐藏，无法恢复，所以只能取原始值
            // getPristineValue(fieldName) 根据字段名获取字段的原始值。
            // 如果不是控制显隐，取当前值，而不是原始值，主要是「必填」、「是否可编辑」的场景
            currentValue = notVisibleChange ? record.get(realField) : record.getPristineValue(realField);
            actualValue = record.get(realField);
          } else if (widgetType === 'RichText') {
            // 'RichText' 富文本特殊处理
            currentValue = record.getPristineValue(realField);
            actualValue = record.get(realField);
            // NOTE: 第一版本富文本富文本是Quill，数据是object类型的。 第二版富文本是ckeditor，存的是html字符串
            const isNullCurrentValue = getRichTextIsNull(JSON.stringify(currentValue));
            const isNullActualValue = getRichTextIsNull(JSON.stringify(actualValue));
            // 判断quill富文本是否为空
            if (isNullCurrentValue) {
              currentValue = '';
            }
            if (isNullActualValue) {
              actualValue = '';
            }
          } else if (MOMENT_FIELDS.includes(widgetType)) {
            // 'DateTime','Date','Time', // 时间相关
            const realFormat = DEFAULT_DATE_FORMAT[widgetType];
            currentValue = record?.get(realField) && moment(record?.get(realField)).format(realFormat);
            actualValue = currentValue;
          } else if (MULTIPLE_FIELDS.includes(widgetType) && Array.isArray(toJS(record.get(realField)))) {
            // 多选值如果是数组，转为以逗号分割的字符串
            currentValue = toJS(record.get(realField)).join(',');
            actualValue = currentValue;
          } else if (widgetType === 'MasterDetail' && record.get(realField)) {
            // 判断多对一字段值为对象的情况
            if (typeof record.get(realField) === 'object') {
              currentValue = record.get(realField)?.id;
            }
            if (typeof record.get(realField) === 'string') {
              currentValue = record.get(realField);
            }
            actualValue = currentValue;
          } else {
            currentValue = record.get(realField);
            actualValue = currentValue;
          }
          if (fieldValueType === 'EXPRESSION') {
            // js表达式取值
            fieldValue = executeExpression(filterItem, record, funcConfig, realField);
          }
          if (fieldValueType === 'VARIABLE') {
            fieldValue = toJS(record.get(originFieldValue));
          }
          // 判断条件
          // // start switch
          switch (filter) { // 根据条件判断
            case OPERATOR.IS: // 判断是否相等
              newFilterFlag = operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.IS_NOT: // 判断是否不相等
              newFilterFlag = !operatorIs(currentValue, fieldValue, widgetType);
              break;
            case OPERATOR.NULL: // 判断为空
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.NOT_NULL: // 判断不为空
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IN_LIST: // 判断是否是数组
              // eslint-disable-next-line no-nested-ternary
              newFilterFlag = (typeof fieldValue === 'string') ? fieldValue?.split(',')?.includes(currentValue) : Array.isArray(fieldValue) ? fieldValue.includes(currentValue) : false;
              break;
            case OPERATOR.NOT_IN_LIST:
              // eslint-disable-next-line no-nested-ternary
              newFilterFlag = !((typeof fieldValue === 'string') ? fieldValue?.split(',')?.includes(currentValue) : Array.isArray(fieldValue) ? fieldValue.includes(currentValue) : false);
              break;
            case OPERATOR.IS_EMPTY:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_NOT_EMPTY:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.IS_EARLY_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_LATER_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_EARLY_THAN:
              newFilterFlag = !compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_NOT_LATER_THAN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.STARTS_WITH:
              newFilterFlag = compareText('start', fieldValue, currentValue);
              break;
            case OPERATOR.ENDS_WITH:
              newFilterFlag = compareText('end', fieldValue, currentValue);
              break;
            case OPERATOR.CONTAINS:
              newFilterFlag = compareText('contains', fieldValue, currentValue);
              break;
            case OPERATOR.DOES_NOT_CONTTAIN:
              newFilterFlag = !compareText('contains', fieldValue, currentValue);
              break;
            case OPERATOR.BETWEEN:
              newFilterFlag = compareTime(fieldValue, currentValue);
              break;
            case OPERATOR.IS_GREATER_THAN:
              newFilterFlag = Number(fieldValue) < Number(currentValue);
              break;
            case OPERATOR.IS_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) > Number(currentValue);
              break;
            case OPERATOR.IS_NOT_GREATER_THAN:
              newFilterFlag = Number(fieldValue) >= Number(currentValue);
              break;
            case OPERATOR.IS_NOT_SMALLER_THAN:
              newFilterFlag = Number(fieldValue) <= Number(currentValue);
              break;
            case OPERATOR.IS_CURRENT_USER:
              newFilterFlag = currentValue === funcConfig.personId;
              break;
            case OPERATOR.EXISTS:
              newFilterFlag = !!actualValue;
              break;
            case OPERATOR.DOES_NOT_EXISTS:
              newFilterFlag = !actualValue;
              break;
            case OPERATOR.IS_LIKE:
              newFilterFlag = ['true', true].includes(actualValue);
              break;
            case OPERATOR.IS_STEP:
              newFilterFlag = ['false', false].includes(actualValue);
              break;
            case OPERATOR.IS_NOTHING:
              newFilterFlag = [undefined, '', null].includes(actualValue);
              break;
            case OPERATOR.UPDATE:
              newFilterFlag = realField === fieldCode;
              break;
            case OPERATOR.RECURSIVE_IN:
              newFilterFlag = operatorRecursive(currentValue, fieldValue, widgetType, record, realField);
              break;
            case OPERATOR.RECURSIVE_NOT_IN:
              newFilterFlag = !operatorRecursive(currentValue, fieldValue, widgetType, record, realField);
              break;
            default:
              break;
          }
          // // end switch
          // switch 运算后 => newFilterFlag
          // // start filters[i].conditio
          if (RELATION.AND === filterCondition) {
            // 如果 filters[i].condition === RELATION.AND
            filterFlag = filterFlag && newFilterFlag;
          } else {
            // 如果 filters[i].condition === RELATION.OR
            filterFlag = filterFlag || newFilterFlag;
          }
          // // end
          // 这里return无意义，后改为forEach，可优化掉
          return filterItem;
        });
      }
      if (RELATION.AND === condition) {
        conditionFlag = conditionFlag && filterFlag;
      } else {
        // 当只有一组条件是，且条件是或，已这组条件计算结果为准
        if (conditions?.length === 1) {
          conditionFlag = false;
        }
        conditionFlag = conditionFlag || filterFlag;
      }
      return conditionItem;
    });
  }
  return conditionFlag;
}

/**
 * 计算表达式
 * @param parentKey
 * @param conditionMap
 * @param conditionIds
 * @param record
 * @param extraConditions
 * @param funcConfig
 * @param fieldCode 变更字段编码
 * @param notVisibleChange 是否影响布局结构改变，主要是计算显隐
 */
export function calculateConditions(parentKey, conditionMap, conditionIds, record, extraConditions, funcConfig, fieldCode, notVisibleChange) {
  let conditionFlag;
  if (conditionMap && conditionIds && record) {
    conditionIds.map(conditionId => {
      if (!conditionFlag && conditionMap[conditionId]) {
        conditionFlag = calculateCondition(parentKey, conditionMap[conditionId], record, funcConfig, fieldCode, notVisibleChange);
      }
      return conditionId;
    });
  }
  // 增加额外UI规则判断 (修改 extraConditions 判断， extraConditions为ObservableArray 影响判断，为空的时候也进来了）
  if (record && extraConditions && extraConditions.length > 0) {
    conditionFlag = calculateCondition(parentKey, extraConditions, record, funcConfig, '', notVisibleChange);
  }
  return conditionFlag;
}

export async function calculateConditions1(parentKey, conditionMap, conditionIds, record, extraConditions, funcConfig, fieldCode, notVisibleChange) {
  let conditionFlag;
  if (conditionMap && conditionIds && record) {
    conditionIds.map(async conditionId => {
      if (!conditionFlag && conditionMap[conditionId]) {
        conditionFlag = await calculateCondition1(parentKey, conditionMap[conditionId], record, funcConfig, fieldCode, notVisibleChange);
      }
      return conditionId;
    });
  }
  // 增加额外UI规则判断 (修改 extraConditions 判断， extraConditions为ObservableArray 影响判断，为空的时候也进来了）
  if (record && extraConditions && extraConditions.length > 0) {
    conditionFlag = await calculateCondition1(parentKey, extraConditions, record, funcConfig, notVisibleChange);
  }
  return conditionFlag;
}

// UI规则-执行表达式，目前前端仅支持UI规则设置值使用invoke
export function calculateExpression(expression, record, tenantId) {
  const tenantConfig = window.__yqcloudStores__?.AppState.currentMenuType || {};
  const { personId, person } = window.__yqcloudStores__?.AppState.getUserInfo || {};
  try {
    const config = `
        var personId = "${personId}";
        var person = ${JSON.stringify(person || {})};
        var _tenantId_ = "${tenantId}";
      `;
    const functionExpression = Object.keys(FUNC_BASE).reduce((pre, cur) => {
      if (expression.includes(cur)) {
        const funcBody = FUNC_BASE[cur];
        pre += Array.isArray(funcBody) ? funcBody[2] : funcBody;
      }
      return pre;
    }, config);
    // eslint-disable-next-line no-new-func
    const func = new Function(
      'axios', 'record', 'tenantConfig', 'moment',
      functionExpression + transformDesignExpression(expression)
    );
    func(axios, record, tenantConfig, moment);
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('error==>', e);
    return null;
  }
}

/**
 * 转换UI规则
 * {
 *   conditionMap: { // 根据id获取完整的UI配置
 *     conditionId: conditions,
 *   },
 *   actionMap: { // 字段渲染前，判断是否需要执行UI规则
 *     field_code: {
 *       SHOW: [conditionIds],
 *       HIDE: [conditionIds],
 *       READONLY: [conditionIds],
 *       Not_REQUIRED: [conditionIds],
 *       REQUIRED: [conditionIds],
 *     }
 *   },
 *   fieldConditionMap: { // 字段发生变化时，判断是否有要执行的UI规则
 *     condition_field_code: [conditionIds],
 *   },
 *   settingMap: { // 某个UI规则判断通过，获取需要执行的设置值配置
 *     conditionId: [
 *       {
 *         field: field_code,
 *         filter: 'is',
 *         value: fieldValue,
 *       },
 *       {
 *         field: field_code,
 *         filter: 'is null',
 *         value: undefined,
 *       },
 *       {
 *         executeType: 'EXPRESSION',
 *         expression: string,
 *       },
 *     ],
 *   },
 * }
 * @param policies
 * @param parentKey
 */
export function transformCondition(policies, parentKey) {
  const actionMap = {};
  const conditionMap = {};
  const settingMap = {};
  const fieldConditionMap = {};
  if (policies) {
    policies.map(policy => {
      const { id, action, condition, executeType, expression } = policy;
      let conditionList = [];
      let actionList;
      try {
        conditionList = JSON.parse(condition || '[]');
        actionList = JSON.parse(action || '[]');
      } catch (e) {
        conditionList = [];
        actionList = [];
      }
      conditionMap[id] = conditionList;
      // 拼接fieldConditionMap，条件字段变化时，才触发condition判断
      conditionList.map(conditionItem => {
        const { filters } = conditionItem;
        filters?.map(filterItem => {
          const { field } = filterItem;
          const realField = parentKey ? `${parentKey}.${field}` : field;
          if (fieldConditionMap[realField]) {
            fieldConditionMap[realField] = fieldConditionMap[realField].filter(cId => cId !== id);
            fieldConditionMap[realField].push(id);
          } else {
            fieldConditionMap[realField] = [id];
          }
          return filterItem;
        });
        return conditionItem;
      });

      if (executeType === 'EXPRESSION') {
        const expressionConfig = {
          executeType,
          expression,
        };
        if (settingMap[id]) {
          settingMap[id].push(expressionConfig);
        } else {
          settingMap[id] = [expressionConfig];
        }
      } else {
        actionList.map(actionItem => {
          const { attributes, field, widgetType, fieldValue, fieldLovValue, filter } = actionItem;
          if (attributes === ACTION.SETTINGS) {
            // 设置值
            const realField = parentKey ? `${parentKey}.${field}` : field;
            const settingConfig = {
              field: realField,
              filter,
              fieldValue,
              widgetType,
              fieldLovValue,
            };
            if (settingMap[id]) {
              settingMap[id].push(settingConfig);
            } else {
              settingMap[id] = [settingConfig];
            }
          } else if (attributes) {
            // 控制字段必输、显示隐藏等
            fieldValue?.map(fieldCode => {
              const realFieldCode = parentKey ? `${parentKey}.${fieldCode}` : fieldCode;
              if (actionMap[realFieldCode]) {
                if (actionMap[realFieldCode][attributes]) {
                  actionMap[realFieldCode][attributes].push(id);
                } else {
                  actionMap[realFieldCode][attributes] = [id];
                }
              } else {
                actionMap[realFieldCode] = { [attributes]: [id] };
              }
              return fieldCode;
            });
          }
          return actionItem;
        });
      }
      return policy;
    });
  }

  return {
    actionMap,
    conditionMap,
    settingMap,
    fieldConditionMap,
  };
}

const RULE = {
  UNIQUE: 'unique',
  REGEXP: 'regexp',
  MAX_LENGTH: 'maxLength',
  EQUALS: 'equals',
  RANGE: 'range',
};

export const RULE_TYPE = {
  ASSOCIATION: 'association',
  FIXED: 'fixed',
};

/**
 * 转换校验规则
 * @param rules
 * @returns {{maxLengthMap: *, uniqueMap: *, minRangeMap: *, equalsMap: *, maxRangeMap: *, regexpMap: *}}
 */
export function transformRule(rules) {
  const uniqueMap = {};
  const regexpMap = {};
  const maxLengthMap = {};
  const equalsMap = {};
  const maxRangeMap = {};
  const minRangeMap = {};

  function getUniqueFieldList(objectAttributeCode) {
    const uniqueFieldsList = [];
    const uniqueRules = rules?.filter(rule => rule.type === RULE.UNIQUE) || [];
    const fieldList = uniqueRules?.filter(rule => rule.objectAttributeCode === objectAttributeCode) || [];
    const uniquePrefixList = Array.from(new Set(fieldList?.map(rule => rule.uniquePrefix)));
    uniquePrefixList.map(uniquePrefix => {
      if (uniquePrefix) {
        const uniqueFieldList = uniqueRules.filter(rule => rule.uniquePrefix === uniquePrefix) || [];
        uniqueFieldsList.push(uniqueFieldList.map(rule => rule.objectAttributeCode) || []);
      } else {
        uniqueFieldsList.push([objectAttributeCode]);
      }
      return uniquePrefix;
    });
    return uniqueFieldsList;
  }

  if (rules) {
    rules.map(rule => {
      const { type, objectAttributeCode, errorMessage, jsonData } = rule;
      const { regexpConfig, maxLengthConfig, equalsConfig, maxConfig, minConfig } = jsonData || {};
      if (objectAttributeCode) {
        switch (type) {
          case RULE.UNIQUE:
            uniqueMap[objectAttributeCode] = {
              unique: getUniqueFieldList(objectAttributeCode),
              errorMessage,
            };
            break;
          case RULE.REGEXP:
            if (regexpConfig?.value) {
              regexpMap[objectAttributeCode] = {
                regexp: regexpConfig?.value,
                errorMessage,
              };
            }
            break;
          case RULE.MAX_LENGTH:
            if (maxLengthConfig?.value) {
              maxLengthMap[objectAttributeCode] = {
                maxLength: maxLengthConfig?.value,
                errorMessage,
              };
            }
            break;
          case RULE.EQUALS:
            if (equalsConfig?.ruleType) {
              equalsMap[objectAttributeCode] = {
                ...equalsConfig,
                errorMessage,
              };
            }
            break;
          case RULE.RANGE:
            if (maxConfig?.flag) {
              maxRangeMap[objectAttributeCode] = {
                ...maxConfig,
                errorMessage,
              };
            }
            if (minConfig?.flag) {
              minRangeMap[objectAttributeCode] = {
                ...minConfig,
                errorMessage,
              };
            }
            break;
          default:
            break;
        }
      }
      return rule;
    });
  }
  return {
    uniqueMap,
    regexpMap,
    maxLengthMap,
    equalsMap,
    maxRangeMap,
    minRangeMap,
  };
}

// 页面视图

export const FOCUS_FIELD_TYPE = ['Input', 'TextArea', 'RichText', 'Password', 'Email', 'NumberField', 'FloatNumber', 'AutoNumber'];

/**
 * 控制section
 * @param record
 * @param current
 * @param mode
 * @param parentKey
 */
export function calculateHide(record, current, mode, parentKey, funcConfig) {
  const tag = record.get('tag');
  const visibleType = record.get('visibleType');
  const permissionFlag = record.get('permissionFlag');
  const visibleAction = record.get('visibleAction');
  const visibleCondition = record.get('visibleCondition');
  if (visibleType && ['Section', 'Attachment', 'QRCode', 'Tab', 'Button', 'CustomButton'].includes(tag)) {
    let hideFieldFlag = visibleType === 'ALWAYS_NOT_VISIBLE';
    if (visibleType === 'CONDITION') {
      if (permissionFlag !== false || mode === 'PREVIEW') {
        // UI规则校验
        if (visibleAction === ACTION.HIDE && visibleCondition?.length) {
          // section 的显隐可以通过其他字段进行显隐控制
          hideFieldFlag = calculateConditions(parentKey, false, false, current, visibleCondition, funcConfig, '', tag === 'Section');
        }
        if (visibleAction === ACTION.SHOW && visibleCondition?.length) {
          hideFieldFlag = !calculateConditions(parentKey, false, false, current, visibleCondition, funcConfig, '', tag === 'Section');
        }
      } else {
        // 当无权限时，不再进行UI规则判断
        hideFieldFlag = true;
      }
    }
    return hideFieldFlag;
  }
  return false;
}

export async function calculateHide1(record, current, mode, parentKey, funcConfig, notVisibleChange) {
  const tag = record.get('tag');
  const visibleType = record.get('visibleType');
  const permissionFlag = record.get('permissionFlag');
  const visibleAction = record.get('visibleAction');
  const visibleCondition = record.get('visibleCondition');
  if (visibleType && ['Section', 'Attachment', 'QRCode', 'Tab', 'Button', 'CustomButton', 'AssetServiceItemJumpBtn'].includes(tag)) {
    let hideFieldFlag = visibleType === 'ALWAYS_NOT_VISIBLE';
    if (visibleType === 'CONDITION') {
      if (permissionFlag !== false || mode === 'PREVIEW') {
        // UI规则校验
        if (visibleAction === ACTION.HIDE && visibleCondition?.length) {
          hideFieldFlag = await calculateConditions1(parentKey, false, false, current, visibleCondition, funcConfig, notVisibleChange);
        }
        if (visibleAction === ACTION.SHOW && visibleCondition?.length) {
          hideFieldFlag = !(await calculateConditions1(parentKey, false, false, current, visibleCondition, funcConfig, notVisibleChange));
        }
      } else {
        // 当无权限时，不再进行UI规则判断
        hideFieldFlag = true;
      }
    }
    return hideFieldFlag;
  }
  return false;
}

/**
 * 替换url中的变量
 * @param url
 * @param record
 * @param prefix
 * @returns {*}
 */
export function formatUrlParams(url, record, prefix = '$') {
  if (!url || !record) {
    return url;
  }
  const urlList = url.split('/')?.map(item => {
    if (item.startsWith(prefix)) {
      return record.get(item.substr(1))?.id || record.get(item.substr(1));
    }
    return item;
  });
  return urlList.join('/');
}

function getRootSection(sections, section) {
  const parentSection = sections.find(s => s.id === section.parentId);
  if (parentSection?.parentId) {
    return getRootSection(sections, parentSection);
  }
  return parentSection;
}

/**
 * 1、根据Section中的字段将dataSet字段补全
 * 2、处理变量集中的字段
 * @param jsonData
 */
export function transformJsonData(jsonData) {
  const variableSetFields = [];
  const dsFieldMap = {};
  const sections = jsonData.sections.map(section => {
    // 对于变量集，将变量集字段放到formDataSet中
    if (section.variableSetFlag && section.parentId) {
      const rootSection = getRootSection(jsonData.sections, section);
      section.fields = section.fields?.map(field => {
        const widgetConfig = JSON.parse(JSON.stringify(field?.widgetConfig).replace(new RegExp(field?.code, 'g'), `${rootSection.code}.${field.code}`));
        variableSetFields.push({
          ...field,
          widgetConfig,
          code: `${rootSection.code}.${field.code}`,
          variableSetFlag: true, // 标识为变量集下的字段
        });
        return simplifyField(field);
      });
      return {
        ...section,
        code: rootSection.code,
      };
    } else {
      // 变量视图中所有字段，用于替换DataSet中的字段
      section.fields = section.fields?.map(field => {
        if (field.tag === 'Field') {
          dsFieldMap[`${field.id}-${field.code}`] = field;
        } else if (field.tag === 'Table') {
          // 获取表格中的字段
          const { id, widgetConfig: { fields } } = field;
          fields.map(tField => {
            dsFieldMap[`${id}-${tField.code}`] = tField;
            return tField;
          });
        }
        return simplifyField(field);
      });
    }
    return section;
  });
  const datasets = jsonData.datasets.map(dataset => {
    if (dataset.tag === 'Form') {
      // 补全formDataSet中字段配置
      // 注意：不在视图中的字段（即页面视图中的字段）先不要初始化到ds中，后续加载页面视图配置后会addField到ds中
      const fields = [];
      dataset.fields.map(field => {
        if (field && ['VariableSet', 'Input'].includes(field.widgetType)) {
          fields.push(field);
        }
        if (field.code && dsFieldMap[`${field.id}-${field.code}`]) {
          fields.push(dsFieldMap[`${field.id}-${field.code}`]);
        }
        return field;
      });
      return {
        ...dataset,
        fields: [
          ...fields,
          ...variableSetFields,
        ],
      };
    } else if (dataset.tag === 'Table') {
      // 补全表格dataSet中字段配置
      const fields = dataset.fields.map(field => {
        if (field.code && dsFieldMap[`${dataset.id}-${field.code}`]) {
          return dsFieldMap[`${dataset.id}-${field.code}`];
        }
        return field;
      });
      return {
        ...dataset,
        fields,
      };
    }
    return dataset;
  });
  return {
    ...jsonData,
    sections,
    datasets,
  };
}

const HAS_CONDITION = [
  'MobileTable',
  'Table',
  'ClewToneButton',
  'MasterDetail',
  'MultipleSelect',
  'Radio',
  'Select',
  'SelectBox',
];

/**
 * 根据视图配置，计算dsFields、datasets、expressionButtons
 * @param sections
 * @param dsFields
 * @param datasets
 * @param expressionButtons
 */
function getConfigFromSections(
  sections = [],
  dsFields = [],
  datasets = [],
  expressionButtons = [],
) {
  sections.map(section => {
    // 变量集：仅将变量集本身加入ds，变量集下的字段不处理
    if (section?.variableSetFlag) {
      if (section?.variableId) {
        dsFields.push({
          id: uuidv4(),
          code: section.code,
          widgetType: 'VariableSet',
        });
      }
      return section;
    }
    // 处理section中的字段
    section.fields.map(field => {
      if (field.tag === 'Field' || field.tag === 'VariablePage') {
        // 普通字段或变量视图
        if (field.widgetType === 'MasterDetail') {
          let nameFieldCode = field?.widgetConfig?.relationLovNameFieldCode;
          // 当值列表的业务对象为中间表时，需要特殊处理
          if (field?.widgetConfig?.relationLovBusinessObjectId !== field?.relationObjectId) {
            const nameFieldList = nameFieldCode?.split(':');
            if (nameFieldList?.length > 1) {
              nameFieldList.shift();
              nameFieldCode = nameFieldList.join(':');
            }
          }
          // m2o字段需要将名称字段也加入到ds
          dsFields.push({
            id: uuidv4(),
            widgetType: 'Input',
            code: `${field.code}:${nameFieldCode || 'name'}`,
          });
        }
        if (field.widgetType === 'Cascader') {
          // 级联组件类型，保存时添加 所选的级联字段
          field.widgetConfig.cascadeFields?.forEach
            && field.widgetConfig.cascadeFields?.forEach(item => {
              dsFields.push({
                id: uuidv4(),
                code: item.value,
                widgetType: 'Select',
              });
            });
        }
        dsFields.push({
          id: field.id,
          widgetType: field.widgetType,
          code: field.code,
          widgetConfig: {
            fieldAction: field.widgetConfig?.fieldAction,
          },
        });
      } else if (field.tag === 'Table') {
        // 表格
        const {
          id,
          widgetConfig: {
            fields,
            filters,
            filterFlag,
            pageSize,
            selection,
            modelId,
            parentFieldCode,
            relatedFieldCode,
            condition = [],
            orderBy = [],
            buttons,
            lineButtons,
            autoQueryFlag,
            preciseQueryFlag,
            followPageRefreshFlag,
            tableLinks,
          },
        } = field;
        // 对于需要执行表达式或工作流按钮，需要单独放在expressionButtons中供后端使用
        [...(buttons || []), ...(lineButtons || [])]?.map(btn => {
          const { type: btnType, id: btnId, expression = '' } = btn;
          if (btnType === 'EXPRESSION') {
            expressionButtons.push({
              id: btnId,
              expression,
              type: 'EXPRESSION',
            });
          }
          if (btnType === 'WORKFLOW') {
            expressionButtons.push({
              id: btnId,
              code: btn?.workflowId?.code,
              type: 'WORKFLOW',
            });
          }
          return btn;
        });
        // 对于头行结构的页面，需要将关联字段加入表格搜索字段中
        const fuzzySearch = filters?.filter(filter => filter.filterFlag) || [];
        if (relatedFieldCode) {
          fuzzySearch.push({
            filterFlag: true,
            code: relatedFieldCode,
            widgetType: 'MasterDetail',
          });
        }
        // 生成表格ds字段
        const tFields = [];
        fields.map(tField => {
          if (tField.widgetType === 'MasterDetail') {
            let nameFieldCode = tField?.widgetConfig?.relationLovNameFieldCode;
            // 当值列表的业务对象为中间表时，需要特殊处理
            if (tField?.widgetConfig?.relationLovBusinessObjectId !== tField?.relationObjectId) {
              const nameFieldList = nameFieldCode?.split(':');
              if (nameFieldList?.length > 1) {
                nameFieldList.shift();
                nameFieldCode = nameFieldList.join(':');
              }
            }
            // m2o字段需要将名称字段也加入到ds
            dsFields.push({
              id: uuidv4(),
              widgetType: 'Input',
              code: `${tField.code}:${nameFieldCode || 'name'}`,
            });
          }
          // 日期范围类型， 保存时添加 开始和结束的时间字段
          if (tField.widgetType === 'Range') {
            dsFields.push({
              code: tField.widgetConfig.startFieldCode,
              widgetType: 'DateTime',
              widgetConfig: {
                format: 'YYYY-MM-DD HH:mm:ss',
              },
            });
            dsFields.push({
              code: tField.widgetConfig.endFieldCode,
              widgetType: 'DateTime',
              widgetConfig: {
                format: 'YYYY-MM-DD HH:mm:ss',
              },
            });
          }
          if (tField.widgetType === 'Cascader') {
            // 级联组件类型，保存时添加 所选的级联字段
            field.widgetConfig.cascadeFields?.forEach
              && field.widgetConfig.cascadeFields?.forEach(item => {
                dsFields.push({
                  code: item.value,
                  widgetType: 'Select',
                });
              });
          }

          tFields.push({
            id: tField.id,
            widgetType: tField.widgetType,
            code: tField.code,
            widgetConfig: {
              fieldAction: tField.widgetConfig?.fieldAction,
            },
          });
          return tField;
        });
        // 将条件跳转中的字段也放入datasets，用于判断
        tableLinks?.forEach(tField => {
          const { condition: tableLinkCondition } = tField;
          tableLinkCondition.forEach(data => {
            const { filters: linkFilters } = data;
            linkFilters.forEach(filter => {
              if (!tFields.some(t => t.code === filter.field)) {
                tFields.push({
                  id: filter.filterUuid,
                  widgetType: filter.widgetType,
                  code: filter.field,
                });
              }
            });
          });
        });
        datasets.push({
          businessObjectId: modelId,
          id,
          tag: 'Table',
          fields: tFields,
          fuzzySearch,
          pageSize,
          selection,
          filterFlag,
          parentFieldCode,
          relatedFieldCode,
          condition,
          orderBy,
          autoQuery: autoQueryFlag === undefined || autoQueryFlag,
          preciseQuery: preciseQueryFlag, // 精确查询
          followPageRefreshFlag, // 跟随页面刷新
        });
      } else if (field.tag === 'Tree') {
        const {
          widgetConfig: {
            parentObjectId, childObjectId, relatedFieldId, childButtons, parentButtons,
          }, id } = field;
        const childDataSetId = uuidv4();
        // 对于需要执行表达式或工作流按钮，需要单独放在expressionButtons中供后端使用
        [...(childButtons || []), ...(parentButtons || [])]?.map(btn => {
          const { type: btnType, id: btnId, expression = '' } = btn;
          if (btnType === 'EXPRESSION') {
            expressionButtons.push({
              id: btnId,
              expression,
              type: 'EXPRESSION',
            });
          }
          if (btnType === 'WORKFLOW') {
            expressionButtons.push({
              id: btnId,
              code: btn?.workflowId?.code,
              type: 'WORKFLOW',
            });
          }
          return btn;
        });
        datasets.push({
          businessObjectId: parentObjectId,
          id,
          tag: 'Tree',
          fields: [],
          fuzzySearch: [],
          childDataSetId,
          relatedFieldId,
        });
        if (parentObjectId !== childObjectId) {
          datasets.push({
            businessObjectId: childObjectId,
            id: childDataSetId,
            tag: 'Tree',
            fields: [],
            fuzzySearch: [],
            relatedFieldId,
          });
        }
      } else if (field.tag === 'ServiceItemPage') {
        dsFields.push({
          id: uuidv4(),
          widgetType: 'Lov',
          code: 'service_item_id',
        });
      } else if (field.tag === 'Page' && field.widgetConfig?.relatedPrimaryKey) {
        dsFields.push({
          id: uuidv4(),
          widgetType: 'Lov',
          code: field.widgetConfig?.relatedPrimaryKey,
        });
      }
      return field;
    });
    return section;
  });
}

/**
 * 1、生成expressionButtons列表
 * 2、根据jsonData生成DataSetList
 * @param data
 * @param needJson
 */
export function transformPostData(data, needJson = true) {
  const { businessObjectId, id: dsId } = data;
  const jsonData = typeof (data.jsonData) === 'string' ? JSON.parse(data.jsonData) : data.jsonData;
  const dsFields = []; // 表单ds字段
  const datasets = []; // ds列表
  const expressionButtons = [];
  // 对于需要执行表达式或工作流按钮，需要单独放在expressionButtons中供后端使用
  [...(jsonData?.buttons || []), ...(jsonData?.actions || [])].map(btn => {
    const { type: btnType, id: btnId, expression = '' } = btn;
    if (btnType === 'EXPRESSION') {
      expressionButtons.push({
        id: btnId,
        expression,
        type: 'EXPRESSION',
      });
    }
    if (btnType === 'WORKFLOW') {
      expressionButtons.push({
        id: btnId,
        code: btn?.workflowId?.code,
        type: 'WORKFLOW',
      });
    }
    return btn;
  });
  // 根据视图配置，计算dsFields、datasets、expressionButtons
  getConfigFromSections(jsonData?.sections, dsFields, datasets, expressionButtons);
  // 添加表单的ds
  datasets.push({
    id: dsId,
    businessObjectId,
    tag: 'Form',
    fields: dsFields,
    fuzzySearch: [],
  });

  // START: 处理多余的dataSet的数据
  jsonData.sections.forEach((section) => {
    section?.fields?.forEach((field) => {
      if (field.widgetType !== 'MasterDetail') {
        delete field.widgetConfig?.mappingField;
        delete field.widgetConfig?.variableFilter;
      }
      if (['CustomButton', 'Custom', 'TreeLov'].every(cmp => field.widgetType !== cmp)) {
        delete field.widgetConfig?.customConfig;
      }
      if (field.widgetType !== 'MobileTable' && field.widgetType !== 'Table') {
        delete field.widgetConfig?.actions;
        delete field.widgetConfig?.filters;
        delete field.widgetConfig?.tableLinks;
        delete field.widgetConfig?.buttons;
        delete field.widgetConfig?.fields;
        delete field.widgetConfig?.lineButtons;
      }
      if (!HAS_CONDITION.includes(field.widgetType)) {
        delete field.widgetConfig?.condition;
      }
      if (field.widgetType !== 'Chart') {
        delete field.widgetConfig?.chartParams;
      }
      if (field.widgetType !== 'VariableTable') {
        delete field.widgetConfig?.variableFields;
      }
    });
  });
  // END: 处理多余的dataSet的数据

  // 返回完整视图数据
  if (!needJson) {
    return {
      ...data,
      jsonData: {
        ...jsonData,
        datasets,
        expressionButtons,
      },
    };
  }
  return {
    ...data,
    jsonData: JSON.stringify({
      ...jsonData,
      datasets,
      expressionButtons,
    }),
  };
}

export class ModalAction {
  handleOk({ viewMode, dataSet, parentDataSet }) {
    return async () => {
      if (viewMode === 'READONLY') {
        return true;
      }
      if (await dataSet?.validate()) {
        await dataSet?.submit();
        if (parentDataSet) {
          parentDataSet?.query();
        }
        return true;
      }
      return false;
    };
  }

  handleClose({ viewMode, dataSet, intl, parentDataSet, viewRecord }) {
    return async () => {
      if (viewMode !== 'READONLY') {
        // 当前数据中修改的字段如果只是因为UI规则导致改变，则不需要弹出提示
        const dirtyField = [];
        // 先获取所有修改的字段
        dataSet?.updated.forEach(record => {
          const { dirtyData } = record;
          if (dirtyData && dirtyData.size) {
            dirtyData.forEach((value, key) => {
              dirtyField.push(key);
            });
          }
        });
        const policiesData = toJS(viewRecord?.getState('policiesData'));
        let isAnyDirtyFieldNotInSettingMap = false;
        if (policiesData && policiesData.settingMap) {
          // 检查 dirtyField 中是否有字段不在 settingMap 中
          const { settingMap } = policiesData;
          isAnyDirtyFieldNotInSettingMap = dirtyField
            .filter(field => field !== '__dirty')
            .some(field => {
              return !Object.values(settingMap).some(settingArray => {
                return settingArray.some(setting => setting.field === field);
              });
            });
        }

        if (isAnyDirtyFieldNotInSettingMap) {
          return Modal.confirm({
            title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
            children: (
              <div>
                <p>{intl.formatMessage({ id: 'lcr.components.desc.close.tip', defaultMessage: '当前页面修改还未保存，确认关闭吗？' })}</p>
              </div>
            ),
          }).then(async (button) => {
            if (button === 'ok') {
              if (viewMode === 'READONLY') {
                return true;
              }
              await dataSet?.reset();
              // await parentDataSet?.reset();
              return true;
            } else {
              return false;
            }
          });
        } else {
          await dataSet?.reset();
          // await parentDataSet?.reset();
          return true;
        }
      } else {
        await dataSet?.reset();
        // await parentDataSet?.reset();
        return true;
      }
      // if (viewMode !== 'READONLY' && dataSet?.updated.length > 0) {
      //   return Modal.confirm({
      //     title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
      //     children: (
      //       <div>
      //         <p>{intl.formatMessage({ id: 'lcr.components.desc.close.tip', defaultMessage: '当前页面修改还未保存，确认关闭吗？' })}</p>
      //       </div>
      //     ),
      //   }).then(async (button) => {
      //     if (button === 'ok') {
      //       if (viewMode === 'READONLY') {
      //         return true;
      //       }
      //       await dataSet?.reset();
      //       // await parentDataSet?.reset();
      //       return true;
      //     } else {
      //       return false;
      //     }
      //   });
      // } else {
      //   await dataSet?.reset();
      //   // await parentDataSet?.reset();
      //   return true;
      // }
    };
  }
}

export async function validateMessage(dataSet, intl, scPageRef) {
  const result = await dataSet?.current?.validate(true);
  const [flag, _ds] = await validateServiceItemPage(scPageRef, true);
  if (!result || !flag) {
    const ds = !result ? dataSet : _ds;
    const validationErrors = ds.getValidationErrors();
    if (validationErrors?.length) {
      const errors = validationErrors[0]?.errors;
      if (errors?.length) {
        let messageLabels = []; // 必填限制
        let customErrorLabels = []; // 自定义校验
        const longLabels = []; // 长度限制
        errors.forEach(v => {
          if (v?.errors[0]?.ruleName === 'customError') {
            customErrorLabels.push(v?.errors[0]?.$validationMessage);
          }
          if (v?.errors[0]?.injectionOptions?.label) {
            messageLabels.push(v?.errors[0]?.injectionOptions?.label);
          }
          if (v?.errors[0]?.ruleName === 'tooLong') {
            longLabels.push(v?.field?.get('label'));
          }
        });
        // 标准校验提示
        if (messageLabels.length > 0) {
          // eslint-disable-next-line no-chinese/no-chinese
          messageLabels = messageLabels.toString().replace(/,/g, '、');
          message.error(intl.formatMessage({ id: 'lcr.components.desc.required.tips.less', defaultMessage: '请填写{fields}字段' }, { fields: `${messageLabels}` }));
        }
        // 自定义校验提示
        if (customErrorLabels.length > 0) {
          // eslint-disable-next-line no-chinese/no-chinese
          customErrorLabels = [...new Set(customErrorLabels)].toString().replace(/,/g, '、');
          message.error(intl.formatMessage({ id: `${customErrorLabels}` }));
        }
        if (longLabels.length > 0) {
          // eslint-disable-next-line no-chinese/no-chinese
          message.error(intl.formatMessage({ id: 'lcr.components.desc.too.long.tips.less', defaultMessage: '{fields} 字段超出最大长度限制' }, { fields: `${longLabels.join('、')}` }));
        }
      }
    }
  }
  return result && flag;
}

export function getCascadeList(list = []) {
  // 给选项集的父子层级添加__level参数
  list.forEach && list.forEach(item => {
    if (item.parentId === '0') {
      item.__level = 0;
    } else {
      let level = 0;
      let current = item;
      while (current.parentId !== '0') {
        level += 1;
        // eslint-disable-next-line no-loop-func
        current = list.find(_item => _item.id === current.parentId);
      }
      item.__level = level;
    }
  });
  return list;
}

export function handleBack(lastViewId, history) {
  let list = [];
  try {
    list = JSON.parse(sessionStorage.getItem('lastViews'));
  } catch (e) {
    list = [];
  }
  let url;
  let filter;
  if (list?.length && lastViewId) {
    url = list?.[list.length - 1];
    while (url?.split('?')?.[0] === window.location.hash?.split('?')?.[0]) {
      const searchParams = qs.parse(window.location.hash);
      filter = searchParams?.filter || filter;
      list.pop();
      url = list?.[list.length - 1];
    }
    try {
      sessionStorage.setItem('lastViews', JSON.stringify(list));
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log('sessionStorage not support');
    }
    let backPath = url.split('#')[1];
    const [path, search] = backPath?.split('?');
    backPath = `${path}?${qs.stringify({
      ...qs.parse(search),
      filter,
    })}`;
    if (backPath.includes('new')) {
      // 如果上一个视图为新建视图，则跳跳过，再向上查询跳转
      history.push({
        pathname: `/lc/engine/${lastViewId}`,
        search: history.location?.search,
      });
    } else {
      history.push(backPath);
    }
  } else {
    return false;
  }
}

/**
 * 字段默认值转换
 * @param fieldDto
 * @param defaultData
 * @returns {{[p: string]: *}}
 */
export function calculateFieldDefaultValue(fieldDto, defaultData) {
  const { widgetType, code } = fieldDto || {};
  const { relationLovNameFieldCode = 'name', idField = 'id' } = fieldDto?.widgetConfig || {};
  let defaultValue;
  if (defaultData[code]) {
    // 对于多对一字段，需要将名称字段也设置默认值
    if (widgetType === 'MasterDetail' && defaultData[code]) {
      if (typeof defaultData[code] === 'object') {
        // 如果默认值为对象，如 { idField: 1, nameField: 'a' }
        // id为空，则默认值设为空
        defaultValue = defaultData[code][idField] ? {
          [idField]: defaultData[code][idField],
          [relationLovNameFieldCode]: defaultData[code][relationLovNameFieldCode],
        } : undefined;
      } else {
        // 如果默认值为id，则尝试从默认值获取名称字段fieldCode:nameFieldCode
        defaultValue = {
          [idField]: defaultData[code],
        };
        if (defaultData[`${code}:${relationLovNameFieldCode}`]) {
          defaultValue[relationLovNameFieldCode] = defaultData[`${code}:${relationLovNameFieldCode}`];
        }
      }
    } else if (MULTIPLE_FIELDS.includes(widgetType)) {
      // 对于多选字段，转换为逗号分割的字符串
      const value = defaultData[code];
      try {
        const jsonValue = JSON.parse(value);
        if (Array.isArray(jsonValue)) {
          defaultValue = jsonValue.join(',');
        } else {
          defaultValue = value;
        }
      } catch (e) {
        defaultValue = value;
      }
    } else if (BOOLEAN_FIELDS.includes(widgetType)) {
      const value = defaultData[code];
      defaultValue = typeof value === 'string' ? value === 'true' : value;
    } else {
      defaultValue = defaultData[code];
    }
  } else if (widgetType === 'Input' && code.includes(':') && !defaultData[code]) {
    // 多对一字段默认值为对象时，需要给名称字段也赋一下默认值，用于展示
    // 尝试对名称字段进行截断，从默认值中获取对应的多对一字段，如：
    // user_id: { idField: 1, nameField: 'a' } => user_id:nameField = user_id.nameField
    const fieldCodeList = code.split(':');
    const nameFieldCode = fieldCodeList.pop();
    const originFieldCode = fieldCodeList.join(':');
    if (defaultData[originFieldCode] && defaultData[originFieldCode][nameFieldCode]) {
      defaultValue = defaultData[originFieldCode][nameFieldCode];
    }
  }
  return defaultValue;
}

export function getWidgetData(jsonData, id) {
  let data;
  toJS(jsonData)?.sections.forEach(section => {
    const findData = section?.fields?.find(field => field.id === id);
    if (findData) {
      data = findData;
    }
  });
  return data;
}

export function getSectionData(jsonData, id) {
  let data;
  toJS(jsonData)?.sections?.forEach(section => {
    if (section.id === id) {
      data = section;
    }
  });
  return data;
}

/**
 * 安全的 truthy 和 falsey 判断
 *   1、'1'、true、'true' 应该判断为 true
 *   'false'、0、'0' 已经所有 falsey 值都返回 false
 *   undefined 和 null 返回 undefined，说明本来没有值
 * 应用于【点赞\踩】
 * @param value
 * @returns {boolean}
 */
export function safeBoolean(value) {
  if (typeof value === 'boolean') {
    return value;
  } else if ([1, '1', 'true'].includes(value)) {
    return true;
  } else if ([undefined, null].includes(value)) {
    return undefined;
  }
  return false;
}
