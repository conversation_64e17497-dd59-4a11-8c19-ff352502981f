import React, { useMemo } from 'react';
import { withRouter } from 'react-router-dom';
import { ConfigProvider } from 'choerodon-ui';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import _ from 'lodash';
import { StoreProvider } from './stores';
import { transformResponse } from './lovConfig';
import { transformField } from './stores/DataSetManager';
import MainView from './MainView';
import './components/layout-register/layouts';
import '@/components/editor-register/editors';

import './index.less';

export default inject('AppState')(formatterCollections({ code: ['lcr.components'] })(injectIntl((withRouter((props) => {
  const { AppState: { currentMenuType: { organizationId: tenantId } }, viewId, intl } = props;

  const currentView = window.location.hash;
  try {
    if (sessionStorage.getItem('lastViews')) {
      const list = JSON.parse(sessionStorage.getItem('lastViews'));
      if (currentView !== list[list.length - 1]) {
        list.push(currentView);
        sessionStorage.setItem('lastViews', JSON.stringify(list));
      }
    } else {
      sessionStorage.setItem('lastViews', JSON.stringify([currentView]));
    }
  } catch (e) {
    // eslint-disable-next-line no-console
    console.error('sessionStorage not support');
  }

  const lovDefineBatchAxiosConfig = (codes) => {
    return {
      url: `/lc/v1/${tenantId}/object_options/id/batch`,
      method: 'GET',
      params: codes.reduce((obj, val) => {
        obj[val] = val;
        return obj;
      }, {}),
      transformResponse: data => {
        const lovCodeData = {};
        const jsonData = JSON.parse(data);
        Object.keys(jsonData).forEach(lovCode => {
          const lovData = jsonData[lovCode];
          const transformData = transformResponse(
            JSON.stringify(lovData),
            lovData?.name,
            (map, f) => transformField(
              {
                fieldMap: map,
                field: f,
                viewId,
                tenantId,
                intl,
              },
            ),
            intl,
            tenantId,
          );
          lovCodeData[lovCode] = transformData;
        });
        return lovCodeData;
      },
    };
  };

  const useLovDefineBatch = (code, field) => {
    // 只有低代码的值列表走批量接口
    const lovObj = field && field.props && field.props.get('lovDefineAxiosConfig') && field.props.get('lovDefineAxiosConfig')();
    if (lovObj?.url?.includes('object_options/id')) {
      return true;
    }
    return false;
  };

  const mainView = useMemo(() => {
    return (
      <ConfigProvider lovDefineBatchAxiosConfig={lovDefineBatchAxiosConfig} useLovDefineBatch={useLovDefineBatch}>
        <StoreProvider {...props}>
          <MainView />
        </StoreProvider>
      </ConfigProvider>
    );
  }, Object.keys(_.omit(props, ['modal'])).map(key => props[key]));

  return mainView;
})))));

/* externalize: PageLoader */
