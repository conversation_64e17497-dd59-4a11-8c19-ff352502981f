import { createElement } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import LayoutRegister from './components/layout-register';

export default observer((props) => {
  // tableLineRecord 表格行数据，用于行内自定义按钮
  const { record, dataSet, tableLineRecord, context } = props;
  const tag = record.get('tag') || 'Section';
  const layout = LayoutRegister.get(tag);

  if (layout) {
    const { preview } = layout;
    return createElement(preview, {
      className: classNames('preview-item', tag),
      key: record.get('id'),
      record,
      dataSet,
      tableLineRecord,
      context,
      ...props,
    });
  }
  return null;
});
