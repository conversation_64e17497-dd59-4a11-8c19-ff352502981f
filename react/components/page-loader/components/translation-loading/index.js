/**
 * 工单翻译，全局翻译时，会完全覆盖单据详情
 */
import React, { useContext } from 'react';
import { withErrorBoundary, Button } from '@zknow/components';
import styles from './TranstionLoading.module.less';
import Store from '../../stores';

function TranslateLoading() {
  const { mainStore, intl } = useContext(Store);

  const handleClick = () => {
    mainStore.setTranslateLoading(false);
    mainStore.setTranslateManualCancel(true);
  };

  return (
    <div className={styles.wrap}>
      <div className={styles.inner}>
        <img
          className={styles.image}
          src={`${window._env_.ICON_SERVER}/static/intelligent-loading-min.gif`}
          alt="ai_loading"
        />
        <h3 className={styles.title}>{intl.formatMessage({ id: 'lcr.components.desc.translate.running', defaultMessage: '智能翻译中' })}</h3>
        <p className={styles.text}>{intl.formatMessage({ id: 'lcr.components.desc.translate.description', defaultMessage: '文本识别需要您稍作等待，AI数字助理正在全力翻译，请不要关闭页面或点击取消' })}</p>
        <Button onClick={handleClick}>{intl.formatMessage({ id: 'lcr.components.desc.translate.button.cancel', defaultMessage: '取消翻译' })}</Button>
      </div>
    </div>
  );
}

export default withErrorBoundary(TranslateLoading, { fallback: <span /> });
