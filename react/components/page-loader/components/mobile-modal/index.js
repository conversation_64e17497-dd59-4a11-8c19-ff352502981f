import React from 'react';
import { observer } from 'mobx-react-lite';
import { Alert } from 'choerodon-ui';
import { TextField, Form } from 'choerodon-ui/pro';
import './index.less';

export default observer((props) => {
  const { modal, record, mobileFields, intl, AppState } = props;

  function call(code) {
    const iCall = AppState?.getICall?.ref?.call;
    const phoneNumber = record.get(code);
    if (phoneNumber && iCall) {
      iCall(String(phoneNumber));
    }
    modal.close();
  }

  return (
    <div>
      <Alert
        message={intl.formatMessage({ id: 'lcr.components.desc.mobile.tips', defaultMessage: '请点击需要进行拨号的手机号' })}
        type="info"
      />
      <Form
        record={record}
        className="lc-mobile-form"
        labelWidth="auto"
      >
        {mobileFields.map(f => (
          <TextField
            name={f.get('code')}
            onClick={() => call(f.get('code'))}
            readOnly
          />))}
      </Form>
    </div>
  );
});
