import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { Icon } from '@zknow/components';
import Title from '@/components/title';
import { getAiSummary } from '@/service';
import AiImg from '@/assets/images/yan-ai-white-icon.svg';

import styles from './SectionHeader.module.less';
import './index.less';

export default observer(({ record, isActive, setIsActive, onClickEdit, tenantId, businessObjectCode, instanceId, formDataSet, intl, activeGpt }) => {
  const name = record.get('name');
  const mode = record.get('mode');
  const hiddenFlag = record.get('hiddenFlag');
  const aiPromptFlag = record.get('aiPromptFlag');
  const aiPromptTitle = record.get('aiPromptTitle');
  const aiPromptId = record.get('promptTemplateId');

  const [promptLoading, setPromptLoading] = useState(false);
  const promptLoadingTitle = intl.formatMessage({ id: 'lcr.components.desc.ai.prompt.loading', defaultMessage: '智能解析中' });

  async function handleEdit(e) {
    e.stopPropagation();
    onClickEdit(e);
  }

  async function handleAiPrompt() {
    const params = {
      tenantId,
      aiPromptId,
      businessObjectCode,
      ticketId: instanceId,
    };
    setPromptLoading(true);
    const result = await getAiSummary(params);
    setPromptLoading(false);
    const { changedParams } = result;
    if (formDataSet?.current && changedParams) {
      Object.keys(changedParams).forEach(v => {
        formDataSet?.current.set(v, changedParams[v]);
      });
    }
  }

  // 精简
  if (mode === 'REDUCED') {
    return null;
  }
  // 卡片
  if (mode === 'CARD') {
    return (
      <div
        className={styles.cardHeader}
        onClick={() => setIsActive(!isActive)}
      >
        <span>{name}</span>
        {onClickEdit && <div
          className={classnames(styles.icon, '_section-header-icon')}
          onClick={handleEdit}
        >
          <Icon type="write" />
        </div>}
      </div>
    );
  }

  // 标准
  return (
    <>
      <div
        className="lc-form-viewer-section-header"
        onClick={() => setIsActive(!isActive)}
      >
        <Title title={name} sectionFlag />
        {hiddenFlag ? null : <Icon theme="filled" type={isActive ? 'DownOne' : 'RightOne'} />}
      </div>
      {activeGpt && aiPromptFlag ? (
        <div
          className={classnames(styles['ai-title'], {
            [styles.promptLoading]: promptLoading,
          })}
          onClick={() => handleAiPrompt()}
        >
          <img src={AiImg} alt="" className={styles['ai-icon']} />
          <span className={styles['ai-name']}>{promptLoading ? (
            <>
              {promptLoadingTitle}
              <span className={styles['ai-loading']} />
            </>
          ) : aiPromptTitle}</span>
        </div>
      ) : null}
    </>
  );
});
