@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

// @font-face {
//   font-family: DingTalk-JinBuTi;
//   src: url("../../../../assets/fonts/DingTalk-JinBuTi.ttf") format('truetype');
// }

@font-face {
  font-family: DingTalk-JinBuTi;
  src: url("../../../../assets/fonts/DingTalk-JinBuTi.woff2") format("woff2"),
  url("../../../../assets/fonts/DingTalk-JinBuTi.woff") format("woff");
  font-display: swap;
}

.cardHeader {
  position: relative;
  background-color: @yq-primary-color-12 !important;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 16px;
  padding: 0 16px;
  &:hover {
    .icon {
      display: block !important;
    }
  }
}
.icon {
  display: none;
  position: absolute;
  height: 42px;
  width: 42px;
  top: 1px;
  right: 3px;
  padding: 13px;
  cursor: pointer;
}

.ai-title {
  display: inline-flex;
  cursor: pointer;
  font-size: 0.12rem;
  color: #fff;
  font-family: DingTalk-JinBuTi, DingTalk;
  font-weight: normal;
  height: 0.24rem;
  background: linear-gradient(135deg, #53FFC6, #439CFF, #BB4BFF);
  border-radius: 0.12rem;
  padding: 0 0.08rem 0 0.02rem;
  vertical-align: middle;
  align-items: center;
  margin-bottom: 0.08rem;
  margin-left: 12px;
  &:hover {
    background-size: 200% 200%;
    animation: flowColors 1s infinite;
  }

  &.promptLoading {
    padding-right: 0.2rem;
  }

  .ai-icon {
    width: 0.2rem;
    height: 0.2rem;
    margin: 0.02rem 0;
  }

  .ai-name {
    margin-left: 4px;
  }

  .ai-loading {
    display: inline-block;
    min-width: 2px;
    min-height: 2px;
    box-shadow: 2px 0 currentColor, 6px 0 currentColor, 10px 0 currentColor;
    animation: dot 3s infinite step-start both;

    &::before {
      content: '';
    }
  }
}

@keyframes flowColors {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes dot {
  25% { box-shadow: none; }
  50% { box-shadow: 2px 0 currentColor; }
  75% { box-shadow: 2px 0 currentColor, 6px 0 currentColor; }
}
