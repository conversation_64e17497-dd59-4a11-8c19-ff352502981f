import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { calculateHide } from '@/components/page-loader/utils';

import './index.less';

const Z_INDEX = 10;

export default observer(({ record, dataSet, viewId, context }) => {
  const {
    person, personId, tenantId, mode, parentKey, surveyFlag,
    surveyHasAnchorComponentParentViewId, fixedSurveyHasAnchorRect, instanceId,
  } = context;
  const [current, setCurrent] = useState('');
  const [top, setTop] = useState('unset');
  const [position, setPosition] = useState('absolute');
  const [width, setWidth] = useState(false);
  const children = record.children?.filter(section => section.get('mode') === 'STANDARD');

  const SHARE_SURVEY_MODE_TOP = surveyFlag ? 24 : 0; // 公开调查计算锚点所需要附加的高度
  const FIXED_TOP = surveyFlag ? 76 : 136; // 锚点固定时的高度

  /**
   * 根据section层级计算zIndex，防止相互覆盖
   * @param item
   * @param zIndex
   * @returns {*}
   */
  function getZIndex(item, zIndex) {
    if (item.parent) {
      return getZIndex(item.parent, zIndex - 1);
    }
    return zIndex;
  }

  let originTop = false;

  useEffect(() => {
    const pageEle = document.getElementsByClassName(`lc-form-page-loader-anchor-${surveyHasAnchorComponentParentViewId || viewId}-${instanceId}`)[0];
    const pageEleRect = pageEle?.getBoundingClientRect();
    let parentEle = document.getElementById(`section-${record.get('id')}-${instanceId}`);
    let parentEleRect = parentEle?.getBoundingClientRect();
    const currentSectonEle = document.querySelector(`#section-${record.get('id')}-${instanceId} .lc-page-loader-section-display`);
    const currentSectonEleRect = currentSectonEle?.getBoundingClientRect();
    const currentContentSectonEle = document.querySelector(`#section-${record.get('id')}-${instanceId} .lc-page-loader-section-display .hasAnchor`);
    // 存储原始top
    if (!originTop || parentEleRect.y > originTop) {
      originTop = parentEleRect.y;
    }

    const handScroll = () => {
      parentEle = document.getElementById(`section-${record.get('id')}-${instanceId}`);
      parentEleRect = parentEle?.getBoundingClientRect();
      
      // 存储原始top
      if (!originTop || parentEleRect.y > originTop) {
        originTop = parentEleRect.y;
      }
      // 计算锚点TOP位置，如果已经滚动到当前锚点范围内，则开启绝对定位
      // 锚点原始位置 - 锚点直接父级Section位置 - 锚点直接父级Section相对视口距离
      const topOffset = originTop - parentEleRect?.y - parentEle.offsetTop + SHARE_SURVEY_MODE_TOP - (fixedSurveyHasAnchorRect?.top || 0);
      if (parentEleRect?.y < pageEleRect.y && (parentEleRect.y + parentEleRect.height) > 0) {
        setTop(topOffset);
      } else {
        setTop('unset');
      }

      let hasSelect = false;
      const offset = 8;
      // 滚动时，设置锚点选中
      children.map(section => {
        // 锚点
        const anchorEle = document.getElementById(`anchor-${record.get('id')}-${instanceId}`);
        const anchorEleRect = anchorEle?.getBoundingClientRect();
        // section
        const sectionEle = document.getElementById(`section-${section.get('id')}-${instanceId}`);
        const sectionEleRect = sectionEle?.getBoundingClientRect();
        if (!hasSelect && sectionEleRect && anchorEleRect && (sectionEleRect.y + sectionEleRect.height - offset) >= (anchorEleRect.y + anchorEleRect.height)) {
          setCurrent(section.get('id'));
          hasSelect = true;
        }
        return section;
      });
    };
    // seciton嵌套时sticky不生效, 按视图两列7:3判断
    if (pageEleRect.width * 0.7 >= currentSectonEleRect.width) {
      pageEle?.addEventListener('scroll', handScroll, true);
      setPosition('absolute');
    } else {
      setTop(0);
      setPosition('sticky');
      currentContentSectonEle.style.paddingTop = 0;
    }
    return () => {
      pageEle?.removeEventListener('scroll', handScroll);
    };
  }, [record]);

  function scrollIntoView(section) {
    const parentEle = document.getElementById(`section-${record.get('id')}-${instanceId}`);
    const parentEleRect = parentEle?.getBoundingClientRect();
    // 存储原始top
    if (!originTop || parentEleRect.y > originTop) {
      originTop = parentEleRect.y;
    }
    const itemEle = document.getElementById(`section-${section.get('id')}-${instanceId}`);
    const itemEleRect = itemEle.getBoundingClientRect();
    // 滚动后会被锚点盖住，需要向下滚动一下，保证标题显示完整
    const scrollElements = document.getElementsByClassName(`lc-form-page-loader-anchor-${surveyHasAnchorComponentParentViewId || viewId}-${instanceId}`);
    if (scrollElements.length) {
      const scrollEle = scrollElements[0];
      // Section位置 - 锚点直接父级Section位置 - 锚点高度 + 锚点直接父级Section相对视口距离 - 公开调查额外高度
      scrollEle.scrollTop = itemEleRect.y - parentEleRect.y - 54 + parentEle.offsetTop - SHARE_SURVEY_MODE_TOP;
    }
    setTimeout(() => {
      setCurrent(section.get('id'));
    }, 100);
  }

  function renderAnchor(section, index) {
    return (
      <span
        className={classnames('lc-form-viewer-section-anchor-item', {
          current: current === section.get('id') || (!current && index === 0),
        })}
        onClick={() => scrollIntoView(section)}
      >
        {section.get('name')}
      </span>
    );
  }

  function filterPane(pane) {
    const funcConfig = { person, personId, tenantId };
    const hideFlag = calculateHide(pane, dataSet?.current, mode, parentKey, funcConfig);
    if (hideFlag) {
      return false;
    }
    return true;
  }

  return (
    <div
      className={classnames('lc-form-viewer-section-anchor', {
        fixed: top !== 'unset',
      })}
      style={{ 
        top,
        zIndex: getZIndex(record, Z_INDEX),
        width: fixedSurveyHasAnchorRect?.width || (position === 'fixed' ? width : ''),
        margin: fixedSurveyHasAnchorRect?.margin,
        position,
      }}
      id={`anchor-${record.get('id')}-${instanceId}`}
    >
      {children.filter(filterPane).map(renderAnchor)}
      <div className="lc-form-viewer-section-anchor-line" />
    </div>
  );
});
