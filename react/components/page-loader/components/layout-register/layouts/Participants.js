import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import Participants from '@/renderer/participants';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  return (
    <Participants
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      udmFlag={udmFlag}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutParticipants */
