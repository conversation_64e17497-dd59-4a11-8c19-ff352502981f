@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

@table-prefix-cls: ~'@{c7n-pro-prefix}-table';

.lc-page-loader-table {
  width: 100%;
  &.table-view {
    height: 4rem;
  }

  &-rowAction {
    font-size: 0.18rem;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
    margin-left: 10px;
    vertical-align: middle;

    &:hover {
      color: @primary-color;
    }

    &.selection {
      margin-left: 0;
    }
  }

  &-headerAction {
    width: 0.2rem;
    height: 0.2rem;
    color: rgba(0, 0, 0, 0.65);
    cursor: pointer;
    margin-right: 0.16rem;
  }

  &-listView-form {
    .lc-title {
      margin-top: 0.2rem;
    }

    .c7n-pro-transfer-wrapper {
      width: 100%;

      .c7n-pro-transfer {
        width: calc(50% - 0.4rem);

        .c7n-pro-transfer-body-search-wrapper {
          .c7n-pro-input-wrapper {
            width: 100%;
          }
        }
      }
    }
  }

  &-filter-form {
    .conditionSelect-main .conditionSelect-main-content .content-left .content-select-fieldValue {
      width: 2.21rem;
    }
  }

  &-action-wrapper {
    width: 1.68rem;
    display: flex;
    justify-content: space-between;

    .action-name {
      display: inline-block;
      width: 1.45rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .action-icon {
      vertical-align: text-bottom;
      margin-right: 0.08rem;
    }

    .action-delete {
      display: none !important;
      line-height: 0.37rem;
    }

    &:hover {
      .action-delete {
        display: inline-block !important;
        color: @primary-color;
      }
    }
  }

  &-popover {
    color: @primary-color;
    cursor: pointer;
  }

  &-file {
    width: 100%;
    display: inline-flex;
    align-items: center;

    .yqcloud-icon-park-wrapper {
      &:hover {
        color: @primary-color;
        cursor: pointer;
      }
    }

    &-upload-btn {
      margin-bottom: 0.16rem;
    }
  }

  &-file-container {
    width: 100%;
    display: flex;
    height: 0.56rem;
    padding: 0.08rem;
    > span {
      display: inline-flex;
      align-items: center;
    }

    &:hover {
      background-color: @yq-fill-3;
      border-radius: 0.04rem;

      .file-actions {
        display: inline-flex;
      }
    }

    .file-icon {
      align-items: center;
    }

    .file-info {
      margin: 0 0.08rem;
      .file-info-name {
        width: 3.5rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .file-info-size {
        color: @yq-text-6;
        font-size: 12px;
      }
    }

    .file-actions {
      display: none;
      .file-action {
        margin: 0 0.04rem;

        &:hover {
          cursor: pointer;
          color: @primary-color;
        }
      }
    }

    .cancel-upload {
      display: inline-flex;
      align-items: center;
    }
  }

  &-file-container-single {
    display: inline-flex;
    height: auto;
    padding: 0;

    &:hover {
      background-color: transparent;
    }

    .file-info {
      display: inline-flex;
      align-items: center;

      .file-info-name {
        max-width: 1.2rem;
      }
    }

    // .file-actions {
    //   display: inline-flex;
    // }
  }

  .@{table-prefix-cls}-selection-column {
    .c7n-pro-radio-wrapper {
      margin: 0 0.03rem;
    }
  }

  .@{table-prefix-cls}-yqcloud-table-search-tag-content {
    display: flex;
    align-items: center;
    margin-top: 8px;
  }

  .z-index-100 {
    z-index: 100;
  }
}

.c7n-pro-table-row:hover,
.c7n-pro-table-row-hover {
  .lc-page-loader-table-file-container-single {
    .file-info-size {
      display: none;
    }

    .file-actions {
      display: inline-flex;
    }
  }
}
//
.yqcloud-history-modal {
  .c7n-pro-pagination-wrapper {
    margin-bottom: 0;
    border-bottom-width: 0;
  }
  .c7n-pro-modal-body.c7n-pro-modal-drawer-body {
    padding-top: 0;
    padding-bottom: 0;
  }
  .c7n-pro-table-combo-filter-single-wrapper {
    padding-left: 0;
  }
}
.yqcloud-export-modal {
  .advanced-filter-condition-container {
    .filter-condition-header {
      border-left: .03rem solid @primary-color;
      padding-left: 0.08rem;
      font-weight: 500;
      font-size: .14rem;
      line-height: .14rem;
      margin: 0 0 0.12rem 0;
    }

    .export-fuzzy-params {
      margin-bottom: 0.04rem;
      &-help {
        margin-top: 0;
        height: 0.2rem;
        font-weight: 400;
        margin-bottom: 0.04rem;
      }
    }
  }
  .filter-condition-title {
    // display: none;
    margin: 0 0 0.12rem 0;
  }
  .filter-condition {
    background: #fff;
    border-radius: 4px;
    // border: 1px solid rgba(0, 0, 0, 0.15);
    // padding: 0.16rem;
    // margin-top: 0.12rem;
  }
  .filter-condition-group {
    border: none;
    padding: 0;
    margin-top: 0;
  }
  .conditionSelect-main-first {
    padding-top: 12px;
  }
  .order-condition-wrapper-inline {
    border: none;
    margin: 0;
    padding: 0 0 0.24rem 0;
  }
  .order-condition-title-inline {
    border-left: .03rem solid @primary-color;
    padding-left: 0.08rem;
    font-weight: 500;
    font-size: .14rem;
    line-height: .14rem;
  }
  .yq-mt-16 {
    margin-top: 16px;
    .c7n-pro-field-output-mix {
      padding: .06rem 0 .04rem .16rem;
    }
  }
}
.field-checkbox-container {
  background: #fff;
  border-radius: 4px;
  // border: 1px solid rgba(0, 0, 0, 0.15);
  padding: 0 0 0.24rem 0;
  // margin-top: 0.12rem;

  .filter-field-list {
    border-left: .03rem solid @primary-color;
    padding-left: 0.08rem;
    font-weight: 500;
    font-size: .14rem;
    line-height: .14rem;
    margin: 0 0 0.12rem 0;
  }

  .export-fx {
    font-size: 12px;
    height: 20px;
    width: 20px;
    display: inline-flex;
    justify-content: center;
    border-radius: 2px;
    background-color: @yq-fill-3;
    border: 1px solid rgba(203, 210, 220, 0.25);
    margin-right: 16px;
  }

  .export-field-calculated .c7n-pro-checkbox-inner + span {
    padding-right: 0;
  }

  .c7n-pro-checkbox-wrapper {
    cursor: move;
  }
}
.lc-page-loader-variable-table {
  .@{table-prefix-cls}-yqcloud-table-search-tag,
  .@{table-prefix-cls}-yqcloud-toolbar-filter {
    display: none;
  }
}
.lc-table-condition-search {
  &-buttons {
    display: flex;
    align-items: center;
    height: 0.46rem;
    margin-top: 6px;
    background: #f7f7f7;
    padding: 0.07rem 0.16rem;
    border: 1px solid #e8e8e8;
    & > span {
      border-right: 1px solid #e8e8e8;
      margin: 0 12px;
      height: 100%;
    }
    & .c7n-pro-btn {
      background: #fff;
      border: 1px solid rgba(0, 0, 0, 0.15);
    }
  }
  & .filter-condition {
    padding: 0;
    border-bottom: 0.01rem solid #e8e8e8;
  }
  & .filter-condition-group {
    border: none;
    max-height: 2.76rem;
    overflow: scroll;
  }
  & .content-group-name {
    color: #595959;
  }
  & .filter-condition-blank {
    display: none;
  }
}

.c7n-pro-tooltip-popup-inner-dark {
  .lc-page-loader-table-popover {
    color: #fff !important;
  }
}

.c7n-menu-item-active {
  background: rgba(0, 0, 0, 0.05) !important;
}

.table-line {
  .c7n-pro-checkbox-wrapper {
    margin: 0 auto;
  }
  .@{table-prefix-cls}-inner-row-height-fixed {
    white-space: pre-wrap;
    word-break: break-all;
  }

  .@{table-prefix-cls}-body .@{table-prefix-cls}-cell-inner {
      display: flex;
      align-items: center;
    & > span,
    & > a {
      height: auto;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      line-height: 1.5;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }

  &-2 {
    .@{table-prefix-cls}-cell-inner {
      -webkit-line-clamp: 2;
      & > span,
      & > a {
        -webkit-line-clamp: 2;
      }
    }
  }
  &-3 {
    .@{table-prefix-cls}-cell-inner {
      -webkit-line-clamp: 3;
      & > span,
      & > a {
        -webkit-line-clamp: 3;
      }
    }
  }
  &-4 {
    .@{table-prefix-cls}-cell-inner {
      -webkit-line-clamp: 4;
      & > span,
      & > a {
        -webkit-line-clamp: 4;
      }
    }
  }
  &-auto {
    .yqcloud-components-table-hover-action {
      display: flex !important;
      top: calc(50% - 12px);
      word-break: keep-all;
    }
    .@{table-prefix-cls}-cell-prefix {
      padding-top: 4px;
    }
  }
}

.lc-table {
  th {
    line-height: 1.5;
  }

  &-row-text {
    white-space: pre-wrap;
  }

  &-footer {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .c7n-pro-output-multiple-block {
    padding: 0.01rem 0.08rem;
    font-size: 0.12rem;
    line-height: 0.2rem;
  }

  .lc-table-action-right {
    position: sticky;
    z-index: 2;
    right: 0;
  }

  .@{table-prefix-cls}-fixed-right {
    .lc-table-action-right-lock {
      background: #fff !important;

      .yqcloud-components-table-hover-action-tableBtnCenter {
        margin-top: 0.02rem;
      }

      .@{table-prefix-cls}-cell-inner.c7n-pro-output {
        padding: 0.0065rem 0;
      }
    }
  }

  // 处理固定列操作列下header样式
  .@{table-prefix-cls}-cell-fix-right:not(.@{table-prefix-cls}-customized-column) {
    .@{table-prefix-cls}-cell-inner {
      margin-left: 0.12rem;
    }
  }

  .@{table-prefix-cls}-sticky-shadow.@{table-prefix-cls}-sticky-right {
    right: 1rem !important; // 修复表格有滚动条时样式问题，前提操作列固定70px
  }

  &-action-item {
    display: flex;
    align-items: center;

    .yqcloud-icon-park-wrapper {
      margin-right: 8px;
    }
  }
}

.@{table-prefix-cls}-combo-filter-menu-option-content {
  .yq-filter-menu-option {
    display: flex;
    align-items: center;

    .yqcloud-icon-park-wrapper {
      margin-right: 0.12rem;
    }

    .yq-filter-menu-option-text {
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}

.lc-table-row-preview {
  &-popover {
    .c7n-popover-title {
      padding: 12px 16px;
      line-height: 22px;
      color: #12274d;
    }

    .c7n-popover-inner-content {
      padding: 0 0 0.12rem 0;
    }
  }

  &-content {
    height: 230px; // 由于需要计算位置，这里只能写成固定值
    overflow: auto;

    .yq-components-wysiwyg-preview {
      width: 100%;
      word-break: break-all;

      img {
        width: 100%;
      }
    }

    .lc-page-info-preview-title {
      min-width: 1.77rem;
      // min-height: 0.32rem;
      margin: 0;
      font-weight: 500;
      font-size: unset;
      border-bottom: 0.01rem solid rgba(203, 210, 220, 0.5);
      padding: 0.12rem 0.16rem;
      line-height: 0.22rem;
      color: #12274d;
    }
  }
}
