import React, { useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal } from 'choerodon-ui/pro';
import qs from 'query-string';
import { withErrorBoundary } from '@zknow/components';
import TicketRelation from '@/renderer/ticket-relation';
import PageLoader from '@/components/page-loader';

const modalKey = Modal.key();
const Index = observer(({ record, dataSet, context }) => {
  const {
    intl,
    history,
    viewId: lastViewId,
    instanceId,
    tenantId,
    viewDataSet,
    onJumpNewPage,
    udmFlag,
  } = context;
  const { businessObjectCode, code: viewCode } = viewDataSet.current?.toData();
  const config = record?.toData() || {};
  const pageRef = useRef();
  let modal;

  /**
     * 打开视图
     */
  async function openView(view = {}, _dataSet, current) {
    const { viewSize, openType, viewId, viewName } = view;
    const viewModalStyle = { width: Number(viewSize) };

    const handleOk = async () => {
      if (await pageRef.current?.formDataSet?.validate()) {
        await pageRef.current?.formDataSet?.submit();
        _dataSet.query();
        modal = false;
        return true;
      }
      return false;
    };

    const handleClose = () => {
      if (modal && pageRef.current?.formDataSet?.updated.length > 0) {
        return Modal.confirm({
          title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
          children: (
            <div>
              <p>{intl.formatMessage({ id: 'lcr.components.desc.close.tip', defaultMessage: '当前页面修改还未保存，确认关闭吗？' })}</p>
            </div>
          ),
        }).then(async (button) => {
          if (button === 'ok') {
            await pageRef.current?.formDataSet?.reset();
            modal = false;
            return true;
          } else {
            return false;
          }
        });
      } else {
        return true;
      }
    };

    if (openType === 'NEW') {
      if (onJumpNewPage) {
        onJumpNewPage({ record: current, viewId });
      } else {
        const { solutionId } = qs.parse(history.location?.search || '');
        const ticketId = current.get('id');
        // `/lc/engine/${viewId}/${current.get('id')}${lastViewId ? `/${lastViewId}` : ''}${history.location?.search}`
        // 生产单据 INC00013201 万华需求，觉得打开的路由不统一，现在统一给跳到后台工作台
        // 生产单据 INC00026536 如果当前路由是 itsm/ticket/detail, 就还用 itsm/ticket/detail 打开关联单据
        const targetPath = history?.location?.pathname?.includes('itsm/ticket/detail') ? '/itsm/ticket/detail' : '/itsm/workspace';
        history.push(`${targetPath}?solutionId=${solutionId}&tenantId=${tenantId}&ticketId=${ticketId}&viewId=${viewId}&extraInstanceId=${ticketId}`);
      }
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (modal) {
        await modal.close();
      }
      modal = Modal.open({
        title: openType === 'MIDDLE' ? viewName : '',
        children: (
          <PageLoader
            instanceId={current.get('id')}
            viewId={viewId}
            pageRef={pageRef}
            mode="MODIFY"
            openType={openType}
            parentDataSet={_dataSet}
          />
        ),
        key: `${modalKey}-${record.get('id')}`,
        drawer: openType === 'RIGHT',
        style: viewModalStyle,
        destroyOnClose: true,
        onOk: handleOk,
        onCancel: handleClose,
        onClose: handleClose,
        closeOnLocationChange: true,
        okButton: true,
      });
    }
    return true;
  }

  // 共享服务项生成的单据不显示关联单据
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketRelation
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      config={config}
      openView={openView}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketRelation */
