@import '~choerodon-ui/lib/style/themes/default';
.lc-itemRelated-form {
  .c7n-pro-field-label {
    max-width: 1.02rem !important;
    min-width: 1.02rem !important;
    color: #595959 !important;
    padding: 0.04rem 0 0.04rem 0.16rem !important;
  }
  .itemRelated-wrapper {
    display: flex;
    align-items: center;
    .lov {
      display: none;
    }
    &-add {
      display: flex;
      align-items: center;
      height: 34px;
    }
    &-cooper {
      display: flex;
      align-items: center;
      cursor: pointer;
      .item-name {
        color: @primary-color;
        text-decoration: underline;
      }
      &:hover {
        .itemRelated-wrapper-icon-delete-hasvalue {
          display: flex !important;
          align-items: center;
        }
      }
    }
    &-icon {
      color: @primary-color;
      cursor: pointer;
      &-add {
        &-novalue {
          display: inline-block;
        }
        &-hasvalue {
          display: none !important;
        }
      }
      &-delete {
        &-novalue {
          display: none !important;
        }
        &-hasvalue {
          display: none !important;
          height: 30px;
          margin-left: 12px;
        }
      }
    }
  }
}
