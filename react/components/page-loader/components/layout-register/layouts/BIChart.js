import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import { getEnv } from '@zknow/utils';
import { Icon, Empty, withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import './Chart.less';

const Index = observer(({ record, className, context }) => {
  const { tenantId, configDataSet } = context;
  const [timestamp, setTimestamp] = useState(0);
  const [iframeUrl, setIframeUrl] = useState();
  const [token, setToken] = useState();
  const reportId = record?.get('widgetConfig.reportId');
  const chartId = record.get('widgetConfig.biChartId');
  const biHeight = record.get('widgetConfig.biChartHeight');
  const chartName = record.get('widgetConfig.chartName');
  const reportName = record?.get('widgetConfig.reportName');
  const refreshFlag = record?.get('widgetConfig.refreshFlag');
  const refreshRate = record?.get('widgetConfig.refreshRate') || 'NONE';
  const position = JSON.parse(record?.get('widgetConfig.reportPosition') || '{}');
  const biUrl = window._env_.FINE_BI_DOMAIN || getEnv('FINE_BI_DOMAIN') || 'https://bi.dev.yqcloud.com';
  // const biUrl = 'https://apps.dev.yqcloud.com/#/inner_link/fine_bi_domain';
  const reportUrl = `${biUrl}/webroot/decision/v5/design/report/${chartId}/view?no_menu=true&tenant_id=${tenantId}&&timestamp=${timestamp}`;

  let timer;

  const loadSSOToken = async () => {
    // 需要优化
    if (!configDataSet.get(0) && !configDataSet.getState('queryFlag')) {
      const result = await configDataSet.query();
      if (result && !result.failed) {
        setToken(result?.ssoToken);
        setIframeUrl(`${reportUrl}&ssoToken=${result?.ssoToken}`);
        configDataSet.setState('queryFlag', true);
      }
    } else if (configDataSet.get(0)) {
      const result = configDataSet.get(0).toData();
      setToken(result?.ssoToken);
      setIframeUrl(`${reportUrl}&ssoToken=${result?.ssoToken}`);
    }
  };

  const iframeRef = useRef();

  useEffect(() => {
    loadSSOToken();
  }, []);

  useEffect(() => {
    if (refreshRate !== 'NONE') {
      if (timer) {
        clearInterval(timer);
      }
      timer = setInterval(() => {
        setTimestamp(new Date().getTime());
      }, refreshRate * 1000);
    }
    return () => clearInterval(timer);
  }, [refreshRate]);

  const calculateRowHeight = (data, row) => {
    let height = 0;
    row.children.forEach(id => {
      const chart = data[id];
      if (chart.meta.height > height) {
        height = chart.meta.height;
      }
    });
    return height * 8 + 120;
  };

  const calculateFrameHeight = (data) => {
    const rows = Object.keys(data).filter(key => key.startsWith('ROW'));
    const rowHeight = rows.map(row => calculateRowHeight(data, data[row]));
    if (rowHeight && rowHeight.length > 0) {
      return rowHeight.reduce((a, b) => a + b);
    } else {
      return 0;
    }
  };

  useEffect(() => {
    const iframe = iframeRef.current;
    if (iframe) {
      iframe.style.height = biHeight;
      // const iframeHeight = calculateFrameHeight(position) || 500;
      // iframe.height = iframeHeight;
      // iframe.style.height = `calc(100vh - ${iframe.getClientRects()[0]?.top + 14}px)`;
    }
  }, [iframeRef.current]);

  return (
    <div className={classNames('lc-page-loader-chart', className)}>
      <div className="lc-page-loader-chart-header">
          <span className="lc-page-loader-chart-name">
            {chartName}
          </span>
        {refreshFlag
          ? (
            <span
              className="lc-page-loader-chart-refresh"
            >
                <Icon type="Refresh" onClick={() => setTimestamp(new Date().getTime())} />
              </span>
          ) : null}
      </div>
      {iframeUrl
        ? (
          <iframe
            src={iframeUrl}
            title={chartName}
            style={{ height: `${biHeight}px` }}
            className="lc-page-loader-chart-iframe"
            ref={iframeRef}
            height={biHeight}
          />
        ) : <Empty type="nodata" />}
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutBIChart */
