import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import UpgradeOrder from '@/components/upgrade-order';

const Index = observer(({ record, context }) => {
  return (
    <UpgradeOrder 
      record={record}
      context={context}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutUpgradeOrder */
