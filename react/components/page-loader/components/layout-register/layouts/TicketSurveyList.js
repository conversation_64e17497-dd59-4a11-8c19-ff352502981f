/*
 * @Author: xiaoreya
 * @Date: 2022-01-24 19:46:29
 * @Description:
 */
import React from 'react';
import { withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import TicketSurveyList from '@/renderer/survey-list';

const Index = observer(({ record, dataSet, context }) => {
  const { tenantId, instanceId, viewDataSet, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  // 共享服务项生成的单据不展示调查列表
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketSurveyList
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketSurveyList */
