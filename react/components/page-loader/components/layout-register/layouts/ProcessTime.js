import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import ProcessTime from '@/renderer/process-time';

const Index = observer(({ record, dataSet, context }) => {
  const { instanceId: id, viewDataSet, onCancel, tenantId } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  return <ProcessTime instanceId={id} formDataSet={dataSet} businessObjectCode={businessObjectCode} />;
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutProcessTime */
