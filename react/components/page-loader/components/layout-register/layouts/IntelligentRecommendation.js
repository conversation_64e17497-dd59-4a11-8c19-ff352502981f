import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { TextField } from 'choerodon-ui/pro';
import axios from 'axios';
import { debounce } from 'lodash';
import { withErrorBoundary } from '@zknow/components';
import GlobalSearch from '@/components/global-search';
import { useOpenView } from './IntelligentSearch';

import './IntelligentRecommendation.less';

const Index = observer(({ record, dataSet, context }) => {
  const {
    tenantId,
    instanceId,
    viewDataSet,
    intl,
    onJumpNewPage,
    lastViewId,
    history: { location: { search } },
    history,
    udmFlag,
  } = context;
  const { businessObjectCode, jsonData } = viewDataSet.current?.toData();
  const config = record?.toData() || {};
  const searchRef = useRef();
  const [disabledRecommendation, setDisabledRecommendation] = useState(false);
  const { problemSourceFields, appliedFields, searchRange } = config?.widgetConfig;
  const queryFields = useMemo(() => problemSourceFields.split(',') || [], [problemSourceFields]);

  async function loadConfig(data) {
    const url = `/itsm/v1/${tenantId}/resolution/${businessObjectCode}/${instanceId}/check`;
    const res = await axios.post(url, data);
    if (res?.failed) {
      setDisabledRecommendation(true);
    } else {
      setDisabledRecommendation(!res);
    }
  }

  useEffect(() => {
    if (!instanceId) {
      return;
    }
    if (jsonData && JSON.stringify(jsonData).indexOf('TicketSolution') !== -1) {
      const sections = viewDataSet.current?.get('jsonData')?.sections || [];
      let editablePerson = [];
      let editableRole = [];
      sections.forEach((s) => {
        (s?.fields || []).forEach((v) => {
          // "ALWAYS_VISIBLE"
          if (v.widgetType === 'TicketSolution') {
            editablePerson = v.widgetConfig?.editablePerson || [];
            editableRole = JSON.parse(v.widgetConfig?.editableRole) || [];
          }
        });
      });
      const data = {
        specialList: editablePerson?.map(v => ({
          receiverFieldId: v?.id,
          receiverFieldFullId: v?.fullId,
          receiverField: v?.path,
        })),
        roleIdList: editableRole?.map(v => v?.id),
      };
      loadConfig(data);
    }
  }, []);

  const debounceHandleSearch = useCallback(debounce((res) => {
    searchRef.current.value = res;
  }, 500), []);

  useEffect(() => {
    const searchValue = queryFields.map(fieldKey => {
      const value = dataSet?.current?.get(fieldKey);
      if (value) {
        let res = '';
        if (typeof value === 'string') {
          res = value.replace(new RegExp('<[^>]*>', 'g'), ' ');
          res = res.replace(new RegExp('\\s+', 'g'), ' ');
          res = res.replace(new RegExp('&nbsp;', 'g'), '');
        }
        return res;
      }
      return '';
    }).filter(value => value.trim()).join('. ');
    debounceHandleSearch(searchValue);
  }, queryFields.map(fieldKey => dataSet?.current?.get(fieldKey)));

  const handleRecommendation = (r, modalInstance) => {
    if (JSON.stringify(jsonData).indexOf('TicketSolution') !== -1) {
      if (disabledRecommendation) {
        return false;
      } else {
        const solution = r.get('originSolution');
        dataSet?.current?.set(appliedFields, solution);
        const element = document.getElementById('yq-write-solution');
        if (modalInstance) {
          modalInstance?.current?.close();
          setTimeout(() => {
            if (element) {
              element.click();
            }
          }, 500);
        } else {
          element && element.click();
        }
      }
    } else {
      const solution = r.get('originSolution');
      dataSet?.current?.set(appliedFields, solution);
    }
  };

  const openView = useOpenView({
    record,
    intl,
    onJumpNewPage,
    history,
    lastViewId,
  });

  // 共享服务项生成的单据不展示智能推荐
  if (udmFlag) {
    return <div />;
  }

  return (
    <div className="yq-global-search-recommendation">
      <div className="yq-global-search-recommendation-title">
        <span>{intl.formatMessage({ id: 'lcr.components.desc.search.recommendation', defaultMessage: '智能推荐' })}</span>
      </div>
      <TextField ref={searchRef} style={{ display: 'none' }} />
      <GlobalSearch
        dataSet={dataSet}
        // placeholder={placeholder}
        dSearchRef={searchRef}
        queryText={searchRef?.current?.value}
        config={{ searchRange }}
        defaultConfig={config}
        openView={openView}
        serviceCatalogFlag
        onRecommendation={(r, modalInstance) => handleRecommendation(r, modalInstance)}
        history={history}
        search={search}
        isSide
        intl={intl}
        bestSolution
        channel={instanceId === 'new' ? 'SUBMIT_SERVICE_ITEM' : 'WORK_ORDER_PROCESS'} // 区分详情和新建，新建事件单和提交服务项保持一致
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutIntelligentRecommendation */
