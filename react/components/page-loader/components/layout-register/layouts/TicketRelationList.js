import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketRelationList from '@/renderer/ticket-relation-list';

const Index = observer(({ record, dataSet, context }) => {
  const {
    instanceId,
    tenantId,
    viewDataSet,
    onJumpNewPage,
    udmFlag,
  } = context;
  const { businessObjectCode, code: viewCode } = viewDataSet.current?.toData();
  const config = record?.toData() || {};

  // 共享服务项生成的单据不展示关联单据
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketRelationList
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      viewId={record?.get('widgetConfig.viewId')}
      config={config}
      onJumpNewPage={onJumpNewPage}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketRelationList */
