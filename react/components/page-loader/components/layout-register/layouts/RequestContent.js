import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Form, NumberField, message } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';
import CardWrapper from '@/components/card-wrapper';
import { submitRequestContent } from '@/utils';
import PageLoader from '../../..';
import { calculateCondition } from '../../../utils';
import styles from './RequestContent.module.less';

const modalKey = Modal.key();

const isNumber = num => !Number.isNaN(Number(num));
const getNumebr = (num, toFixed) => (isNumber(num) ? Number(num).toFixed(toFixed) : '-');

const calTotalPrice = (record) => {
  if (!record) return 0;
  let _total = 0;
  if (record) {
    const price = Number(record.get('price'));
    const quantity = Number(record.get('quantity'));
    if (!Number.isNaN(price) && !Number.isNaN(quantity)) {
      _total = price * quantity;
    }
    if (Number.isNaN(price) || Number.isNaN(quantity)) {
      _total = '-';
    }
  }
  record?.set('__totalPrice', _total);
};

const PriceHeader = observer(({ labels, intl, record }) => {
  const total = getNumebr(record?.get('__totalPrice'), 2);
  const valueMap = {
    price: `￥${getNumebr(record?.get('price'), 2)}`,
    quantity: getNumebr(record?.get('quantity')),
  };
  return (
    <div className={styles.headerWrapper}>
      <div>
        {
          labels
            .map(item => (
              <span className={styles.label}>
                {item.label}:
                <span> {valueMap[item.name]}</span>
              </span>
            ))
        }
      </div>
      {labels.length === 2 ? <div>
        <span className={styles.total}>
          {`${intl.formatMessage({ id: 'lcr.components.desc.total.price', defaultMessage: '合计' }) }: `}
          <span>{` ￥${total}`}</span>
        </span>
      </div> : null}
    </div>
  );
});

// 组件只能用于服务项视图组件中（ServiceItemPage）会传入requestItemConfig
const Index = observer(({ record, context, dataSet, className, ...rest }) => {
  const {
    intl, parentKey, person, personId, viewDataSet, dsManager,
    mode,
    requestItemConfig = {},
    tenantId,
    pageRef,
  } = context;
  const {
    requestDataSet,
    createViewId,
  } = requestItemConfig;

  const formDataSet = (viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id')))
      || pageRef?.current?.formDataSet;
  const {
    editType,
    editAction,
    editCondition,
  } = record?.toData() || { widgetConfig: {} };

  const name = record.get('name');
  const hideQuantity = record.get('widgetConfig.hideQuantity');
  const hidePrice = record.get('widgetConfig.hidePrice');

  function calculateEditable() {
    if (mode === 'PREVIEW' || mode === 'DISABLED') return false;
    let disabled = editType === 'ALWAYS_NOT_EDIT';
    if (editType === 'CONDITION' && dataSet?.current) {
      const funcConfig = { person, personId, tenantId };
      const flag = calculateCondition(parentKey, editCondition, dataSet?.current, funcConfig);
      disabled = editAction === 'READONLY' ? flag : !flag;
    }
    return !disabled;
  }

  const editable = calculateEditable();

  useEffect(() => {
    calTotalPrice(formDataSet?.current);
  }, [formDataSet?.current]);

  const getLabels = () => {
    const labels = [];
    if (!hidePrice) {
      labels.push({ label: intl.formatMessage({ id: 'lcr.components.desc.unit.price', defaultMessage: '单价' }), name: 'price', step: 0.01 });
    }
    if (!hideQuantity) {
      labels.push({ label: intl.formatMessage({ id: 'lcr.components.desc.quantity', defaultMessage: '数量' }), name: 'quantity', step: 1 });
    }
    return labels;
  };

  const handlePriceChange = (value, key) => {
    if (requestDataSet?.current) {
      // 触发视图的submit
      requestDataSet.current.set('__dirty', true);
        requestDataSet?.current.get(key) !== undefined
          && requestDataSet?.current.set(key, value);
    }
    calTotalPrice(formDataSet?.current);
  };

  const handleOpenEdit = () => {
    const labels = getLabels();
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.modify.request.content', defaultMessage: '修改请求内容' }),
      children: (
        <>
          <PageLoader
            mode="MODIFY"
            viewId={createViewId}
            formDataSet={formDataSet}
            variableFlag
            showHeaderFlag={false}
          />
          <Form record={formDataSet?.current} columns={2}>
            {getLabels().map(label => <NumberField {...label} precision={2} onChange={(value) => handlePriceChange(value, label.name)} />)}
            {labels?.length === 2
              ? <NumberField name="__totalPrice" precision={2} disabled label={intl.formatMessage({ id: 'lcr.components.desc.total.price', defaultMessage: '合计' })} />
              : null}
          </Form>
        </>
      ),
      style: { width: 1000 },
      onOk: async () => {
        // 服务项视图的提交
        await submitRequestContent({
          dataRef: requestItemConfig?.scPageRef,
          tenantId,
        });
        message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
      },
      key: modalKey,
    });
  };

  if (mode === 'PREVIEW') {
    return (
      <CardWrapper
        title={name}
        className={styles.container}
        description={<PriceHeader
          record={formDataSet?.current}
          labels={getLabels()}
          intl={intl}
        />}
      />
    );
  }
  return (
    <CardWrapper
      title={name}
      className={styles.container}
      description={<PriceHeader
        record={formDataSet?.current}
        labels={getLabels()}
        intl={intl}
      />}
      onClickEdit={editable ? handleOpenEdit : null}
    >
      <PageLoader
        viewId={createViewId}
        mode="OUTPUT"
        formDataSet={formDataSet}
        variableFlag
        showHeaderFlag={false}
        sectionReadOnly
        requestItemConfig={requestItemConfig} // 根据这个判断视图类型，需要传一下
      />
    </CardWrapper>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutRequestContent */
