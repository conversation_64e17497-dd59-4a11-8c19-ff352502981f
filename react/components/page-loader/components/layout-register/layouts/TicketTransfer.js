import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import axios from 'axios';
import { message } from 'choerodon-ui';
import TicketTransferCmp from '@/components/ticket-transfer';

const Index = observer(({ record, context, dataSet }) => {
  const { widgetConfig = {} } = record?.toData();
  const { pageRef, viewDataSet, tenantId } = context;

  const [viewData] = viewDataSet?.toData() || [];
  // 业务对象是否启用回复
  const [replyEnable, setReplyEnable] = React.useState(false);

  React.useEffect(() => {
    const getConfig = async (code) => {
      const res = await axios.get(`/lc/v1/${tenantId}/business_objects/code/${code}`);
      if (!res || res?.failed === true) {
        message.error(res?.message);
      } else {
        setReplyEnable(res?.journalFlag || false);
      }
    };
    if (viewData?.businessObjectCode) {
      getConfig(viewData?.businessObjectCode);
    }
  }, [viewData?.businessObjectCode]);

  return (
    <TicketTransferCmp
      dataSet={dataSet}
      widgetConfig={widgetConfig}
      replyEnable={replyEnable}
      pageRef={pageRef}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketTransfer */
