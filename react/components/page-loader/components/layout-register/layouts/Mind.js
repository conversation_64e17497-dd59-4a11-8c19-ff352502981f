import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import Mind from '@/components/mind-render';

const Index = observer(({ record, context }) => {
  const { tenantId, instanceId, intl } = context;

  const createViewConfig = {
    viewId: record.get('widgetConfig.createViewId'),
    viewName: record.get('widgetConfig.createViewName'),
    viewType: record.get('widgetConfig.createViewType'),
    viewSize: record.get('widgetConfig.createViewSize'),
    relationLovNameFieldCode: record.get('widgetConfig.createView.relationLovNameFieldCode'),
    relationLovValueFieldCode: record.get('widgetConfig.createView.relationLovValueFieldCode'),
    selfRelationLovNameFieldCode: record.get('widgetConfig.createView.relationLovNameFieldCode'),
    selfRelationLovValueFieldCode: record.get('widgetConfig.createView.relationLovValueFieldCode'),
  };

  const viewViewConfig = {
    viewId: record.get('widgetConfig.viewViewId'),
    viewName: record.get('widgetConfig.viewViewName'),
    viewType: record.get('widgetConfig.viewViewType'),
    viewSize: record.get('widgetConfig.viewViewSize'),
    relationLovNameFieldCode: record.get('widgetConfig.viewView.relationLovNameFieldCode'),
    relationLovValueFieldCode: record.get('widgetConfig.viewView.relationLovValueFieldCode'),
    selfRelationLovNameFieldCode: record.get('widgetConfig.viewView.relationLovNameFieldCode'),
    selfRelationLovValueFieldCode: record.get('widgetConfig.viewView.relationLovValueFieldCode'),
  };

  return (
    <Mind
      tenantId={tenantId}
      businessObjectId={record.get('widgetConfig.businessObjectId')}
      showFieldId={record.get('widgetConfig.showFieldId')}
      showFieldCode={record.get('widgetConfig.showFieldCode')}
      subBusinessObjectId={record.get('widgetConfig.subBusinessObjectId')}
      relationFieldId={record.get('widgetConfig.relationFieldId')}
      relationFieldCode={record.get('widgetConfig.relationFieldCode')}
      subShowFieldIds={record.get('widgetConfig.subShowFieldIds')}
      colorFieldPath={record.get('widgetConfig.colorFieldPath')}
      rootColorFieldPath={record.get('widgetConfig.rootColorFieldPath')}
      dataId={instanceId}
      selfRelationFieldId={record.get('widgetConfig.selfRelationFieldId')}
      selfRelationFieldCode={record.get('widgetConfig.selfRelationFieldCode')}
      deleteFlag={record.get('widgetConfig.deleteFlag')}
      dragFlag={record.get('widgetConfig.dragFlag')}
      removeFlag={record.get('widgetConfig.removeFlag')}
      editFlag={record.get('widgetConfig.editFlag')}
      createFlag={record.get('widgetConfig.createFlag')}
      createViewConfig={createViewConfig}
      viewFlag={record.get('widgetConfig.viewFlag')}
      expandFlag={record.get('widgetConfig.defaultExpandMind')}
      expandLayers={record.get('widgetConfig.defaultExpandMindLayers')}
      viewViewConfig={viewViewConfig}
      intl={intl}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutMind */
