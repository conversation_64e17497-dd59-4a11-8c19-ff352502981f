@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

//.lc-low-handlePrint {
//  table {
//    td {
//      border: 0.1px solid;
//    }
//  }
//
//  img {
//    width: 100%;
//  }
//}

.lc-expression-form {
  .c7n-pro-field-label {
    max-width: 1.02rem !important;
    min-width: 1.02rem !important;
    color: #595959 !important;
    padding: 0.04rem 0 0.04rem 0.16rem !important;
  }
  &.nopadding {
    .c7n-pro-select-wrapper {
      width: 100%;
      padding: 0;
    }
  }
  .c7n-pro-select-wrapper {
    width: 100%;
    padding: 1px 10px;
  }
  .lc-assetRelation-info {
    border: 1px solid #cce6ff;
    border-radius: 4px;
    &-select {
      background-color: @primary-x;
      color: @primary-color;
      &:hover {
        background-color: @yq-primary-4-14;
      }
      cursor: pointer;
    }
    &-detail {
      display: flex;
      flex-wrap: wrap;
      padding: 16px;
      > div {
        width: 50%;
        &:last-child {
          width: 100%;
        }
      }
      &-label {
        color: #595959;
      }
    }
    &-title {
      display: flex;
      justify-content: space-between;
      padding: 12px 16px;
      background-color: @table-current-row-bg;
      line-height: 16px;
      .img {
        &-asset {
          margin-right: 8px;
        }
        &-state {
          position: relative;
          top: -17px;
          right: -16px;
          width: unset;
        }
      }
      .name {
        display: flex;
        align-items: center;
        &-link {
          margin: 0 16px;
        }
        .i-icon {
          color: @primary-color;
          visibility: hidden;
          cursor: pointer;
        }
        &-text {
          font-size: 16px;
          font-weight: 500;
          color: #12274d;
        }
      }
    }
    &:hover {
      .lc-assetRelation-info-title {
        .name {
          .i-icon {
            visibility: visible;
          }
        }
      }
    }
    &-lov {
      display: none;
    }
    &-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 39px;
      .i-icon {
        color: @primary-color;
        margin-right: 16px;
      }
    }
  }
}
.lc-asset-info-modal {
  .c7n-pro-modal-body {
    padding-top: 0;
  }
}
