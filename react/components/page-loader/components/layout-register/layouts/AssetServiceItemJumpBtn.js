import React, { useRef, useMemo, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { Button, withErrorBoundary } from '@zknow/components';
import { message, Modal } from 'choerodon-ui/pro';
import { validateMessage, calculateHide1 } from '../../../utils';
import { openPageByUrl } from '@/utils';
import PageLoader from '../../..';

const modalKey = Modal.key();
const Index = observer((props) => {
  const {
    record, dataSet, formDataSet, modal,
    context, relatedFieldCode, instanceId, fromTable,
    tableRecord, btnName, lineButton = false, btnLength,
  } = props;
  const { mode, history, intl, onJumpNewPage, tenantId,
    viewDataSet, pageContext, dsManager, instanceId: parentInstanceId,
    person, personId, parentKey, detailViewId:
    scPageRef,
  } = context;
  const assetId = dataSet.current?.get('id');
  const assetName = dataSet.current?.get('name');
  // 使用人id需要带到服务项提单页面
  const assignedUserId = dataSet.current?.get('assigned_user_id')?.id || '';
  const srcSuffx = `?assetId=${assetId}&assetName=${assetName}&assignedUserId=${assignedUserId}`;
  const parentViewId = viewDataSet?.current?.get('id');
  const name = btnName || record.get('name');
  const viewId = record.get('viewId');
  const viewName = record.get('viewName');
  const detailViewId = record.get('detailViewId');
  const openType = record.get('openType') || 'NEW';
  const viewSize = record.get('viewSize') || 800;
  const hideFooter = record.get('hideFooter');
  const [isHide, setIsHide] = useState();

  let openModal;
  const lastInstance = {
    id: '',
    index: 0,
  };
  const pageRef = useRef();
  const modalStyle = useMemo(() => ({ width: Number(viewSize) }), []);
  useEffect(() => {
    (async () => {
      const funcConfig = { person, personId, tenantId };
      // eslint-disable-next-line no-nested-ternary
      const current = lineButton ? tableRecord : fromTable ? formDataSet?.current : dataSet?.current;
      const hideFlag = await calculateHide1(record, current, mode, parentKey, funcConfig, true);
      setIsHide(hideFlag);
    })();
  }, [formDataSet?.current, dataSet?.current]);

  if ((isHide && !lineButton) || (isHide && lineButton && btnLength === 1)) {
    return null;
  }
  async function openView(result, insId = 'new', fieldMap = {}) {
    let defaultData = {};
    if (result) {
      defaultData = result;
    }
    if (relatedFieldCode && instanceId) {
      defaultData[relatedFieldCode] = instanceId;
    }
    if (openType === 'NEW') {
      if (onJumpNewPage) {
        if (modal) {
          await modal.close();
        }
        let newRecord;
        if (insId !== 'new') {
          const newData = {
            id: insId,
            ...fieldMap,
          };
          newRecord = {
            get: (key) => {
              return newData[key];
            },
            toData: () => newData,
          };
        }
        onJumpNewPage({ viewId, defaultData, record: newRecord });
      } else {
        // 视图ID/实例ID/上一个视图ID/新建后需要跳转的详情视图ID
        history.push({
          pathname: `/lc/engine/${viewId}/${parentInstanceId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`,
          search: history.location?.search,
          state: defaultData, // 新打开视图默认值
        });
      }
    } else if (openType === 'NEWTAB') {
      openPageByUrl(`/lc/engine/${viewId}/${parentInstanceId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`);
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (openModal) {
        await openModal.close();
        if (lastInstance.id === (parentInstanceId)) {
          lastInstance.index += 1;
        } else {
          lastInstance.id = (parentInstanceId);
          lastInstance.index = -1;
        }
      }
      // 对于多层级跳转的视图，需要获取其他视图的数据，可以通过pageContext获取
      const parentViewCode = viewDataSet.current?.get('code');
      const parentFormDataSet = dsManager.get(parentViewId);
      const newPageContext = pageContext || {};
      if (newPageContext[parentViewCode]) {
        newPageContext[parentViewCode].push({
          instanceId: parentInstanceId,
          formDataSet: parentFormDataSet,
          viewDataSet,
        });
      } else {
        newPageContext[parentViewCode] = [{
          instanceId: parentInstanceId,
          formDataSet: parentFormDataSet,
          viewDataSet,
        }];
      }

      const extraModalOptions = {};

      if (hideFooter) {
        extraModalOptions.footer = null;
      }

      openModal = Modal.open({
        title: viewName || name,
        children: (
          <PageLoader
            pageContext={newPageContext}
            viewId={viewId}
            pageRef={pageRef}
            mode="MODIFY"
            defaultData={defaultData}
            openType={openType}
            parentDataSet={dataSet}
            parentFormDataSet={formDataSet}
            instanceId={parentInstanceId}
            detailViewId={detailViewId}
            lastInstance={lastInstance}
            openBy="Button"
            onJumpNewPage={onJumpNewPage}
          />
        ),
        key: `${modalKey}-${viewId}`,
        drawer: openType === 'RIGHT',
        style: modalStyle,
        destroyOnClose: true,
        onOk: async () => {
          if (await validateMessage(pageRef.current?.formDataSet, intl, scPageRef)) {
            dataSet.query();
            openModal = false;
            return true;
          }
          return false;
        },
        onCancel: () => {
          openModal = false;
            pageRef.current?.formDataSet?.reset();
        },
        ...extraModalOptions,
      });
    }
    return true;
  }
  const handleJump = () => {
    let src = '';
    if (record.get('viewId')) {
      return openView();
    }
    if (!record.get('serviceObjectId')) {
      return message.error(intl.formatMessage({ id: 'lcr.renderer.asset.error' }));
    }
    if (record.get('serviceVariableFlag')) {
      src = `${window.location.origin}/#/itsm/portal/service_catalog/${record.get('serviceCatalogType')}/${record.get('variableViewId')}/${record.get('serviceObjectId')}`;
    } else {
      src = `${window.location.origin}/#/itsm/portal/service_catalog/NEW_RECORD/${record.get('variableViewId')}/${record.get('serviceObjectId')}`;
    }
    window.location.href = src + srcSuffx;
  };
  return (
    <Button color={record?.get('color')} icon={record?.get('icon')} onClick={handleJump}>{record?.get('name')}</Button>
  );
});

export default withErrorBoundary(inject('AppState')(
  formatterCollections({
    code: 'lcr.renderer',
  })(Index)
));

/* externalize: LcLayoutAssetServiceItemJumpBtn */
