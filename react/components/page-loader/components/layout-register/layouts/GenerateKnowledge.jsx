import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import GenerateKnowledge from '@/renderer/transform-knowledge';

const Index = observer(({ record, context, feature }) => {
  const { instanceId, viewDataSet, dsManager } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));

  return (
    <GenerateKnowledge
      instanceId={instanceId}
      viewDataSet={viewDataSet}
      formDataSet={formDataSet}
      viewRecord={record}
      feature={feature}
      config={{
        color: record.get('color'),
        icon: record.get('icon'),
        name: record.get('name'),
        id: record.get('id'),
      }}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutGenerateKnowledge */
