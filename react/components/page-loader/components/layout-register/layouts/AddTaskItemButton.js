import React, { useEffect, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { getQueryParams } from '@zknow/utils';
import { withErrorBoundary, Icon } from '@zknow/components';
import { DataSet, Menu, Lov } from 'choerodon-ui/pro';
import axios from 'axios';
import styles from './AddTaskItem.module.less';
import { transformResponse } from '@/components/page-loader/lovConfig';
import { transformField } from '@/components/page-loader/stores/DataSetManager';

const AddTaskItemButton = injectIntl(observer((props) => {
  const { record, feature, context, intl, formDataSet, dataSet: tableDataSet } = props;
  const { tenantId, pageRef } = context;
  const name = record?.get('name');
  const id = record?.get('id');
  const taskLovId = record?.get('widgetConfig.addTaskItemLovId.id');

  const taskDs = useMemo(() => new DataSet({
    autoCreate: true,
    paging: false,
    fields: [
      {
        name: 'field',
        type: 'object',
        lovCode: taskLovId,
        multiple: true,
        lovDefineAxiosConfig: lovCode => ({
          url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
          method: 'GET',
          transformResponse: data => transformResponse(
            data,
            data?.name,
            (map, f) => transformField(
              {
                fieldMap: map,
                field: f,
                // viewId,
                tenantId,
                intl,
              },
            ),
            intl,
            tenantId,
          ),
        }),
        lovQueryAxiosConfig: (lovCode, lovConfig = {}, { data, params }) => {
          lovConfig.method = 'POST';
          const { parentIdField, treeFlag, idField } = lovConfig || {};
          let searchFlag = false;
          const queryParams = getQueryParams(data, ['current_params', '__page_params', parentIdField]);

          Object.keys(queryParams).forEach((v) => {
            if (v.indexOf('search_') !== -1) {
              searchFlag = true;
            }
          });

          if (treeFlag === 'Y' && parentIdField) {
            if (data[parentIdField]) {
              params.size = 999;
            } else if (!searchFlag) {
              queryParams[parentIdField] = '0';
            }
          }

          // eslint-disable-next-line camelcase
          const data_params = {
            ...queryParams,
            ...(data?.current_params || {}),
            ...(params || {}),
          };
          delete data_params?.current_params;

          return {
            url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
            method: 'POST',
            data: {
              params: data_params,
              conditions: [],
            },
            params,
            transformResponse: (originData) => {
              try {
                const jsonData = JSON.parse(originData);
                return {
                  ...jsonData,
                  content: jsonData?.content?.map(item => {
                    if (searchFlag) {
                      // 搜索时，树形结构打平显示
                      return {
                        ...item,
                        isLeaf: true,
                        [parentIdField]: null,
                        id: item[idField] || item.id,
                        primaryKey: item.id,
                      };
                    }
                    return {
                      ...item,
                      id: item[idField] || item.id,
                      primaryKey: item.id,
                    };
                  }) || [],
                };
              } catch (error) {
                return [];
              }
            },
          };
        },
      },
    ],
    events: {
      update: ({ value, dataSet }) => {
        if (value?.length) {
          const template = formDataSet?.current?.get('task_template_id');
          const templateId = template?.id || template;
          if (templateId) {
            tableDataSet.status = 'loading';
            const createFlag = pageRef?.current?.pageRecord?.get?.('viewType') !== 'INSERT';
            // 设计的是任务项必须通过工单子模版生成，因为字段的映射是通过工单模版实现的
            axios.post(`/itsm/v1/${tenantId}/task/sub_templates/apply?templateId=${templateId}`, {
              ticketMap: formDataSet?.current?.toData() || {},
              subMapList: value || [],
              createFlag, // 告诉后端是否需要后台直接创建数据，新建视图不需要直接创建，详情视图需要直接创建
            })
              .then(res => {
                if (!createFlag) {
                  if (Array.isArray(res) && res?.length) {
                    res.forEach((item) => {
                      const r = tableDataSet.create({});
                      r.set(item);
                    });
                    // tableDataSet.appendData(res);
                  }
                } else {
                  tableDataSet.query();
                }
              })
              .catch((err) => {})
              .finally(() => {
                tableDataSet.status = 'ready';
              });
          }
        }
        dataSet.loadData([]);
      },
    },
  }), [taskLovId]);

  // 没选择工单模板时，添加巡检项按钮应该隐藏
  // https://choerodon.com.cn/#/agile/work-list/issue?type=project&id=243303070577803264&name=%E7%94%84%E7%9F%A5%E9%A1%B9%E7%9B%AE%E7%BE%A4&category=AGILE&organizationId=1128&paramIssueId=698274994100039680&paramName=yq-23691
  // 工单模版没有开启子模版时也要隐藏【是不是业务逻辑侵入太多了？】
  return formDataSet?.current?.get('task_template_id') && (formDataSet?.current.getState('subTemplateFlag') || formDataSet?.current?.get('task_template_id:apply_sub_template_flag')) ? (
    <>
      {feature === 'table-action' ? (
        <Menu.Item
          key={id}
          className="c7n-menu-item"
          onClick={() => {}}
        >
          <Icon type="AddFour" style={{ marginRight: 8 }} />
          {name}
        </Menu.Item>
      ) : (
        <Lov
          key={id}
          dataSet={taskDs}
          name="field"
          mode="button"
          clearButton={false}
          funcType="raised"
          color="secondary"
          icon="AddFour"
        >
          <span className={styles.button}>
            <Icon type="AddFour" style={{ marginRight: 8 }} />
            {name}
          </span>
        </Lov>
      )}
    </>
  ) : null;
}));

export default withErrorBoundary(AddTaskItemButton, { fallback: <span /> });

/* externalize: LcLayoutAddTaskItemButton */
