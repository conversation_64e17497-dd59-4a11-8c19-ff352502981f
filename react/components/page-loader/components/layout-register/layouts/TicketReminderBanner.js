import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketReminderBanner from '@/renderer/ticket-reminder-banner';

const Index = observer(({ record, dataSet, context }) => {
  const {
    instanceId,
    tenantId,
    viewDataSet,
    onJumpNewPage,
  } = context;
  const { businessObjectCode, code: viewCode } = viewDataSet.current?.toData();
  const config = record?.toData() || {};

  return (
    <TicketReminderBanner
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      viewId={record?.get('widgetConfig.viewId')}
      config={config}
      onJumpNewPage={onJumpNewPage}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketReminderBanner */
