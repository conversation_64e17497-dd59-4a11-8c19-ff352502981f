import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Tabs, Tooltip } from 'choerodon-ui';
import { Icon, withErrorBoundary } from '@zknow/components';
import classNames from 'classnames';
import { calculateHide } from '../../../utils';
import Preview from '../../../Preview';
import './Tab.less';

const { TabPane } = Tabs;

const Index = observer((props) => {
  const tabRef = useRef(null); // 用于 panel 内的组件找到自己在哪个 tab 中

  const { record, dataSet, className, context, setOverflowDisplay, ...rest } = props;
  const { person, personId, tenantId, mode, parentKey, history: { location: { search } } } = context;
  const tabPosition = record.get('tabPosition') || 'top';
  // 非top类型, 不设置tabType
  const tabType = tabPosition === 'top' ? record.get('tabType') : undefined;
  const [activeKey, setActiveKey] = useState('0'); // ID 不可控
  useEffect(() => {
    if (setOverflowDisplay) setOverflowDisplay('hidden');
  }, []);
  useEffect(() => {
    if (search) {
      let index = '0';
      const objURL = {};
      search.replace(
        new RegExp('([^?=&]+)(=([^&]*))?', 'g'),
        ($0, $1, $2, $3) => {
          objURL[$1] = $3;
        }
      );
        record.children?.forEach((item, i) => {
          if (item.get('name') === decodeURIComponent(objURL.tabName)) {
            index = `${i}`;
          }
        });
        setActiveKey(index);
    }
  }, [search]);

  function renderName(name, icon, index) {
    if (tabPosition === 'top') {
      return (
        <span className="lc-page-loader-tab-name-wrapper">
          {icon && (<Icon
            type={icon}
            className="lc-page-loader-tab-icon"
            theme={`${index}` === activeKey ? 'filled' : 'outline'}
          />)}
          <span className="lc-page-loader-tab-name">{name}</span>
        </span>
      );
    }
    const engReg = new RegExp(/^[A-Za-z]{5}/); // 至少前 5 字母英文
    const shortName = engReg.test(name) ? name.slice(0, 7) : name.slice(0, 4);
    return (
      <Tooltip placement="left" title={name} className="lc-page-loader-tab-right-wrapper">
        <span className="lc-page-loader-tab-right-icon">
          <Icon type={icon} theme={`${index}` === activeKey ? 'filled' : 'outline'} />
        </span>
        <span className="lc-page-loader-tab-right-name">{shortName}</span>
      </Tooltip>
    );
  }

  function filterPane(pane, index) {
    const funcConfig = { person, personId, tenantId };
    const hideFlag = calculateHide(pane, dataSet?.current, mode, parentKey, funcConfig);
    if (hideFlag) {
      return false;
    }
    return true;
  }

  function renderPane(pane, index) {
    const name = pane.get('name');
    const icon = pane.get('icon');

    return (
      <TabPane tab={renderName(name, icon, index)} key={index}>
        {pane.children?.filter(filterPane)?.map(child => (
          <Preview
            key={child.key}
            record={child}
            context={context}
            currentTabRef={tabRef}
          />
        ))}
      </TabPane>
    );
  }

  function handleTabChange(_key) {
    setActiveKey(`${_key}`);
    if (tabPosition !== 'top') {
      const nodeList = document.querySelectorAll('.lc-form-page-loader-content-root');
      if (nodeList?.length) {
        nodeList.forEach(dom => {
          dom.scrollTo(0, 0);
        });
      }
    }
  }

  return (
    <div
      ref={tabRef}
      className={classNames('lc-page-loader-tab', className)}
      {...rest}
      data-tabposition={tabPosition}
    >
      <Tabs
        activeKey={activeKey}
        onChange={handleTabChange}
        type={tabType}
        tabPosition={tabPosition}
        className={tabPosition === 'top' ? '' : 'lc-page-loader-tab-right'}
      >
        {record.children.filter(filterPane).map(renderPane)}
      </Tabs>
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTab */
