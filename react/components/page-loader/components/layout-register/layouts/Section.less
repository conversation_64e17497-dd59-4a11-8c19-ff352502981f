.lc-page-loader-section {
  // 覆盖ui默认样式
  .c7n-tabs-vertical.c7n-tabs-left > .c7n-tabs-content {
    padding-left: 1px;
  }
  &-display {
    background: #fff;
  }
  &-layout {
    display: none;

    &.active {
      display: flex;
    }

    &.ml {
      padding-left: 0.24rem;
    }

    &.hasAnchor {
      padding-top: 0.46rem;
      margin-left: 0.01rem;
    }
  }

  &.hasPadding {
    padding: 0.16rem;
  }

  &.hasAnchor {
    position: relative;
  }
}
