import React, { useState, useRef, useEffect, useMemo } from 'react';
import classnames from 'classnames';
import compact from 'lodash/compact';
import { omit } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';
import { ExternalComponent, ClickText, TableHoverAction, Icon, ModalTitle, useFilter, withErrorBoundary, Button } from '@zknow/components';
import { Popover } from 'choerodon-ui';
import { toJS } from 'mobx';
import {
  Modal, TextField, Tooltip, message, TextArea, Switch, CheckBox,
  DateTimePicker, DatePicker, MonthPicker, YearPicker, TimePicker, NumberField, TreeSelect,
  Select, UrlField, Password, Table, Menu, Lov, DataSet,
  Currency,
} from 'choerodon-ui/pro';
import moment from 'moment';
import AdvancedFilter from '@/components/yq-table-advanced-filter';
import Operators from '@/components/yq-table-filter-operators';
import YQColor from '@/components/yq-color';
import LovSelect from '@/components/lov-select';
import { deltaToHtml, dealDuration, openPageByUrl, getMockDataByWidgetType, MAX_ROW } from '@/utils';
import { columnRenderer, DATE_FIELDS, DEFAULT_DATE_FORMAT } from './utils';
import {
  calculateCondition,
  calculateHide,
  EDIT_BUTTON_TYPES,
  formatUrlParams,
  getCurrentDate,
  TABLE_BUTTON_TYPES,
} from '../../../utils';
import Preview from '../../../Preview';
import RepairSLAHistory from './RepairSLAHistory';
import BatchPressTicket from './BatchPressTicket';
import BatchRepairSLA from './BatchRepairSLA';
import PageLoader from '../../..';
import BatchImport from '@/components/batch-import';
import { openViewById, isInIframe } from '@/utils';
import {
  HistoryDetailForm,
  ExportForm,
  File,
  TableUploadColumn,
  IntlModal,
} from './TableComponents';
import MobileModal from '../../mobile-modal';

import './Table.less';

const { Column } = Table;
const modalKey = Modal.key();
const createModalKey = Modal.key();
const historyModalKey = Modal.key();

const Index = ({ record, className, context, ...rest }) => {
  const {
    dsManager, intl, history, viewId: lastViewId, mode, instanceId, tenantId, personId, person,
    viewDataSet, columnDataSet, prefixCls, tableFilterDataSet, tableViewDataSet,
    exportDataSet, downloadHistoryDataSet, parentKey, onJumpNewPage, tableSuffixes,
    portalProps, onFilterChange, tableFilterId, tableFilterCode, themeColor,
    tableFilterWorkbenchId, tableFilterNotice, // 工作台三连；tableFilterNotice 修改筛选器后通知外部
    formDataSet, AppState, requestItemConfig, scItemViewFlag, serviceItemId,
  } = context;

  const advancedFilterRef = useRef();
  // 获取主题色
  let primaryColor = '#2979ff';
  if (themeColor && themeColor !== 'undefined') {
    const colorArr = themeColor.split(',');
    if (colorArr.length) {
      primaryColor = colorArr[0];
    }
  }
  const fields = record.getCascadeRecords('widgetConfig.fields');
  const buttons = record.getCascadeRecords('widgetConfig.buttons').filter(btn => !btn.get('parentId'));
  const lineButtons = record.getCascadeRecords('widgetConfig.lineButtons');
  // 可搜索
  const filterFlag = record.get('widgetConfig.filterFlag');
  // 默认查询
  const tableAutoQuery = record.get('widgetConfig.autoQueryFlag');
  // 行内控制
  const inlineFlag = record.get('widgetConfig.inlineFlag');
  const inlineFields = record.get('widgetConfig.inlineFields');
  // 树形展示：父级字段
  const parentFieldCode = record.get('widgetConfig.parentFieldCode');
  // 树形展示：多关联对象
  const selectMultipleObject = record.get('widgetConfig.selectMultipleObject');
  // 树形展示flag
  const treeFlag = record.get('widgetConfig.treeFlag');
  // 树形类型：normal自身，association关联
  const treeDisplayType = record.get('widgetConfig.treeDisplayType');
  // 子级树形展示,兼容'[]'脏数据
  const subTreeFlag = treeFlag && treeDisplayType === 'association' && !!selectMultipleObject && selectMultipleObject !== '[]';
  // 头行结构：关联字段
  const relatedFieldCode = record.get('widgetConfig.relatedFieldCode');
  const businessObjectId = record.get('widgetConfig.modelId');
  // 首列点击跳转
  const tableLinkFlag = record.get('widgetConfig.tableLinkFlag');
  const tableLinkType = record.get('widgetConfig.tableLinkType');
  const tableLinkUrl = record.get('widgetConfig.tableLinkUrl');
  const tableLinkViewType = record.get('widgetConfig.tableLinkViewType');
  const tableLinkViewSize = record.get('widgetConfig.tableLinkViewSize');
  const tableLinkViewId = record.get('widgetConfig.viewId');
  const tableLinkViewName = record.get('widgetConfig.viewName');
  // 表格行条件跳转
  const tableLinks = record.get('widgetConfig.tableLinks');
  const tableLinkFieldCode = record.get('widgetConfig.tableLinkFieldCode') || 'id';
  // 行预览
  const previewViewId = record.get('widgetConfig.previewViewId');
  // const previewViewName = record.get('widgetConfig.previewViewName');
  // 表格视图
  const columnViews = viewDataSet?.current?.get('personalColumns') || [];
  const columnViewId = columnViews.find(v => v.defaultFlag)?.id;
  // 表格筛选
  const headMode = record?.get('widgetConfig.headMode');
  const tableFilterFlag = record?.get('widgetConfig.tableFilterFlag') || false;
  const advancedFilterFlag = record?.get('widgetConfig.advancedFilterFlag') || false;
  const tableFilters = viewDataSet?.current?.get('filters') || [];
  const defaultFilterId = tableFilters.find(v => v.defaultFlag)?.id;
  const [filterId, setFilterId] = useState(tableFilterId || defaultFilterId);
  // 视图类型
  const viewType = viewDataSet?.current?.get('viewType');
  const isTableView = viewType === 'TABLE';
  // 快速添加筛选器
  const quickAddFilterFlag = record?.get('widgetConfig.quickAddFilterFlag') || false;
  // 是否允许修改列表导出条件
  const modifyConditionFlag = record.get('widgetConfig.modifyConditionFlag') === undefined || record.get('widgetConfig.modifyConditionFlag');
  // 固定操作列
  const fixedActionFlag = record.get('widgetConfig.fixedActionFlag') || false;
  // 行高
  const rowHeight = record.get('widgetConfig.rowHeight') || 'L';
  // 表格是否显示原始值
  const pristineFlag = !inlineFlag && !(viewType === 'INSERT') && viewType;
  const rowHeightMap = useMemo(() => ({
    L: { rows: 1, resize: 'none', rowHeight: 42, class: '' },
    M: { rows: 2, resize: 'none', rowHeight: 62, class: 'table-line table-line-2' },
    H: { rows: 3, resize: 'none', rowHeight: 82, class: 'table-line table-line-3' },
    SH: { rows: 4, resize: 'none', rowHeight: 102, class: 'table-line table-line-4' },
    AUTO: { rows: 4, resize: 'vertical', rowHeight: 'auto', class: 'table-line-auto' },
    undefined: {},
  }), []);
  // 表格筛选字段
  const tableFilterFields = record.get('widgetConfig.filters') || [];
  const [queryFieldsLimit, setQueryFieldsLimit] = useState(tableFilterFields
    ?.filter(field => field.filterFlag && (field.quickFlag || (field.quickFlag === undefined && !field.fuzzyFlag)))?.length || 3);
  // 汇总设置
  const summaryFlag = record.get('widgetConfig.summaryFlag');
  const summaryPosition = record.get('widgetConfig.summaryPosition') || 'BOTTOM';
  const summaryFields = record.get('widgetConfig.summaryFields');
  const summaryFieldList = JSON.parse(summaryFields || '[]').map(field => field.code);
  // 表格实时编辑标识
  const liveEditFlag = record.get('widgetConfig.liveEditFlag') || false;

  const modalStyle = useMemo(() => ({ width: 520 }), []);
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  const conditionModalStyle = useMemo(() => ({ width: 1000 }), []);
  const bodyModalStyle = useMemo(() => ({ padding: '0.16rem' }), []);
  const ds = dsManager.get(record.get('id'));
  if ((serviceItemId && !requestItemConfig && !scItemViewFlag) || (viewType === 'INSERT' && !serviceItemId && !scItemViewFlag && !requestItemConfig)) {
    ds.paging = false;
  }
  const formDs = dsManager.get(lastViewId);
  const fuzzySearchData = dsManager.getConfig(record.get('id'))?.get('fuzzySearch');
  const pageRef = useRef();
  let modal;
  const lastInstance = {
    id: '',
    index: 0,
  };
  const queryFieldsOverride = useMemo(() => {
    const _queryFieldsOverride = {};
    if (!tableFilterFields?.length) return {};
    tableFilterFields
      .filter(_field => _field && ['DateTime', 'MasterDetail', 'Like', 'RickText'].includes(_field.widgetType))
      .forEach(_field => {
        if (_field.path) {
          if (_field.widgetType === 'DateTime' && _field.isDefaultEndData) {
            _queryFieldsOverride[_field.path] = (<DateTimePicker
              name={_field.path}
              defaultTime={[
                moment('00:00:00', 'HH:mm:ss'),
                moment('23:59:59', 'HH:mm:ss'),
              ]}
            />);
          }
          if (_field.widgetType === 'MasterDetail') {
            _queryFieldsOverride[_field.path] = (
              <Lov name={_field.path} searchMatcher="fuzzy_params_" />
            );
          }
          if (_field.widgetType === 'Like') {
            const { likeText = intl.formatMessage({ id: 'query.likeText' }), dislikeText = intl.formatMessage({ id: 'query.dislikeText' }) } = _field?.widgetConfig;
            _queryFieldsOverride[_field.path] = (
              <Select name={_field.path}>
                <Select.Option value="true">{likeText}</Select.Option>
                <Select.Option value="false">{dislikeText}</Select.Option>
              </Select>
            );
          }
        }
      });
    return _queryFieldsOverride;
  }, [tableFilterFields?.length]);
  /**
   * 计算筛选器快捷搜索字段
   * @param _id
   * @returns {boolean|any}
   */
  function getBaseFilterCode(_id) {
    if (!tableFilterFlag) {
      return false;
    }
    const currentFilter = tableFilters.find(v => v.id === _id);
    return JSON.parse(currentFilter?.personalFilter || '[]');
  }

  useEffect(() => {
    if (tableFilterId) {
      setFilterId(tableFilterId);
      if (ds.queryDataSet?.current) {
        // 切换筛选器 将查询条件初始化
        ds.queryDataSet.current.reset();
        ds.setQueryParameter('__condition', undefined);
        ds.setQueryParameter('__orderBy', undefined);
      }
      refresh(tableFilterId);
      setQueryFieldsLimit(getBaseFilterCode(tableFilterId)?.length
        || tableFilterFields?.filter(field => field.filterFlag
          && (field.quickFlag || (field.quickFlag === undefined && !field.fuzzyFlag)))?.length || 3);
    }
  }, [tableFilterId]);

  useEffect(() => {
    if (tableFilterFlag) {
      ds.setQueryParameter('filter_id', filterId);
    } else if (tableFilterId) {
      ds.setQueryParameter('filter_id', tableFilterId);
    }
    if (tableFilterCode) {
      ds.setQueryParameter('filter_code', tableFilterCode);
    }
    const businessObjectCode = context?.pageRef?.current?.pageRecord?.get('businessObjectCode') || viewDataSet?.current?.get('businessObjectCode');
    if (businessObjectCode) {
      ds?.setState('businessObjectCode', businessObjectCode);
    }
  }, []);

  useFilter(isTableView && (tableAutoQuery === undefined || tableAutoQuery) && !tableFilterId && ds);

  useEffect(() => {
    if (ds && mode === 'PREVIEW') {
      const rowData = {};
      const tableData = [];
      fields?.map((field) => {
        let fieldValue = getMockDataByWidgetType(field.get('widgetType'), intl, undefined, field);
        if (field.get('widgetType') === 'Range') {
          rowData[field.get('widgetConfig.endFieldCode')] = fieldValue.start;
          rowData[field.get('widgetConfig.startFieldCode')] = fieldValue.end;
          fieldValue = {
            [field.get('widgetConfig.endFieldCode')]: fieldValue.start,
            [field.get('widgetConfig.startFieldCode')]: fieldValue.end,
          };
        }
        rowData[field.get('code')] = fieldValue;
        return field;
      });
      for (let i = 0; i < MAX_ROW; i++) {
        tableData.push({ ...rowData, id: i });
      }
      ds.loadData(tableData);
    }
  }, [ds]);

  function refresh(newFilterId = filterId) {
    if (tableFilterFlag) {
      ds.setQueryParameter('filter_id', newFilterId);
    } else if (tableFilterId) {
      ds.setQueryParameter('filter_id', tableFilterId);
    }
    if (tableFilterCode) {
      ds.setQueryParameter('filter_code', tableFilterCode);
    }
    ds.query();
  }

  /**
     * 表格内部筛选器变化
     * @param id
     */
  function filterCallback(id) {
    if (!filterId || filterId !== id) {
      setFilterId(id);
      ds.setState('__SEARCHTEXT__', '');
      ds.setQueryParameter('fuzzy_params_', '');
      // 通知外部筛选器
      if (onFilterChange && filterId !== id) {
        onFilterChange(id);
      } else {
        refresh(id);
      }
    }
  }

  /**
     * 渲染表格可点击列
     * @param props
     * @param widgetType
     * @param renderer
     * @returns {*}
     */
  function renderTableLink(props, widgetType, renderer, viewConfig) {
    const { record: current, name } = props;
    let text = props.text;
    const value = current.get(name);

    if (current.editing) {
      return renderer(props, text || '-');
    }

    // 处理首列点击为时间格式的情况
    if (DATE_FIELDS.includes(widgetType)) {
      text = moment.isMoment(value) ? moment(value).format(DEFAULT_DATE_FORMAT[widgetType]) : text;
    } else if (widgetType === 'RichText') {
      text = deltaToHtml(value);
    }

    // 默认跳转配置

    let view = { ...viewConfig };
    let isNewPage = tableLinkType === 'FIXED_URL' || (tableLinkViewType === 'NEW' && !onJumpNewPage);
    // /lc/engine/视图id/记录id/返回视图id
    let path = `/lc/engine/${tableLinkViewId}${current.get(tableLinkFieldCode)
      ? `/${current.get(tableLinkFieldCode) || current.get(tableLinkFieldCode)?.id}` : ''}${lastViewId ? `/${lastViewId}` : ''}`;
    if (tableLinkType === 'FIXED_URL') {
      path = formatUrlParams(tableLinkUrl, current);
    }
    // 根据配置判断跳转
    if (tableLinks?.length && viewConfig?.index === 0) {
      const funcConfig = { person, personId, tenantId };
      tableLinks.map(link => {
        const {
          condition,
          tableLinkType: linkType,
          tableLinkUrl: linkUrl,
          tableLinkViewSize: linkViewSize,
          tableLinkViewType: linkViewType,
          viewId: linkViewId,
          viewName: linkViewName,
          tableLinkTicketType,
          tableLinkSubObject,
        } = link;
        const result = calculateCondition(parentKey, condition, current, funcConfig);
        const jumpSubTaskFlag = current?.get('__mainObjectDataId') && tableLinkTicketType === 'child' && current?.get('__subObjectId') === tableLinkSubObject?.id;
        // 新增逻辑列表子任务跳转配置的子级条件
        if (result && (jumpSubTaskFlag || (!current.get('__mainObjectDataId') && tableLinkTicketType !== 'child'))) {
          view = {
            viewSize: linkViewSize,
            openType: linkViewType,
            viewId: linkViewId,
            viewName: linkViewName,
          };
          isNewPage = linkType === 'FIXED_URL' || (linkViewType === 'NEW' && !onJumpNewPage);
          if (tableLinkTicketType === 'child') {
            path = '';
          } else {
            path = `/lc/engine/${linkViewId}${current.get(tableLinkFieldCode)
              ? `/${current.get(tableLinkFieldCode) || current.get(tableLinkFieldCode)?.id}` : ''}${lastViewId ? `/${lastViewId}` : ''}`;
          }
          if (linkType === 'FIXED_URL') {
            path = formatUrlParams(linkUrl, current);
          }
        }
        return link;
      });
    }
    return (
      <ClickText
        record={current}
        onMouseDown={() => openView(view, current.dataSet, current)}
        valueField={name}
        history={isNewPage && !view?.fieldViewFlag && history}
        path={isNewPage && !view?.fieldViewFlag && path}
        isLowCode
      >
        {renderer(props, text || `${viewConfig?.index === 0 ? '-' : ''}`)}
      </ClickText>
    );
  }

  function renderPopoverContent(value, randomKey) {
    if (value) {
      try {
        const fileList = JSON.parse(value);
        if (fileList?.length) {
          return fileList.map((file) => {
            let fileKey = file;
            let fileSize = 0;
            if (typeof file === 'object') {
              fileKey = file.fileKey;
              fileSize = file.fileSize;
            }
            return (
              <File
                fileKey={fileKey}
                fileSize={fileSize}
                intl={intl}
                popoverId={randomKey}
              />
            );
          });
        } else {
          return <File fileKey={value} fileSize={0} intl={intl} popoverId={randomKey} />;
        }
      } catch (e) {
        return <File fileKey={value} fileSize={0} intl={intl} popoverId={randomKey} />;
      }
    }
    return '';
  }

  function openFileListModal(value, rowRecord, fieldName, isEdit, field) {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.file.list', defaultMessage: '文件列表' }),
      children: (
        <TableUploadColumn
          dataRecord={rowRecord}
          fieldCode={fieldName}
          isEdit={isEdit}
          configData={field}
          intl={intl}
          tenantId={tenantId}
        />
      ),
      key: modalKey,
      style: modalStyle,
      drawer: true,
      destroyOnClose: true,
      footer: null,
    });
  }

  function renderFileColumn(value, rowRecord, fieldName, isEdit, field) {
    let fileName = value;
    const singleFlag = field?.widgetConfig?.fileFormat === 'single';
    if (singleFlag) {
      // eslint-disable-next-line no-nested-ternary
      fileName = value || isEdit ? (
        <TableUploadColumn
          dataRecord={rowRecord}
          fieldCode={fieldName}
          isEdit={isEdit}
          configData={field}
          intl={intl}
          tenantId={tenantId}
        />
      ) : '-';
    } else {
      const fileList = value ? JSON.parse(value) : [];
      fileName = isEdit ? (
        <>
          {`${fileList.length}个附件`}
          <Button funcType="flat" color="secondary" style={{ marginLeft: 4, fontWeight: 'normal' }} onClick={() => openFileListModal(value, rowRecord, fieldName, isEdit, field)}>
            {intl.formatMessage({ id: 'lcr.components.desc.file.edit', defaultMessage: '编辑附件' })}
          </Button>
        </>
      ) : (
        <>
          {`${fileList.length}个附件`}
          <Icon style={{ marginLeft: 4 }} onClick={() => openFileListModal(value, rowRecord, fieldName, isEdit, field)} type="ExpandLeft" />
        </>
      );
    }
    return (
      <span className={`${prefixCls}-table-file`}>
        {fileName}
      </span>
    );
  }

  function renderPopoverText(value, randomKey) {
    let fileName = value;
    if (value) {
      try {
        const fileList = JSON.parse(value);
        if (fileList?.length > 1) {
          fileName = intl.formatMessage({ id: 'lcr.components.desc.click.preview', defaultMessage: '点击查看' });
        } else {
          const nameStrList = (fileList[0].fileKey || fileList[0])?.split('@');
          fileName = nameStrList?.length ? nameStrList[nameStrList.length - 1] : value;
        }
      } catch (e) {
        const nameStrList = value?.split('@');
        fileName = nameStrList?.length ? nameStrList[nameStrList.length - 1] : value;
      }
    }
    return (
      <span id={randomKey} className={`${prefixCls}-table-popover`}>
        {fileName}
      </span>
    );
  }

  const renderColumnFooter = (dataSet, name, precision) => {
    const summaryValue = dataSet?.reduce((sum, record) => {
      const n = record.get(name) || 0;
      return sum += n;
    }, 0);
    return (
      <div className='lc-table-footer'>
        {NumberField.format(summaryValue, undefined, { minimumFractionDigits: precision })}
      </div>
    );
  };

  function renderColumn(field = {}, index) {
    const {
      code, name, autoWidthFlag, minWidth = 150, width, align = 'left', widgetType,
      relationLovId, placeHolder, help, sortableFlag, calculatedFlag, scope, module, lock,
    } = field;
    const {
      options, dataSource, renderer, attributeFields, format, passTimeFlag,
      onlyLeafFlag, relationLovNameFieldCode, relationLovValueFieldCode,
      variableFilter, condition, displayMethod, durationMode, precision,
    } = field?.widgetConfig || {};
    const fieldToView = field?.jsonData ? JSON.parse(field?.jsonData)?.fieldToView : {};
    // 根据表格配置的可行内编辑字段，判断当前字段是否可编辑
    let inlineFieldList = [];
    let inlineEditingFlag = false;
    if (inlineFlag) {
      try {
        inlineFieldList = JSON.parse(inlineFields || '[]');
      } catch (e) {
        inlineFieldList = [];
      }
      inlineEditingFlag = inlineFieldList?.find(inline => inline.code === code);
    }
    const columnProps = {
      name: code,
      header: name,
      minWidth,
      align,
      lock: lock ? 'left' : false,
    };

    const editMode = inlineEditingFlag && liveEditFlag;

    // 列宽
    if (!autoWidthFlag && width) {
      columnProps.width = width;
    }

    // c7n组件和moment日期格式不一致，需要转换
    const realFormat = format?.replace('yyyy', 'YYYY')?.replace('mm', 'MM')?.replace('dd', 'DD')?.replace(':MM', ':mm')
      || DEFAULT_DATE_FORMAT[widgetType];
    // 根据不同类型，使用不同的渲染器
    switch (widgetType) {
      case 'Input':
      case 'TextArea':
      case 'EmailField':
        // 默认单行文本
        columnProps.tooltip = 'none';
        if (inlineFlag) {
          columnProps.renderer = ({ name: fieldName, record: current, text }) => {
            if ((current.editing && inlineEditingFlag) || editMode) {
              if (widgetType === 'TextArea') {
                return <TextArea
                  name={fieldName}
                  record={current}
                  style={{ width: '100%' }}
                  resize={rowHeightMap[rowHeight]?.resize}
                  rows={rowHeightMap[rowHeight]?.rows}
                />;
              }
              return <TextField name={fieldName} record={current} style={{ width: '100%' }} />;
            }
            return <Tooltip title={text} placement="topLeft"><span
              className="lc-table-row-text"
            >{text}</span></Tooltip>;
          };
        } else {
          columnProps.renderer = ({ text }) => {
            return <Tooltip title={text} placement="topLeft"><span
              className="lc-table-row-text"
            >{text}</span></Tooltip>;
          };
        }
        break;
      case 'Currency':
        if (summaryFieldList.includes(code) && summaryFlag && summaryPosition === 'BOTTOM') {
          columnProps.footer = (dataSet, name) => renderColumnFooter(dataSet, name, 2);
        }
        columnProps.renderer = ({ record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <Currency name={fieldName} record={current} style={{ width: '100%' }} />;
          }
          return <span>{text}</span>;
        };
        break;
      case 'FloatNumber':
      case 'NumberField':
        if (summaryFieldList.includes(code) && summaryFlag && summaryPosition === 'BOTTOM') {
          columnProps.footer = (dataSet, name) => renderColumnFooter(dataSet, name, widgetType === 'FloatNumber' ? precision : 0);
        }
        columnProps.renderer = ({ record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            const step = widgetType === 'FloatNumber' && precision ? 10 ** -precision : 1;
            return <NumberField name={fieldName} step={step} precision={widgetType === 'FloatNumber' && precision ? precision : 0} record={current} style={{ width: '100%' }} />;
          }
          return text;
        };
        break;
      case 'Switch':
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <Switch name={fieldName} record={current} />;
          }
          return value ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
        };
        break;
      case 'CheckBox':
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <CheckBox name={fieldName} record={current} />;
          }
          return value ? intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }) : intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
        };
        break;
      case 'SelectBox':
      case 'MultipleSelect':
        columnProps.tooltip = 'none';
        columnProps.renderer = ({ value, record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return (
              <Select
                name={fieldName}
                record={current}
                searchable
                multiple=","
                optionsFilter={(node) => {
                  if (node && !node.get('enabledFlag') && dataSource === 'lookup') {
                    if (current?.get(fieldName)?.find(nodeCode => nodeCode === node?.get('code'))) {
                      return true;
                    }
                    return false;
                  }
                  return true;
                }}
              />
            );
          }
          if (dataSource === 'optionSet') {
            return value === text ? (options?.find(item => item?.value === value)?.meaning || text) : text;
          }
          return text;
        };
        break;
      case 'Radio':
        columnProps.renderer = ({ value, record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return (
              <Select
                name={fieldName}
                record={current}
                searchable
                optionsFilter={(node) => {
                  if (node && !node.get('enabledFlag') && dataSource === 'lookup') {
                    if (current?.get(fieldName) === node?.get('code')) {
                      return true;
                    }
                    return false;
                  }
                  return true;
                }}
              />
            );
          }
          if (dataSource === 'optionSet') {
            return value === text ? (options?.find(item => item?.value === value)?.meaning || text) : text;
          }
          return text;
        };
        break;
      case 'Select':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value, record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return (
              <TreeSelect
                name={fieldName}
                searchable
                record={current}
                onOption={(({ record: node }) => {
                  if (node?.children && onlyLeafFlag) {
                    return {
                      disabled: true,
                    };
                  }
                  if (node && !node.get('enabledFlag') && dataSource === 'lookup') {
                    return {
                      disabled: true,
                    };
                  }
                  return {};
                })}
                optionsFilter={(node) => {
                  if (node && !node.get('enabledFlag') && dataSource === 'lookup') {
                    if (current?.get(fieldName) === node?.get('code')) {
                      return true;
                    }
                    return false;
                  }
                  return true;
                }}
              />
            );
          }
          if (dataSource === 'optionSet') {
            return value === text ? (options?.find(item => item?.value === value)?.meaning || text) : text;
          }
          return text;
        };
        break;
      case 'Url':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <UrlField name={fieldName} record={current} />;
          }
          return (
            <a href={value} target="_blank" rel="noreferrer">
              {value}
            </a>
          );
        };
        break;
      case 'DateTime':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <DateTimePicker name={fieldName} record={current} min={passTimeFlag ? undefined : moment()} />;
          }
          return <span>{value ? moment(value).format(realFormat) : ''}</span>;
        };
        break;
      case 'Date':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            const props = {
              name: fieldName,
              min: passTimeFlag ? undefined : moment(),
              record: current,
            };
            const COMPONENTS = {
              'YYYY-MM-DD': <DatePicker {...props} />,
              YYYYMMDD: <DatePicker {...props} />,
              'YYYY-MM': <MonthPicker {...props} />,
              YYYY: <YearPicker {...props} />,
            };
            return COMPONENTS[format] || <DatePicker {...props} />;
          }
          return <span>{value ? moment(value).format(realFormat) : ''}</span>;
        };
        break;
      case 'Time':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value, record: current, name: fieldName }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <TimePicker name={fieldName} record={current} />;
          }
          return (value ? moment(value).format(realFormat) : '');
        };
        break;
      case 'Password':
        columnProps.renderer = ({ record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <Password name={fieldName} record={current} />;
          }
          return text;
        };
        break;
      case 'MasterDetail':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            if (displayMethod === 'DROP_DOWN') {
              return (
                <LovSelect
                  current={current}
                  name={fieldName}
                  help={help}
                  config={{
                    textField: relationLovNameFieldCode,
                    valueField: relationLovValueFieldCode,
                    relationLovId,
                    variableFilter,
                    condition,
                    placeHolder,
                  }}
                />
              );
            }
            const restConfig = displayMethod === 'DROP_DOWN_LIST'
              ? {
                viewMode: 'popup',
                popupSearchMode: 'single',
                searchMatcher: 'fuzzy_params_',
              }
              : {};
            return (
              <Lov
                record={current}
                name={fieldName}
                help={help}
                {...restConfig}
                tableProps={{
                  onRow: ({ record: r }) => ({
                    isLeaf: r.get('isLeaf'),
                  }),
                  className: 'lc-lov-table',
                }}
              />
            );
          }
          // TODO: 如果多对一字段未设置relationLovId，text取不到到值，导致点击组件报错
          return text || (current.get(fieldName) && current.get(fieldName)[relationLovNameFieldCode]) || (current.get(fieldName) && '-');
        };
        break;
      case 'Image':
        columnProps.renderer = ({ value, record: rowRecord, name: fieldName }) => {
          const isEdit = (rowRecord.editing && inlineEditingFlag) || editMode;
          return renderFileColumn(value, rowRecord, fieldName, isEdit, field);
        };
        break;
      case 'Upload':
        columnProps.renderer = ({ value, record: rowRecord, name: fieldName }) => {
          const isEdit = (rowRecord.editing && inlineEditingFlag) || editMode;
          return renderFileColumn(value, rowRecord, fieldName, isEdit, field);
        };
        columnProps.tooltip = 'none';
        break;
      case 'RichText':
        columnProps.tooltip = 'overflow';
        columnProps.renderer = ({ value }) => {
          // 新版本为html，老版本为数组[]
          let res = '';
          if (typeof value === 'string') {
            res = value.replace(/<figure[^>]*>.*?<\/figure>/gs, (match) => {
              // 如果<figure>标签内包含<img>标签，则返回"[图片]"
              if (match.includes('<img')) {
                return `[${intl.formatMessage({ id: 'lcr.components.desc.picture', defaultMessage: '图片' })}]`;
              }
              // 否则返回空
              return '';
            });
            res = res.replace(new RegExp('<[^>]*>', 'g'), ' ');
            res = res.replace(new RegExp('\\s+', 'g'), ' ');
            res = res.replace(new RegExp('&nbsp;', 'g'), '');
            res = res.replace(new RegExp('&quot;', 'g'), '"');
          } else {
            const deltaValue = value?.ops || value;
            if (deltaValue && Array.isArray(toJS(deltaValue))) {
              deltaValue.forEach(v => {
                res += v?.insert?.image || v?.insert;
              });
            }
            res = res.replace('\n', '');
          }
          return <span>{res}</span>;
        };
        break;
      case 'Duration':
        columnProps.renderer = ({ value }) => dealDuration({ value, intl, durationMode });
        break;
      case 'ColorPicker':
        columnProps.renderer = ({ name: fieldName, record: columnRecord }) => (
          <YQColor
            name={fieldName}
            record={columnRecord}
            preview
            addonAfter={field?.widgetConfig?.showColorValue}
          />
        );
        break;
      case 'MobileField':
        columnProps.renderer = ({ record: current, name: fieldName, text }) => {
          if ((current.editing && inlineEditingFlag) || editMode) {
            return <NumberField name={fieldName} numberGrouping={false} record={current} style={{ width: '100%' }} />;
          }
          return <span>{text}</span>;
        };
        break;
      case 'Like':
        columnProps.renderer = ({ value }) => {
          return <span>{value ? field?.widgetConfig?.likeText : field?.widgetConfig?.dislikeText}</span>;
        };
        break;
      default:
        columnProps.tooltip = 'overflow';
        break;
    }

    // 仅设置了属性字段，默认渲染为STATUS_TAG
    if (attributeFields && attributeFields.length) {
      const lastRenderer = columnProps.renderer;
      columnProps.tooltip = 'none';
      columnProps.renderer = (params) => {
        const { record: current, text } = params;
        if ((current.editing && inlineEditingFlag) || editMode) {
          return lastRenderer ? lastRenderer(params) : text;
        }
        return columnRenderer(params, 'STATUS_TAG', attributeFields, intl);
      };
    }

    // 设置了渲染器
    if (renderer) {
      const lastRenderer = columnProps.renderer;
      columnProps.tooltip = 'none';
      columnProps.renderer = (params) => {
        const { record: current, text } = params;
        if (current.editing) {
          return lastRenderer ? lastRenderer(params) : text;
        }
        return columnRenderer(params, renderer, attributeFields, intl);
      };
    }

    if (scope && module) {
      columnProps.renderer = (props) => (
        <ExternalComponent
          system={{ scope, module }}
          tenantId={tenantId}
          onJumpNewPage={onJumpNewPage}
          businessObjectCode={context?.pageRef?.current?.pageRecord?.get('businessObjectCode')}
          {...props}
        />
      );
    }

    // 设置了表格列跳转
    if ((tableLinkFlag && index === 0) || fieldToView?.viewId) {
      const lastRenderer = columnProps.renderer;
      const nextRenderer = (props, text) => {
        props.text = text;
        return lastRenderer ? lastRenderer(props, renderer, attributeFields) : text;
      };
      const view = index === 0 ? {
        viewSize: tableLinkViewSize,
        openType: tableLinkViewType,
        viewId: tableLinkViewId,
        viewName: tableLinkViewName,
        index,
      } : {
        viewSize: fieldToView?.tableLinkViewSize,
        openType: fieldToView?.tableLinkViewType,
        viewId: fieldToView?.viewId,
        viewName: fieldToView?.viewName,
        fieldViewFlag: true,
        name: code,
        index,
      };
      columnProps.renderer = (props) => renderTableLink(props, widgetType, nextRenderer, view);
    }

    return (
      <Column
        {...columnProps}
        sortable={viewType === 'INSERT' ? false : sortableFlag || sortableFlag === undefined}
      />
    );
  }

  /**
     * 根据视图渲染表格列
     * @returns {*[]|*}
     */
  function renderColumns() {
    if (tableFilterFlag) {
      let tableFields;
      const filterRecord = tableFilterDataSet.find(r => r.get('id') === filterId);
      const personalColumn = filterRecord ? JSON.parse(filterRecord?.get('personalColumn') || '{}') : {};
      if (Object.keys(personalColumn).length > 0) {
        tableFields = Object.keys(personalColumn).map(i => ({
          name: i,
          sort: personalColumn[i].sort,
          lock: personalColumn[i].lock,
          hidden: personalColumn[i].hidden,
        })).sort((a, b) => (a?.sort ?? Number.MAX_SAFE_INTEGER) - (b?.sort ?? Number.MAX_SAFE_INTEGER));
        const mergeFields = [];
        // 先按照筛选器中存在的字段排序，然后再添加新的字段
        tableFields?.forEach(v => {
          let fieldData = {};
          const field = fields.find(i => i.get('code') === v.name);
          const fieldRecord = columnDataSet.find(f => f.get('objectFieldPath') === field?.get('code'));
          // 兼容修复数据
          if (!field?.get('widgetConfig') && fieldRecord) {
            fieldData = {
              ...(fieldRecord?.toData() || {}),
              ...(field?.toData() || {}),
              name: field.get('name') || fieldRecord.get('label'),
              lock: v.lock,
              hidden: v.hidden,
            };
            mergeFields.push(fieldData);
          } else if (field) {
            fieldData = {
              ...field.toData(),
              lock: v.lock,
              hidden: v.hidden,
            };
            mergeFields.push(fieldData);
          }
        });
        fields.forEach(v => {
          const field = mergeFields.find(i => i.code === v.get('code'));
          if (!field) {
            mergeFields.push(v.toData());
          }
        });
        return mergeFields.map((item, index) => renderColumn(item, index));
      }
    }

    return fields?.map((field, index) => {
      let fieldData = {};
      const fieldRecord = columnDataSet.find(f => f.get('objectFieldPath') === field?.get('code'));
      // 兼容修复数据
      if (!field?.get('widgetConfig') && fieldRecord) {
        fieldData = {
          ...(fieldRecord?.toData() || {}),
          ...(field?.toData() || {}),
          name: field.get('name') || fieldRecord.get('label'),
        };
      } else if (field) {
        fieldData = field.toData();
      }
      return renderColumn(fieldData, index);
    });
  }

  async function handleImport(btn) {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.batch.import', defaultMessage: '批量导入' }),
      children: <BatchImport businessObjectId={businessObjectId} handleDownloadTemplateAndData={handleExport} btnInfo={btn} />,
      key: modalKey,
      style: modalStyle,
      bodyStyle: { padding: 0 },
      destroyOnClose: true,
      okCancel: false,
      okText: intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }),
    });
  }

  const funTypeMap = (r) => ({
    IMPORT: () => { handleImport(r); },
    EXPORT: handleExport,
    EXPORT_HISTORY: handleWatchHistory,
    REFRESH: () => ds?.query(),
    CONFIG: () => openViewById(lastViewId),
  });

  /**
     * 渲染按钮
     * @returns {*[]}
     */
  function getButtons() {
    const btns = [];
    // 变量视图表格不支持操作
    if (requestItemConfig) {
      return btns;
    }
    buttons.map(btn => {
      // 只读模式限制服务请求组件内表格操作
      if (scItemViewFlag && mode === 'DISABLED' && EDIT_BUTTON_TYPES.includes(btn.get('type'))) {
        return null;
      }
      // 表格按钮特殊处理
      if (TABLE_BUTTON_TYPES.includes(btn.get('type'))) {
        btn.setState('tableAction', funTypeMap(btn)[btn.get('type')]);
      }
      const funcConfig = { person, personId, tenantId };
      const parentDs = formDataSet || dsManager.get(lastViewId);
      let hideFlag = false;
      if (parentDs?.current) {
        hideFlag = calculateHide(btn, parentDs?.current, mode, parentKey, funcConfig);
      }
      if (!hideFlag) {
        btns.push(
          <Preview
            key={btn.key}
            record={btn} // 当前按钮
            dataSet={ds} // 当前表格Ds
            formDataSet={formDs} // 表格所属视图的Ds
            relatedFieldCode={relatedFieldCode} // 表格行关联单据头的字段
            instanceId={instanceId} // 单据头的主键
            fromTable
            tableId={record?.get('id')}
            context={omit(context, 'modal')}
            tableRecord={record}
            funcType="raised"
          />
        );
      }

      return btn;
    });
    return btns;
  }

  const saveExportField = async (fieldData = []) => {
    let url = `lc/v1/${tenantId}/field/export/storage?viewId=${exportDataSet.current.get('viewId')}`;
    if (ds.getQueryParameter('filter_code')) {
      url += `&filterCode=${ds.getQueryParameter('filter_code')}`;
    }
    if (ds.getQueryParameter('filter_id')) {
      url += `&filterId=${ds.getQueryParameter('filter_id')}`;
    }
    const res = await axios.post(url, { fields: fieldData });
    if (res?.failed) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.export.memory.save.fail', defaultMessage: '导出字段记忆保存失败' }));
    }
  };
  // 导出
  function handleExport(templateInfo) {
    const fromImportFlag = !!templateInfo?.id;
    const orderBy = record.get('widgetConfig.orderBy');
    exportDataSet.create({
      type: 'EXPORT',
      viewId: lastViewId,
      objectId: businessObjectId,
      asyncFlag: ds.totalCount > 1000,
      basicViewId: columnViewId,
      basicFilterId: filterId,
      totalCount: ds.totalCount,
    });
    Modal.open({
      title: fromImportFlag ? intl.formatMessage({ id: 'lcr.components.pageLoader.downloadTemplateAndData', defaultMessage: '下载模板及数据' }) : intl.formatMessage({ id: 'lcr.components.pageLoader.export', defaultMessage: '导出' }),
      className: 'yqcloud-export-modal',
      children: (
        <ExportForm
          dataSet={exportDataSet}
          businessObjectId={businessObjectId}
          tenantId={tenantId}
          prefixCls={prefixCls}
          intl={intl}
          lastViewId={lastViewId}
          tableFilterDataSet={tableFilterDataSet}
          tableViewDataSet={tableViewDataSet}
          columnDataSet={columnDataSet}
          defaultFilterId={filterId}
          defaultColumnViewId={columnViewId}
          modifyConditionFlag={modifyConditionFlag}
          queryDataSet={ds.queryDataSet}
          fuzzySearchData={fuzzySearchData}
          fields={fields}
          tableDataSet={ds}
          viewDataSet={viewDataSet}
          datasetId={record.get('id')}
          templateInfo={templateInfo}
          relatedFieldCode={relatedFieldCode}
          instanceId={instanceId}
          tableOrderConfig={orderBy}
        />
      ),
      key: createModalKey,
      style: conditionModalStyle,
      destroyOnClose: true,
      onOk: async () => {
        const sortFields = exportDataSet.getState('sortFields') || [];
        const sourceData = exportDataSet.current.toData();
        const { viewId, type, objectId, asyncFlag, viewName } = sourceData;
        sourceData.fields = sortFields;
        const postMap = {
          viewId,
          type,
          objectId,
          asyncFlag,
          filterId: ds.getQueryParameter('filter_id'),
          filterCode: ds.getQueryParameter('filter_code'),
          businessObjectId,
          params: {
            fuzzy_params_: ds.getState('__SEARCHTEXT__'),
          },
          jsonData: JSON.stringify(sourceData),
          lcCondition: JSON.stringify({
            filterUuid: uuidv4(),
            condition: 'AND',
            field: relatedFieldCode,
            filter: 'is',
            fieldValueType: 'CONSTANT',
            fieldValue: instanceId,
          }),
          labelOrderBy: orderBy,
        };
        if (templateInfo?.id) {
          postMap.templateId = templateInfo.id;
        }
        // 导出提交
        if (asyncFlag) {
          exportDataSet.setState('loading', true);
          const result = await axios.post(`data/v1/${tenantId}/dataTasks`, JSON.stringify(postMap));
          if (result && !result.failed) {
            message.success(intl.formatMessage({ id: 'lcr.components.desc.export.running', defaultMessage: '导出进行中' }));
            saveExportField(sourceData.fields);
          } else {
            message.error(intl.formatMessage({ id: 'lcr.components.desc.export.failed', defaultMessage: '导出失败' }));
          }
        } else {
          exportDataSet.setState('loading', true);
          const result = await axios.post(
            `dataread/v1/${tenantId}/dataTasks/immediate`,
            JSON.stringify(postMap),
            { responseType: 'blob' },
          );
          if (result && !result.failed && result.type !== 'application/json') {
            const blob = new Blob([result]);
            let filename = '';
            if (templateInfo?.name && templateInfo?.objectName) {
              filename = `${templateInfo.objectName}-${templateInfo.name}.xlsx`;
            } else if (viewName) {
              filename = `${viewName}-${getCurrentDate()}.xlsx`;
            } else {
              filename = `${getCurrentDate()}.xlsx`;
            }
            if ('msSaveOrOpenBlob' in navigator) {
              // ie使用的下载方式
              window.navigator.msSaveOrOpenBlob(blob, filename);
            } else {
              const elink = document.createElement('a');
              // 设置下载文件名
              elink.download = filename;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              document.body.removeChild(elink);
            }
            message.success(intl.formatMessage({ id: 'lcr.components.desc.export.success', defaultMessage: '导出成功' }));
            saveExportField(sourceData.fields);
          } else {
            message.error(intl.formatMessage({ id: 'lcr.components.desc.export.failed', defaultMessage: '导出失败' }));
          }
        }
        exportDataSet.setState('loading', false);
        return false;
      },
      onClose: () => {
        exportDataSet.reset();
      },
    });
  }

  // 查看历史
  function handleWatchHistory() {
    Modal.open({
      title: (<ModalTitle title={intl.formatMessage({ id: 'lcr.components.desc.export.history', defaultMessage: '导出历史' })} dataSet={downloadHistoryDataSet} />),
      className: 'yqcloud-history-modal',
      children: (
        <HistoryDetailForm
          dataSet={downloadHistoryDataSet}
          businessObjectId={businessObjectId}
          tenantId={tenantId}
          prefixCls={prefixCls}
          intl={intl}
        />
      ),
      drawer: true,
      key: historyModalKey,
      style: mdModalStyle,
      destroyOnClose: true,
      footer: (okbtn, cancel) => [],
    });
  }

  /**
     * 表格面包按钮
     * 不常用操作
     * @returns {*[]|Array}
     */
  function getTableActions() {
    const actions = [];

    const customActions = (record.getCascadeRecords('widgetConfig.actions') || []).map(action => {
      const funcConfig = { person, personId, tenantId };
      const hideFlag = calculateHide(action, ds?.current, mode, parentKey, funcConfig);
      if (hideFlag) {
        return null;
      }
      // 自定义按钮
      if (action?.get('scope') && action.get('module')) {
        return {
          name: action.get('name'),
          onClick: () => {
          },
          element: (
            <Preview
              record={action}
              dataSet={ds}
              feature="table-action" // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
              context={context}
            />
          ),
        };
      }

      // 配置按钮
      const type = action.get('type');
      const tag = action.get('tag');
      if (tag === 'Button' && TABLE_BUTTON_TYPES.includes(type)) {
        return {
          name: action?.get('name'),
          onClick: () => {
          },
          element: (
            <Menu.Item
              key={action?.get('id')}
              onClick={funTypeMap(action)[type]}
            >
              <div className="lc-table-action-item">
                <Icon type={action?.get('icon')} />
                <span className="lc-table-action-item-name">
                  {action?.get('name')}
                </span>
              </div>
            </Menu.Item>
          ),
        };
      }
      if (tag === 'RepairSLAHistory') {
        return {
          name: action.get('name'),
          onClick: () => {
          },
          element: (
            <RepairSLAHistory
              record={action}
              dataSet={ds}
              feature="table-action" // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
              context={context}
            />
          ),
        };
      }
      if (tag === 'BatchRepairSLA') {
        return {
          name: action.get('name'),
          onClick: () => {
          },
          element: (
            <BatchRepairSLA
              record={action}
              dataSet={ds}
              feature="table-action"
              context={context}
            />
          ),
        };
      }
      if (tag === 'BatchPressTicket') {
        return {
          name: action.get('name'),
          onClick: () => {
          },
          element: (
            <BatchPressTicket
              record={action}
              dataSet={ds}
              feature="table-action"
              context={context}
            />
          ),
        };
      }
      return null;
    });
    return [...compact(customActions), ...actions];
  }

  const previewView = useMemo(() => {
    return (
      <div className="lc-table-row-preview-content">
        <PageLoader
          instanceId={ds?.current?.get('id')}
          viewId={previewViewId}
          pageRef={pageRef}
          mode="OUTPUT"
          parentDataSet={ds}
          parentFormDataSet={formDs}
          lastInstance={lastInstance}
          showHeaderFlag
          infoFlag
        />
      </div>
    );
  }, [previewViewId, ds?.current?.get('id')]);

  /**
     * 表格行预览
     * 每行的第一列叹号
     */
  const getRowAction = () => {
    if (!previewViewId) {
      return null;
    }
    return (
      <Popover
        placement="leftTop"
        popupClassName="lc-table-row-preview-popover"
        content={previewView}
        overlayStyle={{ width: 624 }}
        // title={previewViewName}
        trigger="click"
        autoAdjustOverflow
        arrowPointAtCenter
      >
        <Icon
          type="info"
          onClick={() => {
          }}
          className={classnames('lc-page-loader-table-rowAction', {
            selection: ds?.props?.selection,
          })}
        />
      </Popover>
    );
  };

  /**
     * 打开视图
     * @param view
     * @param dataSet
     * @param current
     * @param viewMode
     * @param defaultData
     * @returns {boolean}
     */
  async function openView(view = {}, dataSet, current, viewMode = 'MODIFY', defaultData) {
    const { viewSize, openType, viewId, viewName, name, btnType, fieldViewFlag } = view;
    const viewModalStyle = { width: Number(viewSize) };

    function getInstanceId() {
      if (btnType === 'CREATE' || btnType === 'COPY') {
        return undefined;
      } else if (btnType === 'OPEN') {
        return current.get('id');
      } else if (fieldViewFlag) {
        if (current.get(name)?.id) {
          return current.get(name)?.id;
        } else {
          const realName = name.split(':');
          if (realName.length > 1) {
            realName.pop();
          }
          return current.get(realName.join(':'));
        }
      } else if (current.get('__mainObjectDataId')) { // 子任务id
        current.get('id');
      } else {
        return current.get(tableLinkFieldCode)?.id || current.get(tableLinkFieldCode);
      }
    }

    if (openType === 'NEW') {
      if (onJumpNewPage && !fieldViewFlag) {
        onJumpNewPage({ record: current, viewId, defaultData, instanceId: getInstanceId() });
      } else if (onJumpNewPage) {
        const actualInstanceId = getInstanceId();
        const tempRecord = (new DataSet({})).create({ number: current.get(tableLinkFieldCode)?.number, id: actualInstanceId });
        onJumpNewPage({ record: tempRecord, viewId, defaultData, instanceId: actualInstanceId });
      } else {
        let newUrl = `/lc/engine/${viewId}`;
        if (current.get(tableLinkFieldCode)) {
          newUrl += `/${getInstanceId()}`;
        }
        if (lastViewId) {
          newUrl += `/${lastViewId}`;
        }
        history.push({
          pathname: newUrl,
          search: history.location?.search,
        });
      }
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (modal) {
        await modal.close();
        if (lastInstance.id === getInstanceId()) {
          lastInstance.index += 1;
        } else {
          lastInstance.id = getInstanceId();
          lastInstance.index = -1;
        }
      }
      modal = Modal.open({
        title: openType === 'MIDDLE' ? (viewName || name) : '',
        children: (
          <PageLoader
            instanceId={getInstanceId()}
            viewId={viewId}
            pageRef={pageRef}
            mode={viewMode}
            openType={openType}
            parentDataSet={dataSet}
            onJumpNewPage={onJumpNewPage}
            defaultData={defaultData}
            parentFormDataSet={formDs}
            lastInstance={lastInstance}
          />
        ),
        fullScreen: isInIframe(),
        key: `${modalKey}-${record.get('id')}`,
        drawer: openType === 'RIGHT',
        mask: openType !== 'RIGHT',
        style: isInIframe() ? undefined : viewModalStyle,
        bodyStyle: bodyModalStyle,
        destroyOnClose: true,
        closeOnLocationChange: true,
        okButton: viewMode !== 'READONLY',
        okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
        cancelText: viewMode === 'READONLY' ? intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }) : undefined,
      });
    } else if (openType === 'NEWTAB') {
      let newUrl = `/lc/engine/${viewId}`;
      if (current.get(tableLinkFieldCode)) {
        newUrl += `/${getInstanceId()}`;
      }
      if (lastViewId) {
        newUrl += `/${lastViewId}`;
      }
      openPageByUrl(newUrl);
    }
    return true;
  }

  /**
     * 渲染表格行按钮
     * @param dataSet
     * @param current
     * @returns {*}
     */
  const renderTableAction = ({ dataSet, record: current }) => {
    if (current.get('__mainObjectDataId')) { // 子任务不显示行内按钮
      return null;
    }
    const actions = [];
    // 行内编辑：编辑时显示"保存"和"取消"，只读时显示"编辑"按钮
    if (current.editing) {
      const saveRecord = lineButtons.find((btn) => btn.get('type') === 'lineSave');
      const cancelRecord = lineButtons.find((btn) => btn.get('type') === 'lineCancel');
      const linePreConfirm = (executionFun, currenLine) => {
        if (currenLine?.get('confirmFlag')) {
          if (currenLine.get('confirmType') === 'custom') {
            Modal.open({
              title: currenLine.get('modalTitle'),
              children: (
                <ExternalComponent
                  system={{ scope: currenLine.get('scope'), module: currenLine.get('module') }}
                  tenantId={tenantId}
                  config={currenLine.get('customConfig')}
                  data={current?.toData()}
                  dataSet={dataSet}
                  callback={executionFun}
                  orgDataSet={dataSet}
                  // 获取按钮类型
                  buttonConfig={currenLine?.toData()}
                  orgformDataSet={dsManager?.pageRef?.current?.formDataSet}
                />
              ),
              style: {
                width: 800,
              },
            });
          } else {
            Modal.confirm({
              title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
              children: (
                <div>{currenLine.get('confirmText')}</div>
              ),
              okText: currenLine.get('okText'),
              cancelText: currenLine.get('cancelText'),
            }).then((button) => {
              if (button === 'ok') {
                executionFun();
              }
            });
          }
        } else {
          executionFun();
        }
      };
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
        onClick: async () => {
          const click = async () => {
            const validate = await ds.validate();
            if (!validate) {
              return false;
            } else if ((viewType && viewType !== 'INSERT') || scItemViewFlag) {
              await ds.submit();
              current.editing = false;
              ds.query();
            } else {
              current.editing = false;
            }
          };
          linePreConfirm(click, saveRecord);
        },
        icon: saveRecord?.get('icon') || 'CheckOne',
        key: 'inlineSave',
      });
      actions.push({
        name: intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' }),
        onClick: () => {
          const click = () => {
            if (current.get('id')) {
              current.reset();
            } else {
              dataSet.remove(current);
            }
            current.editing = false;
          };
          linePreConfirm(click, cancelRecord);
        },
        icon: cancelRecord?.get('icon') || 'CloseOne',
        key: 'inlineCancel',
      });
    }

    // 非行内或者只读时，显示其他按钮
    if (!current.editing && lineButtons?.length) {
      lineButtons.filter(line => line.get('type') !== 'lineSave' && line.get('type') !== 'lineCancel').map((btnRecord) => {
        const icon = btnRecord.get('icon');
        const name = btnRecord.get('name');
        const id = btnRecord.get('id');
        const type = btnRecord.get('type');
        const tag = btnRecord.get('tag');
        // 只读模式限制服务请求视图表格操作
        if (scItemViewFlag && mode === 'DISABLED' && EDIT_BUTTON_TYPES.includes(btnRecord.get('type'))) {
          return null;
        }
        const action = btnRecord.get('action');
        // 自定义按钮
        const scope = btnRecord.get('scope');
        const module = btnRecord.get('module');
        // 确认信息
        const confirmFlag = btnRecord.get('confirmFlag');
        const confirmText = btnRecord.get('confirmText');
        const okText = btnRecord.get('okText');
        const cancelText = btnRecord.get('cancelText');
        const confirmType = btnRecord.get('confirmType');
        const confirmModalTitle = btnRecord.get('modalTitle');
        const confirmScope = btnRecord.get('scope');
        const confirmModule = btnRecord.get('module');
        const confirmCustomConfig = btnRecord.get('widgetConfig.customConfig');
        // 打开视图配置
        const viewId = btnRecord.get('viewId');
        const relateObjectId = btnRecord.get('businessObjectId');
        const viewName = btnRecord.get('viewName');
        const openType = btnRecord.get('openType') || 'NEW';
        const viewSize = btnRecord.get('viewSize') || 800;
        // 更新按钮配置
        const updateFieldCode = btnRecord.get('updateFieldCode');
        const trueValueText = btnRecord.get('trueValueText');
        const falseValueText = btnRecord.get('falseValueText');
        const trueValueIcon = btnRecord.get('trueValueIcon');
        const falseValueIcon = btnRecord.get('falseValueIcon');

        const funcConfig = { person, personId };
        const hideFlag = calculateHide(btnRecord, current, mode, parentKey, funcConfig);
        if (hideFlag) {
          return btnRecord;
        }

        // 工单催办的行按钮，特殊处理
        if ((scope && module && tag === 'CustomButton') || type === 'PressTicket') {
          // 工单催办的行按钮显示隐藏由行数据确定
          // 放在这里判断是因为，如果放在 PressTicket 内部控制，按钮不显示，但是 Action 的包裹元素依然会保留
          // 造成有个空白区域的问题
          if (type !== 'PressTicket' || current.get('reminder_flag')) {
            actions.push({
              name,
              onClick: () => { },
              element: (
                <Preview
                  record={btnRecord}
                  tableLineRecord={current}
                  dataSet={ds}
                  feature="table-action" // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
                  context={context}
                  lineButton // 行按钮
                  fixedActionFlag={fixedActionFlag}
                />
              ),
            });
          }
          return btnRecord;
        }

        const view = {
          viewSize,
          openType,
          viewId,
          viewName,
          name,
          btnType: type,
        };

        const loadExpression = async () => {
          if (mode === 'PREVIEW') {
            return null;
          }
          if (type !== 'CREATE' && type !== 'COPY') {
            openView(view, dataSet, current);
            return true;
          }
          const fieldMap = {
            _parentId: current.get('id'), // 计算默认值将父级id传入
          };
          let relParams;
          if (relateObjectId !== businessObjectId) {
            relParams = {
              multiObjectFlag: true,
              relateObjectId: businessObjectId,
              relateDataId: current.get('id'),
            };
          }
          const result = await axios({
            url: `lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/calculate`,
            method: 'POST',
            data: JSON.stringify(fieldMap),
            params: relParams,
          });
          // const result = await axios.post(
          //   `lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/calculate`,
          //   JSON.stringify(fieldMap),
          // );
          if (result && !result.failed) {
            const obj = type === 'COPY' ? { ...result, ...current.toData() } : result;
            if (type === 'COPY') {
              delete obj.id;
              obj.createFromCopy = true;
            }
            openView(view, dataSet, current, 'MODIFY', obj);
          } else {
            const obj = type === 'COPY' ? current.toData() : null;
            if (type === 'COPY') {
              delete obj.id;
              obj.createFromCopy = true;
            }
            openView(view, dataSet, current, 'MODIFY', obj);
          }
        };

        function executionAction() {
          if (action === 'OPEN_VIEW' && viewId) {
            loadExpression();
          }
          return true;
        }

        async function handleSubmit() {
          // 提交前触发submit事件，dataSet默认submit中修改数据不会被提交
          if (dataSet.props?.events?.submit) {
            await dataSet.props?.events?.submit({ dataSet, data: dataSet.current?.toData() });
          }
          await dataSet.submit();
        }

        async function handleDelete() {
          await dataSet.delete(dataSet.current, false);
        }

        async function handleRemove() {
          dataSet.current?.set(relatedFieldCode, null);
          await dataSet?.submit();
          dataSet.query();
        }

        async function handleUpdate() {
          current?.set(updateFieldCode, !current.get(updateFieldCode));
          await dataSet?.submit();
          dataSet.query();
        }

        const handleExpression = async () => {
          const fieldMap = dataSet?.current?.toData() || dataSet?.get(0)?.toData() || {};
          // 当表格选中多行时，将多行ids加入参数
          if (dataSet?.selected?.length) {
            fieldMap.__selected_ids = dataSet?.selected?.map(selected => selected.get('id')) || [];
          }
          const result = await axios.post(
            `lc/v1/engine/${tenantId}/dataset/${lastViewId}/executeButton/${id}`,
            JSON.stringify(fieldMap),
          );
          if (!result.failed) {
            message.success(result?.message || intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
            // NODE 表达式执行后触发 DataSet 钩子
            if (dataSet.props?.events?.submitSuccess) {
              await dataSet.props?.events?.submitSuccess({ dataSet, data: dataSet.current?.toData() });
            }
            dataSet.query(dataSet?.currentPage, dataSet?.queryParameter);
          } else {
            message.error(result?.message || intl.formatMessage({ id: 'lcr.components.desc.expression.execute.failed', defaultMessage: '提交失败' }));
          }
        };

        const handleOutbound = () => {
          const iCall = AppState?.getICall?.ref?.call;
          const currentRecord = dataSet.current;
          if (iCall && currentRecord) {
            const mobileFields = fields.filter(f => f.get('widgetType') === 'MobileField');
            if (mobileFields.length === 1) {
              const fieldCode = mobileFields[0]?.get('code');
              const phoneNumber = currentRecord.get(fieldCode);
              if (phoneNumber) {
                iCall(String(phoneNumber));
              }
            } else if (mobileFields.length) {
              Modal.open({
                title: intl.formatMessage({ id: 'lcr.components.desc.mobile.title', defaultMessage: '请选择手机号' }),
                children: (
                  <MobileModal
                    record={currentRecord}
                    mobileFields={mobileFields}
                    AppState={AppState}
                    intl={intl}
                  />
                ),
                footer: null,
                maskClosable: true,
              });
            } else {
              message.warn(intl.formatMessage({ id: 'lcr.components.desc.no.mobile.fields', defaultMessage: '无可用手机号字段' }));
            }
          } else {
            message.warn(intl.formatMessage({ id: 'lcr.components.desc.outbound.tips', defaultMessage: '未开启外呼功能或者外呼功能异常' }));
          }
        };

        const handleMultilingual = () => {
          Modal.open({
            title: intl.formatMessage({ id: 'zknow.common.model.intl', defaultMessage: '多语言' }),
            drawer: true,
            children: (
              <IntlModal intl={intl} record={current} AppState={AppState} />
            ),
            okText: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
          });
        };

        function preConfirm(executionFun) {
          if (confirmFlag) {
            if (confirmType === 'custom') {
              Modal.open({
                title: confirmModalTitle,
                children: (
                  <ExternalComponent
                    system={{ scope: confirmScope, module: confirmModule }}
                    tenantId={tenantId}
                    config={confirmCustomConfig}
                    data={current?.toData()}
                    dataSet={dataSet}
                    callback={executionFun}
                    orgDataSet={dataSet}
                    // 获取按钮类型
                    buttonConfig={btnRecord?.toData()}
                    orgformDataSet={dsManager?.pageRef?.current?.formDataSet}
                  />
                ),
                style: {
                  width: 800,
                },
              });
            } else {
              Modal.confirm({
                title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
                children: (
                  <div>{confirmText}</div>
                ),
                okText,
                cancelText,
              }).then((button) => {
                if (button === 'ok') {
                  executionFun();
                }
              });
            }
          } else {
            executionFun();
          }
        }

        async function handleClick() {
          if (mode === 'PREVIEW') {
            return null;
          }
          switch (type) {
            case 'SUBMIT':
              await preConfirm(handleSubmit);
              break;
            case 'DELETE':
              await preConfirm(handleDelete);
              break;
            case 'REMOVE':
              await preConfirm(handleRemove);
              break;
            case 'UPDATE':
              await preConfirm(handleUpdate);
              break;
            case 'CREATE':
              if (viewId) {
                await preConfirm(executionAction);
              } else {
                await preConfirm(handleSubmit);
              }
              break;
            case 'ADD':
            case 'CUSTOM':
            case 'OPEN':
              await preConfirm(executionAction);
              break;
            case 'COPY':
              await preConfirm(executionAction);
              break;
            case 'EXPRESSION':
              await preConfirm(handleExpression);
              break;
            case 'WORKFLOW':
              await preConfirm(handleExpression);
              break;
            case 'OUTBOUND':
              await handleOutbound();
              break;
            case 'INLINEEDIT':
              preConfirm(() => {
                const editingRecord = ds.find(r => r.editing);
                if (inlineFlag) {
                  if (editingRecord) {
                    editingRecord.reset();
                    editingRecord.editing = false;
                  }
                  ds.current.editing = true;
                }
              });
              break;
            case 'MULTILINGUAL':
              await handleMultilingual();
              break;
            default:
              break;
          }
        }

        let btnName = name;
        if (type === 'UPDATE' && updateFieldCode) {
          btnName = current?.get(updateFieldCode) ? trueValueText : falseValueText;
        }
        let btnIcon = icon;
        if (type === 'UPDATE' && updateFieldCode) {
          btnIcon = current?.get(updateFieldCode) ? trueValueIcon : falseValueIcon;
        } else if (fixedActionFlag) {
          btnIcon = icon || 'ClickTap';
        }

        actions.push({
          key: id,
          name: btnName,
          onClick: () => { },
          element: (
            <Preview
              record={btnRecord}
              tableRecord={current}
              dataSet={ds}
              context={context}
              btnOnClick={handleClick}
              btnName={btnName}
              btnIcon={btnIcon}
              funcType="flat"
              lineButton
              btnLength={lineButtons.length}
              fixedActionFlag={fixedActionFlag}
            />
          ),
        });

        // actions.push({
        //   name: getName(),
        //   icon: getName(),
        //   onClick: handleClick,
        //   key: id,
        // });

        return btnRecord;
      });
    }

    return (
      <TableHoverAction
        record={current}
        actions={actions}
        intlBtnIndex={false}
        className={current?.editing ? 'z-index-100' : ''}
        lock={current?.editing ? true : fixedActionFlag}
        isLc
      />
    );
  };

  function getAutoHeight() {
    if (portalProps?.tableAutoHeight === true) {
      return false;
    }
    return isTableView === true;
  }

  function getTableClass() {
    if (portalProps?.viewType === 'TABLE') {
      // 门户的表格视图样式
      return '';
    }
    return classnames('lc-page-loader-table', className, {
      'table-view': isTableView === true,
    });
  }

  /**
     * 计算操作列宽度
     * @returns {number}
     */
  function getActionWidth() {
    if (fixedActionFlag || inlineFlag) {
      // 最多显示2个
      return 70;
    }
    return 1;
  }

  /**
     * 当没有行按钮隐藏操作列
     * @returns {boolean}
     */
  function hiddenAction() {
    // 变量视图表格不支持操作
    if (requestItemConfig) {
      return true;
    }
    // 开启行内编辑，不隐藏
    if (inlineFlag) {
      return false;
    }
    // 没有行内按钮和多语言隐藏操作列
    if (!lineButtons?.length) {
      return !ds?.props.fields?.find(field => field.isIntl);
    }
    return false;
  }

  /**
     * 表格筛选器axios
     * @param tableProps
     * @returns {{method: string, data: *, url: string}|null|{method: string, url: string}|{method: string, url: string, transformResponse: (function(*=): *)}}
     */
  function tableFilterAdapter(tableProps) {
    const {
      config: { data }, type,
    } = tableProps;
    switch (type) {
      case 'read':
        if (isTableView) {
          const queryParams = tableFilterWorkbenchId ? `&workbenchListId=${tableFilterWorkbenchId}` : '';
          return {
            url: `/lc/v1/${tenantId}/formFilters/queryBySelf/${lastViewId}?page=0&size=999${queryParams}`,
            method: 'get',
            transformResponse: (respData) => {
              let jsonData = [];
              try {
                jsonData = JSON.parse(respData);
              } catch (e) {
                jsonData = [];
              }
              return jsonData.filter(item => !item.hideFlag).map(item => ({
                ...item,
                searchCode: item.code,
                searchName: item.name,
                searchId: item.id,
                searchIcon: item.icon,
              }));
            },
          };
        }
        return null;
      case 'create':
        return {
          url: `/lc/v1/${tenantId}/formFilters/createBySelf`,
          method: 'post',
          data: data[0],
        };
      case 'update':
        return {
          url: `/lc/v1/${tenantId}/formFilters/updateBySelf`,
          method: 'put',
          data: data[0],
        };
      case 'destroy':
        return {
          url: `/lc/v1/${tenantId}/formFilters/deleteBySelf/${data[0]?.id}`,
          method: 'delete',
        };
      default:
        return null;
    }
  }

  /**
     * 筛选器option渲染
     * @param searchId
     * @param text
     * @param searchIcon
     * @returns {*}
     */
  function filterOptionRenderer(searchId, text, searchIcon) {
    return (
      <span
        key={searchId}
        className="yq-filter-menu-option"
      >
        <Icon type={searchIcon} />
        <span className="yq-filter-menu-option-text">{text}</span>
      </span>
    );
  }

  const advancedFilter = useMemo(() => {
    return (
      <AdvancedFilter
        dataSet={ds}
        filterId={filterId}
        businessObjectId={businessObjectId}
        resetCallback={(handleResetClick) => {
          if (!advancedFilterRef.current?.resetAdvancedFilter) {
            if (advancedFilterRef.current) {
              advancedFilterRef.current = {
                ...advancedFilterRef.current,
                resetAdvancedFilter: handleResetClick,
              };
            } else {
              advancedFilterRef.current = {
                resetAdvancedFilter: handleResetClick,
              };
            }
          }
        }}
      />
    );
  }, [businessObjectId]);

  // 子任务不参与多选操作
  const selectionBoxRenderer = ({ record: r, element }) => {
    if (r?.get('__mainObjectDataId')) {
      return null;
    }
    return element;
  };

  if (ds === undefined) {
    return null;
  }

  return (
    <div
      className={getTableClass()}
      style={portalProps?.tableAutoHeight ? {} : { height: portalProps?.height * (680 / 9) - (portalProps?.shouldDisplayTitle ? 45 : 0) }}
      {...rest}
    >
      <Table
        // spin={{ spinning: formDs?.getState?.('childrenSpinning') }}
        queryFieldsLimit={queryFieldsLimit}
        labelLayout="float"
        className={`lc-table ${rowHeightMap[rowHeight]?.class}`}
        // eslint-disable-next-line no-nested-ternary
        editMode={((viewType === 'INSERT' || !viewType) && !scItemViewFlag) ? 'cell' : inlineFlag ? 'inline' : ''}
        pristine={pristineFlag}
        dataSet={ds}
        buttons={getButtons()}
        autoHeight={getAutoHeight()}
        queryBar="comboBar"
        queryFields={queryFieldsOverride}
        queryBarProps={{
          fuzzyQuery: filterFlag && !requestItemConfig, // 变量视图内的表格不支持模糊搜
          title: tableFilterFlag && filterFlag ? undefined : record.get('name'), // 左上角标题
          tableActions: getTableActions(), // 更多按钮
          singleLineMode: headMode !== 'DOUBLE',
          comboFilterBar: {
            searchText: 'fuzzy_params_', // 模糊搜索参数，低代码相较其他页面多了"_"，防止有code为fuzzy_params的字段
            businessObjectId,
            searchId: filterId, // 当前选中筛选器
            filterCallback, // 表格内部筛选器变化
            filterOptionRenderer, // 筛选器option渲染
            tableFilterAdapter: tableFilterFlag && mode !== 'PREVIEW' ? tableFilterAdapter : null, // 筛选器
            filterSave: quickAddFilterFlag,
            filterSaveCallback: (filterData) => {
              if (advancedFilterRef?.current?.saveFilter) {
                advancedFilterRef?.current?.saveFilter('save', filterId, filterData);
              }
            },
            suffixes: tableSuffixes,
          },
          inlineSearchRender: getRowAction(), // 表行动作：预览小i
          advancedFilter: advancedFilterFlag && mode !== 'PREVIEW' ? advancedFilter : null,
          filerMenuAction: tableFilterFlag && mode !== 'PREVIEW' ? (
            <Operators
              dataSet={ds}
              filterId={filterId}
              setFilterId={setFilterId}
              businessObjectId={businessObjectId}
              tableConfig={dsManager.getConfig(record.get('id'))}
              tableFilterFields={tableFilterFields}
              addFlag={quickAddFilterFlag}
              columnDataSet={columnDataSet}
              tableFilterNotice={tableFilterNotice}
              saveCallback={(handleSaveClick) => {
                if (!advancedFilterRef.current?.saveFilter) {
                  if (advancedFilterRef.current) {
                    advancedFilterRef.current = {
                      ...advancedFilterRef.current,
                      saveFilter: handleSaveClick,
                    };
                  } else {
                    advancedFilterRef.current = {
                      saveFilter: handleSaveClick,
                    };
                  }
                }
              }}
            />
          ) : null,
          onReset: () => {
            if (advancedFilterRef?.current?.resetAdvancedFilter) {
              advancedFilterRef?.current?.resetAdvancedFilter();
            }
            ds.query();
          },
        }}
        mode={parentFieldCode || subTreeFlag ? 'tree' : 'list'}
        treeAsync={!!parentFieldCode || subTreeFlag}
        onRow={({ record: current }) => {
          return ({
            isLeaf: current.get('isLeaf') || current.get('isLeaf') === undefined,
          });
        }}
        rowHeight={rowHeightMap[rowHeight]?.rowHeight}
        selectionBoxRenderer={selectionBoxRenderer}
        summaryBar={summaryFlag && summaryPosition === 'TOP' ? summaryFieldList : undefined}
      >
        {renderColumns()}
        {hiddenAction()
          ? null
          : (
            <Column
              header={fixedActionFlag ? intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' }) : undefined}
              width={getActionWidth()}
              renderer={renderTableAction}
              align="left"
              lock={fixedActionFlag ? 'right' : false}
              className={tableLinkFlag && (fixedActionFlag || inlineFlag) ? 'lc-table-action-right-lock' : 'lc-table-action-right'}
              resizable={false}
              tooltip="none" // 修复 TableAction 的按钮出现tooltip的小黑框，造成无法点中按钮
            />
          )}
      </Table>
    </div>
  );
};

export default withErrorBoundary(Index);

/* externalize: LcLayoutTable */
