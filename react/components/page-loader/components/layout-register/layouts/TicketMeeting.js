import React from 'react';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';

const Index = observer(({ record, dataSet, context }) => {
  const { instanceId, viewDataSet, tenantId, udmFlag } = context;
  // 共享服务项生成的单据不展示会议组件
  if (udmFlag) {
    return <div />;
  }
  return (
    <ExternalComponent
      system={{
        scope: 'mt',
        module: 'MeetingRenderer',
      }}
      tenantId={tenantId}
      instanceId={instanceId}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      fallback={<span />}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketMeeting */
