import React from 'react';
import { observer } from 'mobx-react-lite';
import { YqCodeMirror, withErrorBoundary } from '@zknow/components';
import { Output, Form } from 'choerodon-ui/pro';

import './JSExpression.less';

const Index = observer(({ record, context }) => {
  const { tenantId, instanceId, viewDataSet, dsManager } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));
  function renderCmp() {
    return (
      <YqCodeMirror
        name={record.get('widgetConfig.expressionCode') || 'expression'}
        mode="button"
        businessObjectId={record?.get('businessObjectId')}
        viewId={viewDataSet?.current?.get('id')}
        record={formDataSet?.current}
        func="action"
      />
    );
  }

  // TODO：label 样式不够灵活
  return (
    <Form
      className="lc-expression-form"
      labelLayout="horizontal"
      labelWidth={102}
    >
      <Output
        label={record.get('name')}
        renderer={renderCmp}
      />
    </Form>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutJSExpression */
