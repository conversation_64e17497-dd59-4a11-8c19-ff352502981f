import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import { calculateHide } from '../../../utils';

const Index = observer((props) => {
  const { record, dataSet, context } = props;
  const {
    instanceId,
    viewDataSet,
    dsManager,
    person,
    personId,
    parentKey,
    mode,
    viewId,
    quickTicketConfig,
    formDataSet: formDs,
    pageRef,
    tenantId,
  } = context;

  const formDataSet = (viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'))) || formDs;
  const config = record?.toData() || {};

  const funcConfig = { person, personId, tenantId };
  const hideFlag = calculateHide(record, dataSet?.current, mode, parentKey, funcConfig);
  if (hideFlag) {
    return null;
  }

  return (
    <ExternalComponent
      system={{
        scope: 'ticketIntelli',
        module: 'QuickTicketButton',
      }}
      instanceId={instanceId} // 当前记录的id
      formDataSet={formDataSet} // 当前表单ds
      config={config} // 当前组件的配置
      viewId={viewId}
      quickTicketConfig={quickTicketConfig}
      pageRef={pageRef}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutMappingModalButton */
