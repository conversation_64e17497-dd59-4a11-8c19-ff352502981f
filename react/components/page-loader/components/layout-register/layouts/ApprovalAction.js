/*
 * @Author: xiaoreya <<EMAIL>>
 * @Date: 2022-11-07 11:06:42
 * @Description:
 */
import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import ApprovalAction from '@/renderer/approval-action';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, mode } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  return (
    <ApprovalAction
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      viewRecord={record}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      mode={mode}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutApprovalAction */
