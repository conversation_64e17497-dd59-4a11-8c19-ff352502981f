/*
 * @Author: xia<PERSON>ya
 * @Date: 2022-01-24 19:46:29
 * @Description:
 */
import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import LayoutRegister from '../LayoutRegister';
import TicketSurveyDetail from '@/renderer/survey-detail';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');
  const displayMode = record?.get('widgetConfig.displayMode');
    
  return (
    <TicketSurveyDetail
      displayMode={displayMode}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketSurveyDetail */
