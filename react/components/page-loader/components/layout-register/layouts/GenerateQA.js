import React from 'react';
import { observer } from 'mobx-react-lite';
import { Permission } from '@yqcloud/apps-master';
import { injectIntl } from 'react-intl';
import { withErrorBoundary, Button, Icon } from '@zknow/components';
import { Modal, Menu } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { getHtml } from '@/utils';
import AiTag from '@/components/ai-tag';
import { getRichJson } from '@/renderer/utils/utils.js';
import TransQAModal from '../../../../../renderer/ticket-solution/main-view/trans2qa';
import styles from './GenerateQA.module.less';

const modalKey = Modal.key();

const GenerateQA = injectIntl(observer((props) => {
  // record 是按钮这个组件的信息，dataSet是当前表单的信息
  const { record, context, feature, dataSet, instanceId, intl } = props;
  const { tenantId, viewDataSet, AppState } = context;
  const name = record?.get('name');
  const id = record?.get('id');
  const aiPromptFlag = record?.get('aiPromptFlag');
  const ticketSolutionFieldCode = record?.get('ticketSolutionFieldCode') || 'resolution';

  const getExtraInfo = (content) => {
    try {
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  };

  const handleClick = () => {
    const formRecord = dataSet.current;
    const content = formRecord?.get(ticketSolutionFieldCode);
    const businessObjectCode = viewDataSet?.current?.get('businessObjectCode');
    const promptTemplateId = record.get('promptTemplateId');
    const aiPromptTitle = record.get('aiPromptTitle');
    const similarityPromptTemplateId = record.get('similarityPromptTemplateId');
    const similarityPromptTemplateCode = record.get('similarityPromptTemplateObj.code');
    const title = formRecord?.get('short_description') || formRecord.get('shortDescription') || formRecord.get('name') || '';
    let htmlContent;
    try {
      const jsonData = JSON.parse(content);
      htmlContent = getHtml(jsonData);
    } catch (e) {
      htmlContent = content;
    }
    // TODO: 来自移动端的语音转为普通附件保存，目前语音会被丢弃
    const atts = getExtraInfo(content);
    Modal.open({
      key: modalKey,
      title: (
        <div>
          <span>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.trans2qa' })}</span>
          {aiPromptFlag && <AiTag name={aiPromptTitle} onClick={() => { dataSet?.setState('queryAiQA', true); }} />}
        </div>
      ),
      style: { width: '8rem' },
      children: (
        <TransQAModal
          intl={intl}
          tenantId={tenantId}
          currentUser={AppState?.getUserInfo || {}}
          question={title}
          atts={atts}
          businessObjectCode={businessObjectCode}
          promptTemplateId={promptTemplateId}
          instanceId={instanceId}
          answer={htmlContent}
          aiPromptFlag={aiPromptFlag}
          aiPromptTitle={aiPromptTitle}
          similarityPromptTemplateId={similarityPromptTemplateId}
          similarityPromptTemplateCode={similarityPromptTemplateCode}
          formDataSet={dataSet}
        />
      ),
    });
  };

  return (
    <Permission service={['yqcloud-intelligent.qa-library-item.createQaLibraryItem']}>
      <>
        {feature === 'table-action' ? (
          <Menu.Item
            key={id}
            className="c7n-menu-item"
            onClick={handleClick}
          >
            <Icon type="lightning" style={{ marginRight: 8 }} />
            {name}
          </Menu.Item>
        ) : (
          <Button
            key={id}
            className={aiPromptFlag ? styles.button : ''}
            onClick={handleClick}
            funcType="raised"
            color={aiPromptFlag ? 'primary' : 'secondary'}
            icon="ThinkingProblem"
          >
            {name}
          </Button>
        )}
      </>
    </Permission>
  );
}));

export default withErrorBoundary(inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })(GenerateQA)
), { fallback: <span /> });

/* externalize: LcLayoutGenerateQA */
