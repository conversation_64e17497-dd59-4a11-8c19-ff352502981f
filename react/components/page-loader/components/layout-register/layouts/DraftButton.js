import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import DetailButton from '@/renderer/draft-box/button';
import { calculateHide } from '../../../utils';

const Index = observer((props) => {
  const { record, dataSet, feature, tableLineRecord, context } = props;
  const {
    tenantId,
    instanceId,
    viewDataSet,
    intl,
    dsManager,
    modal,
    pageContext,
    person,
    personId,
    parentKey,
    mode,
    disableACL,
    pageRef,
  } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));
  const config = record?.toData() || {};

  const funcConfig = { person, personId, tenantId };
  const hideFlag = calculateHide(record, dataSet?.current, mode, parentKey, funcConfig);
  if (hideFlag) {
    return null;
  }
  return (
    <DetailButton
      intl={intl}
      tenantId={tenantId}
      pageContext={pageContext} // 当前页面所有视图数据
      instanceId={instanceId} // 当前记录的id
      formDataSet={formDataSet} // 当前表单ds
      tableDataSet={dataSet} // 当前列表ds
      tableLineRecord={tableLineRecord}
      viewDataSet={viewDataSet} // 当前视图ds
      config={config} // 当前组件的配置
      modal={modal} // 弹窗实例
      feature={feature} // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
      disableACL={disableACL}
      pageRef={pageRef}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutDraftButton */
