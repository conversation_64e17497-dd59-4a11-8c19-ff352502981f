import React, { useMemo, useRef, useEffect, useState } from 'react';
import axios from 'axios';
import { getEnv } from '@zknow/utils';
import QRCode from 'qrcode.react';
import { observer } from 'mobx-react-lite';
import { v4 as uuidv4 } from 'uuid';
import { Button, Icon, withErrorBoundary, ModalTitle, ExternalComponent } from '@zknow/components';
import queryString from 'query-string';
import omit from 'lodash/omit';
import { Modal, message, Menu, Select, Form } from 'choerodon-ui/pro';
import { calculateHide1, calculateHide, TABLE_BUTTON_TYPES, validateMessage, getCurrentDate, getWidgetData, getSectionData, calculateConditions, ACTION } from '../../../utils';
import DownloadModal from '@/components/download-modal';
import BatchImport from '@/components/batch-import';
import BatchPrint from '@/components/batch-print';
import BatchPrintHistory from '@/components/batch-print-history';
import { openPageByUrl } from '@/utils';
import { ExportForm, HistoryDetailForm } from './TableComponents';
import PageLoader from '../../..';

import './Button.less';

const modalKey = Modal.key();
const modalPrintKey = Modal.key();
const historyModalKey = Modal.key();

/**
 * lineButton: 当前表格行按钮
 * btnLength: 当前表格视图行按钮数量
 * tableRecord： 当前表格行
 */
const Index = observer((props) => {
  const {
    record, dataSet, formDataSet, modal, buttonMode, funcType = 'raised',
    context, relatedFieldCode, instanceId, fromTable, tableId,
    tableRecord, btnName, btnIcon, btnOnClick, lineButton = false, btnLength,
    fixedActionFlag = false, // 行内按钮固定
  } = props;
  const {
    tabMenuDataSet, mode, history, intl, onTicketCreate, onJumpNewPage, lastViewId, parentDataSet, tenantId,
    viewDataSet, pageContext, dsManager, instanceId: parentInstanceId, jumpViewId,
    person, personId, parentKey, detailViewId: parentDetailViewId, openType: parentOpenType,
    variableFlag, tenantNum, events, tabDataSet, buttonPrintDataSet, templateOptionDs,
    exportDataSet, prefixCls, tableFilterDataSet, tableViewDataSet, columnDataSet, downloadHistoryDataSet,
    scPageRef, submissionChannel, scItemViewFlag, AppState: { currentLanguage: language }, sectionDataSet,
  } = context;
  const parentViewId = viewDataSet?.current?.get('id');
  const itemId = viewDataSet?.current?.get('itemId');
  const parentViewType = viewDataSet?.current?.get('viewType');
  // 按钮属性
  const icon = btnIcon || record.get('icon');
  const name = btnName || record.get('name');
  const id = record.get('id');
  const type = record.get('type');
  // 按钮类型。如果未设置，关闭按钮默认线框按钮；其他默认次级按钮。
  const color = record.get('color') || (type === 'CLOSE' ? 'default' : 'secondary');
  // 确认信息
  const confirmFlag = record.get('confirmFlag');
  const confirmText = record.get('confirmText');
  const okText = record.get('okText');
  const cancelText = record.get('cancelText');
  const confirmType = record.get('confirmType');
  const confirmModalTitle = record.get('modalTitle');
  const confirmScope = record.get('widgetConfig.scope') || record.get('scope');
  const confirmModule = record.get('widgetConfig.module') || record.get('module');
  const confirmCustomConfig = record.get('widgetConfig.customConfig');
  // 按钮事件
  const action = record.get('action');
  // 表达式按钮支持后续操作
  const expAction = record.get('expAction');
  const viewId = record.get('viewId');
  const viewName = record.get('viewName');
  const detailViewId = record.get('detailViewId');
  const openType = record.get('openType') || 'NEW';
  const viewSize = record.get('viewSize') || 800;
  const hideFooter = record.get('hideFooter');
  // 更新按钮配置
  const updateFieldCode = record.get('updateFieldCode');
  const trueValueText = record.get('trueValueText');
  const falseValueText = record.get('falseValueText');
  const trueValueIcon = record.get('trueValueIcon');
  const falseValueIcon = record.get('falseValueIcon');
  // 二维码配置
  const qrType = record.get('qrType');
  const qrFields = record.get('qrFields');
  const qrViewType = record.get('qrViewType');
  const qrViewId = record.get('qrViewId');
  const qrShowType = record.get('qrShowType');
  // 表格按钮配置
  const isTableView = viewDataSet?.current?.get('viewType') === 'TABLE';
  const tableAction = record.getState('tableAction');
  // 打印配置
  const printRelatedKey = record.get('widgetConfig.printRelatedKey');
  const printRelatedObjectId = record.get('widgetConfig.printRelatedObjectId');
  // 打开链接
  const link = record.get('link');
  // 打开按钮-属性
  const btnRelationFieldCode = record.get('relationFieldCode');

  let openModal;
  const lastInstance = {
    id: '',
    index: 0,
  };
  const pageRef = useRef();
  const modalStyle = useMemo(() => ({ width: Number(viewSize) }), []);
  const qrModalStyle = useMemo(() => ({ width: 520 }), []);
  const mdModalStyle = useMemo(() => ({ width: 800 }), []);
  const conditionModalStyle = useMemo(() => ({ width: 1000 }), []);
  const bodyStyle = useMemo(() => ({ padding: 0 }), []);

  const [isHide, setIsHide] = useState();
  const [qrCodeValue, setQrCodeValue] = useState();
  const funcConfig = { person, personId, tenantId };

  // TODO: 监听toData有性能问题，应该使用组件上的hidden属性来控制
  useEffect(() => {
    (async () => {
      // eslint-disable-next-line no-nested-ternary
      const current = lineButton ? tableRecord : fromTable ? formDataSet?.current : dataSet?.current;
      const hideFlag = await calculateHide1(record, current, mode, parentKey, funcConfig, true);
      setIsHide(hideFlag);
    })();
  }, [formDataSet?.current?.toData(), dataSet?.current?.toData()]);

  if ((isHide && !lineButton) || (isHide && lineButton && btnLength === 1)) {
    return null;
  }

  /**
   * 打开视图
   * @param result
   * @param insId 打开视图实例id
   * @param fieldMap 原始提交数据
   * @returns {Promise<boolean>}
   */
  async function openView(result, insId = 'new', fieldMap = {}) {
    let defaultData = {};
    if (result) {
      defaultData = result;
    }
    if (relatedFieldCode && instanceId) {
      defaultData[relatedFieldCode] = instanceId;
    }
    if (openType === 'NEW') {
      if (onJumpNewPage) {
        if (modal) {
          await modal.close();
        }
        let newRecord;
        if (insId !== 'new') {
          const newData = {
            ...fieldMap,
            id: insId,
          };
          newRecord = {
            get: (key) => {
              return newData[key];
            },
            toData: () => newData,
          };
        }
        onJumpNewPage({ viewId, defaultData, record: newRecord });
      } else {
        let pushUrl = `/lc/engine/${viewId}/${insId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`;
        if (btnRelationFieldCode) {
          const realId = dataSet.current.get(btnRelationFieldCode);
          pushUrl = `/lc/engine/${viewId}/${realId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`;
        }
        // 视图ID/实例ID/上一个视图ID/新建后需要跳转的详情视图ID
        history.push({
          pathname: pushUrl,
          search: history.location?.search,
          state: defaultData, // 新打开视图默认值
        });
      }
    } else if (openType === 'NEWTAB') {
      if (btnRelationFieldCode) {
        const realId = dataSet.current.get(btnRelationFieldCode);
        openPageByUrl(`/lc/engine/${viewId}/${realId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`);
      } else {
        openPageByUrl(`/lc/engine/${viewId}/${insId}/${parentViewId}${detailViewId ? `/${detailViewId}` : ''}`);
      }
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (openModal) {
        await openModal.close();
        if (lastInstance.id === (type === 'OPEN' ? parentInstanceId : undefined)) {
          lastInstance.index += 1;
        } else {
          lastInstance.id = (type === 'OPEN' ? parentInstanceId : undefined);
          lastInstance.index = -1;
        }
      }
      let detailFlag = false;
      // 对于多层级跳转的视图，需要获取其他视图的数据，可以通过pageContext获取
      const parentViewCode = viewDataSet.current?.get('code');
      const parentFormDataSet = dsManager.get(parentViewId);
      const newPageContext = pageContext || {};
      if (newPageContext[parentViewCode]) {
        newPageContext[parentViewCode].push({
          instanceId: parentInstanceId,
          formDataSet: parentFormDataSet,
          viewDataSet,
        });
      } else {
        newPageContext[parentViewCode] = [{
          instanceId: parentInstanceId,
          formDataSet: parentFormDataSet,
          viewDataSet,
        }];
      }

      const extraModalOptions = {};

      if (hideFooter) {
        extraModalOptions.footer = null;
      }

      openModal = Modal.open({
        title: viewName || name,
        children: (
          <PageLoader
            pageContext={newPageContext}
            viewId={viewId}
            pageRef={pageRef}
            mode="MODIFY"
            defaultData={defaultData}
            openType={openType}
            parentDataSet={dataSet}
            parentFormDataSet={formDataSet}
            instanceId={type === 'OPEN' ? parentInstanceId : result?.id}
            detailViewId={detailViewId}
            lastInstance={lastInstance}
            openBy="Button"
            onJumpNewPage={onJumpNewPage}
          />
        ),
        key: `${modalKey}-${viewId}`,
        drawer: openType === 'RIGHT',
        style: modalStyle,
        destroyOnClose: true,
        onOk: async () => {
          if (type === 'ADD' && parentViewType !== 'INSERT') {
            const tableDataSet = pageRef.current?.tableDataSet;
            const selectRecords = tableDataSet?.selected;
            selectRecords.map(rowRecord => {
              rowRecord.set(relatedFieldCode, instanceId);
              return rowRecord;
            });
            await tableDataSet?.submit();
            dataSet.query();
            openModal = false;
            return true;
          } else if (type === 'CREATE' && (parentViewType === 'INSERT' || !parentViewType) && !scItemViewFlag) {
            // 新建视图中，行新增不直接提交数据
            const currentTableDs = dsManager.get(tableId);
            // 校验提交信息
            const validate = await pageRef.current?.formDataSet?.current?.validate();
            if (!validate) {
              return false;
            }
            const currentData = pageRef.current?.formDataSet?.current?.toData();
            if (currentData && currentTableDs) {
              dataSet.setState('businessObjectId', pageRef.current?.pageData?.businessObjectId);
              dataSet.setState('relatedFieldCode', relatedFieldCode);
              currentTableDs.create({ ...currentData });
            }
            openModal = false;
            return true;
          } else if (await validateMessage(pageRef.current?.formDataSet, intl, scPageRef)) {
            const res = await pageRef.current?.formDataSet?.submit();
            dataSet.query();
            if (type === 'CREATE' && detailViewId && !detailFlag && res?.content?.length) {
              detailFlag = true;
              Modal.open({
                key: `${modalKey}-${detailViewId}`,
                style: modalStyle,
                drawer: openType === 'RIGHT',
                children: (
                  <PageLoader
                    pageContext={{}}
                    viewId={detailViewId}
                    pageRef={pageRef}
                    mode="MODIFY"
                    defaultData={{}}
                    openType={openType}
                    parentDataSet={dataSet}
                    instanceId={res?.content[0]?.id}
                    openBy="Button"
                  />
                ),
              });
              return true;
            }
            openModal = false;
            return true;
          }
          return false;
        },
        onCancel: () => {
          openModal = false;
          pageRef.current = { formTableDsList: [] };
          pageRef.current?.formDataSet?.reset();
        },
        ...extraModalOptions,
      });
    }
    return true;
  }

  const handleExpression = async () => {
    if (!fromTable) {
      const recordValidate = await validateMessage(dataSet, intl, scPageRef);
      if (!recordValidate) {
        return false;
      }
    }
    const fieldMap = dataSet?.current?.toData() || dataSet?.get(0)?.toData() || {};
    // 当表格选中多行时，将多行ids加入参数
    if (dataSet?.selected?.length) {
      fieldMap.__selected_ids = dataSet?.selected?.map(selected => selected.get('id')) || [];
    }
    // 数据中默认加入父级id
    fieldMap.__parent_id = parentInstanceId;
    // 单据提交渠道
    if (submissionChannel) {
      fieldMap.submission_channel = submissionChannel;
    }
    try {
      const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${parentViewId}/executeButton/${id}`, JSON.stringify(fieldMap));
      if (!result.failed) {
        message.success(result?.message || intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
        if (expAction === 'OPEN_VIEW') {
          // 新建对象的id
          openView(result, result?.id || result?.content[0]?.id || fieldMap?.id || fieldMap._id, fieldMap);
          return true;
        }
        if (expAction === 'RELOAD') {
          window.location.reload();
          return true;
        }
        // NODE 表达式执行后触发 DataSet 钩子
        if (dataSet.props?.events?.submitSuccess) {
          await dataSet.props?.events?.submitSuccess({ dataSet, data: fieldMap });
        }
        // 新页面新增，则跳转至详情页或列表页
        if (!modal && parentInstanceId === 'new' && !events?.submitSuccess) {
          if (jumpViewId && result?.content && result?.content[0]?.id) {
            history.push({
              pathname: `/lc/engine/${jumpViewId}/${result?.content[0]?.id}/${lastViewId}`,
              search: history.location?.search,
            });
          } else if (lastViewId) {
            history.push({
              pathname: `/lc/engine/${lastViewId}`,
              search: history.location?.search,
            });
          } else {
            dataSet.setState('noCheck', true);
            dataSet.reset();
            return true;
          }
        }
        // 刷新父级，如表格打开弹窗，弹窗修改后需要刷新表格
        if (parentDataSet) {
          parentDataSet?.query(parentDataSet?.currentPage, parentDataSet?.queryParameter);
        }
        // 对于通过表达式按钮保存表单:
        // 由于不是通过ds提交，当前Ds还是处于dirty状态，弹窗在关闭时，会提示有修改未保存
        // 这里需要在invoke接口调用成功后，弹窗关闭前reset当前Ds
        if (dataSet.current && ['sync', 'update'].includes(dataSet.current.status)) {
          dataSet.reset();
          // 如果页面包含必填字段，reset后会触发必填校验提示，当前场景不校验
          dataSet.setState('noCheck', true);
        }
        if (modal) {
          modal.close();
        } else {
          // 非弹窗页面，提交后刷新当前页数据
          dataSet.query(dataSet?.currentPage, dataSet?.queryParameter);
        }
        return true;
      } else {
        message.error(result?.message || intl.formatMessage({ id: 'lcr.components.desc.expression.execute.failed', defaultMessage: '提交失败' }));
        return false;
      }
    } catch (e) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.expression.execute.failed', defaultMessage: '提交失败' }));
      return false;
    }
  };

  const loadExpression = async () => {
    if (type !== 'CREATE' && type !== 'COPY') {
      openView();
      return true;
    }
    const fieldMap = {
      _parentId: instanceId, // 计算默认值将父级id传入
    };
    if (relatedFieldCode && instanceId) {
      fieldMap[relatedFieldCode] = instanceId;
    }
    const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/calculate`, JSON.stringify(fieldMap));
    if (result && !result.failed) {
      const obj = type === 'COPY' ? { ...result, ...dataSet?.current?.toData() } : result;
      if (type === 'COPY') {
        delete obj.id;
        obj.createFromCopy = true;
      }
      openView(obj);
    } else {
      const obj = type === 'COPY' ? dataSet?.current?.toData() : null;
      if (type === 'COPY') {
        delete obj.id;
        obj.createFromCopy = true;
      }
      openView(obj);
    }
  };

  function executionAction() {
    if (action === 'OPEN_VIEW' && viewId) {
      loadExpression();
    }
    return true;
  }
  function handleBatchPrintHistory() {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.print.template.history', defaultMessage: '打印历史' }),
      children: (
        <BatchPrintHistory />
      ),
      drawer: true,
      style: { width: 800 },
      footer: null,
    });
  }
  async function handleBatchPrint() {
    const selectRecords = dataSet?.selected || [];
    const dataIds = selectRecords.map(r => r.get('id')).join(',');
    if (selectRecords.length) {
      templateOptionDs.setState('selectedDataIds', dataIds.split(',')[0]);
      templateOptionDs.setQueryParameter('printRelatedKey', printRelatedKey);
      templateOptionDs.setQueryParameter('printRelatedObjectId', printRelatedObjectId);
      await templateOptionDs.query();
      if (buttonPrintDataSet.current) {
        templateOptionDs.setState('listFlag', templateOptionDs.toData()?.[0]?.listFlag);
        buttonPrintDataSet.current.set('templateId', templateOptionDs.toData()?.[0]?.templateId);
      }

      Modal.open({
        title: intl.formatMessage({ id: 'lcr.components.desc.print', defaultMessage: '打印' }),
        children: (
          <BatchPrint printDataSet={buttonPrintDataSet} selectRecords={selectRecords} templateOptionDs={templateOptionDs} />
        ),
        style: { width: 600 },
        onOk: async () => {
          const val = await buttonPrintDataSet.validate();
          if (val) {
            const data = buttonPrintDataSet.current.toData();
            if (buttonPrintDataSet.current.get('printType') === 'derict') {
              // 直接下载
              const res = await axios.get(`/report/v1/${tenantId}/report-print/template/pdf`, {
                params: {
                  businessObjectId: viewDataSet.current.get('businessObjectId'),
                  templateId: data.templateId,
                  dataId: dataIds,
                  printRelatedKey,
                  printRelatedObjectId,
                },
                responseType: 'blob',
              });
              if (!res?.failed) {
                const blob = new Blob([res], { type: 'application/pdf' });
                const link = window.URL.createObjectURL(blob);
                // 创建一个隐藏的iframe用于加载PDF
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none'; // 确保iframe不会显示在页面上
                iframe.src = link;
                document.body.appendChild(iframe);

                // 加载完成后自动打印
                iframe.onload = function () {
                  iframe.contentWindow.print();
                };
              } else {
                message.error(`${intl.formatMessage({ id: 'lcr.components.desc.print.template.api.error', defaultMessage: '接口错误数据: {message}' }, { message: res?.message })}`);
              }
            } else {
              // 后台下载
              const requestBody = {
                businessObjectId: viewDataSet.current.get('businessObjectId'),
                templateId: data.templateId,
                dataId: dataIds,
                printRelatedKey,
                printRelatedObjectId,
              };
              const res = await axios.post(`/report/v1/${tenantId}/report-print/template/pdf/background`, null, { params: requestBody });
              if (!res?.failed) {
                message.success(intl.formatMessage({ id: 'lcr.components.desc.print.template.async.success', defaultMessage: '已加入到打印任务中，请到“打印历史”中查看进度。' }));
              } else {
                message.error(`${intl.formatMessage({ id: 'lcr.components.desc.print.template.api.error', defaultMessage: '接口错误数据: {message}' }, { message: res?.message })}`);
              }
            }
            return true;
          }
          return false;
        },
      });
    } else {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.print.template.choose.at.least', defaultMessage: '请选择至少一条记录' }));
    }
  }
  async function handlePrint() {
    const currentMenu = tabMenuDataSet?.find?.(r => r.getState('current'));
    let dataId = currentMenu?.get?.('id') || instanceId || tabMenuDataSet?.toData?.()?.[0]?.id;
    const { search } = history?.location || {};
    if (!dataId) {
      try {
        dataId = queryString?.parse?.(search)?.ticketId;
      } catch (e) {
        message.error(e);
      }
    }
    templateOptionDs.setQueryParameter('printRelatedKey', printRelatedKey);
    templateOptionDs.setQueryParameter('printRelatedObjectId', printRelatedObjectId);
    await templateOptionDs.query();
    if (buttonPrintDataSet.current) buttonPrintDataSet.current.set('templateId', templateOptionDs.toData()?.[0]?.templateId);
    openModal = Modal.open({
      title: viewName || name,
      children: (
        <Form dataSet={buttonPrintDataSet}>
          <Select name="templateId" />
        </Form>
      ),
      key: `${modalPrintKey}`,
      // drawer: openType === 'RIGHT',
      // style: modalStyle,
      destroyOnClose: true,
      onOk: async () => {
        const val = await buttonPrintDataSet.validate();
        if (val) {
          const data = buttonPrintDataSet.current.toData();
          const res = await axios.get(`/report/v1/${tenantId}/report-print/template/pdf`, {
            params: {
              businessObjectId: viewDataSet.current.get('businessObjectId'),
              templateId: data.templateId,
              dataId,
              printRelatedKey,
              printRelatedObjectId,
            },
            responseType: 'blob',
          });
          if (!res?.failed) {
            const blob = new Blob([res], { type: 'application/pdf' });
            const link = window.URL.createObjectURL(blob);
            // 创建一个隐藏的iframe用于加载PDF
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none'; // 确保iframe不会显示在页面上
            iframe.src = link;
            document.body.appendChild(iframe);

            // 加载完成后自动打印
            iframe.onload = function () {
              iframe.contentWindow.print();
            };
          } else {
            message.error(`${intl.formatMessage({ id: 'lcr.components.desc.print.template.api.error', defaultMessage: '接口错误数据: {message}' }, { message: res?.message })}`);
          }
          return true;
        }
        return false;
      },
      onCancel: () => {

      },
    });
  }

  async function getAllTableData() {
    const data = [];
    let submitFlag = true;
    if (dsManager?.pageRef.current?.formTableDsList?.length > 0) {
      const flags = await Promise.all(dsManager?.pageRef.current?.formTableDsList.map(ds => ds.validate()));
      const falseFlag = flags.indexOf(false);
      if (falseFlag > -1) {
        return {
          data,
          submitFlag,
        };
      } else {
        dsManager?.pageRef.current?.formTableDsList.forEach((ds) => {
          const currentDsId = ds.id;
          const widgetData = getWidgetData(viewDataSet?.current?.get('jsonData'), currentDsId);
          const sectionRecord = sectionDataSet.find(r => r.get('id') === widgetData?.widgetConfig?.parentId);
          const hideFlag = calculateHide(sectionRecord, dsManager?.pageRef?.current?.formDataSet?.current, mode, '', funcConfig);
          if (!hideFlag) {
            const { name: tableName = '', widgetConfig: { requiredFlag, minRows, maxRows, requiredCondition, requiredAction } = {} } = widgetData;
            let requiredFlagValue = requiredFlag === 'ALWAYS_NOT_REQUIRED' ? false : requiredFlag === 'ALWAYS_REQUIRED';
            if (requiredFlag === 'CONDITION') {
              requiredFlagValue = calculateConditions(null, null, null, dsManager?.pageRef.current?.formDataSet?.current, requiredCondition, funcConfig, null, true);
              if (requiredAction === ACTION.Not_REQUIRED) {
                requiredFlagValue = !requiredFlagValue;
              }
            }
            if (!requiredFlagValue || (requiredFlagValue && minRows <= ds.length && ds.length <= maxRows)) {
              const currentBusId = ds.getState('businessObjectId');
              const currentRelFieldCode = ds.getState('relatedFieldCode');
              if (currentBusId) {
                const currentData = {
                  businessObjectId: currentBusId,
                  relatedFieldCode: currentRelFieldCode,
                  data: ds.toData(),
                };
                data.push(currentData);
              }
            } else {
              message.error(intl.formatMessage({ id: 'lcr.components.desc.table.requeired.tips', defaultMessage: '{name}必填，最少{minRows}条，最多{maxRows}条' }, { tableName, minRows, maxRows }));
              submitFlag = false;
            }
          }
        });
      }
    }
    return {
      data,
      submitFlag,
    };
  }

  async function handleSubmit() {
    const recordValidate = await validateMessage(dataSet, intl, scPageRef);
    if (!recordValidate) {
      return false;
    }
    // 提交前触发submit事件，dataSet默认submit中修改数据不会被提交
    if (dataSet.props?.events?.submit) {
      await dataSet.props?.events?.submit({ dataSet, data: dataSet.current?.toData() });
    }
    // onTicketCreate 创建单据，自定义点击方法，用于itsm处理一些数据
    if (onTicketCreate) {
      await onTicketCreate(dataSet);
    } else {
      try {
        // canSubmit 表示是否可以提交，如果是新增视图，需要校验行的数据，根据行的数据来判断是否可以提交
        let canSubmit = false;
        if (parentViewType === 'INSERT') {
          const { data, submitFlag } = await getAllTableData();
          // 子任务在新建视图的兼容
          const subTaskDataSet = dsManager?.pageRef?.current?.subTaskDataSet;
          if (subTaskDataSet?.length) {
            const subTaskBusinessObject = dsManager?.pageRef?.current?.subTaskBusinessObject;
            data.push({
              businessObjectId: subTaskBusinessObject?.id,
              relatedFieldCode: 'target_id',
              data: (subTaskDataSet?.toData?.() || []).map(item => ({
                target_type: dsManager?.pageRef?.current?.pageRecord?.get('businessObjectCode'),
                businessObjectId: subTaskBusinessObject?.id,
                ...omit(item, ['draft_flag', 'id']),
              })),
            });
          }
          if (!submitFlag) {
            return false;
          }
          canSubmit = submitFlag;
          dataSet.current.set('_children', data);
        } else {
          canSubmit = true;
        }
        if (canSubmit) {
          if (parentViewType === 'INSERT' && fromTable) {
            dataSet.forEach((record) => {
              if (record.editing) {
                record.editing = false;
              }
            });
            return;
          }
          const resData = await dataSet.submit();
          if (!resData?.failed) {
            if (!modal && resData?.content[0]?.id && parentInstanceId === 'new' && !events?.submitSuccess) {
              if (jumpViewId) {
                if (dsManager && dsManager?.pageRef && dsManager?.pageRef.current && dsManager?.pageRef.current.formTableDsList) {
                  dsManager.pageRef.current.formTableDsList = [];
                }
                history.push({
                  pathname: `/lc/engine/${jumpViewId}/${resData?.content[0]?.id}/${lastViewId}`,
                  search: history.location?.search,
                });
              } else if (lastViewId) {
                if (dsManager && dsManager?.pageRef && dsManager?.pageRef.current && dsManager?.pageRef.current.formTableDsList) {
                  dsManager.pageRef.current.formTableDsList = [];
                }
                history.push({
                  pathname: `/lc/engine/${lastViewId}`,
                  search: history.location?.search,
                });
              } else {
                dataSet.setState('noCheck', true);
                dataSet.reset();
                return true;
              }
            }
            if (parentDataSet) {
              parentDataSet?.query(parentDataSet?.currentPage, parentDataSet?.queryParameter);
            } else {
              dataSet.query();
            }
            // 如果是新建弹窗视图且配置了详情视图，则创建成功默认将弹窗更新为详情
            if (modal) {
              if (parentDetailViewId && resData?.content[0]?.id) {
                modal.update({
                  children: (
                    <PageLoader
                      pageContext={{}}
                      viewId={parentDetailViewId}
                      pageRef={pageRef}
                      mode="MODIFY"
                      defaultData={{}}
                      openType={parentOpenType}
                      parentDataSet={parentDataSet}
                      instanceId={resData?.content[0]?.id}
                    />
                  ),
                });
                return false;
              }
              if (pageRef?.current) {
                pageRef.current.formTableDsList = [];
              }
              modal.close();
            }
            return true;
          } else {
            message.error(resData?.message);
            return false;
          }
        }
      } catch (e) {
        console.log(e);
        throw new Error(e);
      }
    }
  }

  async function handleDelete() {
    const records = fromTable && dataSet.selection === 'multiple' ? dataSet.selected || [] : dataSet.current;
    await dataSet.delete(records, false);
  }

  async function handleRemove() {
    dataSet.current?.set(relatedFieldCode, null);
    await dataSet?.submit();
    dataSet.query();
  }

  async function handleUpdate(actionType) {
    if (['true', 'false'].includes(actionType)) {
      const selectRecords = dataSet.selected;
      selectRecords?.map(rowRecord => {
        rowRecord.set(updateFieldCode, actionType === 'true');
        return rowRecord;
      });
    } else {
      dataSet?.current?.set(updateFieldCode, !dataSet?.current.get(updateFieldCode));
    }
    await dataSet?.submit();
    dataSet.query();
    return true;
  }

  async function preConfirm(executionFun) {
    if (confirmFlag) {
      if (confirmType === 'custom') {
        // eslint-disable-next-line no-nested-ternary
        const current = lineButton ? tableRecord : fromTable ? formDataSet?.current : dataSet?.current;
        // 2024.12.18 px确认必填校验通过再弹自定义确认框
        const res = await current?.validate();
        let orgformDataSet;
        try {
          const keys = Object.keys(pageContext);
          if (keys.length > 0) {
            const firstKey = keys[0];
            const firstValue = pageContext[firstKey];
            orgformDataSet = firstValue?.[0]?.formDataSet;
          }
        } catch (error) {
          //
        }
        if ((lineButton || fromTable || res)) {
          Modal.open({
            title: confirmModalTitle,
            children: (
              <ExternalComponent
                system={{ scope: confirmScope, module: confirmModule }}
                tenantId={tenantId}
                config={confirmCustomConfig}
                data={current?.toData()}
                dataSet={fromTable ? formDataSet : dataSet}
                callback={executionFun}
                // 列表删除按钮还需拿到dataSet
                orgDataSet={dataSet}
                // 获取按钮类型
                buttonConfig={record?.toData()}
                // 还要展示视图名称
                viewDataSet={viewDataSet}
                // 头行结构需要详情dataSet
                orgformDataSet={orgformDataSet || dsManager?.pageRef?.current?.formDataSet}
              />
            ),
            style: {
              width: 800,
            },
          });
        }
      } else {
        Modal.confirm({
          title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
          children: (
            <div>{confirmText}</div>
          ),
          okText,
          cancelText,
        }).then((button) => {
          if (button === 'ok') {
            executionFun();
          }
        });
      }
    } else {
      await executionFun();
    }
  }

  function handleQRCodeDownload() {
    const canvas = document.getElementById('lc-qrCode');
    const img = new Image();
    img.src = canvas.toDataURL('image/png');
    img.onload = () => {
      const link = document.createElement('a');
      link.href = img.src;
      link.download = `${intl.formatMessage({ id: 'lcr.components.desc.qr.code', defaultMessage: '二维码' })}.png`;
      const event = new MouseEvent('click');
      link.dispatchEvent(event);
    };
  }

  async function handleQRCode() {
    const currentViewId = viewDataSet.current?.get('id');
    const businessObjectId = viewDataSet.current?.get('businessObjectId');
    const realViewId = qrViewType === 'PC' ? currentViewId : qrViewId;
    let qrValue = `${getEnv('API_HOST')}/lc/v1/${tenantId}/views/scanQRCode/${realViewId}${parentInstanceId ? `?dataId=${parentInstanceId}` : ''}`;
    if (!variableFlag && parentInstanceId && businessObjectId && qrType === 'RETRIEVE_DATA') {
      let fields = [];
      try {
        const fieldList = JSON.parse(qrFields);
        fields = fieldList.map(f => f.path);
      } catch (e) {
        fields = [];
      }
      try {
        const result = await axios.post(`/lc/v1/${tenantId}/business_objects/data/${businessObjectId}/${parentInstanceId}?showType=${qrShowType}`, fields);
        if (!result.failed) {
          qrValue = qrShowType === 'json'
            ? JSON.stringify({
              ...result,
              viewId: realViewId,
            })
            : result;
        } else {
          message.error(intl.formatMessage({ id: 'lcr.components.desc.execute.failed', defaultMessage: '查询数据失败' }));
          return false;
        }
      } catch (e) {
        return false;
      }
    }
    // 服务项中，默认跳转移动提单视图
    if (variableFlag && itemId) {
      qrValue = `${getEnv('MB_HOST')}/pages/create-order/index?tenant=${tenantNum}&itemId=${itemId}&qrCode=true`;
    }
    setQrCodeValue(qrValue);
    // Modal.open({
    //   title: viewName || name,
    //   children: (
    //     <div style={{ textAlign: 'center' }}>
    //       <QRCode
    //         value={qrValue}
    //         size={255}
    //         fgColor="#000"
    //         includeMargin
    //         id="lc-qrCode"
    //       />
    //     </div>
    //   ),
    //   key: modalKey,
    //   style: qrModalStyle,
    //   bodyStyle,
    //   destroyOnClose: true,
    //   okText: intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' }),
    //   onOk: handleQRCodeDownload,
    // });
  }

  function handleQRClose(e) {
    e.stopPropagation();
    setQrCodeValue('');
  }

  function renderQRCOde() {
    return (
      <div
        key={id}
        className="lc-button-qrCode-wrapper"
      >
        <QRCode
          value={qrCodeValue}
          size={152}
          fgColor="#000"
          includeMargin
          id="lc-qrCode"
        />
        <div className="qrCode-text">
          <span
            className="qrCode-download"
            onClick={handleQRCodeDownload}
          >
            {intl.formatMessage({ id: 'lcr.components.desc.import.click.download', defaultMessage: '点击下载' })}
          </span>
          {intl.formatMessage({ id: 'lcr.components.desc.qrcode', defaultMessage: '二维码' })}
        </div>
        <Icon
          type="close"
          className="qrCode-close"
          onClick={handleQRClose}
        />
      </div>
    );
  }

  async function handleImport() {
    const businessObjectId = tableRecord.get('widgetConfig.modelId');
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.batch.import', defaultMessage: '批量导入' }),
      children: <BatchImport businessObjectId={businessObjectId} handleDownloadTemplateAndData={handleExport} btnInfo={record} parentViewType={parentViewType} dataSet={dataSet} relatedFieldCode={relatedFieldCode} />,
      key: modalKey,
      style: qrModalStyle,
      bodyStyle,
      destroyOnClose: true,
      okCancel: false,
      okText: intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' }),
    });
  }
  const saveExportField = async (fieldData = []) => {
    const url = `lc/v1/${tenantId}/field/export/storage?viewId=${exportDataSet.current.get('viewId')}`;
    const res = await axios.post(url, { fields: fieldData });
    if (res?.failed) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.export.memory.save.fail', defaultMessage: '导出字段记忆保存失败' }));
    }
  };
  async function handleExport() {
    const dsConfig = dsManager.getConfig(tableId);
    const fuzzySearchData = dsConfig?.get('fuzzySearch');
    const columnViews = viewDataSet?.current?.get('personalColumns') || [];
    const columnViewId = columnViews.find(v => v.defaultFlag)?.id;
    const realViewId = viewDataSet?.current?.get('id');
    const tableFilters = viewDataSet?.current?.get('filters') || [];
    const defaultFilterId = tableFilters.find(v => v.defaultFlag)?.id;
    const fields = tableRecord.getCascadeRecords('widgetConfig.fields');
    const businessObjectId = tableRecord.get('widgetConfig.modelId');
    const orderBy = tableRecord?.get('widgetConfig.orderBy');
    const modifyConditionFlag = tableRecord.get('widgetConfig.modifyConditionFlag') === undefined || tableRecord.get('widgetConfig.modifyConditionFlag');
    exportDataSet.create({
      type: 'EXPORT',
      viewId: realViewId,
      objectId: businessObjectId,
      asyncFlag: false,
      basicViewId: columnViewId,
      basicFilterId: defaultFilterId,
      totalCount: dataSet?.totalCount,
    });
    Modal.open({
      title: intl.formatMessage({ id: 'zknow.common.button.export', defaultMessage: '导出' }),
      className: 'yqcloud-export-modal',
      children: (
        <ExportForm
          dataSet={exportDataSet}
          businessObjectId={businessObjectId}
          tenantId={tenantId}
          prefixCls={prefixCls}
          intl={intl}
          lastViewId={realViewId}
          tableFilterDataSet={tableFilterDataSet}
          tableViewDataSet={tableViewDataSet}
          columnDataSet={columnDataSet}
          defaultFilterId={defaultFilterId}
          defaultColumnViewId={columnViewId}
          modifyConditionFlag={modifyConditionFlag}
          queryDataSet={dataSet.queryDataSet}
          fuzzySearchData={fuzzySearchData}
          fields={fields}
          tableDataSet={dataSet}
          viewDataSet={viewDataSet}
          language={language}
          datasetId={tableId}
          relatedFieldCode={relatedFieldCode}
          instanceId={instanceId}
          tableOrderConfig={orderBy}
        />
      ),
      key: 'table-btn-export-modal',
      style: conditionModalStyle,
      destroyOnClose: true,
      onOk: async () => {
        const sortFields = exportDataSet.getState('sortFields') || [];
        const sourceData = exportDataSet.current.toData();
        const { viewId: sourceDataViewId, type: sourceDataType, objectId, asyncFlag, viewName: sourceDataViewName } = sourceData;
        sourceData.fields = sortFields;
        const postMap = {
          viewId: sourceDataViewId,
          type: sourceDataType,
          objectId,
          asyncFlag,
          jsonData: JSON.stringify({ ...sourceData, datasetId: tableId }),
          lcCondition: JSON.stringify({
            filterUuid: uuidv4(),
            condition: 'AND',
            field: relatedFieldCode,
            filter: 'is',
            fieldValueType: 'CONSTANT',
            fieldValue: instanceId,
          }),
          labelOrderBy: orderBy,
        };
        // 导出提交
        if (asyncFlag) {
          exportDataSet.setState('loading', true);
          const result = await axios.post(`data/v1/${tenantId}/dataTasks`, JSON.stringify(postMap));
          if (result && !result.failed) {
            message.success(intl.formatMessage({ id: 'lcr.components.desc.export.running', defaultMessage: '导出进行中' }));
            saveExportField(sourceData.fields);
          } else {
            message.error(intl.formatMessage({ id: 'lcr.components.desc.export.failed', defaultMessage: '导出失败' }));
          }
        } else {
          exportDataSet.setState('loading', true);
          const result = await axios.post(
            `data/v1/${tenantId}/dataTasks/immediate`,
            JSON.stringify(postMap),
            { responseType: 'blob' },
          );
          if (result && !result.failed && result.type !== 'application/json') {
            const blob = new Blob([result]);
            let filename = '';
            if (sourceDataViewName) {
              filename = `${sourceDataViewName}-${getCurrentDate()}.xlsx`;
            } else {
              filename = `${getCurrentDate()}.xlsx`;
            }
            if ('msSaveOrOpenBlob' in navigator) {
              // ie使用的下载方式
              window.navigator.msSaveOrOpenBlob(blob, filename);
            } else {
              const elink = document.createElement('a');
              // 设置下载文件名
              elink.download = filename;
              elink.style.display = 'none';
              elink.href = URL.createObjectURL(blob);
              document.body.appendChild(elink);
              elink.click();
              document.body.removeChild(elink);
            }
            message.success(intl.formatMessage({ id: 'lcr.components.desc.export.success', defaultMessage: '导出成功' }));
            saveExportField(sourceData.fields);
          } else {
            message.error(intl.formatMessage({ id: 'lcr.components.desc.export.failed', defaultMessage: '导出失败' }));
          }
        }
        exportDataSet.setState('loading', false);
        return false;
      },
      onClose: () => {
        exportDataSet.reset();
      },
    });
  }

  async function handleWatchHistory() {
    const businessObjectId = viewDataSet.current?.get('businessObjectId');
    Modal.open({
      title: (<ModalTitle title={intl.formatMessage({ id: 'lcr.components.desc.export.history', defaultMessage: '导出历史' })} dataSet={downloadHistoryDataSet} />),
      className: 'yqcloud-history-modal',
      children: (
        <HistoryDetailForm
          dataSet={downloadHistoryDataSet}
          businessObjectId={businessObjectId}
          tenantId={tenantId}
          prefixCls={prefixCls}
          intl={intl}
        />
      ),
      drawer: true,
      key: historyModalKey,
      style: mdModalStyle,
      destroyOnClose: true,
      footer: (okbtn, cancel) => [],
    });
  }

  async function handleAttachment() {
    const jsonData = viewDataSet?.current?.get('jsonData');
    const fileName = viewDataSet?.current?.get('name');
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === parentViewId)?.fields || [];
    const attachments = [];
    const formRecord = dataSet?.current;
    if (formRecord && dsFieldList) {
      dsFieldList.map(file => {
        if (['Upload', 'Image'].includes(file.widgetType)) {
          attachments.push(formRecord.get(file.code));
        }
        return file;
      });
    }
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.attachment', defaultMessage: '下载附件' }),
      children: (
        <DownloadModal attachments={attachments} fileName={`${fileName}_${parentInstanceId}`} />
      ),
      key: modalKey,
      style: mdModalStyle,
      bodyStyle,
      destroyOnClose: true,
    });
  }

  function handleClose() {
    if (tabDataSet?.current) {
      // 如果有tabDataSet就表示为itsm下的标签页类型，此时需要关闭当前标签页
      tabDataSet.remove(tabDataSet.current);
      return null;
    }
    if (modal) {
      modal.close();
    } else if (!record?.cascadeParent?.get('viewType')) {
      // 如果没有一般表示在最外层，此时刷新一下列表或当前页面
      dataSet.query();
    } else {
      history.goBack();
    }
  }

  const handleInlineCreate = async () => {
    const fieldMap = {
      _parentId: instanceId, // 计算默认值将父级id传入
    };
    let defaultData = {};
    // 变量视图viewType为null，不请求默认值接口
    if (fromTable && tableId && parentViewType) {
      defaultData = await axios.post(
        `lc/v1/engine/${tenantId}/dataset/${parentViewId}/${tableId}/calculate`,
        JSON.stringify(fieldMap),
      );
    }
    if (relatedFieldCode && instanceId) {
      defaultData[relatedFieldCode] = instanceId;
    }
    const newRecord = dataSet.create(defaultData, 0);
    dataSet.setState('businessObjectId', tableRecord.get('widgetConfig.modelId'));
    dataSet.setState('relatedFieldCode', relatedFieldCode);
    newRecord.editing = true;
  };

  async function handleClick(actionType) {
    if (mode === 'PREVIEW') {
      return null;
    }
    switch (type) {
      case 'PRINT':
        await preConfirm(handlePrint);
        break;
      case 'BATCHPRINT':
        await preConfirm(handleBatchPrint);
        break;
      case 'BATCHPRINTHISTORY':
        await preConfirm(handleBatchPrintHistory);
        break;
      case 'SUBMIT':
        await preConfirm(handleSubmit);
        break;
      case 'DELETE':
        await preConfirm(handleDelete);
        break;
      case 'REMOVE':
        await preConfirm(handleRemove);
        break;
      case 'UPDATE':
        await preConfirm(() => handleUpdate(actionType));
        break;
      case 'CREATE':
        if (viewId) {
          await preConfirm(executionAction);
        } else {
          await preConfirm(handleSubmit);
        }
        break;
      case 'ADD':
      case 'CUSTOM':
      case 'OPEN':
        await preConfirm(executionAction);
        break;
      case 'COPY':
        await preConfirm(executionAction);
        break;
      case 'EXPRESSION':
        await preConfirm(handleExpression);
        break;
      case 'WORKFLOW':
        await preConfirm(handleExpression);
        break;
      case 'QRCode':
        await handleQRCode();
        break;
      case 'Attachment':
        await handleAttachment();
        break;
      case 'CLOSE':
        await preConfirm(handleClose);
        break;
      case 'IMPORT':
        await handleImport();
        break;
      case 'EXPORT':
        await handleExport();
        break;
      case 'EXPORT_HISTORY':
        await handleWatchHistory();
        break;
      case 'INLINECREATE':
        await preConfirm(handleInlineCreate);
        break;
      case 'REFRESH':
        await dataSet.query(dataSet?.currentPage, dataSet?.queryParameter);
        break;
      case 'OPEN_URL':
        window.open(link);
        break;
      default:
        break;
    }
    return true;
  }

  if (fromTable && type === 'UPDATE') {
    return [
      <Button
        funcType="raised"
        color={color}
        icon={falseValueIcon}
        onClick={() => handleClick('true')}
        id={`${id}-true`}
        key={`${id}-true`}
      >
        {falseValueText}
      </Button>,
      <Button
        funcType="raised"
        color={color}
        icon={trueValueIcon}
        onClick={() => handleClick('false')}
        id={`${id}-false`}
        key={`${id}-false`}
      >
        {trueValueText}
      </Button>,
    ];
  }

  function getName() {
    if (lineButton) {
      return name;
    }
    if (type === 'UPDATE' && updateFieldCode) {
      return dataSet?.current?.get(updateFieldCode);
    }
    return name;
  }

  function getIcon() {
    if (lineButton) {
      return icon;
    }
    if (type === 'UPDATE' && updateFieldCode) {
      return dataSet?.current?.get(updateFieldCode);
    }
    return icon;
  }

  if (buttonMode === 'menu') {
    return (
      <Menu.Item
        onClick={isTableView && TABLE_BUTTON_TYPES.includes(type) ? tableAction : handleClick}
        key={id}
        className="c7n-menu-item"
      >
        <div id={id} style={{ display: 'flex', alignItems: 'center' }}>
          <Icon style={{ color: '#8c8c8c' }} type={icon} />
          <span style={{ marginLeft: 8, color: '#595959' }}>
            {getName()}
            {type === 'QRCode' && qrCodeValue ? renderQRCOde() : null}
          </span>
        </div>
      </Menu.Item>
    );
  }

  return (
    <Button
      funcType={funcType}
      color={color}
      icon={getIcon()}
      disabled={(dataSet?.getState('wysiwyg-uploading') === true) || (isHide && lineButton && btnLength > 1)}
      onClick={isTableView && TABLE_BUTTON_TYPES.includes(type) ? tableAction : btnOnClick || handleClick}
      id={id}
      key={id}
    >
      {!lineButton || (lineButton && !fixedActionFlag) ? getName() : null}
      {/* {getName()} */}
      {type === 'QRCode' && qrCodeValue ? renderQRCOde() : null}
    </Button>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutButton */
