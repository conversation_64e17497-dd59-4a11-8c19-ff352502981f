import React, { useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { Tooltip } from 'choerodon-ui/pro';
import { withErrorBoundary, ExternalComponent, Icon } from '@zknow/components';

import './Conversion.less';

const Index = observer(({ record, dataSet, context }) => {
  const { intl } = context;
  const label = record.get('name');
  const [number, setNumber] = useState('');

  return (
    <div className="conversion-wrapper">
      <div className="conversion-title">
        <span>{label}</span>
        {number
          ? (
            <span className="conversion-number">
              {number}
              <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}>
                <Icon
                  type="Copy"
                  onClick={() => copy(number)}
                  theme="filled"
                  className="conversion-number-copy"
                />
              </Tooltip>
            </span>
          ) : null}
      </div>
      <ExternalComponent
        system={{ scope: 'intelligent', module: 'intelligent_session_record' }}
        callback={<span />}
        formDataSet={dataSet}
        ticketFlag
        numberCallback={setNumber}
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutConversion */
