import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, TextField, Icon, Form, DataSet } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';
import Location from '@/components/location';

const modalKey = Modal.key();

const Index = observer(({ record, dataSet, context }) => {
  const { intl } = context;
  const label = record.get('name');
  const url = record.get('widgetConfig.locationUrl');
  const fieldCode = record.get('widgetConfig.locationFieldCode');
  const defaultValue = dataSet?.current?.get(fieldCode);

  const locationDataSet = useMemo(() => new DataSet({
    autoCreate: true,
    selection: false,
    fields: [{
      name: 'location',
      type: 'string',
      label: label,
    }],
  }), []);

  function handleChange(local) {
    locationDataSet.setState('location', local);
  }

  function getLocalText(local) {
    return local.poiaddress;
  }

  function handleClick() {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.chose.location', defaultMessage: '选择位置' }),
      children: (
        <Location
          url={url}
          defaultValue={defaultValue}
          onChange={handleChange}
        />
      ),
      key: modalKey,
      drawer: false,
      fullScreen: true,
      destroyOnClose: true,
      onOk: () => {
        const local = locationDataSet.getState('location');
        if (locationDataSet.current) {
          locationDataSet.current.set('location', getLocalText(local));
        }
        if (dataSet?.current) {
          dataSet?.current.set(fieldCode, JSON.stringify(local));
        }
        locationDataSet.setState('location', null);
      },
      onCancel: () => {
        dataSet.setState('location', null);
        return true;
      },
    });
  }

  function renderText() {
    if (dataSet?.current?.get(fieldCode)) {
      try {
        const local = JSON.parse(dataSet?.current?.get(fieldCode));
        return getLocalText(local);
      } catch (e) {
        return '';
      }
    }
    return '';
  }

  return (
    <Form
      labelWidth="100"
      labelLayout="horizontal"
      record={locationDataSet?.current}
    >
      <TextField
        name="location"
        suffix={<Icon type="search" onClick={handleClick} />}
        placeholder={intl.formatMessage({ id: 'lcr.components.desc.chose.location', defaultMessage: '选择位置' })}
        onDoubleClick={handleClick}
        renderer={renderText}
      />
    </Form>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutLocation */
