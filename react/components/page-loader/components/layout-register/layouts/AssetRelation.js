import React, { useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { Output, Form, Lov, Modal } from 'choerodon-ui/pro';
import { Icon, ExternalComponent, withErrorBoundary } from '@zknow/components';
import Title from '../../../../title';
import AssetImg from '../../../../../assets/images/assetImg/icon-package.svg';
import AssetUsingImg from '../../../../../assets/images/assetImg/tag-use.svg';
import AssetPairImg from '../../../../../assets/images/assetImg/tag-maintenance.svg';
import { queryServiceItemInfo, queryAssetInfo } from '../../../../../service';

import './asset.less';

const ticketsBusinessObjectCode = ['INCIDENT', 'CHANGE', 'PROBLEM'];

const modalKey = Modal.key();

const Index = inject('AppState')(observer((props) => {
  const { record, context, AppState: { userInfo: { personId: userId } } } = props;
  const {
    tenantId, viewDataSet, dsManager, intl, formDataSet: dataSet, requestItemConfig,
    history, history: { location }, history: { location: { search, params } },
  } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));
  const label = record?.get('name');
  const assetType = record?.get('widgetConfig')?.assetType || 'HARDWARE';
  const [viewVariable, setViewVariable] = useState({});
  const [needRelation, setNeedRelation] = useState(false);
  const [valName, setValName] = useState('');
  const [expand, setExpand] = useState(true);
  const [infoValue, setInfoValue] = useState(null);
  const lovRef = useRef();

  async function queryServiceItem() {
    if (viewDataSet?.current?.get('itemId')) {
      await queryServiceItemInfo({ tenantId, itemId: viewDataSet?.current?.get('itemId') })
        .then(res => {
          if (res && !res.failed) {
            formDataSet?.current?.set('assetTaskType', res.assetTaskType);
            if (res.type === 'ASSET' && (res.assetTaskType !== 'REQUEST' || requestItemConfig)) {
              let lovCode = '';
              let lovParas = {};
              setNeedRelation(true);
              if (requestItemConfig) {
                // 服务项视图，走变量视图逻辑
                lovCode = 'CATEGORY_HARDWARE_NOT_USER';
                lovParas = { type: assetType };
              } else if (res.assetTaskType === 'REPAIR') {
                lovCode = 'USER_CATEGORY_HARDWARE';
                lovParas = { state: 'USING', type: assetType };
              } else if (res.assetTaskType) {
                lovCode = 'USER_NOT_DISCARD_HARDWARE';
                lovParas = { type: assetType };
              }
              formDataSet.addField(`${assetType}assetRelation`, {
                type: 'object',
                multiple: false,
                lovCode,
                label: record.get('name'),
                dynamicProps: {
                  lovPara: ({ record: r }) => {
                    return { categoryId: r.get('assetTypeId'), userId, ...lovParas };
                  },
                },
                transformRequest: (value) => {
                  if (requestItemConfig) {
                    return JSON.stringify(value);
                  }
                  return value;
                },
                transformResponse: (value, data) => {
                  if (requestItemConfig) {
                    try {
                      return JSON.parse(value);
                    } catch (e) {
                      return value;
                    }
                  }
                  return value;
                },
              });
            } else {
              setNeedRelation(false);
            }
          }
        });
    } else {
      const lovCode = location.pathname.includes('service_catalog') ? 'USER_CATEGORY_HARDWARE' : 'USER_NOT_DISCARD_HARDWARE';
      const lovPara = location.pathname.includes('service_catalog') ? { state: 'USING', userId } : {};
      formDataSet.addField(`${assetType}assetRelation`, {
        type: 'object',
        multiple: false,
        lovCode,
        label: record.get('name'),
        dynamicProps: {
          lovPara: () => {
            return { type: assetType, ...lovPara };
          },
        },
      });
      setNeedRelation(true);
    }
  }

  useEffect(() => {
    if (viewDataSet?.current && ticketsBusinessObjectCode.includes(viewDataSet?.current?.get('businessObjectCode'))) {
      queryServiceItem();
    } else if (formDataSet?.current) {
      queryServiceItem();
      if (params && params.assetInfo) {
        setNeedRelation(true);
        const data = params.assetInfo;
        const infoObj = {
          code: data.code,
          name: data.name,
          categoryname: data['category_id:name'],
          modelCode: data['model_id:model_code'],
          inStockDate: data.in_stock_date,
          dueDate: data.in_use_date,
          description: data.description,
          state: data.state,
          mgmtDeptName: data['mgmt_dept_id:real_name'],
          mgmtUserName: data['mgmt_user_id:real_name'],
        };
        const lovObj = {
          code: data.code,
          name: data.name,
        };
        formDataSet.current.set('assetId', data.id);
        formDataSet.current.set('assetCode', data.code);
        formDataSet.current.set('assetInfo', JSON.stringify(infoObj));
        formDataSet.current.set(`${assetType}assetRelation`, lovObj);
      }
    } else if (dataSet?.current) {
      if (dataSet?.current.get('item_id:variable_view_id:_variable')) {
        setValName('item_id:variable_view_id:_variable');
        setNeedRelation(true);
      }
      if (dataSet?.current.get('req_item_id:item_id:variable_view_id:_variable')) {
        setValName('req_item_id:item_id:variable_view_id:_variable');
        setNeedRelation(false);
      }
      if (dataSet?.current.get('request_item_id:item_id:variable_view_id:_variable')) {
        setValName('request_item_id:item_id:variable_view_id:_variable');
        setNeedRelation(true);
      }
      const currentVal = dataSet?.current.get('item_id:variable_view_id:_variable') || dataSet?.current.get('req_item_id:item_id:variable_view_id:_variable') || dataSet?.current.get('request_item_id:item_id:variable_view_id:_variable');
      setViewVariable(currentVal);
      dataSet.addField(`${assetType}assetRelation`, {
        type: 'object',
        multiple: false,
        lovCode: 'CATEGORY_HARDWARE_NOT_USER',
        label: record.get('name'),
        dynamicProps: {
          lovPara: ({ record: r }) => {
            return { categoryId: currentVal.assetTypeId, userId, type: assetType };
          },
        },
      });
      dataSet?.current.set(`${assetType}assetRelation`, currentVal?.[`${assetType}assetRelation`]);
    }
  }, [formDataSet?.current, dataSet?.current, needRelation]);

  useEffect(() => {
    const currentVal = dataSet?.current?.get(valName);
    if (formDataSet?.current?.get('assetInfo') || currentVal?.assetInfo || (ticketsBusinessObjectCode.includes(viewDataSet?.current?.get('businessObjectCode')) && formDataSet?.current?.get(`${assetType.toLowerCase()}_id`))) {
      getValue();
    }
  }, [valName]);

  function handleDelete() {
    if (!viewDataSet?.current?.get('itemId')) {
      formDataSet.current.set(`${assetType.toLowerCase()}_id`, null);
      formDataSet.current.set(`${assetType.toLowerCase()}_id:name`, null);
      setInfoValue(null);
    } else if (formDataSet?.current) {
      formDataSet.current.set('assetId', null);
      formDataSet.current.set('assetCode', null);
      formDataSet.current.set('assetInfo', null);
      formDataSet.current.set(`${assetType}assetRelation`, null);
      setInfoValue(null);
    } else if (dataSet?.current) {
      const currentVal = { ...viewVariable };
      currentVal.assetId = null;
      currentVal.assetCode = null;
      currentVal.assetInfo = null;
      currentVal[`${assetType}assetRelation`] = null;
      setViewVariable({ ...currentVal });
      dataSet.current.set(valName, currentVal);
      setInfoValue(null);
    }
    queryServiceItem();
  }

  function handleChooseAsset() {
    lovRef?.current?.openModal();
  }

  function renderState(str) {
    if (!str) {
      return null;
    } else if (str === 'USING') {
      return <div className="img-state">
        <img src={AssetUsingImg} alt="" />
      </div>;
    } else if (str === 'REPAIR') {
      return <div className="img-state">
        <img className="img-state" src={AssetPairImg} alt="" />
      </div>;
    }
  }
  async function handleQueryAssetInfo(id) {
    try {
      await queryAssetInfo({ tenantId, assetId: id, assetType })
        .then(res => {
          setInfoValue(res);
        });
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log(error);
    }
  }

  function getValue() {
    let currentVal = dataSet?.current.get(valName);
    if (viewDataSet?.current && ticketsBusinessObjectCode.includes(viewDataSet?.current?.get('businessObjectCode')) && formDataSet?.current?.get(`${assetType.toLowerCase()}_id`)) {
      currentVal = formDataSet?.current.toData();
      handleQueryAssetInfo(formDataSet?.current?.get(`${assetType.toLowerCase()}_id`)?.id || formDataSet?.current?.get(`${assetType.toLowerCase()}_id`));
    } else if (formDataSet?.current?.get('assetInfo')) {
      try {
        setInfoValue(JSON.parse(formDataSet?.current?.get('assetInfo')));
      } catch (e) {
        setInfoValue(formDataSet?.current?.get('assetInfo'));
      }
    } else if (currentVal['assetInfo:id']) {
      handleQueryAssetInfo(currentVal['assetInfo:id']);
    } else {
      setInfoValue(currentVal.assetInfo);
    }
  }

  function handleClickName(value) {
    const { viewId, name, lastViewId, id } = value;
    if (!viewId) return null;
    if (record?.get('widgetConfig.detailLinkViewType') === 'NEW') {
      if (formDataSet?.current) {
        history.push({
          pathname: `/itsm/portal/asset_center/${viewId}`,
          search: `${search}`,
          params: { text: name },
        });
      } else {
        history.push({
          pathname: `/lc/engine/${viewId}/${id}/${lastViewId}`,
          search: `${search}`,
        });
      }
    } else {
      Modal.open({
        key: modalKey,
        title: intl.formatMessage({ id: 'lcr.components.desc.asset.asset.info', defaultMessage: '资产详情' }),
        className: 'lc-asset-info-modal',
        children: (
          <ExternalComponent
            system={{
              scope: 'lcr',
              module: 'PageLoader',
            }}
            viewId={viewId}
            showHeaderFlag={false}
            hiddenFooter
          />
        ),
        drawer: record?.get('widgetConfig.detailLinkViewType') === 'RIGHT',
        style: { width: record?.get('widgetConfig.detailLinkViewSize') || '8rem' },
        destroyOnClose: true,
        footer: null,
      });
    }
  }

  function renderCmp() {
    if (infoValue) {
      return (<div className="lc-assetRelation-info">
        <div className="lc-assetRelation-info-title">
          <div className="name">
            <div className="img-asset"><img src={AssetImg} alt="" /></div>
            <span className="name-text">{infoValue.code}</span>
            <span className="name-link name-text" onClick={() => handleClickName(infoValue)}>{infoValue.name}</span>
            <Icon type="delete" onClick={handleDelete} />
          </div>
          {renderState(infoValue.state)}
        </div>
        <div className="lc-assetRelation-info-detail">
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.manager.group', defaultMessage: '管理部门：' })}</span>{infoValue.mgmtDeptName}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.manager.user', defaultMessage: '管理人员：' })}</span>{infoValue.mgmtUserName}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.asset.type', defaultMessage: '资产类别：' })}</span>{infoValue.categoryname}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.modal.type', defaultMessage: '产品型号：' })}</span>{infoValue.modelCode}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.in.stock', defaultMessage: '入库日期：' })}</span>{infoValue.inStockDate}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.due', defaultMessage: '计划到期：' })}</span>{infoValue.dueDate}</div>
          <div><span className="lc-assetRelation-info-detail-label">{intl.formatMessage({ id: 'lcr.components.desc.asset.description', defaultMessage: '说明：' })}</span>{infoValue.description}</div>
        </div>
      </div>);
    } else {
      return (
        <div className="lc-assetRelation-info lc-assetRelation-info-select" onClick={handleChooseAsset}>
          <Lov ref={lovRef} name={`${assetType}assetRelation`} onChange={handleChange} className="lc-assetRelation-info-lov" />
          <div className="lc-assetRelation-info-btn">
            <Icon type="plus" style={{ marginRight: '0.04rem' }} />
            <span className="text">{intl.formatMessage({ id: 'lcr.components.desc.asset.choose.asset', defaultMessage: '选择资产' })}</span>
          </div>
        </div>
      );
    }
  }

  function handleChange(value) {
    setInfoValue(value);
    if (viewDataSet?.current && ticketsBusinessObjectCode.includes(viewDataSet?.current?.get('businessObjectCode'))) {
      formDataSet.current.set(`${assetType.toLowerCase()}_id`, value.id);
      formDataSet.current.set(`${assetType.toLowerCase()}_id:name`, value.name);
    } else if (formDataSet?.current) {
      formDataSet.current.set('assetId', value.id);
      formDataSet.current.set('assetCode', value.code);
      formDataSet.current.set('assetInfo', JSON.stringify(value));
      formDataSet.current.set(`${assetType}assetRelation`, value);
      formDataSet.current.set('assetType', assetType);
    } else if (dataSet?.current) {
      const currentVal = { ...viewVariable };
      currentVal.assetId = value.id;
      currentVal.assetCode = value.code;
      currentVal.assetInfo = value;
      currentVal.assetType = assetType;
      setViewVariable({ ...currentVal });
      dataSet.current.set(valName, currentVal);
    }
  }

  return needRelation
    ? (
      <>
        <div
          className="lc-form-viewer-section-header"
          onClick={() => setExpand(!expand)}
        >
          <Title title={label} sectionFlag />
          <Icon theme="filled" type={expand ? 'DownOne' : 'RightOne'} />
        </div>
        {expand && <Form
          className="lc-expression-form"
          record={formDataSet?.current || dataSet?.current}
          labelLayout="none"
        >
          <Output
            renderer={renderCmp}
          />
        </Form>}
      </>
    ) : null;
}));

export default withErrorBoundary(Index);
