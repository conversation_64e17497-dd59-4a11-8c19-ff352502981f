import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { withErrorBoundary, Button, Icon } from '@zknow/components';
import { message, Modal, DataSet, Form, TextArea, SelectBox, Menu, Tooltip, Table } from 'choerodon-ui/pro';
import lodashFilter from 'lodash/filter';
import axios from 'axios';
import { getServiceSetting } from '@/service';
import './BatchPressTicket.less';

const { Option } = SelectBox;

const Result = ({ intl, successCount, totalCount, failCount, failTickets }) => {
  const [expand, setExpand] = useState(false);
  const allSuccess = successCount === totalCount;
  const allFailed = failCount === totalCount;

  let status;
  if (allFailed) {
    status = 'failed';
  } else if (allSuccess) {
    status = 'success';
  } else {
    status = 'partial';
  }

  const iconMapping = {
    failed: {
      icon: 'close-one',
      color: '#F34C35',
      title: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.failed' }),
    },
    success: {
      icon: 'check-one',
      color: '#1AB335',
      title: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.success' }),
    },
    partial: {
      icon: 'info',
      color: '#ffcb6b',
      title: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.partial', defaultMessage: '部分成功' }),
    },
  };
  const ds = new DataSet({
    paging: false,
    fields: [
      { name: 'number', label: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.ticketNumber' }) },
      { name: 'reason', label: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.reason' }) },
    ],
    data: failTickets || [],
  });

  return (
    <div className="lc-table-action-pressTicket">
      <Icon theme="filled" fill={iconMapping[status].color} type={iconMapping[status].icon} size={50} />
      <div className="lc-table-action-pressTicket-title">{iconMapping[status].title}</div>
      <div className="lc-table-action-pressTicket-context">
        {intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.summary' }, { totalCount })}
        <span style={{ color: '#1AB335' }}>{successCount}</span>
        {intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.summary.success' })}
        <span style={{ color: '#F34C35' }}>{failCount}</span>
        {intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.summary.fail' })}
      </div>
      {failCount ? (
        <>
          <div
            className="lc-table-action-pressTicket-button"
            onClick={() => { setExpand(!expand); }}
          >
            <span>{expand ? intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.view.fold' }) : intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.view' })}</span>
            <span style={{ display: expand ? 'none' : 'inline-flex' }}><Icon type="right" size="16" /></span>
            <span style={{ display: !expand ? 'none' : 'inline-flex' }}><Icon type="down" size="16" /></span>
          </div>
          {expand && (
            <Table
              border
              highLightRow={false}
              queryBar="none"
              selectionMode="none"
              dataSet={ds}
              columns={[
                { name: 'number' },
                { name: 'reason' },
              ]}
            />
          )}
        </>
      ) : null}
    </div>
  );
};

const ReminderForm = observer(({ dataSet, options, intl }) => {
  return (
    <Form dataSet={dataSet}>
      <SelectBox vertical name="messageTips">
        {lodashFilter(options, 'enabledFlag').map(({ code, meaning }) => (<Option value={code}>{meaning}</Option>))}
        <Option value="OTHER">{intl.formatMessage({ id: 'lcr.renderer.pressTicket.other' })}</Option>
      </SelectBox>
      {dataSet.current.get('messageTips') === 'OTHER' && (
        <TextArea
          autoSize={{ minRows: 3, maxRows: 4 }}
          resize="height"
          name="message"
        />
      )}
    </Form>
  );
});

// NOTE 批量催办无法根据服务配置的开关进行显隐控制
//  因为这个按钮可能拖入全部类型单据，而催单的开关挂在业务对象上
//  所以默认拖入组件就一定会显示，无权限就让后端抛异常
// FIXME 也许这个功能就不应该关联到业务对象上，而是一个租户设置，无关单据类型
const Index = injectIntl(observer(({ intl, record, context, feature }) => {
  const { dsManager, tenantId, viewDataSet } = context;
  const tableDataSet = dsManager?.pageRef?.current?.tableDataSet;
  const name = record?.get('name');
  const id = record?.get('id');
  const color = record?.get('color');
  const [reminderSettingFlag, setReminderSettingFlag] = useState(false);

  useEffect(() => {
    checkPressSetting();
  }, [viewDataSet?.current?.get('businessObjectCode'), viewDataSet?.current?.get('businessObjectId')]);

  const handlePressTicket = async () => {
    if (!tableDataSet?.selected?.length) {
      message.info(intl.formatMessage({ id: 'lcr.renderer.pressTicket.dataNeed' }));
      return null;
    }
    const pressTicketDataSet = new DataSet({
      autoCreate: true,
      fields: [
        {
          name: 'message',
          type: 'string',
        },
        {
          name: 'messageTips',
          type: 'string',
          label: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content' }),
        },
        {
          name: 'messageOption',
          lookupCode: 'REMINDER_TICKET_MESSAGE',
        },
      ],
      events: {
        update: ({ name: n, record: r }) => {
          if (n === 'messageTips') {
            r.set('message', '');
          }
        },
      },
    });
    const options = await pressTicketDataSet.getField('messageOption').fetchLookup();
    pressTicketDataSet.current.set({ messageTips: options?.[0]?.code });

    Modal.open({
      title: (
        <div className="ticker-reminder-modal-title">
          <span>{intl.formatMessage({ id: 'lcr.renderer.pressTicket.title' })}</span>
          <span className="ticker-reminder-modal-title-icon">
            <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.pressTicket.help' })}>
              <Icon type="help" size="16" />
            </Tooltip>
          </span>
        </div>
      ),
      children: <ReminderForm dataSet={pressTicketDataSet} options={options} intl={intl} />,
      okText: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.send' }),
      onOk: async () => {
        let messageText = pressTicketDataSet.current?.get('message');
        if (pressTicketDataSet.current.get('messageTips') !== 'OTHER') {
          const quickItem = options.find(o => o.code === pressTicketDataSet.current.get('messageTips'));
          if (quickItem) {
            messageText = quickItem?.meaning || '';
          }
        }
        const ticketInfoMap = tableDataSet?.selected.map((i) => {
          const businessObjectCode = i.get('source_table');
          const businessObjectId = businessObjectCode ? '' : viewDataSet?.current?.get('businessObjectId');
          return {
            id: i.get('id'),
            businessObjectCode,
            businessObjectId,
          };
        });
        const pressData = {
          message: messageText,
          ticketInfo: ticketInfoMap,
        };
        const res = await axios.post(`itsm/v1/${tenantId}/ticket/reminders?batchFlag=true`, pressData);
        if (typeof res?.success === 'boolean') {
          tableDataSet.query();
          Modal.open({
            title: (
              <div className="ticker-reminder-modal-title">
                <span>{intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.feedback' })}</span>
                <span className="ticker-reminder-modal-title-icon">
                  <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.feedback.help' })}>
                    <Icon type="help" size="16" color="rgba(18, 39, 77, 1)" />
                  </Tooltip>
                </span>
              </div>
            ),
            children: <Result {...res} intl={intl} />,
            footer: null,
          });
          return true;
        }
      },
    });
  };

  const checkPressSetting = async () => {
    if (viewDataSet?.current?.get('businessObjectCode') && viewDataSet?.current?.get('businessObjectCode') === 'SVS_TASK') {
      setReminderSettingFlag(true);
    } else {
      const res = await getServiceSetting(tenantId, viewDataSet?.current?.get('businessObjectId'));
      setReminderSettingFlag(res?.reminderSettingFlag);
    }
  };

  if (!reminderSettingFlag) {
    return null;
  }

  return <>
    {feature === 'table-action' ? (
      <Menu.Item key={id} className="c7n-menu-item" onClick={handlePressTicket}>
        <Icon type="lightning" style={{ marginRight: 8 }} />
        {name}
      </Menu.Item>
    ) : (
      <Button
        key={id}
        onClick={handlePressTicket}
        funcType="raised"
        color={color}
        icon="lightning"
      >
        {name}
      </Button>
    )}
  </>;
}));

export default withErrorBoundary(inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })(Index)
), { fallback: <span /> });

/* externalize: LcLayoutBatchPressTicket */
