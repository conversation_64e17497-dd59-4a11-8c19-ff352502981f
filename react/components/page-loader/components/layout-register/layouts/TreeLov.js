import React, { useMemo, useEffect } from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, Icon } from '@zknow/components';
import { lovStore } from 'choerodon-ui/dataset';
import { Form, Lov, message, DataSet, Modal } from 'choerodon-ui/pro';
import axios from 'axios';
import './TreeLov.less';

const flat = (arr) => {
  return Array.isArray(arr) ? arr.reduce((prev, { key, value }) => ({ ...prev, [key]: value }), {}) : arr;
};

function parseIntl(mapping, lang, fallback) {
  const intlArr = mapping
    ? mapping.split(';').map(it => {
      const [key, value] = it.split('=');
      return {
        [key]: value,
      };
    })
    : [];
  const locale = intlArr.find((it) => !!it[lang]);
  return locale?.[lang] || fallback;
}

const Index = observer(({ record, context, dataSet }) => {
  const { intl, tenantId, AppState: { currentLanguage: language } } = context;
  // description 映射为存储字段
  const {
    description: fieldName,
    placeHolder: defaultValueField,
    name,
    requiredType,
    editType,
    customConfig,
    widgetConfig: { customConfig: _customConfig },
  } = record.toData();
  const DEFAULT_LOV_CODE = 'CSM_TENANT_PRODUCT_SUBMIT';

  const lovPara = useMemo(() => flat(customConfig || _customConfig || []) ?? {}, [customConfig]);

  const messageText = parseIntl(lovPara?.intlMessage, language, intl.formatMessage({ id: 'lcr.renderer.treeLov.prohibit' }));

  const lovProps = useMemo(() => {
    // 剔除 lovCode
    const { lovCode = DEFAULT_LOV_CODE, intlMessage, textField, placeholder, ..._lovPara } = lovPara;
    return [{
      type: 'object',
      name: `${fieldName}`,
      label: name,
      required: requiredType === 'ALWAYS_REQUIRED',
      disabled: editType === 'ALWAYS_NOT_EDIT',
      lovCode,
      textField: textField || 'parentPath',
      valueField: 'id',
      // 子级数据查询不分页，所以要处理下防止查询子级的数据时带上 dataSet 的 page 参数
      lovQueryAxiosConfig: (code, config, { params, data }) => {
        const defaultUrl = config?.originData?.requestUrl;
        const url = defaultUrl.replace('{tenantId}', tenantId);
        return {
          url,
          method: 'get',
          data,
          params,
        };
      },
      dynamicProps: {
        lovPara: () => {
          const recordData = dataSet?.current?.toData() || {};
          return Object.entries(_lovPara).reduce((prev, [key, value]) => ({
            ...prev,
            [key]: recordData[value] || recordData?.['sc_req_item_id:item_id:variable_view_id:_variable']?.[value],
          }), {});
        },
      },
    }];
  }, [fieldName, name, requiredType, editType, lovPara, dataSet?.current]);

  const lovDataSet = useMemo(() => new DataSet({
    autoCreate: true,
    selection: false,
    fields: [...lovProps],
    events: {
      update: ({ name: n, value }) => {
        if (n === `${fieldName}`) {
          if (!value || !value?.selectFlag) {
              dataSet?.current?.set({
                [fieldName]: undefined,
                [defaultValueField]: undefined,
                [`${defaultValueField}:${fieldName}`]: undefined,
                [`${defaultValueField}:${fieldName}:product_name`]: undefined,
                [`${defaultValueField}:${fieldName}:name`]: undefined,
              });
              value && !value?.selectFlag && message.warn(messageText);
          } else {
              dataSet?.current?.set({
                [fieldName]: value,
                [defaultValueField]: value?.id,
                [`${defaultValueField}:${fieldName}`]: value?.id,
                [`${defaultValueField}:${fieldName}:product_name`]: value?.name,
                [`${defaultValueField}:${fieldName}:code`]: value?.code,
                [`${defaultValueField}:${fieldName}:name`]: value?.name,
              });
          }
        }
      },
    },
  }), [lovProps]);

  useEffect(() => {
    // init value
    const lovConfig = lovStore.getConfig(lovPara?.lovCode || DEFAULT_LOV_CODE);
    if (lovConfig && dataSet?.current?.get(fieldName) && lovDataSet?.current) {
      const defaultUrl = lovConfig?.originData?.requestUrl;
      const defaultId = dataSet?.current?.get(defaultValueField)?.id || dataSet?.current?.get(defaultValueField);
      const nameField = dataSet?.current?.get(`${defaultValueField}:name`);
      const lovParams = Object.entries(lovPara).reduce((prev, [key, value]) => ({
        ...prev,
        [key]: dataSet?.current?.get(value)?.id || dataSet?.current?.get(value),
      }), {});
      try {
        const url = defaultUrl.replace('{tenantId}', tenantId);
        axios.get(url, {
          params: {
            ...lovParams,
            id: defaultId,
          },
        }).then(res => {
          if (!res?.failed) {
            const { parentPath } = (res?.content || [])?.find(i => i?.id === defaultId) || {};
              lovDataSet?.current?.set(fieldName, {
                id: defaultId,
                name: nameField,
                parentPath: parentPath || nameField,
                selectFlag: true, // 树形数据中可选择的层级标志
              });
          } else {
              lovDataSet?.current?.set(fieldName, {
                id: defaultId,
                name: nameField,
                parentPath: nameField,
                selectFlag: true, // 树形数据中可选择的层级标志
              });
          }
        });
      } catch (err) {
        throw Error(err?.message);
      }
    }
  }, [
    lovStore.getConfig(lovPara?.lovCode || DEFAULT_LOV_CODE),
      dataSet?.current,
      lovPara?.lovCode,
      lovDataSet?.current,
  ]);

  return (
    <Form dataSet={lovDataSet}>
      <Lov
        name={`${fieldName}`}
        onBeforeSelect={(r) => {
          if (!r?.get('selectFlag')) {
            if (lovPara?.warningType === 'modal') {
              Modal.open({
                children: (
                  <div className="lc-component-treeLov">
                    <Icon theme="filled" type="attention" fill="#2979FF" className="lc-component-treeLov-icon" />
                    <div className="lc-component-treeLov-title">{messageText}</div>
                  </div>
                ),
                header: null,
                className: 'lc-component-treeLov-modal',
                maskClosable: true,
              });
            } else {
              message.warn(messageText);
            }
            return false;
          }
        }}
        tableProps={{
          selectionMode: 'click',
          onRow: ({ dataSet: ds, record: r }) => ({
            // 保留 onRow，防止树形折叠器丢失
            isLeaf: r.get('leaf') || r.get('isLeaf'),
          }),
        }}
        placeholder={lovPara?.placeholder ? '      ' : ''} // 为了通用性，需要去除占位符
      />
    </Form>
  );
});

export default withErrorBoundary(inject('AppState')(formatterCollections({ code: ['lcr.renderer'] })(Index)));

/* externalize: LcLayoutTreeLov */
