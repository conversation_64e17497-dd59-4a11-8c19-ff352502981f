import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');
  const defaultFilter = record.get('widgetConfig.defaultFilter');

  return (
    <ExternalComponent
      system={{
        scope: 'itsm',
        module: 'YQDynamic',
      }}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      defaultFilter={defaultFilter}
      viewDataSet={viewDataSet}
      viewRecord={record}
      fallback={<span />}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutDynamic */
