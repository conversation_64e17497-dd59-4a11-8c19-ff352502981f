@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.title {
  font-size: 16px;
  font-weight: 500;
  margin: 16px 0;
}

.icon {
  vertical-align: text-top;
}

.switch {
  :global {
    .c7n-pro-radio-button {
      padding: 3px 8px;
    }

    .c7n-pro-radio-checked {
      .c7n-pro-radio-inner {
        background: @yq-primary-color-12 !important;
      }
    }
  }
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}
