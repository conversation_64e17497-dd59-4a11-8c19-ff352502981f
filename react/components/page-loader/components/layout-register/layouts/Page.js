import React, { useRef, useState, useEffect } from 'react';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import { ACTION, calculateConditions, formatUrlParams } from '@/components/page-loader/utils';
import PageLoader from '../../..';
import './Page.less';

const Index = observer(({ record, className, context, dataSet, ...rest }) => {
  const {
    mode, formDataSet, dsManager, viewId, viewDataSet, ticketFlag, instanceId,
    person, personId, parentKey, handlePageDsFieldMap, tenantId,
    mainStore,
  } = context;
  const id = viewDataSet.current?.get('id');
  const ds = formDataSet || dsManager.get(viewId || id);
  const {
    id: pageId,
    widgetConfig: {
      defaultViewType,
      viewId: relatedViewId,
      readonlyType,
      readonlyAction,
      readonlyCondition,
      relatedPrimaryKey,
      pageUrl,
      pageHeight,
    },
  } = record?.toData() || { widgetConfig: {} };

  const [pageViewId, setPageViewId] = useState(relatedViewId);
  const [pageViewUrl, setPageViewUrl] = useState(pageUrl);

  const pageRef = useRef();
  const funcConfig = { personId, person, tenantId };

  useEffect(() => {
    if (defaultViewType === 'VARIABLE') {
      const expressionData = mainStore.getPageCompViewData.get(pageId);
      if (expressionData) {
        if (expressionData?.id) {
          setPageViewId(expressionData.id);
        } else {
          setPageViewUrl(expressionData?.url);
        }
      } else {
        setPageViewId(undefined);
        setPageViewUrl(undefined);
      }
    }
  }, [mainStore.getPageCompViewData?.size]);

  let disabled = readonlyType === 'ALWAYS_NOT_EDIT';
  if (readonlyType === 'CONDITION') {
    if (readonlyAction === ACTION.READONLY && readonlyCondition?.length) {
      disabled = calculateConditions(parentKey, false, false, ds?.current, readonlyCondition, funcConfig);
    }
    if (readonlyAction === ACTION.EDITABLE && readonlyCondition?.length) {
      disabled = !calculateConditions(parentKey, false, false, ds?.current, readonlyCondition, funcConfig);
    }
  }

  if (!pageViewId && !pageViewUrl) {
    return null;
  }

  if ((defaultViewType === 'FIXED_URL' && pageViewUrl) || (!pageViewId && pageViewUrl)) {
    const url = formatUrlParams(pageViewUrl, ds?.current);
    const newUrl = url.startsWith('/') ? `${window.location.origin}/#${url}` : url;
    return (
      <iframe
        title="page-fixed-url"
        src={newUrl}
        style={{
          width: '100%',
          height: `${pageHeight}px` || '150px',
          border: 'none',
          overflow: 'hidden',
        }}
      />
    );
  }

  if (relatedPrimaryKey) {
    const releatedId = ds?.current?.get(relatedPrimaryKey)?.id || ds?.current?.get(relatedPrimaryKey);
    if (!releatedId) return null;

    return (
      <div className={classnames('lc-page-loader-page', className)} {...rest}>
        <PageLoader
          viewId={pageViewId}
          pageRef={pageRef}
          viewData={false}
          mode={disabled ? 'DISABLED' : mode}
          ticketFlag={ticketFlag}
          instanceId={releatedId}
          showHeaderFlag={false}
          pageViewFlag
          disableACL
          variablePage={defaultViewType === 'VARIABLE'}
        />
      </div>
    );
  }

  return (
    <div className={classnames('lc-page-loader-page', className)} {...rest}>
      <PageLoader
        viewId={pageViewId}
        pageRef={pageRef}
        viewData={false}
        mode={disabled ? 'DISABLED' : mode}
        formDataSet={ds}
        ticketFlag={ticketFlag}
        instanceId={instanceId}
        showHeaderFlag={false}
        pageViewFlag
        variablePage={defaultViewType === 'VARIABLE'}
        setParentPageDsFieldMap={handlePageDsFieldMap} // 子视图字段获取钩子
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutPage */
