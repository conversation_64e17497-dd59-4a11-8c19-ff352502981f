import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import Preview from '../../../Preview';

const Index = observer((props) => {
  const { record, dataSet, className, context, hideFlag, ...rest } = props;
  const { buttonDataSet } = context;

  function renderPreview(r) {
    if (hideFlag) {
      return null;
    }
    return (
      <Preview
        key={r.key}
        record={r}
        dataSet={dataSet}
        context={context}
      />
    );
  }

  const map = {
    LEFT: 'flex-start',
    MID: 'center',
    RIGHT: 'flex-end',
  };

  const cls = {
    display: 'flex',
    justifyContent: map[record.get('buttonAreaArrangement')],
    padding: '4px 16px',
  };

  const ButtonArea = () => {
    return (
      <div
        style={{
          ...cls,
        }}
      >
        {buttonDataSet
          .treeData
          .filter(r => r.get('areaId') === record.get('id'))
          .map(renderPreview)}
      </div>
    );
  };

  return <ButtonArea />;
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutButtonArea */
