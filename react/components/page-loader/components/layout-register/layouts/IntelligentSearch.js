import React, { useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import { Modal } from 'choerodon-ui/pro';
import PageLoader from '@/components/page-loader';

export function useOpenView({
  record,
  intl,
  onJumpNewPage,
  history,
  lastViewId,
}) {
  const modalKey = Modal.key();
  const pageRef = useRef();
  let modal;
  /**
     * 打开视图
     */
  return async function openView(view = {}, _dataSet, current) {
    const { viewSize, openType, viewId, viewName } = view;
    const viewModalStyle = { width: Number(viewSize) };

    const handleOk = async () => {
      if (await pageRef.current?.formDataSet?.validate()) {
        await pageRef.current?.formDataSet?.submit();
        _dataSet.query();
        modal = false;
        return true;
      }
      return false;
    };

    const handleClose = () => {
      if (modal && pageRef.current?.formDataSet?.updated.length > 0) {
        return Modal.confirm({
          title: intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' }),
          children: (
            <div>
              <p>{intl.formatMessage({ id: 'lcr.components.desc.close.tip', defaultMessage: '当前页面修改还未保存，确认关闭吗？' })}</p>
            </div>
          ),
        }).then(async (button) => {
          if (button === 'ok') {
            await pageRef.current?.formDataSet?.reset();
            modal = false;
            return true;
          } else {
            return false;
          }
        });
      } else {
        return true;
      }
    };

    if (openType === 'NEW') {
      if (onJumpNewPage) {
        onJumpNewPage({ record: current, viewId });
      } else {
        history.push(
              `/lc/engine/${viewId}/${current.get('id')}${lastViewId ? `/${lastViewId}` : ''}${
                history.location?.search
              }`
        );
      }
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (modal) {
        await modal.close();
      }
      modal = Modal.open({
        title: openType === 'MIDDLE' ? viewName : '',
        children: (
          <PageLoader
            instanceId={current.get('id')}
            viewId={viewId}
            pageRef={pageRef}
            mode="MODIFY"
            openType={openType}
            parentDataSet={_dataSet}
          />
        ),
        key: `${modalKey}-${record.get('id')}`,
        drawer: openType === 'RIGHT',
        style: viewModalStyle,
        destroyOnClose: true,
        onOk: handleOk,
        onCancel: handleClose,
        onClose: handleClose,
        closeOnLocationChange: true,
        okButton: true,
      });
    }
    return true;
  };
}

const Index = observer(({ record, dataSet, className, context }) => {
  const {
    intl,
    history,
    viewId: lastViewId,
    instanceId,
    tenantId,
    viewDataSet,
    onJumpNewPage,
  } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const config = record?.toData() || {};

  const openView = useOpenView({
    record,
    intl,
    onJumpNewPage,
    history,
    lastViewId,
  });

  return (
    <ExternalComponent
      system={{
        scope: 'itsm',
        module: 'IntelligentSearch',
      }}
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      config={config}
      fallback={<span />}
      openView={openView}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutIntelligentSearch */
