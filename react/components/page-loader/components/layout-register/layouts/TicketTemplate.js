import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketTemplate from '@/renderer/ticket-template';

// 工单模版
const TicketTemplateRender = observer((props) => {
  const { dataSet, context, record } = props;
  const businessObjectId = context?.viewDataSet?.current?.get('businessObjectId');

  // 普通视图引入的【服务项视图】中不显示
  return !context?.scItemViewFlag ? (
    <TicketTemplate
      businessObjectId={businessObjectId}
      formDataSet={dataSet}
      context={context}
      cpmRecord={record}
    />
  ) : null;
});

export default withErrorBoundary(TicketTemplateRender, { fallback: <span /> });

/* externalize: LcLayoutTicketTemplate */
