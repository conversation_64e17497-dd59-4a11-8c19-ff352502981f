/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-11-07 11:06:42
 * @Description: 
 */
import React, { useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import OldTicketSLA from '@/renderer/sla';
import NewTicketSLA from '@/renderer/service-agreement';
import { getCurrentTenantIsUseServiceAgreement } from '@/service';

const Index = observer(({ dataSet, context, record }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const viewCode = viewDataSet.current?.get('code');
  const [useNewSla, setNewSla] = useState(false);
  const [hasGetSetting, setHasGetSetting] = useState(false);

  const {
    widgetConfig: {
      relatedPrimaryKey,
      relatedObjectCode,
    },
  } = record?.toData() || { widgetConfig: {} };

  const ticketId = dataSet?.current?.get(relatedPrimaryKey || 'id');
  const businessObjectCode = relatedObjectCode || viewDataSet.current?.get('businessObjectCode');

  useEffect(() => {
    getTenantOpenNewSla();
  }, []);

  async function getTenantOpenNewSla() {
    const res = await getCurrentTenantIsUseServiceAgreement(tenantId);
    try {
      if (res) {
        setNewSla(res);
      }
      setHasGetSetting(true);
    } catch {
      setNewSla(false);
      setHasGetSetting(true);
    }
  }

  const TicketSLA = useNewSla ? NewTicketSLA : OldTicketSLA;
  if (!hasGetSetting) return null;

  // 共享服务项生成的单据不展示sla
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketSLA
      intl={intl}
      tenantId={tenantId}
      ticketId={ticketId?.id || ticketId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketSLA */
