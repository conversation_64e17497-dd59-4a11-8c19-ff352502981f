import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketFileList from '@/renderer/ticket-file-list';

const Index = observer(({ record, dataSet, context }) => {
  const { tenantId, instanceId, viewDataSet, intl } = context;
  const { businessObjectCode, code: viewCode } = viewDataSet.current?.toData();

  return (
    <TicketFileList
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketFileList */
