import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, ExternalComponent } from '@zknow/components';

const Index = observer(({ record, feature, context }) => {
  const { dsManager } = context;
  const tableDataSet = dsManager?.pageRef?.current?.tableDataSet;
  const pageRecord = dsManager?.pageRef?.current?.pageRecord;
  return (
    <ExternalComponent 
      system={{
        scope: 'itsm',
        module: 'BatchRepairSLARenderer',
      }}
      config={
        { 
          icon: record?.get('icon'),
          name: record?.get('name'),
          id: record?.get('id'), 
          color: record?.get('color'), 
        }
      }
      feature={feature}
      tableDataSet={tableDataSet}
      pageRecord={pageRecord}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutBatchRepairSLA */
