import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import qs from 'qs';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet, onCancel, history: { location: { search } } } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');
  const isDefaultExpand = record.get('widgetConfig.isDefaultExpand');
  const { udmTenantId } = qs.parse(search.substring(1));

  return (
    <ExternalComponent
      system={{
        scope: 'itsm',
        module: 'YQReply',
      }}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      onCancel={() => {
        onCancel && onCancel();
      }}
      isDefaultExpand={isDefaultExpand}
      fallback={<span />}
      udmTenantId={udmTenantId}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutComment */
