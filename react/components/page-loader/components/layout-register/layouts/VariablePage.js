import React, { useRef } from 'react';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import { ACTION, calculateConditions } from '@/components/page-loader/utils';
import PageLoader from '../../..';
import './VariablePage.less';

const Index = observer(({ record, className, context, dataSet, ...rest }) => {
  const {
    code,
    name,
    widgetConfig: {
      readonlyType,
      readonlyAction,
      readonlyCondition,
    },
  } = record.toData();
  const { mode, mainStore, personId, person, parentKey, instanceId, tenantId } = context;
  const funcConfig = { personId, person, tenantId };
  const pageRef = useRef();

  if (mode === 'PREVIEW') {
    return (
      <div className="lc-page-loader-variablePage-preview">{name}</div>
    );
  }

  if (!dataSet?.current) {
    return null;
  }

  const viewId = dataSet.current?.get(code);
  // 标识是否新版变量视图（新版视图数据需要自行根据id查询。就是一个独立的低代码视图）
  const viewFlag = dataSet.current?.get(`${code}:_view_flag`);
  if (!viewId) {
    return null;
  }

  let disabled = readonlyType === 'ALWAYS_NOT_EDIT';
  if (readonlyType === 'CONDITION') {
    if (readonlyAction === ACTION.READONLY && readonlyCondition?.length) {
      disabled = calculateConditions(parentKey, false, false, dataSet?.current, readonlyCondition, funcConfig);
    }
    if (readonlyAction === ACTION.EDITABLE && readonlyCondition?.length) {
      disabled = !calculateConditions(parentKey, false, false, dataSet?.current, readonlyCondition, funcConfig);
    }
  }

  return (
    <div className={classnames('lc-page-loader-variablePage', className)} {...rest}>
      <PageLoader
        instanceId={viewFlag ? instanceId : undefined}
        viewId={viewId}
        pageRef={pageRef}
        viewData={false}
        mode={(disabled || viewFlag) ? 'DISABLED' : mode} // 新版变量视图默认不可编辑
        formDataSet={viewFlag ? undefined : dataSet}
        parentKey={viewFlag ? '' : `${code}:_variable`}
        variableFlag={!viewFlag}
        autoFocus={mainStore.getFocus}
        showHeaderFlag={false}
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutVariablePage */
