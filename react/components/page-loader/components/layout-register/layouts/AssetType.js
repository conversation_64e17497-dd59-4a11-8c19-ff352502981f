import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Output, Form, Lov } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';

import './asset.less';

const Index = observer(({ record, context }) => {
  const {
    viewDataSet, dsManager, formDataSet: dataSet, requestItemConfig,
    history: { location: { params } },
  } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));
  const [valName, setValName] = useState('');
  const [viewVariable, setViewVariable] = useState({});
  const [needType, setNeedType] = useState(true);

  useEffect(() => {
    if (formDataSet?.current) {
      const fieldValue = formDataSet.current.get('assetTypeLov');
      formDataSet.addField('assetTypeLov', {
        type: 'object',
        multiple: false,
        lovCode: 'ASSET_CATEGORY_TREE',
        label: record.get('name'),
        treeFlag: 'Y',
        parentIdField: 'parentId',
        idField: 'id',
        transformRequest: (value) => {
          if (requestItemConfig) {
            return JSON.stringify(value);
          }
          return value;
        },
        transformResponse: (value, data) => {
          if (requestItemConfig) {
            try {
              return JSON.parse(value);
            } catch (e) {
              return value;
            }
          }
          return value;
        },
      });
      if (requestItemConfig && fieldValue) {
        try {
          const fieldValueJson = JSON.parse(fieldValue);
          formDataSet.current.set('assetTypeId', fieldValueJson.assetTypeId);
          formDataSet.current.set('assetTypeCode', fieldValueJson.assetTypeCode);
          formDataSet.current.set('assetTypeLov', fieldValueJson);
        } catch (e) {}
      }
      if (params && params.assetInfo) {
        const info = params.assetInfo;
        const lovData = {
          id: info.category_id,
          name: info['category_id:name'],
        };
        formDataSet.current.set('assetTypeId', info.category_id);
        formDataSet.current.set('assetTypeCode', info['category_id:name']);
        formDataSet.current.set('assetTypeLov', lovData);
        setNeedType(false);
      }
    } else if (dataSet?.current) {
      if (dataSet?.current.get('item_id:variable_view_id:_variable')) {
        setValName('item_id:variable_view_id:_variable');
      }
      if (dataSet?.current.get('req_item_id:item_id:variable_view_id:_variable')) {
        setValName('req_item_id:item_id:variable_view_id:_variable');
      }
      if (dataSet?.current.get('request_item_id:item_id:variable_view_id:_variable')) {
        setValName('request_item_id:item_id:variable_view_id:_variable');
      }
      const currentVal = dataSet?.current.get('item_id:variable_view_id:_variable') || dataSet?.current.get('req_item_id:item_id:variable_view_id:_variable') || dataSet?.current.get('request_item_id:item_id:variable_view_id:_variable');
      setViewVariable(currentVal);
      dataSet.addField('assetTypeLov', {
        type: 'object',
        multiple: false,
        lovCode: 'ASSET_CATEGORY_TREE',
        label: record.get('name'),
        treeFlag: 'Y',
        parentIdField: 'parentId',
        idField: 'id',
      });
      dataSet?.current.set('assetTypeLov', currentVal?.assetTypeLov);
    }
  }, [formDataSet?.current, dataSet?.current]);

  function renderCmp() {
    return (
      <Lov name="assetTypeLov" onChange={handleChange} />
    );
  }

  function handleChange(value) {
    if (formDataSet?.current) {
      formDataSet.current.set('assetTypeId', value?.id);
      formDataSet.current.set('assetTypeCode', value?.code);
    } else if (dataSet?.current) {
      const currentVal = { ...viewVariable };
      currentVal.assetTypeId = value.id;
      currentVal.assetTypeCode = value.code;
      currentVal.assetTypeLov = value;
      setViewVariable({ ...currentVal });
      dataSet.current.set(valName, currentVal);
    }
  }

  return needType && (
    <Form
      className="lc-expression-form nopadding"
      labelLayout="horizontal"
      record={formDataSet?.current || dataSet?.current}
      labelWidth={100}
    >
      <Output
        label={record.get('name')}
        renderer={renderCmp}
      />
    </Form>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutAssetType */
