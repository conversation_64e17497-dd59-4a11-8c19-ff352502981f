@import '~choerodon-ui/lib/style/themes/default';

.ticker-reminder-modal-title {
  display: flex;
  align-items: center;
  height: 100%;

  &-icon {
    display: flex;
    align-items: center;
    margin-left: 8px;
    color: rgba(18, 39, 77, .45);
    cursor: default;
  }
}

.lc-table-action-pressTicket {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;

  &-context {
    margin-bottom: 16px;
  }

  &-title {
    font-weight: 500;
    color: #12274d;
    font-size: 20px;
    margin: 16px 0 12px;
  }

  &-button {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 12px;
    cursor: pointer;
    color: @primary-color;
  }
}
