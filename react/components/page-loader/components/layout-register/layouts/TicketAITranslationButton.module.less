.popoverContent {
  padding: 20px;
  width: 306px;
}

.popover {
  padding-top: 0 !important;

  :global {
    .c7n-popover-arrow {
      display: none !important;
    }

    .c7n-popover-inner-content {
      padding: 0 !important;
    }
  }
}

.button {
  background: linear-gradient(135deg, #53ffc6 0%, #439cff 52%, #bb4bff 100%) !important;
  border-radius: 4px;
  border: none !important;
  color: #fff;
  border-radius: 16px !important;
  display: inline-flex;
  padding-left: 1px !important;
  .icon {
    margin-right: 4px;
  }
  > span {
    display: flex;
    align-items: center;
  }
}

.innerContent {
  .title {
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 16px;
    color: #12274d;
    line-height: 24px;
  }

  .select {
    width: 100%;
  }

  .innerButton {
    margin-top: 12px;
    width: 100%;
  }
}

.unavailable {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  font-size: 15px;
  color: rgba(18, 39, 77, 0.65);
  text-align: center;
}

.checkbox {
  margin: 12px 0 4px;
}
