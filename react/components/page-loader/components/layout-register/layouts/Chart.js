import React, { useState, useEffect, useRef } from 'react';
import classNames from 'classnames';
import { getEnv, getAccessToken } from '@zknow/utils';
import { Icon, Empty, withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import './Chart.less';

const Index = inject('HeaderStore')(observer(({ record, className, HeaderStore: { getTenantConfig: { supersetVersion } } }) => {
  const [timestamp, setTimestamp] = useState(0);
  const accessToken = getAccessToken()?.replace('bearer ', '');
  const chartType = record?.get('chartType');
  const reportId = record?.get('widgetConfig.reportId');
  const chartId = record.get('widgetConfig.chartId');
  const chartName = record.get('widgetConfig.chartName');
  const reportName = record?.get('widgetConfig.reportName');
  const refreshFlag = record?.get('widgetConfig.refreshFlag');
  const refreshRate = record?.get('widgetConfig.refreshRate') || 'NONE';
  const position = JSON.parse(record?.get('widgetConfig.reportPosition') || '{}');
  const superSetUrl = getEnv(supersetVersion === 'superset_v3' ? 'SUPERSET_DOMAIN_V3' : 'SUPERSET_DOMAIN') || 'https://report.preprod.yqcloud.com';
  const hadBackslash = superSetUrl.substr(superSetUrl.length - 1, 1) === '/';
  const reportUrl = `${superSetUrl}${hadBackslash ? '' : '/'}superset/dashboard/${reportId}?timestamp=${timestamp}${accessToken ? `&yqToken=${accessToken}` : ''}`;
  const chartFormData = JSON.parse(record?.get('widgetConfig.chartFormData') || '{}');
  const chartAdhocFilters = record?.get('widgetConfig.chartParams') || [];
  chartFormData.adhoc_filters = [
    ...(chartFormData.adhoc_filters || []),
    ...chartAdhocFilters,
  ];
  const chartUrl = `${superSetUrl}${hadBackslash ? '' : '/'}superset/explore/?form_data=${encodeURIComponent(JSON.stringify(chartFormData))}`;

  let timer;

  const iframeRef = useRef();

  useEffect(() => {
    if (refreshRate !== 'NONE') {
      if (timer) {
        clearInterval(timer);
      }
      timer = setInterval(() => {
        setTimestamp(new Date().getTime());
      }, refreshRate * 1000);
    }
    return () => clearInterval(timer);
  }, [refreshRate]);

  const calculateRowHeight = (data, row) => {
    let height = 0;
    row.children.forEach(id => {
      const chart = data[id];
      if (chart.meta.height > height) {
        height = chart.meta.height;
      }
    });
    return height * 8 + 120;
  };

  const calculateFrameHeight = (data) => {
    const rows = Object.keys(data).filter(key => key.startsWith('ROW'));

    const rowHeight = rows.map(row => calculateRowHeight(data, data[row]));
    let h;
    try {
      h = rowHeight.reduce((a, b) => a + b);
    } catch {
      h = 0;
    }
    return h;
  };

  useEffect(() => {
    const iframe = iframeRef.current;
    if (iframe) {
      const iframeHeight = calculateFrameHeight(position) || 500;
      iframe.height = iframeHeight;
      iframe.style.height = `calc(100vh - ${iframe.getClientRects()[0]?.top + 14}px)`;
    }
  }, [iframeRef.current]);

  return (
    <div className={classNames('lc-page-loader-chart', className)}>
      {refreshFlag
        ? (
          <div className="lc-page-loader-chart-header">
            <span className="lc-page-loader-chart-name" />
            <span
              className="lc-page-loader-chart-refresh"
            >
              <Icon type="Refresh" onClick={() => setTimestamp(new Date().getTime())} />
            </span>
          </div>
        ) : null}
      {(chartType === 'CHART' ? chartId : reportId)
        ? <iframe src={chartType === 'CHART' ? chartUrl : reportUrl} title={chartType === 'CHART' ? chartName : reportName} className="lc-page-loader-chart-iframe" ref={iframeRef} />
        : <Empty type="nodata" />}
    </div>
  );
}));

export default withErrorBoundary(Index);

/* externalize: LcLayoutChart */
