.lc-page-loader-tab {
  height: 100%;

  &-icon {
    margin-right: 0.05rem;
  }

  &-name {
    vertical-align: middle;

    &-wrapper {
      .yqcloud-icon-park-wrapper {
        display: inline-block !important;
      }
    }
  }

  .c7n-tabs-card {
    padding: 0.16rem;
  }

  & > .lc-page-loader-tab-right {
    margin-top: 0 !important;
    padding-top: 0;
  }

  &-right {
    height: 100%;
    min-height: calc(100vh - 1.4rem);

    & > .c7n-tabs-bar {
      border-left: 0.01rem solid rgba(203, 210, 220, 0.5) !important;
      border-right: 0.01rem solid rgba(203, 210, 220, 0.5) !important;

      .c7n-tabs-nav-container {
        margin-right: 0 !important;
        width: 0.60rem;

        .c7n-tabs-nav-wrap {
          margin-right: 0 !important;
          padding-top: 0.11rem;
          background: #fff;

          .c7n-tabs-nav {
            margin-left: 0;

            .c7n-tabs-ink-bar {
              display: none;
            }

            .c7n-tabs-tab {
              margin: 0 auto .08rem;
              padding: 0;
              border-radius: 0.04rem;
              width: 0.52rem;
              height: 0.52rem;
              border: none;
              background-color: unset;
            }
          }
        }
      }
    }

    &-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      // width: 0.42rem;
      // height: 0.45rem;
      width: 0.52rem;
      height: 0.52rem;
      margin: auto;
      // margin-top: 0.06rem;
      border-radius: 0.04rem;
      transition: background-color 0.2s ease;
      padding-top: 4px;

      &:hover {
        background-color: rgba(203, 210, 220, 0.3);
      }
    }

    &-icon {
      display: flex;
      width: 0.16rem;
      height: 0.16rem;
      margin-top: 0.07rem;
      margin-bottom: 0.03rem;
      color: #12274d;
    }

    &-name {
      font-size: 0.11rem;
      font-weight: 400;
      color: #12274d;
      line-height: 0.16rem;
      overflow: hidden;
      // width: 0.3rem;
      width: 52px;
      text-align: center;
      letter-spacing: 0.008rem;
      padding-bottom: 0.04rem;
      user-select: none;
      transform: scale(0.875);
    }

    .c7n-tabs-content {
      border-left-color: transparent !important;
    }

    // // tooltip 悬浮图标不变色
    .lc-page-loader-tab-right-wrapper.c7n-tooltip-open > .lc-page-loader-tab-right-icon {
      color: #12274d;
    }

    // tab 图标激活样式
    .c7n-tabs-tab-active {
      .lc-page-loader-tab-right-wrapper {
        background: @minor-color !important;

        .lc-page-loader-tab-right-icon,
        .lc-page-loader-tab-right-name {
          color: @primary-color;
        }
      }
    }
  }
}
