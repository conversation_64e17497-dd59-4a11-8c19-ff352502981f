import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketHeader from '@/renderer/ticket-header';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, mode, tabMenuDataSet, udmFlag, onJumpNewPage } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  return (
    <TicketHeader
      intl={intl}
      mode={mode}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      tabMenuDataSet={tabMenuDataSet}
      udmFlag={udmFlag}
      onJumpNewPage={onJumpNewPage}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketHeader */
