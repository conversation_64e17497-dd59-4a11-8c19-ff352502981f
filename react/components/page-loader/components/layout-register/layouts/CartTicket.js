import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, ExternalComponent } from '@zknow/components';

const Index = observer((props) => {
  const { context, record } = props;
  const widgetConfig = record?.get('widgetConfig');
  return (
    <ExternalComponent 
      {...props}
      system={{
        scope: 'ticket',
        module: 'CartRelatedTicket',
      }}
      instanceId={context?.instanceId}
      widgetConfig={widgetConfig}
      tabMenuDataSet={context?.tabMenuDataSet}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutCartTicket */
