import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, ExternalComponent } from '@zknow/components';

const Index = observer(({ record, feature, context }) => {
  const { viewDataSet } = context;
  return (
    <ExternalComponent 
      system={{
        scope: 'itsm',
        module: 'RepairSLAHistoryRenderer',
      }}
      config={
        { 
          icon: record?.get('icon'),
          name: record?.get('name'),
          id: record?.get('id'), 
          color: record?.get('color'), 
        }
      }
      viewDataSet={viewDataSet}
      feature={feature}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutRepairSLAHistory */
