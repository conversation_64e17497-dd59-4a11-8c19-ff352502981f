@import '~choerodon-ui/lib/style/themes/default';

.card {
  box-shadow: 0 1px 6px 0 rgba(31, 35, 41, 0.04);
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.65);
  margin: 0 16px 16px 16px;

  &:hover {
    :global {
      ._section-header-icon {
        display: block;
      }
    }
  }
}

.readOnly {
  :global {
    .@{c7n-pro-prefix}-output,
    .@{c7n-pro-prefix}-field-row .@{c7n-pro-prefix}-field-label,
    .@{c7n-pro-prefix}-field-label label > span,
    .@{c7n-pro-prefix}-field-label-vertical {
      line-height: 22px !important;
    }

    .@{c7n-pro-prefix}-field-label {
      padding: 1px 0 1px 16px;
    }

    .@{c7n-pro-prefix}-field-label label,
    .@{c7n-pro-prefix}-field-wrapper {
      padding: 0 0 0 0.16rem !important;
    }

    .lc-page-loader-upload-file {
      padding: 0;
      height: 22px;
      margin-bottom: 0;
      top: 0;
      line-height: 22px;
    }

    // TODO: 「这只是一个临时解决」
    //    有个不规范的配置，就是在卡片模式里面又嵌套了 section
    //    而卡片模式下嵌套的子 section 里面不再是 output 的，而是可以填写的表单项了（应该是 bug）
    //    而样式影响，导致这个模式下的可编辑表单项错乱
    //    不好改，「学军」要的急（晶盛租户）
    .lc-page-loader-section-layout:not(.lc-page-loader-section-display_card-content) {
      .@{c7n-pro-prefix}-output,
      .@{c7n-pro-prefix}-field-row .@{c7n-pro-prefix}-field-label,
      .@{c7n-pro-prefix}-field-label label > span,
      .@{c7n-pro-prefix}-field-label-vertical {
        line-height: 32px !important;
      }

      .@{c7n-pro-prefix}-field-label label,
      .@{c7n-pro-prefix}-field-wrapper {
        padding: 0.06rem 0 0.04rem 0.16rem !important;
      }
    }
  }
}

.cardDefault {
  padding: 16px;
}
