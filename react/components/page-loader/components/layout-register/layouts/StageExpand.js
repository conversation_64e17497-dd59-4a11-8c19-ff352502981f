import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { SelectBox, Tooltip } from 'choerodon-ui/pro';
import { withErrorBoundary, ExternalComponent, Icon } from '@zknow/components';
import RequestProcess from '@/renderer/request-process';
import FlowChart from '@/renderer/flow-chart';

import styles from './StageExpand.module.less';

const { Option } = SelectBox;

const Index = observer(({ record, dataSet, context }) => {
  const { instanceId: id, viewDataSet, onCancel, tenantId, intl } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  const idField = record.get('widgetConfig.relatedPrimaryKey');
  const collapseFlag = record.get('widgetConfig.collapseFlag');
  const flowDisplayMode = record.get('widgetConfig.flowDisplayMode') || ['OVERVIEW'];
  const predictionFlag = record.get('widgetConfig.predictionFlag');
  const flowDisplayModeDefault = record.get('widgetConfig.flowDisplayModeDefault') || 'OVERVIEW';
  const flowPredictionDefault = record.get('widgetConfig.flowPredictionDefault');

  const multipleFlag = Array.isArray(flowDisplayMode?.slice()) && flowDisplayMode.length > 1;

  const [mode, setMode] = useState(multipleFlag
    ? flowDisplayModeDefault
    : (Array.isArray(flowDisplayMode?.slice()) ? flowDisplayMode[0] : flowDisplayMode));

  const instanceId = dataSet?.current?.get(idField)?.id || dataSet?.current?.get(idField) || id;
  if (!instanceId || !dataSet?.current) {
    return null;
  }

  function renderFlow() {
    if (mode === 'CHART') {
      return (
        <div style={{ height: '100%' }}>
          <FlowChart
            instanceId={instanceId}
            viewDataSet={viewDataSet}
            formDataSet={dataSet}
            predictionFlag={predictionFlag}
            flowPredictionDefault={flowPredictionDefault}
          />
        </div>
      );
    } else if (
      ['SC_TASK', 'SC_REQ_ITEM'].includes(businessObjectCode)
      || (
        businessObjectCode === 'WF_TASK_INSTANCE'
        && dataSet?.current?.get('req_item_id')
      )
    ) {
      return <RequestProcess instanceId={instanceId} formDataSet={dataSet} collapseFlag={collapseFlag} />;
    } else {
      return (
        <ExternalComponent
          system={{
            scope: 'itsm',
            module: 'StageExpandRenderer',
          }}
          tenantId={tenantId}
          ticketId={instanceId}
          ticketType={businessObjectCode}
          viewCode={viewCode}
          formDataSet={dataSet}
          viewDataSet={viewDataSet}
          viewRecord={record}
          onCancel={() => {
            onCancel && onCancel();
          }}
          isDefaultExpand={collapseFlag}
          fallback={<span />}
          hiddenTitle
        />
      );
    }
  }

  return (
    <div>
      <div className={styles.flex}>
        <div className={styles.title}>{record.get('name')}</div>
        {multipleFlag
          ? (
            <SelectBox className={styles.switch} mode="button" value={mode} onChange={setMode}>
              <Option value="CHART">
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.flow.chart', defaultMessage: '流程图' })}>
                  <Icon type="BranchOne" className={styles.icon} />
                </Tooltip>
              </Option>
              <Option value="OVERVIEW">
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.flow.overview', defaultMessage: '流程一览' })}>
                  <Icon type="ListMiddle" className={styles.icon} />
                </Tooltip>
              </Option>
            </SelectBox>
          ) : null}
      </div>
      {renderFlow()}
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutStageExpand */
