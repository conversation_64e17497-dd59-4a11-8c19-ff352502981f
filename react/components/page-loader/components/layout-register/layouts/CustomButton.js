import React, { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import { Button } from 'choerodon-ui/pro';
import set from 'lodash/set';
import { calculateHide1 } from '../../../utils';
import { generateDynamicRecord } from '@/service';
import { lang } from './CustomButtonLang';

const Index = observer((props) => {
  const { record, dataSet, feature, tableLineRecord, context, lineButton, tableRecord } = props;
  const {
    tenantId, instanceId, viewDataSet, intl, dsManager, modal, pageContext,
    person, personId, parentKey, mode, tabMenuDataSet, parentDataSet,
  } = context;
  const formDataSet = viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'));
  const scope = record.get('scope');
  const module = record.get('module');
  const customConfig = record?.get('widgetConfig.customConfig') || [];
  const config = record?.toData() || {};
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const currentTableBusinessObjectId = tableRecord?.get('widgetConfig.modelId');
  set(config, 'widgetConfig.customConfig', customConfig);

  const [isHide, setIsHide] = useState();

  useEffect(() => {
    (async () => {
      const funcConfig = { person, personId, tenantId };
      const hideFlag = await calculateHide1(record, lineButton ? tableLineRecord : formDataSet?.current, mode, parentKey, funcConfig, true);
      setIsHide(hideFlag);
    })();
  }, [formDataSet?.current?.toData(), tableLineRecord?.toData()]);

  if (isHide) {
    return null;
  }

  async function generateRecord() {
    const recordId = formDataSet?.current?.get('id');
    const _tls = lang?.[scope]?.[module];
    if (!(_tls && recordId)) return;

    const postData = {
      recordId,
      customAction: _tls?.customAction?.zh_CN,
      customTitle: _tls?.customTitle?.zh_CN,
      customerContent: _tls?.customerContent?.zh_CN,
      _tls,
    };

    await generateDynamicRecord({
      tenantId,
      businessObjectCode,
      postData,
    });
  }

  return (
    <ExternalComponent
      system={{ scope, module }}
      intl={intl}
      tenantId={tenantId}
      pageContext={pageContext} // 当前页面所有视图数据
      instanceId={instanceId} // 当前记录的id
      formDataSet={formDataSet} // 当前表单ds
      parentDataSet={parentDataSet}
      tableDataSet={dataSet} // 当前列表ds
      tableLineRecord={tableLineRecord}
      viewDataSet={viewDataSet} // 当前视图ds
      config={config} // 当前组件的配置
      modal={modal} // 弹窗实例
      feature={feature} // table-action 中虽然定义是按钮，但是样式需要表现为菜单项
      tabMenuDataSet={tabMenuDataSet} // 单据自己的导航tab数据
      generateRecord={generateRecord}
      viewRecord={record}
      currentTableBusinessObjectId={currentTableBusinessObjectId} // 如果表格在详情视图内，则需要获取关联表格的业务对象
      fallback={
        <Button
          disabled
          funcType="raised"
          color="secondary"
          style={{
            border: 'none',
            display: feature === 'table-action' ? 'block' : 'inline-block',
            marginBottom: 6,
            padding: '0!important',
          }}
        >{record.get('name') || ''}</Button>
      }
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutCustomButton */
