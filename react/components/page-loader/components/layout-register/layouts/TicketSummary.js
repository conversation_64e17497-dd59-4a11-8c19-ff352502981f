import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketSummary from '@/renderer/ticket-summary';

// 工单总结
const TicketSummaryRender = observer(({ record, dataSet, context, sectionDisplayMode }) => {
  const { tenantId, instanceId, viewDataSet, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  return (
    <TicketSummary
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      sectionDisplayMode={sectionDisplayMode}
      udmFlag={udmFlag}
    />
  );
});

export default withErrorBoundary(TicketSummaryRender, { fallback: <span /> });

/* externalize: LcLayoutTicketSummary */
