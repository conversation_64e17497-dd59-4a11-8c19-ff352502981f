import React from 'react';
import { observer } from 'mobx-react-lite';
import { Dropdown, Menu } from 'choerodon-ui/pro';
import { Button, withErrorBoundary } from '@zknow/components';
import Preview from '@/components/page-loader/Preview';

const Index = observer(({ record, dataSet, context }) => {
  const name = record.get('name');
  const id = record.get('id');

  function renderMenu() {
    return (
      <Menu onClick={(e) => { e.stopPropagation(); }} style={{ minWidth: 100 }}>
        {record.children?.map(subRecord => {
          return (
            <Preview
              key={subRecord.get('id')}
              record={subRecord}
              dataSet={dataSet}
              context={context}
              buttonMode="menu"
              instanceId={context.instanceId}
            />
          );
        })}
      </Menu>
    );
  }

  return (
    <Dropdown overlay={renderMenu()} trigger={['click', 'focus']}>
      <Button
        icon="Down"
        id={id}
        key={id}
      >
        {name}
      </Button>
    </Dropdown>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutButtonGroup */
