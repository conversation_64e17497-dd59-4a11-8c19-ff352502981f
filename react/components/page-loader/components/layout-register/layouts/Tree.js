import React, { useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { withErrorBoundary } from '@zknow/components';
import { Tree } from 'choerodon-ui/pro';

const TreeNode = ({ record }) => {
  return (
    <span className="lc-page-loader-treeNode">
      <span className="item-text">
        {record.get('name')}
      </span>
    </span>
  );
};

const Index = observer(({ record, className, context, ...rest }) => {
  const { dsManager } = context;
  const ds = dsManager.get(record.get('id'));

  const nodeRenderer = useCallback(({ record: nodeRecord }) => {
    return (
      <TreeNode record={nodeRecord} />
    );
  }, []);

  const handleNode = useCallback(({ record: nodeRecord }) => ({
    isLeaf: !nodeRecord.get('relationObjectId'),
  }), []);

  return (
    <div className={classnames('lc-page-loader-tree', className)} {...rest}>
      <Tree
        dataSet={ds}
        renderer={nodeRenderer}
        onTreeNode={handleNode}
        async
        className="lc-page-loader-tree-content"
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTree */
