/*
 * @Author: xia<PERSON>ya
 * @Date: 2022-04-19 14:52:23
 * @Description: 
 */
import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import ThirdPartyApplication from '@/renderer/third-party-application';

const Index = observer(({ record, dataSet, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  // 共享服务项生成的单据不展示关联猪齿鱼
  if (udmFlag) {
    return <div />;
  }

  return (
    <ThirdPartyApplication
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutThirdPartyApplication */
