import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import UIAction from '@/components/ui-action';

const Index = observer((props) => {
  const { record, context } = props;
  const displayMethod = record.get('displayMethod');
  const {
    instanceId, viewDataSet, dsManager, parentDataSet,
    parentFormDataSet, tabMenuDataSet, modal, onJumpNewPage, scPageRef,
  } = context;
  const id = viewDataSet?.current?.get('id');
  const code = viewDataSet?.current?.get('code');
  const formDataSet = dsManager?.get(id);
  const pageRef = dsManager?.pageRef;

  return (
    <UIAction
      formDataSet={formDataSet}
      viewCode={code}
      pageRef={pageRef}
      formConfigRecord={viewDataSet?.current}
      instanceId={instanceId}
      mode={displayMethod === 'DROP_DOWN' ? 'menu' : 'tiled'}
      parentDataSet={parentDataSet}
      parentFormDataSet={parentFormDataSet}
      tabMenuDataSet={tabMenuDataSet}
      modal={modal}
      onJumpNewPage={onJumpNewPage}
      scPageRef={scPageRef}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutButtonAction */
