import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import QualityInspectionScore from '@/renderer/quality-inspection-score';

const Index = observer(({ record, dataSet, className, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, mode } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  return (
    <QualityInspectionScore
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      viewRecord={record}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      mode={mode}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutQualityInspectionScore */
