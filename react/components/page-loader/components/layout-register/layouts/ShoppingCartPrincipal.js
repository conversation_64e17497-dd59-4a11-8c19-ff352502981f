import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import ShoppingCartPrincipal from '@/renderer/shopping-cart-principal';

const Index = observer((props) => {
  const { dataSet, record, context, labelWidth } = props;
  const { intl, pageRef, tenantId } = context;

  return (
    <ShoppingCartPrincipal 
      formDataSet={dataSet}
      widgetConfig={record?.get('widgetConfig')}
      name={record?.get('name')}
      intl={intl}
      pageRef={pageRef}
      tenantId={tenantId}
      labelWidth={labelWidth}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutShoppingCartPrincipal */
