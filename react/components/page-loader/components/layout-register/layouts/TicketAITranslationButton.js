import React, { useCallback, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import {
  withErrorBoundary,
  Button,
  TRANSLATE_FIELDS,
  Empty,
  Icon,
} from '@zknow/components';
import { message, Select, Skeleton, CheckBox } from 'choerodon-ui/pro';
import { Popover } from 'choerodon-ui';
import styles from './TicketAITranslationButton.module.less';
import { getServiceSetting } from '../../../../../service';
import useTranslate from '../../../../../hooks/useTranslate';
import { isInIframe } from '@/utils';

const { Option } = Select;
const TICKET_TRANSLATION_KEY = 'TICKET_TRANSLATION_KEY';

const PopoverContent = ({ translateSetting, intl, onClick }) => {
  const [targetLang, setTargetLang] = useState('');
  const [localCache, setLocalCache] = useState('');

  useEffect(() => {
    // 检查是否有本地存储
    const cache = localStorage.getItem(TICKET_TRANSLATION_KEY);
    const languages = translateSetting?.targetLanguageList || [];
    if (cache) {
      if (languages.includes(cache)) {
        setLocalCache(cache);
        setTargetLang(cache);
      } else {
        localStorage.removeItem(TICKET_TRANSLATION_KEY);
      }
    } else {
      setTargetLang(languages[0]);
    }
  }, [translateSetting]);

  const langChange = (value) => {
    setTargetLang(value);
    if (localCache) {
      // 有勾选始终翻译为才会记忆
      localStorage.setItem(TICKET_TRANSLATION_KEY, value);
      setLocalCache(value);
    }
  };

  const handleClick = useCallback(() => {
    if (!targetLang) {
      message.warn(intl.formatMessage({ id: 'lcr.components.desc.translate.lang.empty', defaultMessage: '目标语言不能为空' }));
      return false;
    }
    onClick(targetLang);
  }, [targetLang]);

  const getLanguagesText = (lang) => {
    const languages = translateSetting?.languageValueList || [];
    const langObj = languages.find(({ code }) => code === lang);
    return langObj?.value || lang;
  };

  return (
    <div className={styles.innerContent}>
      <div className={styles.title}>{intl.formatMessage({ id: 'lcr.components.desc.translate.to', defaultMessage: '翻译为' })}</div>
      <Select
        clearButton={false}
        className={styles.select}
        value={targetLang}
        onChange={langChange}
        searchable={false}
        trigger="hover"
      >
        {[...new Set((translateSetting?.targetLanguageList || []))].map((lang) => (
          <Option
            value={lang}
            key={lang}
          >{getLanguagesText(lang)}</Option>
        ))}
      </Select>
      <div className={styles.checkbox}>
        <CheckBox
          disabled={!localCache && !targetLang}
          checked={localCache}
          onChange={(value) => {
            if (value) {
              localStorage.setItem(TICKET_TRANSLATION_KEY, targetLang);
              setLocalCache(targetLang);
            } else {
              localStorage.removeItem(TICKET_TRANSLATION_KEY);
              setLocalCache('');
            }
          }}
        >{intl.formatMessage({ id: 'lcr.components.desc.translate.always', defaultMessage: '始终翻译为' })} {getLanguagesText(targetLang)}</CheckBox>
      </div>
      <Button
        className={styles.innerButton}
        funcType="raised"
        color="primary"
        onClick={handleClick}
      >{intl.formatMessage({ id: 'zknow.common.button.translate', defaultMessage: '翻译' })}</Button>
    </div>
  );
};

const TicketAITranslationButton = observer((props) => {
  const { record, context } = props;
  const {
    intl,
    tenantId,
    AppState,
    HeaderStore: {
      getTenantConfig: { gptTenantFlag },
    },
    pageRef,
    instanceId,
    mainStore,
  } = context;
  const name = record?.get('name');
  const id = record?.get('id');
  const businessObjectId = pageRef?.current?.pageData?.businessObjectId || pageRef?.current?.pageRecord?.get('businessObjectId');
  const ticketId = instanceId || pageRef?.current?.formDataSet?.current?.get('id');
  const [showPopover, setShowPopover] = React.useState(false);
  const [translateSetting, setTranslateSetting] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [enableFlag, setEnableFlag] = React.useState(false);

  const handleClick = async () => {
    setLoading(true);
    if (businessObjectId) {
      let ticketTranslationSetting = null;
      const cache = await AppState.customConfig?.[businessObjectId];
      if (cache && !cache.failed) {
        setTranslateSetting(cache.ticketTranslationSettingVO);
        setEnableFlag(cache.ticketTranslationFlag);
        setLoading(false);
        ticketTranslationSetting = cache;
      } else {
        const res = await getServiceSetting(tenantId, businessObjectId);
        if (res && !res.failed) {
          AppState.setCustomConfig(businessObjectId, res);
          setTranslateSetting(res.ticketTranslationSettingVO);
          setEnableFlag(res.ticketTranslationFlag);
          ticketTranslationSetting = res.ticketTranslationSettingVO;
        }
        setLoading(false);
      }
      if (ticketTranslationSetting && pageRef?.current?.formDataSet) {
        const fields = (ticketTranslationSetting.ticketTranslationSettingVO?.fields || []).map(({ code }) => code);
        pageRef.current.formDataSet.setState(TRANSLATE_FIELDS, fields);
      }
    }
  };

  const handleTranslate = useTranslate({
    intl,
    mainStore,
    tenantId,
    formDataSet: pageRef?.current?.formDataSet,
    setShowPopover,
    businessObjectId,
    ticketId,
  });

  const getPopoverContent = () => (
    <div className={styles.popoverContent}>
      <Skeleton loading={loading} active>
        {enableFlag ? (
          <PopoverContent
            translateSetting={translateSetting}
            enableFlag={enableFlag}
            intl={intl}
            onClick={handleTranslate}
          />
        ) : (
          <div className={styles.unavailable}>
            <Empty
              description={false}
              style={{ padding: 0, paddingTop: 12, marginBottom: 6 }}
              innerStyle={{ width: 80, height: 80 }}
              type="permission"
            />
            {intl.formatMessage({ id: 'lcr.components.desc.translate.unavailable', defaultMessage: '工单翻译功能未开启，请您联系租户管理员开启AI翻译功能' })}
          </div>
        )}
      </Skeleton>
    </div>
  );

  const handleVisibleChange = (visible) => {
    setShowPopover(visible);
  };

  return !(isInIframe()) && gptTenantFlag ? (
    <Popover
      key={id}
      content={getPopoverContent()}
      placement="bottomRight"
      arrowPointAtCenter={false}
      trigger="click"
      visible={showPopover}
      onVisibleChange={handleVisibleChange}
      getPopupContainer={el => el}
      overlayClassName={styles.popover}
    >
      <Button
        className={styles.button}
        onClick={handleClick}
        funcType="raised"
        color="primary"
      >
        <Icon className={styles.icon} type="icon-yan-copilot-icon" size={28} />
        {name}
      </Button>
    </Popover>
  ) : null;
});

export default withErrorBoundary(TicketAITranslationButton, { fallback: <span /> });

/* externalize: LcLayoutTicketAITranslationButton */
