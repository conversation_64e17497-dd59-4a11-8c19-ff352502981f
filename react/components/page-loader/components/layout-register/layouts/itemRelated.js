import React, { useEffect, useState, useRef, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { Output, Form, Lov, message, Modal, DataSet } from 'choerodon-ui/pro';
import { Icon, ExternalComponent, withErrorBoundary } from '@zknow/components';
import axios from 'axios';
import { openPageByUrl } from '@/utils';

import './itemRelated.less';

const modalKey = Modal.key();

const Index = observer((props) => {
  const { record, context } = props;
  const { tenantId, instanceId: originInstanceId, viewDataSet, formDataSet, intl, history, history: { location: { search } }, requestItemConfig } = context;
  const instanceId = originInstanceId || requestItemConfig?.instanceId || formDataSet?.current?.get('id') || '0';
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const lovRef = useRef(null);
  const [tableViewId, setTableViewId] = useState('');
  const [detailViewId, setDetailViewId] = useState('');

  const url = `/cmdb/v1/${tenantId}/business-rels`;
  const itemDataSet = useMemo(() => new DataSet({
    autoQuery: true,
    autoCreate: false,
    selection: false,
    autoLocateFirst: true,
    fields: [
      {
        name: 'itemRelated',
        type: 'object',
        lovCode: 'CMDB_CI_ITEM',
        multiple: false,
        label: record.get('name'),
      },
    ],
    transport: {
      read: () => ({
        url: `${url}/item/${instanceId}?businessType=${businessObjectCode}`,
        method: 'get',
      }),
      create: ({ data: [data] }) => ({
        url,
        method: 'post',
        data,
      }),
      destroy: ({ data: [data] }) => ({
        url: `${url}/${data.id}`,
        method: 'delete',
      }),
    },
    events: {
      update: ({ name, value, record: r, dataSet }) => {
        if (name === 'itemRelated') {
          r.set({
            ciId: value.id,
            businessId: instanceId,
            businessType: businessObjectCode,
          });
          dataSet.submit();
        }
      },
    },
  }), []);

  // 加载视图id，用于跳转
  useEffect(() => {
    (async () => {
      try {
        const res = await axios.get(`/lc/v1/${tenantId}/views/form/code/T_CMDB_CI_ITEM_DETAIL`);
        if (res?.failed) {
          message.error(res.message);
        } else {
          setDetailViewId(res.id);
        }
      } catch (e) {
        throw new Error(e);
      }
    })();
    (async () => {
      try {
        const res = await axios.get(`/lc/v1/${tenantId}/views/form/code/T_CMDB_CI_ITEM_TABLE`);
        if (res?.failed) {
          message.error(res.message);
        } else {
          setTableViewId(res.id);
        }
      } catch (e) {
        throw new Error(e);
      }
    })();
  }, []);

  function handleClickAdd() {
    lovRef?.current?.openModal();
  }

  async function handleClickDelete() {
    if (itemDataSet?.current) {
      await itemDataSet.delete(itemDataSet?.current, false);
    }
  }

  function handleClickName(current) {
    const id = current?.get('ciId');
    if (!id) return null;
    const viewType = record?.get('widgetConfig.detailLinkViewType');
    if (viewType === 'NEW') {
      history.push({
        pathname: `/lc/engine/${detailViewId}/${current.get('ciId')}/${tableViewId}`,
        search: `${search}`,
      });
    } else if (viewType === 'NEWTAB') {
      openPageByUrl(`/lc/engine/${detailViewId}/${current.get('ciId')}/${tableViewId}`);
    } else {
      Modal.open({
        key: modalKey,
        title: intl.formatMessage({ id: 'lcr.renderer.configurationItem.details' }),
        className: 'lc-asset-info-modal',
        children: (
          <ExternalComponent
            system={{
              scope: 'lcr',
              module: 'PageLoader',
            }}
            instanceId={id}
            viewId={detailViewId}
            showHeaderFlag={false}
          />
        ),
        drawer: record?.get('widgetConfig.detailLinkViewType') === 'RIGHT',
        style: { width: record?.get('widgetConfig.detailLinkViewSize') },
        destroyOnClose: true,
        footer: () => null,
      });
    }
  }

  function renderItemRelated({ record: current }) {
    const name = current?.get('ciName');

    const addClassName = classNames({
      'itemRelated-wrapper-icon': true,
      'itemRelated-wrapper-icon-add-novalue': !name,
      'itemRelated-wrapper-icon-add-hasvalue': name,
    });
    const deleteClassName = classNames({
      'itemRelated-wrapper-icon': true,
      'itemRelated-wrapper-icon-delete-novalue': !name,
      'itemRelated-wrapper-icon-delete-hasvalue': name,
    });
    return (
      <div className="itemRelated-wrapper">
        <Lov ref={lovRef} name="itemRelated" className="lov" />
        <div className="itemRelated-wrapper-cooper">
          {name && <span className="item-name" onClick={() => handleClickName(current)}>{name}</span>}
          <Icon
            className={deleteClassName}
            type="delete"
            onClick={handleClickDelete}
          />
        </div>
        <div className="itemRelated-wrapper-add">
          <Icon
            className={addClassName}
            type="add-one"
            onClick={handleClickAdd}
          />
        </div>
      </div>
    );
  }

  if (instanceId && businessObjectCode) {
    return (
      <Form
        className="lc-itemRelated-form"
        dataSet={itemDataSet}
        labelWidth={100}
      >
        <Output
          label={record.get('name')}
          renderer={renderItemRelated}
        />
      </Form>
    );
  } else {
    return null;
  }
});

export default withErrorBoundary(inject('AppState')(
  formatterCollections({
    code: 'lcr.renderer',
  })(Index)
));

/* externalize: LcLayoutItemRelated */
