import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import DuplicateProblemAnalysis from '../../../../duplicate-problem-analysis';

const Index = observer(({ record, context }) => {
  return (
    <DuplicateProblemAnalysis record={record} context={context} />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutDuplicateProblemAnalysis */
