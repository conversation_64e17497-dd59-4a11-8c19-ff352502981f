import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketDiagnosis from '@/renderer/ticket-diagnosis';

// 工单智能诊断
const TicketDiagnosisRender = observer((props) => {
  const { record, dataSet, context, sectionDisplayMode, currentTabRef } = props;
  const { tenantId, instanceId, viewDataSet, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  // const viewCode = viewDataSet.current?.get('code');

  return (
    <TicketDiagnosis
      tenantId={tenantId}
      ticketId={instanceId}
      businessObjectCode={businessObjectCode}
      // viewCode={viewCode}
      formDataSet={dataSet}
      // viewDataSet={viewDataSet}
      // viewRecord={record}
      // sectionDisplayMode={sectionDisplayMode}
      udmFlag={udmFlag}
      context={context}
      currentTabRef={currentTabRef}
    />
  );
});

export default withErrorBoundary(TicketDiagnosisRender, { fallback: <span /> });

/* externalize: LcLayoutTicketDiagnosis */
