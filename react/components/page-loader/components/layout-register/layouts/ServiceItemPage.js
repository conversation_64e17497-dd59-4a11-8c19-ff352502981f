import React, { useRef, useEffect } from 'react';
import classnames from 'classnames';
import { observer } from 'mobx-react-lite';
import { useRequest } from 'ahooks';
import { withErrorBoundary } from '@zknow/components';
import { calculateCondition } from '../../../utils';
import PageLoader from '../../..';
import { getRequestContent } from '@/service';
import './VariablePage.less';

const ID_FIELD_TASK = 'req_item_id'; // 任务实例表的请求项id字段
const ID_FIELD_REQUEST = 'request_item_id'; // 请求任务表的请求项id字段
const FILTER_BOS = ['SC_TASK', 'SC_REQ_ITEM', 'WF_TASK_INSTANCE']; // 其他业务对象展示为变量视图

const Index = observer(({ record, className, context, dataSet, ...rest }) => {
  const {
    name,
    editType,
    editAction,
    editCondition,
    widgetConfig: {
      pageType,
    },
  } = record.toData();
  const { mode, scPageRef, instanceId, tenantId, parentKey, person, personId, viewDataSet } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const pageRef = useRef();

  function calculateEditable() {
    if (mode === 'PREVIEW' || mode === 'DISABLED') return false;
    let disabled = editType === 'ALWAYS_NOT_EDIT';
    if (editType === 'CONDITION' && dataSet?.current) {
      const funcConfig = { person, personId, tenantId };
      const flag = calculateCondition(parentKey, editCondition, dataSet?.current, funcConfig);
      disabled = editAction === 'READONLY' ? flag : !flag;
    }
    return !disabled;
  }

  const editable = calculateEditable();

  // 非请求项和请求任务
  const requestItemFlag = FILTER_BOS.includes(businessObjectCode);
  
  const id = requestItemFlag
    ? dataSet?.current?.get(ID_FIELD_TASK) || dataSet?.current?.get(ID_FIELD_REQUEST) || dataSet?.current?.get('id') 
    : null;

  const { data: content, refresh } = useRequest(
    id ? () => getRequestContent({ tenantId, id }) : null, 
    { refreshDeps: [id] }
  );
  useEffect(() => {
    if (
      // 服务请求类的服务项视图 
      ((content?.variableViewId || content?.detailVariableViewId) && id) 
      // 非服务请求类的服务项视图
      || !requestItemFlag
    ) {
      const key = requestItemFlag ? id : instanceId;
      if (pageRef?.current) {
        pageRef.current.serviceItemRefresh = refresh;
        pageRef.current.requestItemFlag = requestItemFlag;
      } else {
        pageRef.current = { serviceItemRefresh: refresh, requestItemFlag };
      }
      if (!scPageRef.current) {
        scPageRef.current = new Map([[key, pageRef]]);
      }
    }
  }, [content, requestItemFlag]);

  const viewId = pageType === 'CREATE' ? content?.variableViewId : content?.detailVariableViewId;

  if (mode === 'PREVIEW') {
    return (
      <div className="lc-page-loader-variablePage-preview">{name}</div>
    );
  }

  if (!dataSet?.current) {
    return null;
  }
 
  if (!requestItemFlag) {
    const scItem = dataSet?.current.get('service_item_id');
    const scItemId = scItem?.id || scItem;
    if (!scItemId || !instanceId) {
      return null;
    }

    // 兼容在普通对象视图中显示服务项视图
    return (
      <div className={classnames('lc-page-loader-variablePage', className)} {...rest}>
        <PageLoader
          mode={editable ? 'MODIFY' : 'DISABLED'}
          pageRef={pageRef}
          showHeaderFlag={false}
          instanceId={instanceId}
          formDataSet={dataSet}
          scItemViewFlag
          scItemViewType={pageType === 'CREATE' ? 'CREATE_PC' : 'QUERY_PC'}
          scItemId={scItemId}
        />
      </div>
    );
  }

  if (!viewId || !id) {
    return null;
  }

  return (
    <div className={classnames('lc-page-loader-variablePage', className)} {...rest}>
      <PageLoader
        viewId={viewId}
        mode={editable ? 'MODIFY' : 'DISABLED'}
        defaultData={content?.jsonData && {
          ...JSON.parse(content?.jsonData),
          price: content?.price,
          quantity: content?.quantity,
        }}
        pageRef={pageRef}
        variableFlag
        showHeaderFlag={false}
        events={{
          update: () => {
              dataSet?.current?.set('__dirty', true);
          },
        }}
        requestItemConfig={{
          instanceId: id,
          requestDataSet: dataSet,
          createViewId: content?.variableViewId,
          updateViewId: content?.detailVariableViewId,
          scPageRef,
        }}
      />
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutServiceItemPage */
