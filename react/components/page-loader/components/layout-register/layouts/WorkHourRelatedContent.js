import { observer } from 'mobx-react-lite';
import React, { useEffect, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';
import WorkhourRelatedContent from '@/renderer/workhour-related-content';

const Index = observer((props) => {
  const { dataSet: formDataSet, context } = props;
  const { pageRef, intl, hiddenRelatedContent } = context;
  const { businessObjectCode, ticketId } = pageRef?.current || {};

  const RelateOptions = useMemo(() => new DataSet({
    data: [{ value: 'REPLY', text: intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' }) }, { value: 'ACTION', text: intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.action', defaultMessage: '动作' }) }],
  }), []);

  // 给工时视图的formDataSet添加工时关联内容的字段
  useEffect(() => {
    formDataSet?.addField('category', {
      label: intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.category', defaultMessage: '工时类别' }),
      lookupCode: 'WORK_TIME_CATEGORY',
    });
    formDataSet?.addField('relate', {
      type: 'string',
      defaultValue: 'REPLY',
      label: intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate', defaultMessage: '关联' }),
      textField: 'text',
      valueField: 'value',
      options: RelateOptions,
    });
  }, [formDataSet]);

  if (hiddenRelatedContent) {
    return null;
  }

  return <WorkhourRelatedContent formDataSet={formDataSet} ticketId={ticketId} businessObjectCode={businessObjectCode} />;
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutWorkHourRelatedContent */
