import React, { createElement } from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import { EditorRegister } from '@zknow/utils';

import './Field.less';

const Index = observer(({ record }) => {
  const widgetType = record.get('widgetType');
  const field = EditorRegister.get(widgetType);
  if (field) {
    return (
      createElement(field.preview, {
        key: record.get('id'),
        record,
      })
    );
  }
  return null;
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutField */
