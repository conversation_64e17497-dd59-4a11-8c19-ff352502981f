import React from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { withErrorBoundary, Button, Icon } from '@zknow/components';
import { Modal, DataSet, Form, TextArea, SelectBox, Menu, Tooltip } from 'choerodon-ui/pro';
import lodashFilter from 'lodash/filter';
import axios from 'axios';
import './BatchPressTicket.less';

const { Option } = SelectBox;

const ReminderForm = observer(({ dataSet, options, intl }) => {
  return (
    <Form dataSet={dataSet}>
      <SelectBox vertical name="messageTips">
        {lodashFilter(options, 'enabledFlag').map(({ code, meaning }) => (<Option value={code}>{meaning}</Option>))}
        <Option value="OTHER">{intl.formatMessage({ id: 'lcr.renderer.pressTicket.other' })}</Option>
      </SelectBox>
      {dataSet.current.get('messageTips') === 'OTHER' && (
        <TextArea
          autoSize={{ minRows: 3, maxRows: 4 }}
          resize="height"
          name="message"
        />
      )}
    </Form>
  );
});

const PressTicket = inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })(injectIntl(observer((props) => {
    const { intl, record, context, feature, tableLineRecord } = props;
    const { tenantId, viewDataSet, tableViewDataSet, pageRef } = context;
    const viewType = viewDataSet?.current?.get('viewType');
    const isTable = viewType === 'TABLE';
    const name = record?.get('name');
    const id = record?.get('id');
    const color = record?.get('color');

    const handlePressTicket = async () => {
      const pressTicketDataSet = new DataSet({
        autoCreate: true,
        fields: [
          {
            name: 'message',
            type: 'string',
          },
          {
            name: 'messageTips',
            type: 'string',
            label: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content' }),
          },
          {
            name: 'messageOption',
            lookupCode: 'REMINDER_TICKET_MESSAGE',
          },
        ],
        events: {
          update: ({ name: n, record: r }) => {
            if (n === 'messageTips') {
              r.set('message', '');
            }
          },
        },
      });
      const options = await pressTicketDataSet.getField('messageOption').fetchLookup();
      pressTicketDataSet.current.set({ messageTips: options?.[0]?.code });

      Modal.open({
        title: (
          <div className="ticker-reminder-modal-title">
            <span>{intl.formatMessage({ id: 'lcr.renderer.pressTicket.title' })}</span>
            <span className="ticker-reminder-modal-title-icon">
              <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.pressTicket.help' })}>
                <Icon type="help" size="16" />
              </Tooltip>
            </span>
          </div>
        ),
        children: <ReminderForm dataSet={pressTicketDataSet} options={options} intl={intl} />,
        okText: intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.send' }),
        onOk: async () => {
          const current = isTable ? tableLineRecord : pageRef?.current?.formDataSet?.current;
          if (!current) return false;
          let messageText = pressTicketDataSet.current?.get('message');
          if (pressTicketDataSet.current.get('messageTips') !== 'OTHER') {
            const quickItem = options.find(o => o.code === pressTicketDataSet.current.get('messageTips'));
            if (quickItem) {
              messageText = quickItem?.meaning || '';
            }
          }
          const businessObjectCode = current.get('source_table');
          // 所有单据类型，由于业务类型不唯一，且行数据无法获取 businessObjectId
          //  后端取值 businessObjectId 优先于 businessObjectCode
          let businessObjectId = tableViewDataSet?.current?.get('businessObjectId') || pageRef?.current?.pageData?.businessObjectId;
          if (businessObjectCode) {
            businessObjectId = '';
          }
          const pressData = {
            message: messageText,
            ticketInfo: [{
              id: current.get('id'),
              businessObjectId,
              businessObjectCode,
            }],
          };
          const res = await axios.post(`itsm/v1/${tenantId}/ticket/reminders`, pressData);
          if (typeof res?.success === 'boolean') {
            const { success, message: m, notificationWays, receivers } = res;
            if (success) {
              if (!isTable) {
                // 刷新表单
                pageRef?.current?.formDataSet.query();
              } else {
                tableLineRecord.dataSet.query();
              }
            }
            Modal.open({
              title: (
                <div className="ticker-reminder-modal-title">
                  <span>{intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.feedback' })}</span>
                  <span className="ticker-reminder-modal-title-icon">
                    <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.pressTicket.feedback.help' })}>
                      <Icon type="help" size="16" color="rgba(18, 39, 77, 1)" />
                    </Tooltip>
                  </span>
                </div>
              ),
              children: (
                <div className="lc-table-action-pressTicket">
                  <Icon theme="filled" fill={success ? '#1AB335' : '#F34C35'} type={success ? 'check-one' : 'close-one'} size={50} />
                  <div className="lc-table-action-pressTicket-title">
                    {success ? intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.success' }) : intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.failed' })}
                  </div>
                  <div className="lc-table-action-pressTicket-context">
                    {success
                      ? (
                        <span>
                          {intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.channels.before' }, { receivers })}
                          <strong>{notificationWays}</strong>
                          {intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.channels.after' }, { receivers })}
                        </span>
                      ) : intl.formatMessage({ id: 'lcr.renderer.pressTicket.content.notification.failedreason' }, { extra: m }) }
                  </div>
                </div>
              ),
              footer: null,
            });
            return true;
          }
        },
      });
    };

    function renderButton() {
      // 只有明确 reminder_flag 为 true 才显示
      if (!isTable && pageRef?.current?.formDataSet?.current?.get('reminder_flag') !== true) {
        return null;
      }
      return (
        <>
          {feature === 'table-action' ? (
            <Menu.Item key={id} className="c7n-menu-item" onClick={handlePressTicket}>
              <Icon type="lightning" style={{ marginRight: 8 }} />
              {name}
            </Menu.Item>
          ) : (
            <Button
              key={id}
              className="lc-table-action-pressTicket-btn"
              onClick={handlePressTicket}
              funcType={isTable ? 'flat' : 'raised'}
              color={isTable ? 'primary' : color}
              icon="lightning"
            >
              {name}
            </Button>
          )}
        </>
      );
    }

    return renderButton();
  })))
);

export default withErrorBoundary(PressTicket, { fallback: <span /> });

/* externalize: LcLayoutPressTicket */
