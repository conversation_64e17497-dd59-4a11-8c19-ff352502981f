import React from 'react';
import { TableStatus, StatusTag } from '@zknow/components';

export const DATE_FIELDS = ['DateTime', 'Date', 'Time'];
export const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

/**
 * 从属性字段中获取颜色
 * @param name 字段名
 * @param current 数据源 Record
 * @param attributeFields 属性字段
 * @returns 颜色字符串或false
 */

function getColor(name, current, attributeFields) {
  try {
    let attributeFieldCode = attributeFields?.length ? attributeFields[0] : '';
    const fieldCodeList = name.split(':');
    if (fieldCodeList.length > 1) {
      fieldCodeList.pop();
      attributeFieldCode = `${fieldCodeList.join(':')}:${attributeFields[0]}`;
    }
    return current.get(attributeFieldCode);
  } catch {
    return false;
  }
}
/**
 * 是否是lc-form-page-loader-content-root下的第一层
 * @param ele sectionElement
 * @returns boolean
 */
export function isRootLoaderColumnChildren(ele) {
  let parentEle = ele;
  for (let i = 0; i < 20; i++) {
    if (!parentEle) return false;
    if (parentEle.className?.includes?.('content-root') && i < 6) return true;
    // 出现新的问题（当用户拖入两次section然后再拖入tab时，则出现布局错乱现象）修复
    if (parentEle.className?.includes?.('lc-page-loader-section-layout') && parentEle?.children?.length === 2) return true;
    parentEle = parentEle.parentElement;
  }
  return false;
}

/**
 * 根据指定渲染器渲染表格列
 * @param columnProps
 * @param renderer
 * @param attributeFields
 * @param intl
 * @returns {*}
 */
export function columnRenderer(columnProps, renderer, attributeFields, intl) {
  const {
    value,
    text,
    name,
    dataSet,
    record: current,
  } = columnProps;
  let color = getColor(name, current, attributeFields);
  switch (renderer) {
    case 'ENABLED_TAG':
      return (
        <TableStatus
          status={value}
          enabledText={intl.formatMessage({ id: 'zknow.common.status.valid', defaultMessage: '有效' })}
          disabledText={intl.formatMessage({ id: 'zknow.common.status.invalid', defaultMessage: '无效' })}
        />
      );
    case 'STATUS_TAG': {
      const optionDataSet = dataSet?.getField(name)?.options;
      const optionRecord = optionDataSet?.find(option => option.get('code') === value || option.get('value') === value);
      color = optionRecord?.get('color') || color || '#2979FF';
      return text && (
        <StatusTag
          name={text}
          color={color}
        >
          {text}
        </StatusTag>
      );
    }
    case 'STATUS_ICON_TAG': {
      const optionDataSet = dataSet?.getField(name)?.options;
      const optionRecord = optionDataSet?.find(option => option.get('code') === value);
      color = optionRecord?.get('color') || color || '#2979FF';
      return text && (
        <StatusTag
          name={text}
          mode="icon"
          color={color}
        >
          {text}
        </StatusTag>
      );
    }
    default:
      return text;
  }
}

export const setFieldInSection = (_layout, fields = []) => {
  const { children } = _layout;
  const _fields = _layout.getCascadeRecords('fields') || [];
  _fields?.length && fields.push(..._fields);
  children && children.forEach(item => setFieldInSection(item, fields));
};

export const getFieldsInSection = (_layout) => {
  const fields = [];
  setFieldInSection(_layout, fields);
  return fields;
};

export const validateSectionFields = async ({ layout, formDs }) => {
  if (!formDs) return;
  const fields = getFieldsInSection(layout).map(field => field.get('code'));
  let validate = await formDs.validate();
  if (!validate) {
    const validationErrors = formDs.getValidationErrors();
    if (validationErrors?.length) {
      const errors = validationErrors[0]?.errors || [];
      const errorFields = errors.map(error => error.field.name);
      validate = !errorFields.find(code => fields.includes(code));
    }
  }
  return validate;
};

export const resetSectionFields = ({ layout, record }) => {
  if (!record) return;
  getFieldsInSection(layout).forEach(field => {
    const pristineValue = record?.getPristineValue(field.get('code'));
    record.init(field.get('code'), pristineValue);
  });
};
