/*
 * @Author: xiaoreya
 * @Date: 2022-04-19 14:52:23
 * @Description:
 */
import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import MultipleChoice from '@/renderer/multiple-choice';

const Index = observer((props) => {
  const { record, dataSet, className, context, labelWidth } = props;
  const { tenantId, instanceId, viewDataSet, intl } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');
  return (
    <MultipleChoice
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      labelWidth={labelWidth}
      expressionDefaultValue={context?.expressionDefaultValue} // 由于新建时还没有 id，在请求 submitByParamsForFields 的时候约定使用 calculate结果中的 ）_id
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutMultipleChoice */
