import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent } from '@zknow/components';
import LayoutRegister from '../LayoutRegister';

const LazyComponents = [
  'ApprovalAction',
  'AssetRelation',
  'AssetCustomerDetail',
  'AssetType',
  'BIChart',
  'BatchPressTicket',
  'BatchRepairSLA',
  'Button',
  'ButtonAction',
  'ButtonArea',
  'ButtonGroup',
  'Chart',
  'ClewToneButton',
  'Comment',
  'Custom',
  'CustomButton',
  'DraftButton',
  'Dynamic',
  'Field',
  'GenerateKnowledge',
  'IntelligentRecommendation',
  'IntelligentSearch',
  'ItemRelated',
  'JSExpression',
  'MappingModalButton',
  'Mind',
  'MultipleChoice',
  'Page',
  'Participants',
  'PressTicket',
  'ProcessTime',
  'RelevantKnowledge',
  'RepairSLAHistory',
  'RequestContent',
  'Section',
  'ServiceItemPage',
  'ShoppingCartButton',
  'StageExpand',
  'Tab',
  'Table',
  'ThirdPartyApplication',
  'TicketAITranslationButton',
  'TicketFileList',
  'TicketHeader',
  'TicketMeeting',
  'TicketRelation',
  'TicketRelationCard',
  'TicketRelationList',
  'TicketSLA',
  'TicketSolution',
  'TicketStage',
  'TicketSurveyDetail',
  'TicketSurveyList',
  'TicketTransfer',
  'TicketWorkHour',
  'Tree',
  'TreeLov',
  'VariablePage',
  'VariableTable',
  'Location',
  'DuplicateProblemAnalysis',
  'UpgradeOrder',
  'GenerateQA',
  'CartTicket',
  'AssetServiceItemJumpBtn',
  'Conversion',
  'QualityInspectionScore',
  'QualityInspectionSuggestions',
  'ShoppingCartPrincipal',
  'SubTasks',
  'TicketSummary',
  'TicketReminderBanner',
  'WorkHourRelatedContent',
  'OriginalTicketNumber',
  'TicketDiagnosis',
  'TicketTemplate',
  'AddTaskItemButton',
];

LazyComponents.forEach(widgetType => {
  LayoutRegister.registry(widgetType, {
    preview: observer(props => (
      <ExternalComponent
        system={{
          scope: 'lcr',
          module: `LcLayout${widgetType}`,
        }}
        {...props}
        fallback={<span />} // 加上这个 fallback， 可以防止动态渲染组件的时候出现大面积白色区域遮盖
      />)),
  });
});
