import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketSolution from '@/renderer/ticket-solution';

const Index = observer(({ record, dataSet, context, sectionDisplayMode }) => {
  const { tenantId, instanceId, viewDataSet, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  // 共享服务项生成的单据不展示解决方案
  return (
    <TicketSolution
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
      sectionDisplayMode={sectionDisplayMode}
      udmFlag={udmFlag}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketSolution */
