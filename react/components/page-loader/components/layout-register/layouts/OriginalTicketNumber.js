import React, { useCallback, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import queryString from 'query-string';
import { withErrorBoundary } from '@zknow/components';
import className from 'classnames';
import styles from './OriginalTicketNumber.module.less';

const OriginalTicketNumberRender = observer((props) => {
  const { tenantId, instanceId, intl } = props.context;
  const [origin, setOrigin] = useState(null);

  useEffect(() => {
    if (queryString.parse(window.location.href.split('?')[1])?.udmTenantId) {
      (async () => {
        try {
          const res = await axios.get(`/itsm/v1/${tenantId}/global/tickets/origin/${instanceId}?viewIdFlag=true`);
          if (res && !res.failed && res.status !== '204') {
            setOrigin(res);
          }
        } catch (e) {
          //
        }
      })();
    }
  }, []);

  const handleClick = useCallback(() => {
    const { originDetailViewId, originTicketId } = origin;
    if (originDetailViewId && originTicketId) {
      window.top.postMessage({
        effect: 'YQ_UDM_ORIGIN',
        id: instanceId,
        originTicketId,
        originDetailViewId,
      }, window.location.origin);
    }
  }, origin);

  return origin ? (
    <div className={styles.wrap}>
      <span className={styles.label}>{intl.formatMessage({ id: 'lcr.renderer.originNumber.title', defaultMessage: '原单据编码' })}</span>
      <span className={className(styles.value, { [styles.link]: origin.originDetailViewId && origin.originTicketId })} onClick={handleClick}>{origin?.originNumber}</span>
    </div>
  ) : null;
});

export default withErrorBoundary(OriginalTicketNumberRender, { fallback: <span /> });

/* externalize: LcLayoutOriginalTicketNumber */
