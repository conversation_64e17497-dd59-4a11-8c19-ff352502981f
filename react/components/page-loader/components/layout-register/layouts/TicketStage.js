import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketStage from '@/renderer/stage';

const Index = observer(({ record, dataSet, context, setOverflowDisplay }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');
  const {
    name,
    widgetConfig: {
      ticketStagTitleFlag,
      ticketStageMode,
    },
  } = record?.toData();

  // 共享服务项生成的单据不展示单据阶段
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketStage
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      setOverflowDisplay={setOverflowDisplay}
      mode={ticketStageMode}
      title={ticketStagTitleFlag ? name : ''}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketStage */
