import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';

const Index = observer((props) => {
  const { dataSet, record } = props;
  return (
    <ExternalComponent
      system={{
        scope: 'itam',
        module: 'AssetRelationListRenderer',
      }}
      formDataSet={dataSet}
      widgetConfig={record?.get('widgetConfig')}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutAssetRelation */
