import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, withErrorBoundary } from '@zknow/components';
import set from 'lodash/set';

const Index = observer(({ record, dataSet, className, context, labelWidth, ...rest }) => {
  const {
    tenantId, instanceId, viewDataSet, intl, dsManager, modal,
    pageContext, parentDataSet, tabMenuDataSet, queryParams, formDataSet: formDs,
    serviceItemId, pageRef, shoppingCartFlag, setOverflowDisplay,
  } = context;
  const formDataSet = (viewDataSet?.current && dsManager?.get(viewDataSet?.current?.get('id'))) || formDs;
  const scope = record.get('widgetConfig.scope');
  const module = record.get('widgetConfig.module');
  const customConfig = record?.get('customConfig') || [];
  const config = record?.toData() || {};
  set(config, 'widgetConfig.customConfig', customConfig);

  return (
    <ExternalComponent
      system={{ scope, module }}
      intl={intl}
      tenantId={tenantId}
      pageContext={pageContext} // 当前页面所有视图数据
      instanceId={instanceId} // 当前记录的id
      formDataSet={formDataSet} // 当前表单ds
      viewDataSet={viewDataSet} // 当前视图ds
      config={config} // 当前组件的配置
      modal={modal} // 弹窗实例
      parentDataSet={parentDataSet} // 如果放在弹窗里，父级页面的数据
      tabMenuDataSet={tabMenuDataSet} // 单据自己的导航tab数据
      queryParams={queryParams} // PageLoader上定义的参数
      fallback={<span />}
      viewRecord={record}
      serviceItemId={serviceItemId} // 服务项id  可有可无
      pageRef={pageRef}
      shoppingCartFlag={shoppingCartFlag} // 是否是购物车的视图
      setOverflowDisplay={setOverflowDisplay}
      labelWidth={labelWidth} // 自定义组件传入父级section设置的labelWidth
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutCustom */
