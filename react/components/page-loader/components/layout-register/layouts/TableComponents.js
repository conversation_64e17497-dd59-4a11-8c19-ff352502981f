import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  Form,
  Lov,
  message,
  Modal,
  Select,
  SelectBox,
  Spin,
  Table,
  TextField,
  Tooltip,
  Transfer,
  Button,
  Output,
  Progress,
} from 'choerodon-ui/pro';
import { Alert } from 'choerodon-ui';
import { v4 as uuidv4 } from 'uuid';
import classnames from 'classnames';
import isNil from 'lodash/isNil';
import uniq from 'lodash/uniq';
import FileSaver from 'file-saver';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { ExternalComponent, FileShower, Icon, IconPicker, TableHoverAction, YqPreview, FileUploader, DragUploader, StatusTag, Button as YQButton } from '@zknow/components';
import { color as colorUtils, getCookieToken } from '@zknow/utils';
import axios from 'axios';
import moment from 'moment';
import Title from '@/components/title';
import { getFileIcon, getFileName, getFileType } from '@/components/editor-register/utils';
import LcTransfer from '@/components/transfer';
import emptyImg from '@/assets/images/empty.png';

const { Column } = Table;
const previewModalKey = Modal.key();

const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

export function addConditionGroupByQuery(rec, fuzzySearchData, columns) {
  const filters = [];
  function getWidgetConfig(v, flag = true) {
    const type = v.widgetType;
    if (['AutoNumber'].includes(type)) {
      return 'Input';
    }
    if (['MasterDetail'].includes(type) && !flag) {
      const fieldConfig = columns.find(column => column.objectFieldPath === v.path);
      const relationLovId = v?.relationLovId || fieldConfig?.relationLovId;
      return relationLovId;
    }
    return type;
  }
  function getFilterCondition(r, v) {
    const type = v.widgetType;
    if (['MultipleSelect', 'MasterDetail', 'Select', 'Radio', 'SelectBox', 'Tag'].includes(type)) {
      return 'is in list';
    }
    if (['Date', 'DateTime', 'Time'].includes(type)) {
      const dateValue = r?.get(v.path);
      if (dateValue && dateValue.start && dateValue.end) {
        return 'between';
      }
      if (dateValue && dateValue.start && !dateValue.end) {
        return 'is later than';
      }
      if (dateValue && !dateValue.start && dateValue.end) {
        return 'is early than';
      }
      return 'between';
    }
    if (['Switch', 'CheckBox', 'Like'].includes(type)) {
      return 'is';
    }
    return 'contains';
  }
  function getValue(r, v) {
    const t = v.widgetType;
    if (['Date', 'DateTime', 'Time'].includes(t)) {
      const dateValue = r?.get(v.path);
      if (dateValue && dateValue.start && dateValue.end) {
        return `${moment(dateValue.start).format(DEFAULT_DATE_FORMAT[t])},${moment(dateValue.end).format(DEFAULT_DATE_FORMAT[t])}`;
      }
      if (dateValue && dateValue.start && !dateValue.end) {
        return `${moment(dateValue.start).format(DEFAULT_DATE_FORMAT[t])}`;
      }
      if (dateValue && !dateValue.start && dateValue.end) {
        return `${moment(dateValue.end).format(DEFAULT_DATE_FORMAT[t])}`;
      }
      return undefined;
    }
    const fieldValue = r?.get(v.path)?.slice();
    if (['MasterDetail'].includes(t) && Array.isArray(fieldValue)) {
      return fieldValue?.map(data => data.id)?.join(',');
    }
    if (['MultipleSelect', 'Select', 'Radio', 'SelectBox', 'Tag'].includes(t) && fieldValue && Array.isArray(fieldValue.slice())) {
      if (fieldValue.slice().length > 0) {
        return fieldValue?.join(',');
      } else {
        return undefined;
      }
    }
    if (['Date', 'DateTime', 'Time'].includes(t)) {
      return fieldValue
        && `${moment(fieldValue.start).format(DEFAULT_DATE_FORMAT[t])},${moment(fieldValue.end).format(DEFAULT_DATE_FORMAT[t])}`;
    }
    return fieldValue;
  }
  function getLovValue(r, v) {
    const t = v.widgetType;
    if (['MasterDetail'].includes(t)) {
      return JSON.stringify(r?.get(v.path));
    }
    return JSON.stringify(v.widgetConfig);
  }
  fuzzySearchData.forEach(v => {
    if (getValue(rec, v) && !filters.find(j => j.field === v.path)) {
      filters.push({
        filterUuid: uuidv4(),
        condition: 'AND',
        field: v.path,
        filter: getFilterCondition(rec, v),
        fieldLovValue: getLovValue(rec, v),
        widgetType: getWidgetConfig(v),
        componentType: getWidgetConfig(v, false),
        fieldValue: getValue(rec, v),
        fieldName: v.label || v.name,
      });
    }
  });
  return filters.length ? [{
    groupId: uuidv4(),
    groupName: 'filter.condition.groupTitle',
    condition: 'AND',
    filters,
  }] : [];
}

export const CreateForm = observer(({ dataSet }) => {
  return (
    <Form
      dataSet={dataSet}
      columns={1}
      labelLayout="horizontal"
      labelWidth="auto"
    >
      <SelectBox name="newMethod" />
      {dataSet?.current?.get('newMethod') !== 'NEW' ? <Select name="basicId" /> : null}
    </Form>
  );
});

// 历史记录
export const HistoryDetailForm = observer((props) => {
  const { dataSet, businessObjectId, tenantId, prefixCls, intl } = props;

  useEffect(() => {
    dataSet.query();
  }, []);

  function renderActions({ record }) {
    return (
      <TableHoverAction
        record={record}
        // eslint-disable-next-line no-nested-ternary
        actions={record.get('status') === 'FAILED' && isNil(record.get('taskInstanceId')) ? [
          {
            name: intl.formatMessage({ id: 'lcr.components.desc.retry', defaultMessage: '重试' }),
            icon: 'Redo',
            onClick: async () => {
              const res = await axios.post(`/data/v1/${tenantId}/dataTasks/retry/${record.get('id')}`);
              if (res?.failed) {
                message.error(res?.message);
              } else {
                dataSet.query();
              }
            },
          },
        ] : record.get('status') === 'COMPLETED' ? [
          {
            name: intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' }),
            icon: 'download',
            onClick: async () => {
              const url = `${window._env_.API_HOST}/hfle/yqc/v1/0/files/download-by-key?fileKey=${record?.get('file')}`;
              window.location.href = url;
            },
          },
        ] : []}
      />
    );
  }
  const renderStatus = ({ value = '', text }) => {
    const colorMap = {
      COMPLETED: colorUtils?.getColorDefaultValue('green'),
      RUNNING: colorUtils?.getColorDefaultValue('orange'),
      PENDING: colorUtils?.getColorDefaultValue('grey'),
      FAILED: colorUtils?.getColorDefaultValue('red'),
      PART_FAILED: colorUtils?.getColorDefaultValue('yellow'),
    };
    return <StatusTag mode="icon" color={colorMap[value] || colorUtils?.getColorDefaultValue('red')}>{text}</StatusTag>;
  };
  const renderSize = ({ record }) => {
    return `${Math.floor((+(record?.get('fileSize') || 0) / 1024))}KB`;
  };
  return (
    <Table
      dataSet={dataSet}
      autoHeight
      pristine
      queryBarProps={{
        title: intl.formatMessage({ id: 'lcr.components.desc.download.history', defaultMessage: '下载历史' }),
      }}
      buttons={[
        <Button onClick={() => dataSet?.query()} funcType="raised" icon="refresh" />,
      ]}
    >
      <Column name="file" />
      <Column name="fileSize" renderer={renderSize} />
      <Column name="createdByName" />
      <Column name="status" renderer={renderStatus} />
      <Column
        name="percent"
        renderer={({ value }) => (
          <Progress value={Number(value)} />
        )}
      />
      <Column name="creationDate" />
      <Column width={50} renderer={renderActions} />
    </Table>
  );
});
// 导出Form
export const ExportForm = observer((props) => {
  const {
    dataSet, businessObjectId, tenantId, intl, tableViewDataSet, tableFilterDataSet,
    columnDataSet, defaultColumnViewId, defaultFilterId, modifyConditionFlag, queryDataSet,
    tableDataSet, fuzzySearchData, fields: tableFields, viewDataSet, datasetId, lastViewId,
    templateInfo, language, modal, relatedFieldCode, instanceId, tableOrderConfig,
  } = props;
  const [condition, setCondition] = useState([]);
  const record = dataSet.current;
  const [sortField, setSortField] = useState([]);
  const [order, setOrder] = useState([]);
  const [fieldTableData, setTableFieldData] = useState([]);
  const [listFields, setListFields] = useState([]);
  const [memoryFields, setMemoryFields] = useState([]);
  const sortableKey = useRef(uuidv4());
  const fromImportFlag = !!templateInfo?.id;
  const getFields = () => {
    try {
      return JSON.parse(templateInfo?.jsonData)
        .fields.map((v) => (language === 'zh_CN' ? v?.name : v?.nameEn))
        // eslint-disable-next-line no-chinese/no-chinese
        .join('、');
    } catch {
      return '';
    }
  };
  function getFilterFieldData(data) {
    condition.forEach(cond => {
      if (cond?.filters?.length) {
        cond?.filters.forEach(item => {
          if (!data.find(v => v.code === item.field)) {
            data.push({
              code: item.field,
              name: item.fieldName,
              widgetType: item.widgetType,
              fieldName: item.fieldName,
            });
          }
        });
      }
    });
    return data;
  }

  /**
   * 表格视图字段改为对象，兼容新旧数据
   * @param data
   * @returns {*}
   */
  function getFieldsList(data) {
    let fields = [];
    try {
      fields = JSON.parse(data);
    } catch (e) {
      fields = [];
    }
    return fields.map(field => field.code || field);
  }

  const updateOkProps = (total) => {
    modal.update({
      okProps: {
        disabled: total === 0,
      },
    });
  };

  useEffect(() => {
    async function loadFields() {
      const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?conditionFlag=true`);
      if (res?.failed) {
        message.error(res?.message);
        setTableFieldData([]);
      } else {
        setTableFieldData(res);
      }
    }
    if (businessObjectId) {
      loadFields();
    }
  }, [businessObjectId]);

  useEffect(() => {
    let personalColumn;
    // 获取当前筛选条件
    const columns = viewDataSet?.current?.get('columns') || [];
    let defaultCondition = addConditionGroupByQuery(queryDataSet?.current, fuzzySearchData, columns);
    let defaultOrderBy = JSON.parse(tableOrderConfig || '[]');
    // 判断是否有默认筛选器，有就带出来
    if (defaultFilterId) {
      const basic = tableFilterDataSet.find(r => r.get('id') === defaultFilterId)?.toData() || {};
      personalColumn = JSON.parse(basic?.personalColumn || '{}');
      defaultCondition = defaultCondition.concat(JSON.parse(basic?.condition || '[]'));
      defaultOrderBy = defaultOrderBy.concat(JSON.parse(basic?.orderBy || '[]'));
    }
    defaultCondition = defaultCondition.concat(JSON.parse(tableDataSet.getQueryParameter('__condition') || '[]'));
    defaultOrderBy = defaultOrderBy.concat(JSON.parse(tableDataSet.getQueryParameter('__orderBy') || '[]'));
    dataSet.current.set('condition', JSON.stringify(defaultCondition));
    dataSet.current.set('orderBy', JSON.stringify(defaultOrderBy));
    setCondition(defaultCondition);
    setOrder(defaultOrderBy);
    const newFields = tableFields.map(item => {
      const fieldData = item.toData();
      if (personalColumn) {
        if (!personalColumn[fieldData.path]?.hidden) {
          return fieldData.path;
        } else {
          return undefined;
        }
      } else {
        return fieldData.path;
      }
    });
    dataSet.current.set('fields', newFields);
    setSortField(newFields);
    // }
  }, []);
  const queryFieldMemory = async () => {
    let url = `lc/v1/${tenantId}/field/export/storage?viewId=${dataSet.current.get('viewId')}`;
    if (tableDataSet.getQueryParameter('filter_code')) {
      url += `&filterCode=${tableDataSet.getQueryParameter('filter_code')}`;
    }
    if (tableDataSet.getQueryParameter('filter_id')) {
      url += `&filterId=${tableDataSet.getQueryParameter('filter_id')}`;
    }
    const res = await axios.get(url);
    if (res && !res.failed && res.fields) {
      setMemoryFields(res.fields);
    }
  };
  useEffect(() => {
    queryFieldMemory();
    setCondition(JSON.parse(record?.get('condition') || '[]'));
    setOrder(JSON.parse(record?.get('orderBy') || '[]'));
    updateOkProps(dataSet?.totalCount);
  }, []);

  useEffect(() => {
    const viewFields = dataSet.current?.get('fields') || [];
    let fields = [];
    if (columnDataSet.toData().length > 0) {
      fields = columnDataSet.toData();
    } else {
      tableFields?.forEach((field) => {
        const fieldData = field.toData();
        if (fieldData) {
          fields.push({
            id: fieldData.id,
            label: fieldData.name,
            objectFieldPath: fieldData.path,
          });
        }
      });
    }
    const calculatedFields = [];
    sortField.forEach((item) => {
      const fieldData = fields.find(f => f.objectFieldPath === item);
      if (fieldData?.calculatedFlag) {
        calculatedFields.push(item.objectFieldPath);
      }
    });
    if (calculatedFields.length > 0) {
      record.set('asyncFlag', true);
    }
    record.setState('calculatedFields', calculatedFields);
    const sortFields = fields?.sort((cur, next) => {
      const curIndex = viewFields.indexOf(cur.objectFieldPath);
      const nextIndex = viewFields.indexOf(next.objectFieldPath);
      if (curIndex === -1) {
        return 1;
      }
      if (nextIndex === -1) {
        return -1;
      }
      return curIndex - nextIndex;
    });
    setListFields(sortFields);
  }, [sortField, dataSet]);

  const handleChangeBaseId = (value, type) => {
    const funcDataSet = type === 'filter' ? tableFilterDataSet : tableViewDataSet;
    const basic = funcDataSet.find(r => r.get('id') === value)?.toData() || {};
    if (type === 'filter') {
      const parsedCondition = JSON.parse(basic?.condition || '[]');
      const parsedOrderBy = JSON.parse(basic?.orderBy || '[]');
      dataSet.current.set({
        condition: basic?.condition,
        orderBy: basic?.orderBy,
      });
      // FIXME: 【临时解决】用户体验优化，防止选择器下拉收起的动画被阻塞
      setTimeout(() => {
        setCondition(parsedCondition);
        setOrder(parsedOrderBy);
      }, 310);
    } else {
      const fields = getFieldsList(basic?.jsonData || '[]');
      dataSet.setState('sortFields', []);
      dataSet.current.set({
        viewName: basic?.name,
        fields,
      });
      setTimeout(() => {
        setSortField(fields);
      }, 310);
    }
  };

  const conditionChange = async (data) => {
    setCondition(data);
    record.set('condition', JSON.stringify(data));
    handleQuery(data);
  };

  const handleQuery = async (data) => {
    let params = {
      __condition: JSON.stringify(data) || tableDataSet.getQueryParameter('__condition'),
      [`search_${relatedFieldCode}`]: instanceId,
    };
    if (tableDataSet.getState('__SEARCHTEXT__')) {
      params = {
        ...params,
        search_fuzzy_params_: tableDataSet.getState('__SEARCHTEXT__'),
      };
    }
    const queryParams = {
      exportFlag: false,
    };
    if (!isNil(defaultFilterId)) {
      queryParams.filter_id = defaultFilterId;
    }

    const res = await axios({
      url: `/lc/v1/engine/${tenantId}/dataset/${lastViewId}/${datasetId}/query`,
      method: 'POST',
      params: queryParams,
      data: params,
    });
    dataSet.current.set('totalCount', res?.totalElements);
    updateOkProps(res?.totalElements);
  };

  return (
    <Spin spinning={dataSet?.getState('loading') || false}>
      {fromImportFlag && <>
        <Form
          labelWidth="auto"
          columns={2}
        >
          <Lov value={templateInfo.name} disabled label={intl.formatMessage({ id: 'lcr.components.desc.import.chosen.template', defaultMessage: '已选模板' })} />
        </Form>
        {fromImportFlag && getFields() && <Alert
          type="info"
          showIcon
          message={
            intl.formatMessage({ id: 'lcr.components.desc.import.field.info', defaultMessage: '所选模板中的可导入字段包含: ' }) + getFields()
          }
          style={{ margin: '10px 0 ' }}
        />}
      </>}
      {!modifyConditionFlag && condition?.length === 0
        ? null
        : (
          <div className="advanced-filter-condition-container">
            <div className="filter-condition-header">
              {fromImportFlag ? intl.formatMessage({ id: 'lcr.components.pageLoader.condition.field' }) : intl.formatMessage({ id: 'lcr.components.pageLoader.export.condition', defaultMessage: '导出条件' })}
            </div>
            {tableDataSet.getState('__SEARCHTEXT__') && (
              <div className="export-fuzzy-params">
                <div className="export-fuzzy-params-help">{intl.formatMessage({ id: 'lcr.components.desc.export.fuzzy.field', defaultMessage: '满足以下模糊搜索条件' })}</div>
                <TextField value={tableDataSet.getState('__SEARCHTEXT__')} disabled />
              </div>
            )}
            <ExternalComponent
              system={{ scope: 'itsm', module: 'YqCondition' }}
              conditionData={condition}
              tableId={businessObjectId}
              fieldTableData={getFilterFieldData(fieldTableData)}
              front="design"
              onChange={(data) => conditionChange(data)}
              sourceFrom="filter"
              isSider={false}
              readOnly={!modifyConditionFlag}
              canHideUser
              extraFieldsFlag
              conditionFlag
              hasVariableType={false}
              hasRecursiveList={['Select', 'Radio']}
              m2mFlag={false} // m2m字段标识
            />
          </div>
        )}
      <div className="field-checkbox-container" style={{ marginTop: 20 }}>
        <div className="filter-field-list">
          {intl.formatMessage({ id: 'lcr.components.desc.export.field', defaultMessage: '导出字段' })}
        </div>
        <LcTransfer list={listFields} sortField={sortField} memoryList={memoryFields} exportDataSet={dataSet} />
      </div>
      {!modifyConditionFlag && order?.length === 0
        ? null
        : (
          <ExternalComponent
            system={{ scope: 'itsm', module: 'OrderCondition' }}
            conditionData={order}
            fieldTableData={fieldTableData}
            front="design"
            readOnly={!modifyConditionFlag}
            onChange={(data) => {
              setOrder(data);
              record.set('orderBy', JSON.stringify(data));
            }}
          />
        )}
      <Form
        dataSet={dataSet}
        columns={1}
        labelLayout="horizontal"
        labelWidth="auto"
        header={intl.formatMessage({ id: 'lcr.components.model.export.method', defaultMessage: '导出方式' })}
        className="yq-mt-16"
      >
        <Output name="totalCount" renderer={({ value }) => (intl.formatMessage({ id: 'lcr.components.desc.export.total.count.line', defaultMessage: '{totalCount}条' }, { totalCount: `${value}` }))} />
        <SelectBox name="asyncFlag" />
      </Form>
      <Alert
        type="info"
        showIcon
        message={
          record?.get('asyncFlag') ? (
            <>
              {intl.formatMessage({ id: 'lcr.components.model.export.help.background.tips', defaultMessage: '导出数据超出1000条\\导出计算字段时，由于数据计算量较大，仅能进行后台导出。' })}
              <br />
              {intl.formatMessage({ id: 'lcr.components.desc.export.help.direct.tips', defaultMessage: '后台导出后，可点击列表更多操作中的“导出历史”查看、下载导出数据。' })}
            </>
          ) : (
            intl.formatMessage({ id: 'lcr.components.desc.export.help.direct.tips', defaultMessage: '后台导出后，可点击列表更多操作中的“导出历史”查看、下载导出数据。' })
          )
        }
      />
    </Spin>
  );
});

export const CreateDetailForm = observer((props) => {
  const { dataSet, type, businessObjectId, tenantId, prefixCls, intl } = props;
  const record = dataSet.current;
  const [condition, setCondition] = useState([]);
  const [order, setOrder] = useState([]);
  const [fieldTableData, setTableFieldData] = useState([]);

  useEffect(() => {
    async function loadFields() {
      const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?conditionFlag=true`);
      if (res?.failed) {
        message.error(res?.message);
        setTableFieldData([]);
      } else {
        setTableFieldData(res);
      }
    }
    if (businessObjectId) {
      loadFields();
    }
  }, [businessObjectId]);

  useEffect(() => {
    setCondition(JSON.parse(record?.get('condition') || '[]'));
  }, []);

  useEffect(() => {
    setOrder(JSON.parse(record?.get('orderBy') || '[]'));
  }, []);

  function getFilterFieldData(data) {
    condition.forEach(cond => {
      if (cond?.filters?.length) {
        cond?.filters.forEach(item => {
          if (!data.find(v => v.code === item.field)) {
            data.push({
              code: item.field,
              name: item.fieldName,
              widgetType: item.widgetType,
            });
          }
        });
      }
    });
    return data;
  }

  if (type === 'FILTER') {
    return (
      <div className={`${prefixCls}-table-filter-form`}>
        <Form
          record={record}
          columns={2}
          labelLayout="horizontal"
          labelWidth="auto"
        >
          <TextField name="name" />
          <IconPicker name="icon" record={record} />
          <SelectBox name="type" />
          {record?.get('type') === 'SPECIFIC_GROUP' ? <Lov name="groupId" /> : null}
        </Form>
        <ExternalComponent
          system={{ scope: 'itsm', module: 'OrderCondition' }}
          conditionData={order}
          fieldTableData={fieldTableData}
          onChange={(data) => {
            setOrder(data);
            record.set('orderBy', JSON.stringify(data));
          }}
        />
        <ExternalComponent
          system={{ scope: 'itsm', module: 'FilterCondition' }}
          conditionData={condition}
          tableId={businessObjectId}
          fieldTableData={getFilterFieldData(fieldTableData)}
          onChange={(data) => {
            setCondition(data);
            record.set('condition', JSON.stringify(data));
          }}
          sourceFrom="filter"
          isSider={false}
          canHideUser
        />
      </div>
    );
  }

  return (
    <div className={`${prefixCls}-table-listView-form`}>
      <Form
        record={record}
        columns={2}
        labelLayout="horizontal"
        labelWidth="auto"
      >
        <TextField name="name" />
        <SelectBox name="type" />
        {record?.get('type') === 'SPECIFIC_GROUP' ? <Lov name="groupId" /> : null}
      </Form>
      <Title title={intl.formatMessage({ id: 'lcr.components.desc.field.select.sort', defaultMessage: '字段选择排序' })} />
      <Transfer
        multiple
        dataSet={dataSet}
        name="jsonData"
        searchable
        sortable="true"
      />
    </div>
  );
});

const FileInfo = observer((props) => {
  const { fileName, fileSize, single = false } = props;
  // eslint-disable-next-line no-nested-ternary
  const size = fileSize ? fileSize / 1024 > 1024
    ? `${Number(fileSize / 1024 / 1024).toFixed(1)}MB`
    : `${Number(fileSize / 1024).toFixed(1)}KB` : null;
  return (
    <div className="file-info">
      <div className="file-info-name">{fileName}</div>
      {fileSize && <div className="file-info-size">{single ? `(${(size || '10KB')})` : size || '10KB'}</div>}
    </div>
  );
});

export const File = observer((props) => {
  const { fileKey, fileSize, intl, popoverId } = props;
  const previewFile = (key) => {
    Modal.open({
      key: previewModalKey,
      title: intl.formatMessage({ id: 'lcr.components.desc.file.preview', defaultMessage: '附件预览' }),
      children: (
        <YqPreview fileKey={key} />
      ),
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
    });
  };

  function handleDownload(url) {
    FileSaver.saveAs(url, getFileName(url));
  }
  return (
    <FileShower
      fileKey={fileKey}
    >
      {({ src }) => (
        src ? (
          <div tabIndex="-1" onClick={(e) => e.stopPropagation()} className="file-container">
            <span>
              <Icon
                type={getFileIcon(getFileType(fileKey)).iconType}
                theme="filled"
                fill={getFileIcon(getFileType(fileKey)).color}
                size="32"
                className="file-container-icon"
              />
              <FileInfo fileName={getFileName(src)} fileSize={fileSize} />
            </span>
            <span>
              <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' })} theme="light">
                <Icon
                  type="PreviewOpen"
                  fill="#595959"
                  onClick={() => {
                    previewFile(fileKey);
                    if (popoverId) {
                      const popoverEle = document.getElementById(popoverId);
                      if (popoverEle) {
                        popoverEle.click();
                      }
                    }
                  }}
                  className="file-container-action"
                />
              </Tooltip>
              <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })} theme="light">
                <Icon
                  type="Download"
                  fill="#595959"
                  className="file-container-action"
                  onClick={() => handleDownload(src)}
                />
              </Tooltip>
            </span>
          </div>
        ) : null
      )}
    </FileShower>
  );
});

export const TableUploadColumn = observer((props) => {
  const { dataRecord, isEdit, fieldCode, configData, intl, tenantId } = props;
  const { widgetConfig, placeHolder, widgetType } = configData || {};
  const { fileFormat = 'single', fileSizeLimit, fileAccept, fileDragUpload } = widgetConfig || {};

  const transformAccept = () => {
    const acceptList = [];
    fileAccept?.split(',')?.forEach(accept => {
      if (accept) {
        acceptList.push(accept.startsWith('.') ? accept : `.${accept}`);
      }
    });
    return acceptList.length ? acceptList.join(',') : undefined;
  };

  const beforeUpload = (file) => {
    dataRecord?.setState(`file-${fieldCode}`, file);
    const fileSize = file.size / 1024 / 1024;
    if (fileSizeLimit && fileSize > fileSizeLimit) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.file.upload.limit', defaultMessage: '上传文件不能大于{limit}MB' }, { limit: fileSizeLimit }));
      return false;
    }
    dataRecord?.setState(`uploading-${fieldCode}`, true);
    return true;
  };

  const handleProgress = (percent) => {
    dataRecord?.setState(`progress-${fieldCode}`, percent > 99 ? 99 : percent);
    if (dataRecord?.getState(`cancel-${fieldCode}`)) {
      dataRecord?.setState(`progress-${fieldCode}`, 100);
      dataRecord?.setState(`cancel-${fieldCode}`, false);
      return 'abort';
    }
  };

  const getFile = () => {
    const file = dataRecord?.get(fieldCode);
    // 设计器附件，无论是否多附件，都存成数组
    let fileList = [];
    if (file) {
      try {
        fileList = JSON.parse(file).filter(item => !(item === null || item?.failed));
      } catch (e) {
        fileList = [file];
      }
    }
    if (fileList?.length !== dataRecord?.getState(`fileLength-${fieldCode}`)) {
      dataRecord?.setState(`progress-${fieldCode}`, 100);
    }
    dataRecord?.setState(`fileLength-${fieldCode}`, fileList?.length);
    return fileList;
  };
  // 预览
  const previewFile = (key) => {
    Modal.open({
      key: previewModalKey,
      title: intl.formatMessage({ id: 'lcr.components.desc.file.preview', defaultMessage: '附件预览' }),
      children: (
        <YqPreview fileKey={key} />
      ),
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
    });
  };
  // 下载附件
  const handleDownload = (url) => {
    let newUrl;
    const token = getCookieToken();
    if (url.includes('?')) {
      newUrl = `${url}&access_token=${token}`;
    } else {
      newUrl = `${url}?access_token=${token}`;
    }
    if (navigator?.userAgent?.includes('wxwork')) {
      copy(newUrl);
      Modal.info({
        title: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips', defaultMessage: '附件地址已复制到剪切板' }),
        children: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips.detail', defaultMessage: '由于企业微信限制，需要您粘贴附件地址到浏览器中下载附件。' }),
      });
    } else {
      FileSaver.saveAs(newUrl, getFileName(url));
    }
  };

  // 删除附件
  const handleDelete = (fileKey) => {
    if (fileFormat !== 'single') {
      const fileListStr = dataRecord?.get(fieldCode);
      try {
        const fileList = JSON.parse(fileListStr);
        dataRecord?.set(fieldCode, JSON.stringify(fileList.filter(key => key.fileKey !== fileKey)));
      } catch (e) {
        dataRecord?.set(fieldCode, '');
      }
    } else {
      dataRecord?.set(fieldCode, '');
    }
    dataRecord?.setState(`uploading-${fieldCode}`, false);
  };

  const showPlaceHolder = () => isEdit
    && (fileFormat === 'multiple' || (getFile(dataRecord, fieldCode)?.length === 0 && fileFormat === 'single'));

  const CurrentFile = observer((fileProps) => {
    const { currentFileKey, currentFileSize } = fileProps;
    return (
      <FileShower
        fileKey={currentFileKey}
      >
        {({ src }) => (
          dataRecord && src ? (
            <div
              tabIndex="-1"
              onClick={(e) => e.stopPropagation()}
              className={
                classnames({
                  'lc-page-loader-table-file-container': true,
                  'lc-page-loader-table-file-container-single': fileFormat === 'single',
                })
              }
            >
              <span>
                <Icon
                  type={getFileIcon(getFileType(currentFileKey)).iconType}
                  theme="filled"
                  fill={getFileIcon(getFileType(currentFileKey)).color}
                  size={fileFormat === 'single' ? 16 : 32}
                  className="file-icon"
                />
                <FileInfo fileName={getFileName(currentFileKey)} fileSize={currentFileSize} fileKey={currentFileKey} single={fileFormat === 'single'} />
              </span>
              <span className="file-actions">
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' })} theme="dark">
                  <Icon
                    type="PreviewOpen"
                    // size="20"
                    onClick={() => previewFile(currentFileKey, getFileType(currentFileKey))}
                    className="file-action"
                  />
                </Tooltip>
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })} theme="dark">
                  <Icon
                    type="Download"
                    // size="20"
                    className="file-action"
                    onClick={() => handleDownload(src)}
                  />
                </Tooltip>
                {isEdit ? (
                  <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })} theme="dark">
                    <Icon
                      type="delete"
                      // size="20"
                      onClick={() => handleDelete(currentFileKey)}
                      className="file-action"
                    />
                  </Tooltip>
                ) : null}
              </span>
            </div>
          ) : null
        )}
      </FileShower>
    );
  });

  const renderFiles = () => {
    const files = getFile();
    if (files) {
      if (fileFormat === 'single') {
        let fileInfo = {};
        if (typeof files[0] === 'object') {
          fileInfo = files[0];
        } else {
          // eslint-disable-next-line no-nested-ternary
          fileInfo = files[0] ? widgetType === 'Image' ? { fileKey: files[0] } : JSON.parse(files[0]) : {};
        }
        return <CurrentFile currentFileKey={fileInfo?.fileKey} currentFileSize={fileInfo?.fileSize} />;
      } else {
        if (files.length === 0) {
          return (
            <div style={{ textAlign: 'center', marginTop: '0.24rem' }}>
              <img alt="empty_page" src={emptyImg} style={{ width: '80px' }} />
              <p style={{ color: '#12274d', opacity: 0.45 }}>{intl.formatMessage({ id: 'zknow.common.model.noData', defaultMessage: '暂无数据' })}</p>
            </div>
          );
        }
        return files.map((file) => {
          let fileKey = file;
          let fileSize;
          if (typeof file === 'object') {
            fileKey = file.fileKey;
            fileSize = file.fileSize;
          }
          if (fileKey === null) {
            dataRecord?.setState(`progress-${fieldCode}`, 100);
            return null;
          }
          return (
            <CurrentFile currentFileKey={fileKey} currentFileSize={fileSize} />
          );
        });
      }
    }
    return '';
  };

  const getNewFileFlag = () => {
    return dataRecord?.getState(`progress-${fieldCode}`)
      && dataRecord?.getState(`progress-${fieldCode}`) !== 100;
  };

  const cancelUpload = () => {
    dataRecord?.setState(`cancel-${fieldCode}`, true);
  };

  const handleCancel = (e) => {
    e.stopPropagation();
    cancelUpload();
  };

  const UploadingFile = observer(({ flag, fileName, fileSize }) => {
    if (!flag) {
      return null;
    }

    return (
      <div
        tabIndex="-1"
        onClick={(e) => e.stopPropagation()}
        className={
          classnames({
            'lc-page-loader-table-file-container': true,
            'lc-page-loader-table-file-container-single': fileFormat === 'single',
          })
        }
      >
        <span>
          <Icon type={getFileIcon(getFileType(fileName)).iconType} theme="filled" fill={getFileIcon(getFileType(fileName)).color} size={fileFormat === 'single' ? 12 : 32} className="file-icon" />
          <FileInfo fileName={fileName} fileSize={fileSize} />
        </span>
        <span style={{ marginRight: 8, position: 'relative' }}>
          <span style={{ color: '#8C8C8C', marginRight: '.06rem', fontSize: '.12rem' }}>
            {`${Math.floor(dataRecord?.getState(`progress-${fieldCode}`))}%`}
          </span>
          <span style={{ position: 'relative', top: -2 }}>
            <Progress type="circle" percent={dataRecord?.getState(`progress-${fieldCode}`)} width={18} format={() => ''} />
          </span>
          <div className="cancel-upload">
            <Icon type="close-one" theme="filled" size="20" fill="#8C8C8C" onClick={(e) => handleCancel(e)} />
          </div>
        </span>
      </div>
    );
  });

  const SingleUpload = observer(() => {
    const files = getFile();
    return (
      <FileUploader
        // disabled={disabled}
        name={fieldCode}
        record={dataRecord}
        fileSize
        overrideProps={{
          // ...rest,
          name: undefined,
          accept: transformAccept(),
          className: 'lc-page-loader-upload',
          beforeUpload: (file) => beforeUpload(file, fileSizeLimit, dataRecord, fieldCode),
          onSuccess: (res) => {
            if (res.failed) {
              dataRecord?.setState(`progress-${fieldCode}`, 100);
            }
          },
          onProgress: ({ percent }) => {
            return handleProgress(percent);
          },
          onError: (error, res) => {
            if (res.failed) {
              dataRecord?.setState(`progress-${fieldCode}`, 100);
            }
          },
          multiple: fileFormat !== 'single',
        }}
        multiple={fileFormat !== 'single'}
        tenantId={tenantId}
      >
        <span onClick={(e) => getNewFileFlag() && e.stopPropagation()}>
          {isEdit && ((fileFormat === 'single' && files.length === 0 && dataRecord?.getState(`progress-${fieldCode}`) === 100) || fileFormat !== 'single') ? (
            <Button
              key="upload-btn"
              icon="UploadTwo"
              disabled={getNewFileFlag()}
              funcType={fileFormat === 'single' ? 'flat' : 'raised'}
              className={fileFormat === 'single' ? '' : 'lc-page-loader-table-file-upload-btn'}
            >
              {placeHolder || intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' })}
            </Button>
          ) : null}
        </span>
        {renderFiles()}
        <div className="new-file-wrapper">
          <UploadingFile flag={getNewFileFlag()} fileName={dataRecord?.getState(`file-${fieldCode}`)?.name} fileSize={dataRecord?.getState(`file-${fieldCode}`)?.size} />
        </div>
      </FileUploader>
    );
  });

  const MultipleUpload = observer(() => {
    return fileDragUpload ? (
      <DragUploader
        dragConfig={{
          showPlaceHolder,
          onlyDragArea: true,
          placeHolder: placeHolder || intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' }),
          dragTip: intl.formatMessage({ id: 'lcr.components.desc.file.upload.drag.tip', defaultMessage: '拖拽文件至此上传' }),
          openFolder: intl.formatMessage({ id: 'lcr.components.desc.file.upload.open.folder', defaultMessage: '打开文件夹' }),
        }}
        name={fieldCode}
        record={dataRecord}
        fileSize
        overrideProps={{
          name: undefined,
          accept: transformAccept(),
          className: 'lc-page-loader-upload',
          beforeUpload: (file) => beforeUpload(file),
          onSuccess: (res) => {
            if (res.failed) {
              dataRecord?.setState(`progress-${fieldCode}`, 100);
            } else {
              message.success(intl.formatMessage({ id: 'lcr.components.desc.upload.success', defaultMessage: '上传成功' }));
            }
          },
          onProgress: ({ percent }) => {
            return handleProgress(percent);
          },
          onError: (error, res) => {
            if (res.failed) {
              dataRecord?.setState(`progress-${fieldCode}`, 100);
            }
          },
          multiple: true,
        }}
        multiple
        tenantId={tenantId}
      >
        {renderFiles()}
        <div className="new-file-wrapper">
          <UploadingFile
            flag={getNewFileFlag()}
            fileName={dataRecord?.getState(`file-${fieldCode}`)?.name}
            fileSize={dataRecord?.getState(`file-${fieldCode}`)?.size}
          />
        </div>
      </DragUploader>
    ) : <SingleUpload />;
  });

  return fileFormat === 'single' ? <SingleUpload /> : <MultipleUpload />;
});

export const QuickAddFilterButton = observer((props) => {
  const {
    intl,
    onClick,
  } = props;
  return (
    <span style={{ margin: '.05rem .1rem 0 .05rem', cursor: 'pointer' }} onClick={onClick}>
      <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.add.filter', defaultMessage: '新建筛选器' })}>
        <Icon type="listAdd" size="20" />
      </Tooltip>
    </span>
  );
});

export const AdvancedSearchButton = observer((props) => {
  const {
    intl,
    onClick,
  } = props;
  return (
    <span style={{ margin: '.05rem .1rem 0 .05rem', cursor: 'pointer' }} onClick={onClick}>
      <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.condition.filter', defaultMessage: '条件筛选' })}>
        <Icon type="filter" size="20" />
      </Tooltip>
    </span>
  );
});

export const AdvancedSearchCondition = observer((props) => {
  const { dataSet, businessObjectId, tenantId, filterDataSet, intl } = props;
  const defalutCondition = [
    {
      groupId: uuidv4(),
      groupName: 'filter.condition.groupTitle',
      condition: 'AND',
      filters: [{
        filterUuid: uuidv4(),
        condition: 'AND',
      }],
    },
  ];
  const [condition, setCondition] = useState(defalutCondition);
  const [fieldTableData, setTableFieldData] = useState([]);
  const addGroupRef = useRef();
  useEffect(() => {
    async function loadFields() {
      const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?conditionFlag=true`);
      if (res?.failed) {
        message.error(res?.message);
        setTableFieldData([]);
      } else {
        setTableFieldData(res);
      }
    }
    if (businessObjectId) {
      loadFields();
    }
  }, [businessObjectId]);

  function getFilterFieldData(data) {
    condition.forEach(cond => {
      if (cond?.filters?.length) {
        cond?.filters.forEach(item => {
          if (!data.find(v => v.code === item.field)) {
            data.push({
              code: item.field,
              name: item.fieldName,
              widgetType: item.widgetType,
            });
          }
        });
      }
    });
    return data;
  }

  async function handleSearch() {
    await dataSet.query();
  }

  function handleSaveFilter() {
    filterDataSet.create({
      condition: JSON.stringify(condition),
      defaultFlag: false,
      icon: 'ad-product',
      businessObjectId,
    });
    const record = filterDataSet.current;
    const FormView = observer((formProps) => {
      return (
        <Form record={formProps.record} labelWidth="auto">
          <TextField name="name" />
          <SelectBox name="type" />
          {record?.get('type') === 'SPECIFIC_GROUP' && <Lov name="groupId" required={record?.get('type') === 'SPECIFIC_GROUP'} />}
        </Form>
      );
    });
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.save.as.filter', defaultMessage: '保存为筛选器' }),
      children: (
        <FormView record={record} />
      ),
      key: Modal.key(),
      destroyOnClose: true,
      onOk: async () => {
        if (await filterDataSet.validate()) {
          await filterDataSet.submit();
          filterDataSet.query();
          return true;
        }
        return false;
      },
      onClose: () => {
        filterDataSet.reset();
      },
    });
  }

  return (
    <span className="lc-table-condition-search">
      <div className="lc-table-condition-search-buttons">
        <Button onClick={handleSearch} color="#12274d">{intl.formatMessage({ id: 'zknow.common.button.exec', defaultMessage: '执行' })}</Button>
        <Button onClick={handleSaveFilter} color="#12274d">{intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}</Button>
        <span />
        <Button ref={addGroupRef} color="#12274d">{intl.formatMessage({ id: 'lcr.components.desc.add.condition', defaultMessage: '添加条件' })}</Button>
      </div>
      <div>
        <ExternalComponent
          system={{ scope: 'itsm', module: 'FilterCondition' }}
          conditionData={condition}
          tableId={businessObjectId}
          fieldTableData={getFilterFieldData(fieldTableData)}
          onChange={(data) => {
            setCondition(data);
            dataSet.setQueryParameter('__condition', JSON.stringify(data));
          }}
          sourceFrom="filter"
          isSider={false}
          canHideUser
          addGroupRef={addGroupRef}
        />
      </div>
    </span>
  );
});

export const IntlModal = observer((props) => {
  const {
    intl,
    record,
    modal,
    AppState: { currentLanguage: language },
  } = props;
  const [disabled, setDisabled] = useState(true);
  const [intlData, setIntlData] = useState();
  const intlFields = record?.dataSet.props.fields.reduce((prev, cur) => (cur.isIntl || (cur?.dynamicProps?.isIntl && cur?.dynamicProps?.isIntl({ record })) ? prev.concat(cur.name) : prev), []) || [];

  async function queryIntlInfo() {
    try {
      const res = await axios.get(`/lc/v1/engine/language?token=${encodeURIComponent(record.get('_token'))}`);
      if (res.failed) {
        message.error(res.message);
        return;
      }
      setIntlData(res);
      return res;
    } catch (err) {
      message.error(err.message);
    }
  }

  async function loadData() {
    const res = await queryIntlInfo();
    const data = {};
    Object.keys(res).forEach((field) => {
      const tls = res[field];
      const tlsData = {};
      tls.forEach((item) => {
        tlsData[item.code] = item.value;
      });
      data[field] = tlsData;
    });
    record.set('_tls', data);
    // 加载到多语言后，根据字段配置设置必填
    if (res) {
      Object.keys(res).forEach((field) => {
        res[field].forEach((langData) => {
          const { code } = langData;
          // 必填校验
          if (
            record.dataSet.getField(field)?.get('required')
            && language === code
          ) {
            record.getField(`_tls.${field}.${code}`).set('required', true);
          }
        });
      });
    }
  }

  useEffect(() => {
    intlFields.forEach((field) => {
      record.set(`_tls.${field}.${language}`, record.get(field));
    });
    loadData();
    return () => {
      record.reset();
      record.getState('_objectVersionNumber') && record.set('objectVersionNumber', record.getState('_objectVersionNumber'));
    };
  }, [record]);

  modal.handleOk(async () => {
    try {
      const tls = record.get('_tls');
      Object.keys(tls).forEach((field) => {
        Object.keys(tls[field]).forEach((lang) => {
          // 必填校验
          if (
            record.dataSet.getField(field)?.get('required')
            && language === lang
          ) {
            record.getField(`_tls.${field}.${lang}`).set('required', true);
          }
          // 多语言修改为null时，后端不会更新数据，处理成空字符串
          if (tls[field][lang] === null) {
            tls[field][lang] = '';
          }
          record.init(field, tls[field][language]);
        });
      });
      const validate = await record?.validate();
      if (!validate) {
        return false;
      }
      const res = await axios.put(`/lc/v1/engine/language?token=${encodeURIComponent(record.get('_token'))}`, tls);
      if (!res || !res?.failed) {
        setDisabled(true);
      }
      record.dataSet.query();
      queryIntlInfo();
      return false;
    } catch (err) {
      return false;
    }
  });

  function handleCancel() {
    intlFields.forEach((field) => {
      record.set(`_tls.${field}.${language}`, record.get(field));
    });
    const data = {};
    Object.keys(intlData).forEach((field) => {
      const tls = intlData[field];
      const tlsData = {};
      tls.forEach((item) => {
        tlsData[item.code] = item.value;
      });
      data[field] = tlsData;
    });
    setDisabled(true);
    return false;
  }

  useEffect(() => {
    modal.update({
      footer: (okBtn, cancelBtn) => {
        const myCancelBtn = <Button funcType="raised" onClick={handleCancel}>{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>;
        return disabled ? [
          <YQButton
            funcType="raised"
            color="primary"
            icon="edit"
            key="edit"
            onClick={() => setDisabled(false)}
          >
            {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
          </YQButton>,
        ] : [
          okBtn, myCancelBtn,
        ];
      },
    });
  }, [disabled]);

  const ITextField = disabled ? Output : TextField;

  const renderField = useCallback(
    (field) => {
      return intlData?.[field]?.map((item) => {
        const { code, name } = item;
        return (
          <ITextField
            name={`_tls.${field}.${code}`}
            label={name}
            key={`${field}-${code}`}
            required={
              language === code
              && record.dataSet.getField(field)?.get('required')
            }
          />
        );
      });
    },
    [intlData, disabled],
  );

  const prefixCls = 'yqcloud-components-table-hover-action';
  const mrStyle = { marginRight: '0.16rem' };
  return (
    <div className={`${prefixCls}-modal`}>
      {uniq(intlFields)?.map((field) => (
        <div className={`${prefixCls}-intl-field`}>
          <h4> <Icon style={mrStyle} type="icon-document" /> {intl.formatMessage({ id: 'zknow.common.model.field', defaultMessage: '字段' })}：{record.dataSet.getField(field)?.get('label')}</h4>
          <Form record={record} labelWidth={70} labelLayout="horizontal">
            {renderField(field)}
          </Form>
        </div>
      ))}
    </div>
  );
});
