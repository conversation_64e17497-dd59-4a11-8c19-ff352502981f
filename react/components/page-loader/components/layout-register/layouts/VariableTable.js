import React, { useState, useEffect, useMemo } from 'react';
import classnames from 'classnames';
import { v4 as uuidv4 } from 'uuid';
import { observer } from 'mobx-react-lite';
import moment from 'moment';
import { ClickText, TableHoverAction, withErrorBoundary } from '@zknow/components';
import { Table, TextField, DataSet, Button, DateTimePicker, DatePicker, TimePicker, NumberField } from 'choerodon-ui/pro';
import { deltaToHtml } from '@/utils';
import './Table.less';

const { Column } = Table;

const WIDGET_TYPE_MAP = {
  Switch: 'boolean',
  CheckBox: 'boolean',
  EmailField: 'email',
  Url: 'url',
  MasterDetail: 'object',
  NumberField: 'number',
  FloatNumber: 'number',
};
const DATE_FIELDS = ['DateTime', 'Date', 'Time'];
const CUSTOM_FIELDS = ['DateTime', 'Date', 'Time', 'Input', 'TextArea', 'NumberField', 'FloatNumber'];
function getTypeByWidgetType(widgetType) {
  return WIDGET_TYPE_MAP[widgetType] || 'auto';
}

const DEFAULT_DATE_FORMAT = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

const Index = observer(({ record, className, context, ...rest }) => {
  const {
    dsManager, intl, mode, viewDataSet, columnDataSet, tableFilterId, pageRef, parentKey,
  } = context;
  const fields = record.getCascadeRecords('widgetConfig.variableFields');
  const filterFlag = record.get('widgetConfig.filterFlag');
  // 首列点击跳转
  const tableLinkFlag = record.get('widgetConfig.tableLinkFlag');
  const tableLinkViewType = record.get('widgetConfig.tableLinkViewType');
  const tableLinkViewSize = record.get('widgetConfig.tableLinkViewSize');
  const tableLinkViewId = record.get('widgetConfig.viewId');
  const tableLinkViewName = record.get('widgetConfig.viewName');
  // 表格视图
  const columnViews = viewDataSet?.current?.get('personalColumns') || [];
  const defaultColumnViewId = columnViews.find(v => v.defaultFlag)?.id;
  const [columnViewId, setColumnViewId] = useState(defaultColumnViewId);
  // 表格筛选
  const tableFilters = viewDataSet?.current?.get('filters') || [];
  const defaultFilterId = tableFilters.find(v => v.defaultFlag)?.id;
  const [filterId, setFilterId] = useState(tableFilterId || defaultFilterId);
  const [depsFields, setDepsFields] = useState([]);
  const [depsData, setDepsData] = useState([]);
  const [fieldMap, setFieldMap] = useState({});
  // 兼容新版服务项视图
  const parentDs = dsManager?.formDataSet || pageRef?.current?.formDataSet;

  useEffect(() => {
    const formDataRecord = parentDs?.current;
    if (formDataRecord && record.get('code')) {
      let depsDataCache = [];
      if (parentKey) {
        const tableData = formDataRecord.get(parentKey);
        depsDataCache = tableData[record.get('code')];
      } else {
        depsDataCache = formDataRecord.get(record.get('code'));
      }
      setDepsData(depsDataCache);
    }
  }, [parentKey, parentDs?.current]);

  useEffect(() => {
    if (tableFilterId) {
      setFilterId(tableFilterId);
      refresh(tableFilterId);
    }
  }, [tableFilterId]);

  // 新建dataSet
  const ds = useMemo(() => new DataSet({ selection: false, paging: false, fields: depsFields, cacheSelection: true, data: depsData }), [depsFields, depsData]);

  const transformField = (field) => {
    const { name, code, widgetType, defaultValue, maxLength, requiredType, editType } = field.toData();
    const { currentTimeFlag, format } = field.toData().widgetConfig;
    const presetProps = {
      name: code,
      label: name,
      type: getTypeByWidgetType(widgetType),
      // defaultValue,
      // isIntl: !!tlFlag,
      // idField: 'id',
      editor: true,
      dynamicProps: {
        required: () => {
          const required = requiredType === 'ALWAYS_REQUIRED';
          return required;
        },
        disabled: () => {
          const disabled = editType === 'ALWAYS_NOT_EDIT';
          return disabled;
        },
      },
    };
    if (maxLength) {
      presetProps.maxLength = maxLength;
    }
    // 转换日期字段默认值
    if (DATE_FIELDS.includes(widgetType)) {
      // 由于choerodon-ui的日期格式比较特殊，需要单独处理
      const realFormat = format?.replace('yyyy', 'YYYY')?.replace('mm', 'MM')?.replace('dd', 'DD')?.replace(':MM', ':mm') || DEFAULT_DATE_FORMAT[widgetType];
      if (realFormat) {
        presetProps.format = realFormat;
        presetProps.transformResponse = (value) => {
          return value ? moment(value).format(realFormat) : value;
        };
        presetProps.transformRequest = (value) => {
          return value ? moment(value).format(DEFAULT_DATE_FORMAT[widgetType]) : value;
        };
      }
      if (currentTimeFlag) {
        presetProps.defaultValue = moment();
        if (realFormat) {
          presetProps.defaultValue = moment().format(realFormat);
        }
      }
    }
    return presetProps;
    // return { fieldName: code, fieldProps: presetProps };
  };

  useEffect(() => {
    const dsFieldConfig = [{ name: 'id', type: 'string' }];
    if (fields) {
      (fields || []).forEach((item) => {
        const fieldDto = transformField(item);
        fieldMap[fieldDto.name] = null;
        dsFieldConfig.push(fieldDto);
        // ds.addField(fieldDto.fieldName, fieldDto.fieldProps);
      },);
    }
    setDepsFields(dsFieldConfig);
  }, []);

  // 传递变量类型表格的数据

  useEffect(() => {
    if (pageRef && pageRef?.current) {
      const variableTableDataRef = (ds.toData() || []).map((v) => {
        if (Object.keys(v).length > 0) {
          return v;
        }
        return null;
      }).filter(i => i);
      // 兼容新版服务项组件
      if (parentDs?.current) {
        parentDs.current.set(record.get('code'), variableTableDataRef);
      }
      if (pageRef.current.variableTableData) {
        pageRef.current.variableTableData[`${record.get('code')}`] = variableTableDataRef;
      } else {
        pageRef.current.variableTableData = {};
        pageRef.current.variableTableData[`${record.get('code')}`] = variableTableDataRef;
      }
    }
  }, [JSON.stringify(ds.toData())]);

  function refresh(newFilterId = filterId, newColumnViewId = columnViewId) {
    ds.setQueryParameter('filter_id', newFilterId);
    ds.setQueryParameter('person_column_id', newColumnViewId);
    ds.query();
  }

  useEffect(() => {
    refresh();
  }, []);

  /**
     * 渲染表格可点击列
     * @param props
     * @param widgetType
     * @returns {*}
     */
  function renderTableLink(props, widgetType) {
    const { record: current, name } = props;
    const view = {
      viewSize: tableLinkViewSize,
      openType: tableLinkViewType,
      viewId: tableLinkViewId,
      viewName: tableLinkViewName,
    };
    const text = widgetType === 'RichText' ? deltaToHtml(current.get(name)) : current.get(name);
    return (
      <ClickText
        record={current}
        // onClick={() => openView(view, current.dataSet, current)}
        valueField={name}
      >
        {text || '-'}
      </ClickText>
    );
  }

  // 渲染时间类型的组件的值
  const renderDateComponentValue = (current, name, format) => {
    return current.get(name) ? moment(current.get(name), format) : undefined;
  };

  // 时间类型的组件的onChange方法
  const handleDateComponentChange = (current, name, timeValue, format) => {
    if (timeValue) {
        current?.set(name, timeValue?.format(format));
    }
  };
  function renderFieldRc(props, widgetType) {
    const { record: current, name, text } = props;
    if (current.getState('editing')) {
      let component = '';
      switch (widgetType) {
        case 'DateTime':
          component = <DateTimePicker
            mode="dateTime"
            format="YYYY-MM-DD HH:mm:ss"
            range={false}
            value={renderDateComponentValue(current, name, 'YYYY-MM-DD HH:mm:ss')}
            onChange={(timeValue, oldValue) => handleDateComponentChange(current, name, timeValue, 'YYYY-MM-DD HH:mm:ss')}
          />;
          break;
        case 'Date':
          component = <DatePicker
            mode="date"
            format="YYYY-MM-DD"
            type="date"
            range={false}
            value={renderDateComponentValue(current, name, 'YYYY-MM-DD')}
            onChange={(timeValue, oldValue) => handleDateComponentChange(current, name, timeValue, 'YYYY-MM-DD')}
          />;
          break;
        case 'Time':
          component = <TimePicker
            format="HH:mm:ss"
            type="time"
            range={false}
            // value={renderDateComponentValue(current, name, 'HH:mm:ss')}
            onChange={(timeValue, oldValue) => handleDateComponentChange(current, name, timeValue, 'HH:mm:ss')}
          />;
          break;
        case 'NumberField':
          component = <NumberField record={current} name={name} />;
          break;
        case 'FloatField':
          component = <TextField record={current} name={name} />;
          break;
        default:
          component = <TextField record={current} name={name} />;
          break;
      }
      return component;
    } else {
      return text;
    }
  }
  /**
     * 渲染把表格列
     * @param field
     * @param index
     * @returns {*}
     */
  function renderColumn(field = {}, index) {
    const { code, name, autoWidthFlag, minWidth, width, widgetType } = field;
    const columnProps = {
      name: code,
      header: name,
    };
    if (widgetType === 'RichText') {
      columnProps.renderer = ({ value }) => deltaToHtml(value);
    }
    if (CUSTOM_FIELDS.includes(widgetType)) {
      columnProps.renderer = (props) => renderFieldRc(props, widgetType);
    }
    if (index === 0) {
      columnProps.lock = true;
      if (tableLinkFlag) {
        columnProps.renderer = (props) => renderTableLink(props, widgetType);
      }
    }
    return (
      <Column
        {...columnProps}
        tooltip="overflow"
        // align="right"
        // editor={r => r?.getState('editing')}
      />
    );
  }

  /**
     * 根据视图渲染表格列
     * @returns {*[]|*}
     */
  function renderColumns() {
    const currentViewJson = columnViews.find(v => v.id === columnViewId)?.jsonData;
    if (currentViewJson) {
      try {
        const columns = JSON.parse(currentViewJson);
        return columns.map((path, index) => {
          let fieldCode;
          let fieldWidth = {};
          if (typeof path === 'string') {
            fieldCode = path;
          } else {
            fieldCode = path.code;
            fieldWidth = {
              width: path.width,
              minWidth: path.minWidth,
              autoWidthFlag: path.autoWidthFlag,
            };
          }
          const field = columnDataSet.find(f => f.get('objectFieldPath') === fieldCode);
          const fieldData = {
            code: field?.get('objectFieldPath'),
            name: field?.get('label'),
            widgetType: field?.get('widgetType'),
            ...(field?.toData() || {}),
            ...fieldWidth,
          };
          return renderColumn(fieldData, index);
        });
      } catch (e) {
        return fields?.map((field, index) => renderColumn(field?.toData(), index));
      }
    }
    return fields?.map((field, index) => renderColumn(field?.toData(), index));
  }

  async function handActionDelete(evt, dataSet, deleteId) {
    evt.stopPropagation();
    const deleteRecord = dataSet.find(r => r.get('id') === deleteId);
    if (deleteRecord) {
      await dataSet.delete(deleteRecord);
      dataSet.query();
      viewDataSet.query();
    }
  }

  const handleAction = (type) => {
    if (type === 'CREATE' && mode !== 'PREVIEW') {
      const uuid = uuidv4();
      const newRecord = ds.create({ id: uuid });
      newRecord.setState('editing', true);
    }
  };

  /**
     * 渲染按钮
     * @returns {*[]}
     */
  const getButtons = useMemo(() => {
    const btns = [
      <Button key="create" funcType="raised" color="primary" onClick={() => handleAction('CREATE')}>
        {intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' })}
      </Button>,
    ];
    return btns;
  }, [ds]);

  /**
     * 渲染表格行按钮
     * @param dataSet
     * @param current
     * @returns {*}
     */
  const renderTableAction = ({ dataSet, record: current }) => {
    let actions = [];
    if (current?.getState('editing')) {
      actions = [
        {
          name: intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' }),
          icon: 'check',
          key: 'check',
          onClick: async () => {
              dataSet.current?.setState('editing', false);
          },
        },
        {
          name: intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' }),
          icon: 'close',
          key: 'check',
          onClick: async () => {
            const cacheFieldValue = dataSet?.current?.getState('cacheValue');
            // 如果有缓存，说明是编辑数据，需要恢复
            if (cacheFieldValue) {
              Object.keys(cacheFieldValue).forEach((v) => {
                dataSet.current?.set(v, cacheFieldValue[v]);
              });
              dataSet.current.setState('editing', false);
            } else {
              // 如果没有缓存，说明是新增数据，需要删除
              dataSet.remove(current);
            }
          },
        },
      ];
      return <TableHoverAction key="action_1" record={current} actions={actions} intlBtnIndex={0} lcMultilanguage />;
    } else {
      actions = [
        {
          name: intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' }),
          icon: 'icon-edit',
          key: 'edit',
          onClick: () => {
            const currentValue = ds.current?.toData();
              ds.current?.setState('cacheValue', currentValue);
              ds.current?.setState('editing', true);
          },
        },
        {
          name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
          icon: 'delete',
          key: 'delete',
          onClick: () => {
            ds.delete(current, false);
            const deleteAfterData = ds.toData().filter((v) => v.id !== current.get('id'));
            ds.loadData(deleteAfterData);
          },
        },
      ];
      return <TableHoverAction key="action_2" record={current} actions={actions} intlBtnIndex={0} lcMultilanguage />;
    }
  };

  return (
    <div className={classnames('lc-page-loader-table', 'lc-page-loader-variable-table', className)} {...rest}>
      <Table
        labelLayout="float"
        dataSet={ds}
        queryBar={filterFlag ? 'comboBar' : 'none'}
        buttons={getButtons}
        queryBarProps={{
          title: record.get('name'),
        }}
      >
        {renderColumns()}
        <Column
          width={250}
          renderer={renderTableAction}
          lock="right"
          tooltip="none"
        />
      </Table>
    </div>
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutVariableTable */
