import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import RelevantKnowledge from '@/renderer/relevant-knowledge';

const Index = observer(({ record, dataSet, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  // 共享服务项生成的单据不展示关联知识
  if (udmFlag) {
    return <div />;
  }

  return (
    <RelevantKnowledge
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
      viewRecord={record}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutRelevantKnowledge */
