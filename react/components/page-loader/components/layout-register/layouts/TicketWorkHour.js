import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import TicketWorkHour from '@/renderer/work-hour';

const Index = observer(({ dataSet, context }) => {
  const { tenantId, instanceId, viewDataSet, intl, udmFlag } = context;
  const businessObjectCode = viewDataSet.current?.get('businessObjectCode');
  const viewCode = viewDataSet.current?.get('code');

  // 共享服务项生成的单据不展示工时组件
  if (udmFlag) {
    return <div />;
  }

  return (
    <TicketWorkHour
      intl={intl}
      tenantId={tenantId}
      ticketId={instanceId}
      ticketType={businessObjectCode}
      viewCode={viewCode}
      formDataSet={dataSet}
      viewDataSet={viewDataSet}
    />
  );
});

export default withErrorBoundary(Index);

/* externalize: LcLayoutTicketWorkHour */
