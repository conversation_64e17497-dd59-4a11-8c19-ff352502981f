/* eslint-disable react/button-has-type */
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2021-12-13 16:47:22
 * @Description:
 */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useContext, useMemo } from 'react';
import ReactFlow, { ReactFlowProvider, MarkerType } from 'react-flow-renderer';
import { observer } from 'mobx-react-lite';
import { Spin } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { changeInitData, updateInitData } from './utils';
import CustomNodeComponent from './components/status-node';
import Store from './stores';
import './index.less';

const LEGENDS = [
  {
    key: 'done',
    borderColor: '#1AB335',
    color: '#E8F7EA',
    defaultMessage: '已流经节点',
  }, {
    key: 'future',
    borderColor: '#6B7285',
    color: 'rgba(107, 114, 133, 0.1)',
    defaultMessage: '未流经节点',
  }, {
    key: 'current',
    borderColor: '#2979FF',
    color: '#F2F7FF',
    defaultMessage: '当前节点',
  },
];

const LayoutFlow = () => {
  const context = useContext(Store);
  const {
    statusMindDataSet,
    stepsDataSet,
    ticketId,
    prefixCls,
    formDataSet,
    ContentHeight,
    BoundingClientRect,
    handleFullScreen,
    title,
    vertical,
    fullscreen,
    intl,
    defaultData = [],
    fitView = true,
  } = context;
  const [elements, setElements] = useState(defaultData);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      if (stepsDataSet?.status === 'ready') {
        stepsDataSet?.setState('currentClickNode', false);
        refresh();
      }
    }
  }, [stepsDataSet?.status, formDataSet?.status]);

  const arrow = useMemo(() => ({ type: MarkerType.ArrowClosed, width: 18, height: 18, color: '#000' }), []);

  useEffect(() => {
    // 一些线条属性
    const opacity = 0.6;
    const _labelStyleLight = { fill: '#595959', fillOpacity: 1 };
    const _labelStyleDark = { fill: '#DEDEDE', fillOpacity: opacity };
    const _styleLight = { stroke: '#595959', strokeOpacity: 1 };
    const _styleDone = { stroke: '#1AB335', strokeOpacity: 1 };

    const selectedId = stepsDataSet?.getState('currentClickNode');
    if (!selectedId && elements.length === 0) return;
    const statusMindData = statusMindDataSet?.current?.toData();
    const eleList = elements?.length > 0
      ? updateInitData(statusMindData, elements)
      : changeInitData(statusMindData, stepsDataSet.toData(), vertical);
    const list = eleList.map((i) => {
      if (i.type === 'step') {
        i.markerEnd = arrow;
        i.animated = false;
        if (i.node === 'DONE') {
          /* 已完成的线就用黑色原生 */
          i.style = i.style ? { ...i.style, ..._styleDone } : { ..._styleDone };
          i.labelStyle = i.labelStyle ? { ...i.labelStyle, ..._labelStyleLight } : { ..._labelStyleLight };
          i.labelBgStyle = { fillOpacity: 1 };
        } else if (i.source === selectedId) {
          i.style = i.style ? { ...i.style, ..._styleLight } : { ..._styleLight };
          i.labelStyle = i.labelStyle ? { ...i.labelStyle, ..._labelStyleLight } : { ..._labelStyleLight };
          i.labelBgStyle = { fillOpacity: 1 };
        } else {
          i.style = i.style ? { ...i.style, ..._styleLight } : { ..._styleLight };
          i.labelStyle = i.labelStyle ? { ...i.labelStyle, ..._labelStyleLight } : { ..._labelStyleLight };
          i.labelBgStyle = { fillOpacity: 1 };
        }
      }
      return i;
    });
    setElements(list);
    setLoading(false);
  }, [stepsDataSet?.getState('currentClickNode'), statusMindDataSet.status]);

  // 刷新
  async function refresh() {
    await statusMindDataSet.query();
    stepsDataSet?.setState('currentClickNode', formDataSet?.current?.get('state_id')?.id || formDataSet?.current?.get('state_id'));
  }

  function getDefaultPosition() {
    if (BoundingClientRect) {
      const StepClientRectWidth = document.getElementsByClassName('step-renderer-main')[0]?.getBoundingClientRect()?.width;
      const x = (BoundingClientRect?.width - StepClientRectWidth) / 2;
      return [x, vertical ? 70 : 0];
    }
    return [100, vertical ? 70 : 0];
  }

  const style = useMemo(() => {
    let height = '100%';
    if (fullscreen) {
      height = 'calc(100% - 40px)';
    } else if (vertical) {
      height = '100%';
    } else {
      height = `${ContentHeight}px`;
    }

    return {
      width: '100%',
      height,
    };
  }, [vertical, fullscreen, ContentHeight]);

  const nodeTypes = {
    status: (e) => (
      <CustomNodeComponent
        currentStateId={stepsDataSet?.getState('currentClickNode')}
        stepsDataSet={stepsDataSet}
        statusMindDataSet={statusMindDataSet}
        {...e}
      />
    ),
  };

  if (loading) return <div className={prefixCls} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}><Spin /></div>;

  function renderLegendItem(legend) {
    return (
      <div className={`${prefixCls}-actions-legend-item`} key={legend.key}>
        <div
          className={`${prefixCls}-actions-legend-block`}
          style={{ borderColor: legend.borderColor, background: legend.color }}
        />
        {intl.formatMessage({ id: `lcr.components.desc.statusMind.${legend.key}`, defaultMessage: legend.defaultMessage })}
      </div>
    );
  }

  function renderLegend() {
    return (
      <div
        className={`${prefixCls}-actions-legend ${vertical ? 'vertical' : ''} ${fullscreen ? 'fullFlow' : ''}`}
      >
        {LEGENDS.map(renderLegendItem)}
      </div>
    );
  }

  function renderActions() {
    return (
      <div className={`${prefixCls}-actions ${vertical ? 'vertical' : ''}`}>
        <div className={`${prefixCls}-actions-content`}>
          {handleFullScreen
            ? (
              <Button
                key="fullscreen"
                icon="FullScreen"
                funcType="raised"
                color="secondary"
                className={`${prefixCls}-actions-fullscreen`}
                onClick={handleFullScreen}
              />
            ) : null}
        </div>
      </div>
    );
  }

  return (
    <div className={`${prefixCls} ${(vertical || fullscreen) ? 'height-100' : ''}`}>
      <div className={`${prefixCls}-headers`}>
        {fullscreen ? '' : title}
      </div>
      {(!vertical || fullscreen) ? renderLegend() : renderActions()}
      <ReactFlowProvider>
        <ReactFlow
          style={style}
          nodes={elements?.filter(i => i.type === 'status')}
          edges={elements?.filter(i => i.type === 'step')}
          connectionLineType="smoothstep" // 这个没用，用node的type控制
          nodeTypes={nodeTypes}
          defaultZoom={0.6}
          defaultPosition={getDefaultPosition()}
          fitView={fitView}
        />
      </ReactFlowProvider>
      {vertical ? (fullscreen ? '' : renderLegend()) : renderActions()}
    </div>
  );
};

export default observer(LayoutFlow);
