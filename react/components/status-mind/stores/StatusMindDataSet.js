/*
 * @Author: x<PERSON><PERSON>ya
 * @Date: 2021-12-14 14:38:36
 */
import { message } from 'choerodon-ui/pro';
import axios from 'axios';
import { DataSet } from 'choerodon-ui/pro';

const StatusMindDataSet = ({
  ticketId,
  businessObjectCode,
  tenantId,
  intl,
  businessObjectId,
  stepsDataSet,
}) => {
  const urlPrefix = `/itsm/v1/${tenantId}/stage`;
  const stages = stepsDataSet.map(stage => ({ id: stage.get('id'), orderSeq: stage.get('orderSeq'), index: stage.index }));
  return {
    autoQuery: false,
    paging: false,
    transport: {
      read: {
        url: `${urlPrefix}/flow?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}&businessObjectId=${businessObjectId}`,
        method: 'get',
        transformResponse(response) {
          try {
            const res = JSON.parse(response);
            if (res?.failed) {
              return response;
            } else {
              res.NODE = res.stateList;
              res.EDGES = res.relationList;
              // res.EDGES = res.relationList.filter((edge) => {
              //   // 判断连线状态的阶段是否连续
              //   const sourceNode = res.stateList.find(state => state.id === edge.source);
              //   const targetNode = res.stateList.find(state => state.id === edge.target);
              //   const sourceStageIndex = stages.find(stage => stage.id === sourceNode.stageId).index;
              //   const targetStageIndex = stages.find(stage => stage.id === targetNode.stageId).index;
              //   if (Math.abs(sourceStageIndex - targetStageIndex) === 1) {
              //     return true;
              //   }
              //   return false;
              // });
              delete res.relationList;
              delete res.stateList;
              return res;
            }
          } catch (e) {
            return response;
          }
        },
      },
    },
    children: {
      EDGES: new DataSet({ paging: false }),
      NODE: new DataSet({ paging: false }),
    },
    events: {

    },
  };
};

export default StatusMindDataSet;
