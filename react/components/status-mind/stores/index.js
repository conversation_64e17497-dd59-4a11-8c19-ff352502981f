import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { DataSet, message } from 'choerodon-ui/pro';
import StatusMindDataSet from './StatusMindDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(observer((props) => {
  const {
    children,
    intl,
    AppState: { currentMenuType: { organizationId: tenantId } },
    activeKey,
    viewDataSet,
    formDataSet,
    stepsDataSet,
  } = props;

  const intlPrefix = 'lc.components.statusMind';
  const prefixCls = 'statusMind-renderer';
  const ContentHeight = 220;

  const ticketId = formDataSet?.current?.get('id');
  const { businessObjectCode, businessObjectId } = viewDataSet?.current?.toData() || {};

  // 状态集合的DataSet
  const statusMindDataSet = useMemo(() => new DataSet(StatusMindDataSet({
    ticketId,
    businessObjectCode,
    tenantId,
    intl,
    businessObjectId,
    stepsDataSet,
  })), [ticketId, businessObjectCode, tenantId, businessObjectId]);

  const value = {
    ...props,
    statusMindDataSet,
    stepsDataSet,
    activeKey,
    ticketId,
    businessObjectCode,
    intlPrefix,
    prefixCls,
    tenantId,
    formDataSet,
    ContentHeight,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
