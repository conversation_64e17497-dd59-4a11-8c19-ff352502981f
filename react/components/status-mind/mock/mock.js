/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-12-22 15:51:41
 * @Description: 接口模拟数据
 */

const edgeType = 'default';
const animated = true;

const stagesData = [
  {
    id: '1',
    name: 'NEW',
    sort: 1,
  },
  {
    id: '2',
    name: 'IN PROCESS',
    sort: 2,
  },
  {
    id: '3',
    name: 'RESOLVED',
    sort: 3,
  },
  {
    id: '4',
    name: 'END',
    sort: 4,
  },
];

const initialData = {
  NODE: [
    {
      id: 'new', // 每个节点的唯一key就好了，数字，code都行
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 0, // 有多少个入口
      sourceHandle: 2, // 有多少个出口
      name: 'New', // 状态名字
      color: '#2EA2FF', // 颜色
      stage: '1', // 关联的阶段
      nodeSort: 1,
      // draggable: false, // 写死，不让拖动
    },
    {
      id: 'inProcess',
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 3, // 有多少个入口
      sourceHandle: 3, // 有多少个出口
      name: 'In Process', // 状态名字
      color: '#FF9500', // 颜色
      stage: '2', // 关联的阶段
      nodeSort: 2,
      // draggable: false, // 写死，不让拖动
    },
    {
      id: 'hold',
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 1, // 有多少个入口
      sourceHandle: 2, // 有多少个出口
      name: 'On Hold', // 状态名字
      color: '#6454F4', // 颜色
      nodeSort: 3,
      stage: '2', // 关联的阶段
      // draggable: false, // 写死，不让拖动
    },
    {
      id: 'solved',
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 2, // 有多少个入口
      sourceHandle: 2, // 有多少个出口
      name: 'Resolved', // 状态名字
      color: '#6454F4', // 颜色
      stage: '3', // 关联的阶段
      nodeSort: 4,
      // draggable: false, // 写死，不让拖动
    },
    {
      id: 'closed',
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 1, // 有多少个入口
      sourceHandle: 0, // 有多少个出口
      name: 'Closed', // 状态名字
      color: '#6B7285', // 颜色
      stage: '4', // 关联的阶段
      nodeSort: 5,
      // draggable: false, // 写死，不让拖动
    },
    {
      id: 'Cancelled',
      type: 'status', // 类型为固定值，适用于我写的节点组件
      targetHandle: 2, // 有多少个入口
      sourceHandle: 0, // 有多少个出口
      name: 'Canceled', // 状态名字
      color: '#939AAA', // 颜色
      stage: '4', // 关联的阶段
      nodeSort: 6,
      // draggable: false, // 写死，不让拖动
    },
  ],
  EDGES: [
    { 
      id: 'new-inProcess', 
      source: 'new', 
      target: 'inProcess', 
      type: edgeType, 
      animated,
      label: 'Start processing',
      arrowHeadType: 'arrow',
    },
    { 
      id: 'new-Cancelled', 
      source: 'new',
      target: 'Cancelled',
      type: edgeType,
      animated, 
    },
    { 
      id: 'inProcess-solved', 
      source: 'inProcess',
      target: 'solved',
      type: edgeType,
      animated,
      label: 'Submit solution', 
    },
    { 
      id: 'inProcess-hold', 
      source: 'inProcess',
      target: 'hold',
      type: edgeType,
      animated, 
    },
    { 
      id: 'inProcess-Cancelled', 
      source: 'inProcess',
      target: 'Cancelled',
      type: edgeType,
      animated, 
    },
    { 
      id: 'solved-inProcess', 
      source: 'solved',
      target: 'inProcess',
      type: edgeType,
      animated,
    },
    { 
      id: 'solved-closed', 
      source: 'solved',
      target: 'closed',
      type: edgeType,
      animated,
      label: 'Confirm that it can be closed',
      arrowHeadType: 'arrowclosed', 
    },
    { 
      id: 'hold-inProcess', 
      source: 'hold',
      target: 'inProcess',
      type: edgeType,
      animated, 
    },
    { 
      id: 'hold-Cancelled', 
      source: 'hold',
      target: 'Cancelled',
      type: edgeType,
      animated, 
    },
  ],
};

export {
  stagesData,
  initialData,
};
