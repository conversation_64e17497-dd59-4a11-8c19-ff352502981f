/* eslint-disable react/button-has-type */
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2021-12-13 16:47:22
 * @Description: 
 */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useCallback } from 'react';
import ReactFlow, {
  ReactFlowProvider,
  addEdge,
  removeElements,
  isNode,
  Handle,
  MiniMap,
  Background,
  Controls,
} from 'react-flow-renderer';
import dagre from 'dagre';

import '../index.less';

const edgeType = 'default';
const animated = true;
const INIT_DATE = [
  {
    id: 1, // 每个节点的唯一key就好了，数字，code都行
    type: 'status', // 类型为固定值，适用于我写的节点组件
    data: {
      id: 1,
      name: '1 HAO WEI', // 状态名字
      color: '#2EA2FF', // 颜色
    },
    position: { x: 0, y: 0 },
  },
  {
    id: 2, // 每个节点的唯一key就好了，数字，code都行
    type: 'status', // 类型为固定值，适用于我写的节点组件
    data: {
      id: 2,
      name: '2 HAO WEI', // 状态名字
      color: '#FF9500', // 颜色
    },
    position: { x: 250, y: 0 },
  },
  {
    id: 3, // 每个节点的唯一key就好了，数字，code都行
    type: 'status', // 类型为固定值，适用于我写的节点组件
    data: {
      id: 3,
      name: '3 HAO WEI', // 状态名字
      color: '#6454F4', // 颜色
    },
    position: { x: 250, y: 150 },
  },
  { 
    id: '1-2', 
    source: 1, 
    target: 2, 
    type: edgeType, 
    animated,
    label: 'KAISHICHULI',
    arrowHeadType: 'arrow',
    sourceHandle: 'right-source-1',
    targetHandle: 'left-target-1',
  },
  { 
    id: '2-3', 
    source: 2, 
    target: 3, 
    type: edgeType, 
    animated,
    arrowHeadType: 'arrow',
    label: 'GUOQU',
    sourceHandle: 'bottom-source-1',
    targetHandle: 'top-target-1',
  },
  { 
    id: '3-2', 
    source: 3, 
    target: 2, 
    type: edgeType, 
    animated,
    label: 'FANHUI',
    arrowHeadType: 'arrow',
    sourceHandle: 'top-source-2',
    targetHandle: 'bottom-target-2',
  },
];

const SHOW_DATA = INIT_DATE;
const dagreGraph = new dagre.graphlib.Graph();
dagreGraph.setDefaultEdgeLabel(() => ({}));

const LayoutFlow = () => {
  // const [elements, setElements] = useState(layoutedElements);
  const [elements, setElements] = useState(SHOW_DATA);
  const [testCount, setTestCount] = useState(0);
  const onConnect = (params) => setElements((els) => addEdge({ ...params, type: 'smoothstep', animated: true }, els));
  const onElementsRemove = (elementsToRemove) => setElements((els) => removeElements(elementsToRemove, els));

  const onLayout = () => {
    setTestCount(testCount + 1);
  };

  const style = {
    width: '100%',
    height: 400,
  };
  
  const CustomNodeComponent = (e) => {
    const { data } = e;
    if (data.id === 1) {
      return (
        <div className="status-node" style={{ borderColor: data.color }}>
          <Handle
            type="source"
            position="right"
            id="right-source-1"
            style={{ top: '50%', borderRadius: 0 }}
          />
          <div>
            {data.name}
          </div>
        </div>
      );
    }
    if (data.id === 2) {
      return (
        <div className="status-node" style={{ borderColor: data.color }}>
          <Handle
            type="target"
            position="left"
            id="left-target-1"
            style={{ top: '50%', borderRadius: 0 }}
          />
          <div>
            {data.name}
          </div>
          <Handle
            type="source"
            position="bottom"
            id="bottom-source-1"
            style={{ left: '33%', borderRadius: 0 }}
          />
          <Handle
            type="target"
            position="bottom"
            id="bottom-target-2"
            style={{ left: '66%', borderRadius: 0 }}
          />
        </div>
      );
    }
    if (data.id === 3) {
      return (
        <div className="status-node" style={{ borderColor: data.color }}>
          <Handle
            type="target"
            position="top"
            id="top-target-1"
            style={{ left: '33%', borderRadius: 0 }}
          />
          <Handle
            type="source"
            position="top"
            id="top-source-2"
            style={{ left: '66%', borderRadius: 0 }}
          />
          <div>
            {data.name}
          </div>
        </div>
      );
    }
    return (
      <div className="status-node" style={{ borderColor: data.color }}>
        <div>
          {data.name}
        </div>
      </div>
    );
  };

  const nodeTypes = {
    status: CustomNodeComponent,
  };

  // style={{ height: '100%' }}
  return (
    <div className="layoutflow">
      <ReactFlowProvider>
        <div className="controls">
          <button onClick={() => onLayout('TB')}>vertical layout</button>
          <button onClick={() => onLayout('LR')}>horizontal layout</button>
        </div>
        <ReactFlow
          style={style}
          elements={elements}
          onConnect={onConnect}
          onElementsRemove={onElementsRemove}
          // connectionLineType="smoothstep"
          nodeTypes={nodeTypes}
        >
          <MiniMap
            nodeColor={(node) => {
              switch (node.type) {
                case 'input':
                  return 'red';
                case 'default':
                  return '#00ff00';
                case 'output':
                  return 'rgb(0,0,255)';
                default:
                  return '#eee';
              }
            }}
            nodeStrokeWidth={3}
          />
        </ReactFlow>
      </ReactFlowProvider>
    </div>
  );
};

export default LayoutFlow;
