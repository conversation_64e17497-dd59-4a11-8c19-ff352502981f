.statusMind-renderer {
  height: 250px;
  .react-flow {
    min-height: unset !important;
    .react-flow__node {
      background-color: #ffffff;
    }
    .react-flow__attribution {
      display: none !important;
    }
  }

  &-headers {
    position: relative;
    height: 0;
    z-index: 999;
    font-size: 16px;
    font-weight: 500;
  }

  &-actions {
    position: relative;
    height: 0;
    top: -32px;
    z-index: 999;
    &-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .action-description {
        margin-left: 6px;
        color: rgba(18, 39, 77);
      }
    }
    &-fullscreen {
      padding: 4px 7px !important;
    }

    &.vertical {
      top: -4px;
      float: right;
    }
    &-legend {
      display: flex;
      justify-content: flex-end;
      padding-bottom: 10px;
      &-item {
        display: flex;
        align-items: center;
        margin-right: 24px;
        font-size: 12px;
      }
      &-block {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 2px solid;
        margin-right: 4px;
      }
      &.fullFlow {
        margin-left: 20px;
        padding: 10px 0 !important;
      }
      &.vertical {
        position: relative;
        bottom: 20px;
        justify-content: flex-start !important;
        padding: 16px 0 0 0 !important;
        background: #fff;
        z-index: 99;

        &.fullFlow {
          position: unset;
          justify-content: flex-end !important;
        }
      }
    }
  }

  &.height-100 {
    height: 100% !important;
  }
}
