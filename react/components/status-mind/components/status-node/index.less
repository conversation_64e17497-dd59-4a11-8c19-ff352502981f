.status-node {
  border: solid 1px #FF9500;
  border-top: solid 4px #FF9500;
  border-radius: 4px;
  width: 120px;
  text-align: center;
  padding: 16px;
}

&.diamond {
  width: 0;
  height: 0;
  border: 29px solid transparent;
  border-left-width: 60px;
  border-right-width: 60px;
  border-bottom-color: #ffd591;
  position: relative;
  top: -29px;

  span {
    display: inline-block;
    width: 90px;
    height: 20px;
    position: relative;
    top: 19px;
    left: -45px;
    z-index: 99;
    text-align: center;
  }
}
&.diamond:after {
  content: '';
  position: absolute;
  left: -60px;
  top: 29px;
  width: 0;
  height: 0;
  border: 29px solid transparent;
  border-left-width: 60px;
  border-right-width: 60px;
  border-top-color: #ffd591;
}
