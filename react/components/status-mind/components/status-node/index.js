/*
 * @Author: x<PERSON><PERSON>ya
 * @Date: 2021-12-22 16:02:18
 * @Description: 自定义节点，状态node
 */
import React, { useMemo } from 'react';
import { Handle } from 'react-flow-renderer';
import { observer } from 'mobx-react-lite';
import { color as colorUtils } from '@zknow/utils';
import classnames from 'classnames';

import './index.less';

const COLOR_MAP = {
  HISTORY: '#1AB335',
  SETTING: '#6B7285',
  CURRENT: '#2979FF',
};

const CustomNodeComponent = (e) => {
  const { data, currentStateId, stepsDataSet, statusMindDataSet } = e;
  let color = COLOR_MAP[data.type] || '#6B7285';
  let isCurrent = currentStateId === data.id;
  if (data.code && data.code.includes(',')) {
    const node = statusMindDataSet.current?.get('NODE')?.find(n => n.id === currentStateId);
    if (node) {
      isCurrent = data.code.split(',').includes(node.code);
      if (isCurrent) {
        color = COLOR_MAP[node.type] || '#6B7285';
      }
    }
  }

  function getTop({ allCount, index }) {
    const step = 100 / (allCount + 1);
    if (allCount === 1) {
      return 50;
    }
    return step * (index + 1);
  }

  // 获取类型
  function getType({ allCount, sourceCount, targetCount, direction, index }) {
    if (allCount === 1) {
      return sourceCount === 1 ? 'source' : 'target';
    }
    if (sourceCount === 0) return 'target';
    if (targetCount === 0) return 'source';
    if (direction === 'right' || direction === 'bottom') {
      if ((index + 1 <= sourceCount)) {
        return 'source';
      }
      return 'target';
    } else if (direction === 'top' || direction === 'left') {
      if ((index + 1 <= targetCount)) {
        return 'target';
      }
      return 'source';
    }
  }

  function renderHandle({ nodeInfo }) {
    // 出发点
    const sourceList = [];
    const directionList = ['right', 'bottom', 'left', 'top'];
    const directionEdgesMap = {
      right: 'top',
      left: 'top',
      bottom: 'left',
      top: 'left',
    };
    directionList.map((j) => {
      // 如果边上有两个点以上
      const allEdgePointCount = nodeInfo?.[`${j}SourceHandle`] + nodeInfo?.[`${j}TargetHandle`];
      if (allEdgePointCount > 1) {
        const arr = [];
        for (let i = 0; i < allEdgePointCount; i++) {
          arr.push(i);
        }
        return arr.map((i, index) => {
          const type = getType({
            allCount: allEdgePointCount,
            sourceCount: nodeInfo?.[`${j}SourceHandle`],
            targetCount: nodeInfo?.[`${j}TargetHandle`],
            direction: j,
            index,
          });
          sourceList.push(
            <Handle
              type={type}
              position={j}
              id={`${j}-${type}-${data.id}-${index}`}
              style={{
                [`${directionEdgesMap[j]}`]: `${
                  getTop({ 
                    allCount: allEdgePointCount,
                    sourceCount: nodeInfo?.[`${j}SourceHandle`],
                    targetCount: nodeInfo?.[`${j}TargetHandle`],
                    direction: j,
                    type,
                    index,
                  })
                }%`,
                borderRadius: 0,
              }}
            />
          );
          return i;
        });
      } else if (allEdgePointCount === 1) {
        const type = getType({
          allCount: allEdgePointCount,
          sourceCount: nodeInfo?.[`${j}SourceHandle`],
          targetCount: nodeInfo?.[`${j}TargetHandle`],
          direction: j,
          index: 0,
        });
        sourceList.push(
          <Handle
            type={type}
            position={j}
            id={`${j}-${type}-${data.id}-${0}`}
            style={{
              [`${directionEdgesMap[j]}`]: `${
                getTop({ 
                  allCount: allEdgePointCount,
                  sourceCount: nodeInfo?.[`${j}SourceHandle`],
                  targetCount: nodeInfo?.[`${j}TargetHandle`],
                  direction: j,
                  type,
                  index: 0,
                })
              }%`,
              borderRadius: 0,
            }}
          />
        );
      }
      return j;
    });
    return sourceList;
  }

  if (data.shape) {
    return (
      <div
        onClick={() => {
          stepsDataSet?.setState('currentClickNode', data?.id);
        }}
      >
        {renderHandle({ nodeInfo: data, type: 'target' })}
        <div
          className={classnames({
            [data.shape]: !!data.shape,
          })}
        >
          <span>{data.name}</span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={classnames({
        'status-node': true,
        'status-node-selected': isCurrent,
      })}
      style={{
        borderColor: color,
        backgroundColor: isCurrent ? colorUtils?.colorOpacityRGB(color, 0.05) : 'unset',
        boxShadow: isCurrent ? `0px 0px 8px 5px ${colorUtils?.colorOpacityRGB(color, 0.2)}` : 'unset',
      }}
      onClick={() => {
        stepsDataSet?.setState('currentClickNode', data?.id);
      }}
    >
      {renderHandle({ nodeInfo: data, type: 'target' })}
      <div>
        {data.name}
      </div>
    </div>
  );
};

export default observer(CustomNodeComponent);
