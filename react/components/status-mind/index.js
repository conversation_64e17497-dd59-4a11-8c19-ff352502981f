/*
 * @Author: xiaoreya
 * @Date: 2021年12月22日 16:22:40
 * @Description: 状态流转图
 *
 * 数据样式可以参考 mock 文件夹中的 mock.js文件.
 * 全 json 分为节点-NODE与连线EDGES两个数组集合。
 *
 * 关于单个node元素的定位
 * 首先定位x轴是根据阶段数组去定位，阶段中是按照从小到大的顺序排列的。
 * 遍历 NODE数组 的数据，根据状态元素node在对应的阶段id，设置x轴位置为 step(间距) * (对应阶段的索引 + 1)
 * 遍历 NODE数组 的数据，相同的阶段id作为一个集合，然后依次计算y轴
 *
 *
 * 关于单个node元素的连接点。
 * 每个边上都可能存在连接点，规定方位为right, bottom, left, bottom
 * 右边：source出口点规定在靠上方，target入口点规定在靠下方
 * 下边：source出口点规定在靠左边，target入口点规定在靠右边
 * 左边：source出口点规定在靠下方，target入口点规定在靠上方
 * 上边：source出口点规定在靠右边，target入口点规定在靠左边
 * 以每一行，每一列为矩阵轴，依次算出上下左右边上各有多少个接入点和输出点， 其他的点暂时不管，默认会使用边上第一个点
 * 每个点都有自己独立的id，id格式为  方位-类型(是source还是target)-当前节点的id-当前边上第几个点
 *
 *
 * 关于连接线
 * 每条线上都有一个出点，一个入点
 * sourceHandle 是从当前点开始画
 * targetHandle 是从当前点结束该条线
 * 只要值跟自己定义的id对应上，线就可以连接起来
 *
 *
 * @param {Function} callback  A function to send each data URI to
 */
import { inject } from 'mobx-react';
import React from 'react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import MainView from './MainView';

export default inject('AppState')(formatterCollections({
  code: ['lc.components'],
})((props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
)));

/* externalize: StatusMind */
