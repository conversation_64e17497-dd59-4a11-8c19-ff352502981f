import React from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import YqPcdPicker from './YqPcdPicker';
import { StoreProvider } from './stores';

export default injectIntl(inject('AppState')(observer((props) => (
  <StoreProvider {...props}>
    <YqPcdPicker />
  </StoreProvider>
))));

/* externalize: YqPcdPicker */
