import React, { useRef, useEffect, useMemo, forwardRef, useImperativeHandle, useState, useCallback, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Cascader } from 'choerodon-ui/pro';
import axios from 'axios';
import store from './stores';
import './index.less';

const REGION_ENUM = {
  'province,city,district': 'province,city',
  'province,city': 'province',
  province: 'Not',
};
const YqPcdPicker = (props) => {
  const { objectFieldDataSet, objectRealFieldDataSet, defaultValue = [], onChange, intl, mode = 'province,city', readOnly, name, record, isForm = false, realValue, sider } = useContext(store);
  const [fieldRecord, setFieldRecord] = useState(null);
  const [init, setInit] = useState(true);

  const handleGetPCDList = () => {
    axios.get(`/hpfm/v1/cn_address/id/${realValue}`).then((res) => {
      if (res && !res.failed) {
        const defaultNames = res.map((v) => v.name);
        const realValues = res.map((v) => v.id);
        const fieldName = defaultNames.join('/');
        const appendData = [];
        let tempProvinceCode;
        let tempCityCode;
        const hasIds = res.map((item) => item.id);
        res.forEach((v, index) => {
          if (res[index + 1]) {
            res[index + 1].parentId = v.id;
          }
          const field = objectFieldDataSet.find((i) => i.get('id') === v.id);
          if (v.level === 'province' || v.level === 'city') {
            if (v.level === 'province') {
              tempProvinceCode = v.code;
            } else {
              tempCityCode = v.code;
            }
            axios.get(`/hpfm/v1/cn_address/list?parentCode=${v.code}`).then((releationData) => {
              if (releationData && !releationData.failed) {
                const reBuildData = (releationData || []).map((item) => {
                  item.parentId = v.id;
                  item.path = v.level === 'province' ? `${v.code}:${item.code}` : `${tempProvinceCode}:${v.code}:${item.code}`;
                  return item;
                }).filter((item) => !hasIds.includes(item.id));
                if (init) {
                  objectFieldDataSet.appendData(reBuildData);
                  setInit(false);
                }
              }
            });
          }
          if (!field) {
            appendData.push(v);
          }
        });
        objectFieldDataSet.appendData(appendData);
        setFieldRecord(objectRealFieldDataSet.create({ field: realValues, name: fieldName }));
      } else {
        setFieldRecord(objectRealFieldDataSet.create({ field: realValue }));
      }
    });
  };
  useEffect(() => {
    async function loadFieldData() {
      if (objectFieldDataSet.toData().length > 0) {
        if (realValue) {
          handleGetPCDList();
        } else if (defaultValue && defaultValue.length > 0) {
          const appendData = [];
          defaultValue.forEach((v) => {
            const field = objectFieldDataSet.find((i) => i.get('id') === v.id);
            if (!field) {
              appendData.push(v);
            }
          });
          objectFieldDataSet.appendData(appendData);
        }
      } else {
        const queryResult = await objectFieldDataSet.query();
        if (queryResult && !queryResult.failed) {
          if (realValue) {
            handleGetPCDList();
          } else if (defaultValue && defaultValue.length > 0) {
            const appendData = [];
            defaultValue.forEach((v) => {
              const field = objectFieldDataSet.find((i) => i.get('id') === v.id);
              if (!field) {
                appendData.push(v);
              }
            });
            objectFieldDataSet.appendData(appendData);
          }
        }
      }
    }
    loadFieldData();
  }, [mode, realValue]);

  useEffect(() => {
    // 切换mode时，清空组件值
    if (fieldRecord) {
      fieldRecord.set('field', undefined);
    }
    // 清空外部数据值
    // if (record && name) {
    //   record.set(name, undefined);
    // }
  }, [mode]);

  useEffect(() => {
    if (record && !realValue) {
      const defaultNames = defaultValue.map((v) => v.name);
      const realValues = defaultValue.map((v) => v.id);
      const fieldName = defaultNames.join('/');
      setFieldRecord(objectRealFieldDataSet.create({ field: realValues, name: fieldName }));
    }
  }, [record]);

  function handleOnChange(value) {
    transFieldCrumb(value);
    const valueArr = value || [];
    const fieldData = objectFieldDataSet?.toData().filter((i) => i.id === valueArr[valueArr.length - 1])[0];
    if (fieldData && record && name) {
      record.set(name, fieldData.id);
    } else if (fieldData && record) {
      record.set('widgetConfig.realValue', fieldData.id);
    } else if (!value && record) {
      // 清空字段
      record.set(name || 'widgetConfig.realValue', value);
    }
  }

  // 缓存路径
  const transFieldCrumb = (value, item) => {
    const fieldCrumb = [];
    // 记录每一次的record,包括path
    value?.forEach((v, index) => {
      const _field = objectFieldDataSet.find((r) => r.get('id') === v);
      if (_field) {
        fieldCrumb.push(_field.toData());
      }
    });
    if (!isForm) {
      record?.set('widgetConfig.regionValues', fieldCrumb);
    }
  };

  async function handleLoadRelationData(lastOptionRecord) {
    if (lastOptionRecord.get('level') === 'province' || lastOptionRecord.get('level') === 'city') {
      const releationData = await axios.get(`/hpfm/v1/cn_address/list?parentCode=${lastOptionRecord.get('code')}`);
      if (releationData && !releationData.failed) {
        const reBuildData = (releationData || []).map((item) => {
          item.parentId = lastOptionRecord.get('id');
          item.path = `${lastOptionRecord.get('path')}:${item.code}`;
          return item;
        }).filter((v) => v);
        lastOptionRecord.setState('isLoaded', true);
        objectFieldDataSet.appendData(reBuildData);
      }
    }
  }

  return (
    <span className="yq-pcd-picker-item">
      <Cascader
        name="field"
        className="content-select-field"
        containerCls={`${sider ? 'yq-pcd-picker-container-sider' : 'yq-pcd-picker-container'}`}
        allowClear
        dataSet={objectRealFieldDataSet}
        record={fieldRecord}
        readOnly={readOnly}
        disabled={readOnly}
        expandTrigger="click"
        searchable
        placeholder={intl.formatMessage({ id: 'lcr.components.desc.yqcloud.pcd.picker.select', defaultMessage: '请选择' })}
        onOption={({ record: optionRecord }) => {
          // 当切换地区模式时，选项数据不做删除，通过children设置子级隐藏
          const level = optionRecord.get('level');
          if (!REGION_ENUM[mode].includes(level) && (level === 'province' || level === 'city')) {
            return {
              children: undefined,
              isLeaf: true,
            };
          }
          return {
            isLeaf: optionRecord.get('leaf'),
          };
        }}
        onChange={handleOnChange}
        loadData={(selectedOptions) => {
          const currentOption = selectedOptions.value;
          if (currentOption?.get('isLeaf') || (currentOption?.children && currentOption.getState('isLoaded'))) {
            return;
          }
          setTimeout(() => {
            handleLoadRelationData(currentOption);
          }, 100);
        }}
      />
    </span>
  );
};

export default observer(YqPcdPicker);
