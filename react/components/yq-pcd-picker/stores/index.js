import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import ObjectFieldDataSet from './ObjectFieldDataSet';
import ObjectRealFieldDataSet from './ObjectRealFieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(observer((props) => {
  const {
    intl,
    children,
    AppState: { currentMenuType: { organizationId: tenantId, type } },
  } = props;

  const objectFieldDataSet = useMemo(() => new DataSet(ObjectFieldDataSet({ tenantId, type })), []);
  const objectRealFieldDataSet = useMemo(() => new DataSet(ObjectRealFieldDataSet({ tenantId, objectFieldDataSet })), [objectFieldDataSet]);

  const value = {
    ...props,
    intl,
    tenantId,
    objectFieldDataSet,
    objectRealFieldDataSet,
    type,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
