import qs from 'qs';

const ObjectFieldDataSet = ({ tenantId, type }) => ({
  autoQuery: false,
  autoQueryAfterSubmit: false,
  selection: false,
  paging: true,
  pageSize: 999,
  dataKey: 'content',
  totalKey: 'totalPages',
  idField: 'id',
  parentField: 'parentId',
  transport: {
    read: ({ data }) => ({
      url: '/hpfm/v1/cn_address/list?level=province',
      method: 'get',
      paramsSerializer: (params) => {
        delete params.objectId;
        return qs.stringify(params);
      },
      transformResponse: ((resp) => {
        if (resp && !resp.failed) {
          try {
            const respData = JSON.parse(resp);
            const newData = respData.map((v) => {
              v.parentId = '0';
              v.path = v.code;
              v.isFirst = true;
              return v;
            }).filter((i) => i);
            return newData;
          } catch (err) {
            return [];
          }
        } else {
          return [];
        }
      }),
    }),
  },
  field: [
    { name: 'id', type: 'string' },
    { name: 'name', type: 'string' },
    { name: 'type', type: 'string' },
  ],
  queryFields: [{ name: 'param', type: 'string' }],
});
export default ObjectFieldDataSet;
