/* eslint-disable no-chinese/no-chinese */
import React, { useContext, useMemo, useState, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';

import { Button, Form, Lov } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';

import Store from './stores';
import './index.less';
import Info from './Info';

const DownloadTemplate = observer(() => {
  const { intl, intlPrefix, prefixCls, importDataSet, tenantId, handleDownloadTemplateAndData, btnInfo, language } = useContext(Store);
  const record = importDataSet?.current;
  const download = intl.formatMessage({ id: 'lcr.components.desc.batch.import.download.template', defaultMessage: '下载模板' });
  const downloadData = intl.formatMessage({ id: 'lcr.components.desc.batch.import.download.template.and.data', defaultMessage: '下载模板及数据' });
  const template = record?.get('template');
  const templateType = btnInfo?.get('templateType')?.length ? btnInfo?.get('templateType') : ['onlyTemplate'];
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    if (btnInfo) {
      btnInfo.get('defaultTemplate') && record?.set('template', btnInfo.get('defaultTemplate'));
    }
  }, [importDataSet.current]);
  const downloadField = useMemo(() => {
    if (template) {
      const getFields = () => {
        try {
          return JSON.parse(template?.jsonData)
            .fields.map((v) => (language === 'zh_CN' ? v?.name : v?.nameEn))
            .join('、');
        } catch {
          return '';
        }
      };

      return intl.formatMessage(
        { id: 'lcr.components.desc.batch.downloadTemplate.message', defaultMessage: '该模板内包含的字段：{fields}' },
        { fields: getFields() }
      );
    }
    return intl.formatMessage({ id: 'lcr.components.desc.batch.import.download.template.not.found', defaultMessage: '暂无模板下载' });
  }, [template]);

  const handleDownload = async () => {
    setLoading(true);
    try {
      if (template?.id) {
        const result = await axios.post(
          `/itsm/v1/${tenantId}/ticket/import/download/${template.id}`,
          {},
          { responseType: 'blob' }
        );

        if (result && !result.failed && result.type !== 'application/json') {
          const blob = new Blob([result], { type: 'application/octet-stream;charset=utf-8' });
          const filename = `${template?.objectName}-${template?.name}.xlsx`;

          if ('msSaveOrOpenBlob' in navigator) {
            // ie使用的下载方式
            window.navigator.msSaveOrOpenBlob(blob, filename);
          } else {
            const eLink = document.createElement('a');
            // 设置下载文件名
            eLink.download = filename;
            eLink.style.display = 'none';
            eLink.href = URL.createObjectURL(blob);
            document.body.appendChild(eLink);
            eLink.click();
            document.body.removeChild(eLink);
          }
        }
      }
    } catch {
      /* */
    }
    setLoading(false);
  };

  return (
    <div className={`${prefixCls}-main-downloadTemplate`}>
      <Form
        dataSet={importDataSet}
        labelWidth="auto"
        className={`${prefixCls}-main-downloadTemplate-form`}
      >
        <Lov name="template" />
      </Form>
      <Info message={downloadField} />
      {
        templateType?.includes('onlyTemplate')
        && <Button
          funcType="raised"
          color="primary"
          onClick={handleDownload}
          wait={200}
          waitType="debounce"
          loading={loading}
          disabled={!template?.id}
        >
          <span style={{ display: 'flex', alignItems: 'center' }}>
            {!loading && <Icon type="download" style={{ marginRight: '8px' }} />}
            {download}
          </span>
        </Button>
      }
      { templateType?.includes('templateAndData')
        && <Button
          funcType="raised"
          color="primary"
          onClick={() => { handleDownloadTemplateAndData(record?.get('template')); }}
          wait={200}
          waitType="debounce"
          loading={loading}
          disabled={!template?.id}
        >
          <span style={{ display: 'flex', alignItems: 'center' }}>
            {!loading && <Icon type="download" style={{ marginRight: '8px' }} />}
            {downloadData}
          </span>
        </Button>}
    </div>
  );
});
export default DownloadTemplate;
