import React, { useContext, useState, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import * as XLSX from 'xlsx';

import { Button, message, Modal } from 'choerodon-ui/pro';
import { Icon, FileUploader } from '@zknow/components';

import Store from './stores';
import './index.less';
import Info from './Info';
import ImportHistory from './ImportHistory';

const modalKey = Modal.key();
const UploadFile = observer(() => {
  const { intl, prefixCls, tenantId, historyDataSet, parentViewType, dataSet, businessObjectId, relatedFieldCode } = useContext(Store);
  const upload = intl.formatMessage({ id: 'lcr.components.desc.batch.import.upload.file', defaultMessage: '上传文件' });
  const limit = intl.formatMessage({ id: 'lcr.components.desc.batch.import.upload.file.limit', defaultMessage: '请确保上传数据不超过500条' });
  const uploadRef = useRef();
  const [loading, setLoading] = useState(false);
  const initState = () => {
    setLoading(false);
  };

  async function handleImport(fileKey) {
    if (!fileKey) return;
    initState();
    setLoading(true);
    const res = await axios.post(`/itsm/v1/${tenantId}/ticket/import`, { file: fileKey });

    if (!res?.failed) {
      setLoading(false);
      message.success(intl.formatMessage({ id: 'lcr.components.desc.batch.import.import.success', defaultMessage: '已上传，请至“导入历史”中查看结果' }));
    } else {
      initState();
      message.error(res?.message);
    }
  }

  const handleDownloadResult = async () => {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.import.history', defaultMessage: '导入历史' }),
      children: <ImportHistory historyDataSet={historyDataSet} intl={intl} />,
      key: modalKey,
      style: { width: 1200 },
      drawer: true,
      bodyStyle: { padding: 24, paddingBottom: 0 },
      destroyOnClose: true,
      footer: () => null,
    });
  };

  const xlsxToJson = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'array' });

        // 1. 获取第一个工作表的名称
        const firstSheetName = workbook.SheetNames[0];
        if (!firstSheetName) {
          return;
        }
        const worksheet = workbook.Sheets[firstSheetName];

        // 2. 将工作表转换为 JSON
        // XLSX.utils.sheet_to_json 默认会将第一行作为表头（键）
        // 后续行作为数据。这正是我们想要的 "跳过第一行（作为数据）并使用列名（第一行）作为key"
        const parsedJson = XLSX.utils.sheet_to_json(worksheet, {
          header: 1, // 第二行做表头
          range: 1, // 从第二行 (index 1) 开始处理
        });

        // 处理数据为以列名为键的结构
        const [header, ...rows] = parsedJson;
        const regex = /\[(.*?)\]/;
        const formattedData = rows.map((row) => header.reduce((acc, key, index) => {
          acc[key.match(regex)[1]] = row[index] || ''; // 如果单元格为空，则设置为 ""
          return acc;
        }, {}));
        formattedData.forEach((item) => {
          dataSet.create(item);
        });
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('Error processing Excel file:', err);
      }
    };

    reader.readAsArrayBuffer(file);
  };

  function beforeUpload(file, fileLimit) {
    const fileSize = file.size / 1024 / 1024;
    if (fileLimit && fileSize > fileLimit) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.file.upload.limit', defaultMessage: '上传文件不能大于{limit}MB' }, { limit: fileLimit }));
      return false;
    }
    if (parentViewType === 'INSERT') {
      dataSet.setState('businessObjectId', businessObjectId);
      dataSet.setState('relatedFieldCode', relatedFieldCode);
      xlsxToJson(file);
      return false;
    }
    return true;
  }

  return (
    <div className={`${prefixCls}-main-uploadFile`}>
      <Info message={intl.formatMessage({ id: 'lcr.components.desc.batch.import.upload.file.message', defaultMessage: '您需下载模板，使用默认模板导入文件。请您仔细阅读要求并按照模板要求填写、上传文件。' })} />
      <div ref={uploadRef} style={{ display: 'none' }}>
        <FileUploader
          overrideProps={{
            accept: ['.xls', '.xlsx'],
            className: 'lc-import-upload',
            beforeUpload: (file) => beforeUpload(file, 20),
          }}
          onChange={(response) => {
            handleImport(response?.fileKey);
          }}
          multiple={false}
          tenantId={tenantId}
        >
          <span ref={uploadRef} />
        </FileUploader>
      </div>
      <div className={`${prefixCls}-main-uploadFile-button-wrap`}>
        <Button
          funcType="raised"
          color="primary"
          wait={200}
          waitType="debounce"
          loading={loading}
          onClick={() => {
            uploadRef.current?.click();
          }}
        >
          <span style={{ display: 'flex', alignItems: 'center' }}>
            {!loading && <Icon type="upload" style={{ marginRight: '8px' }} />}
            {upload}
          </span>
        </Button>
        {/* <Info message={limit} inline /> */}
      </div>
      <div className={`${prefixCls}-main-uploadFile-result`}>
        <div className="download" onClick={handleDownloadResult}>
          {intl.formatMessage({ id: 'lcr.components.desc.batch.import.import.history', defaultMessage: '查看导入历史' })}
        </div>
      </div>
    </div>
  );
});

export default UploadFile;
