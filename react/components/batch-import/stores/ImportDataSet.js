export default ({ intl, intlPrefix, businessObjectId, btnInfo }) => {
  const template = intl.formatMessage({ id: 'lcr.components.model.batch.import.select.template', defaultMessage: '选择模板' });

  return {
    autoCreate: btnInfo?.get('defaultTemplate'),
    fields: [{
      name: 'template',
      label: template,
      type: 'object',
      valueField: 'id',
      textField: 'name',
      lovCode: 'IMPORT_TEMPLATE',
      lovPara: {
        search_businessObjectId: businessObjectId,
      },
    }],
    transport: {},
    events: {},
  };
};
