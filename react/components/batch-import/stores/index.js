import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import ImportDataSet from './ImportDataSet';
import HistoryDataSet from './historyDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId },
        currentLanguage: language,
      },
      businessObjectId,
      handleDownloadTemplateAndData = () => {},
      btnInfo,
    } = props;

    const intlPrefix = 'batch.import';
    const prefixCls = 'batch-import';

    const importDataSet = useMemo(
      () => new DataSet(ImportDataSet({ intl, intlPrefix, businessObjectId, btnInfo })),
      [businessObjectId]
    );

    const historyDataSet = useMemo(() => new DataSet(HistoryDataSet({
      intl, tenantId, businessObjectId,
    })), [tenantId, businessObjectId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      importDataSet,
      historyDataSet,
      handleDownloadTemplateAndData,
      btnInfo,
      language,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
