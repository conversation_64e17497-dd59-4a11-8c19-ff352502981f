import moment from 'moment';
import { getQueryParams } from '@zknow/utils';

export default ({ intl, tenantId, businessObjectId }) => {
  const url = `/data/v1/${tenantId}/import/query/${businessObjectId}`;
  const file = intl.formatMessage({ id: 'lcr.components.model.download.history.file', defaultMessage: '文件' });
  const templateName = intl.formatMessage({ id: 'lcr.components.model.template.name', defaultMessage: '模板名称' });
  const createdBy = intl.formatMessage({ id: 'zknow.common.model.createdBy', defaultMessage: '创建人' });
  const createdDate = intl.formatMessage({ id: 'lcr.components.model.download.history.import.date', defaultMessage: '导入时间' });
  const resultLabel = intl.formatMessage({ id: 'lcr.components.model.import.history.result', defaultMessage: '导入详情' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const momentFormatStart = 'YYYY-MM-DD 00:00:00';
  const momentFormatEnd = 'YYYY-MM-DD 23:59:59';
  return {
    selection: false,
    transport: {
      read: ({ data }) => {
        return {
          url,
          method: 'get',
          data: getQueryParams(data),
        };
      },
    },
    fields: [
      {
        name: 'file',
        type: 'string',
        label: file,
      },
      {
        name: 'templateName',
        type: 'string',
        label: templateName,
      },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'creationDate',
        type: 'string',
        label: createdDate,
      },
      {
        name: 'result',
        type: 'string',
        label: resultLabel,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        lookupCode: 'IMPORT_TASK_STATUS',
      },
    ],
    queryFields: [
      {
        name: 'file',
        type: 'string',
        label: file,
      },
      {
        name: 'templateName',
        type: 'string',
        label: templateName,
      },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        lookupCode: 'IMPORT_TASK_STATUS',
      },
      {
        name: 'creationDate',
        type: 'date',
        range: ['start', 'end'],
        format: 'YYYY-MM-DD',
        label: createdDate,
      },
    ],
    events: {
      query: ({ params, data }) => {
        if (params.search_creationDate) {
          if (params.search_creationDate.start) {
            params.search_startCreationDate = moment(params.search_creationDate.start).format(momentFormatStart);
          }
          params.search_endCreationDate = moment(params.search_creationDate.end).format(momentFormatEnd);
          delete params.search_creationDate;
        }
      },
    },
  };
};
