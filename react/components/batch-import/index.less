.batch-import-main {
  padding: 0.12rem 0.24rem 0.4rem 0.24rem;
  .c7n-tabs-nav {
    margin-left: 0 !important;
  }
  .c7n-tabs-bar {
    margin-bottom: 0.08rem;
  }

  &-uploadFile {
    button {
      span:first-child {
        white-space: nowrap;
      }
    }
    &-button-wrap {
      display: flex;
      align-items: flex-start;
      margin-top: 0.12rem;
    }
    &-progress {
      margin-top: 12px;
      padding-right: 10px;
      &-title {
        margin-bottom: 12px;
        line-height: 22px;
      }
    }
    &-result {
      margin-top: 0.16rem;
      .time {
        color: #8c8c8c;
      }
      .result {
        margin-top: 8px;
        .success {
          padding-left: 10px;
          padding-right: 4px;
          font-size: 20px;
          color: #7bc95a;
        }
        .failed {
          padding-left: 10px;
          padding-right: 4px;
          font-size: 20px;
          color: #f03;
        }
      }
      .creationDate {
        color: #12274d;
        margin-right: 0.08rem;
      }
      .download {
        margin-top: 0.16rem;
        color: #2979ff;
        cursor: pointer;
      }
    }
  }

  &-downloadTemplate {
    .c7n-pro-form-wrapper {
      padding-top: 0.04rem;
      padding-bottom: 0.04rem;
    }
    .c7n-pro-field-label-right {
      padding-left: 0 !important;
    }
    button {
      margin-top: 0.12rem;
    }
  }
}
