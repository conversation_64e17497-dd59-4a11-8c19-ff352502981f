import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, message } from 'choerodon-ui/pro';
import { ClickText, StatusTag } from '@zknow/components';
import './importHistory.less';

const { Column } = Table;
function ImportHistory({ historyDataSet, intl }) {
  useEffect(() => {
    historyDataSet.query();
  });

  function downloadFile(url) {
    window.location.href = url;
  }

  function renderName({ record, text }) {
    const fileName = text ? text.split('@')[1] : '';
    const url = `${window._env_.API_HOST}/hfle/yqc/v1/0/files/download-by-key?fileKey=${record?.get(
      'file'
    )}`;
    return (
      <ClickText record={record} onClick={() => downloadFile(url)}>
        {fileName}
      </ClickText>
    );
  }

  const handleDownload = (_record) => {
    if (_record.get('status') !== 'RUNNING') {
      const url = `${
        window._env_.API_HOST
      }/hfle/yqc/v1/0/files/download-by-key?fileKey=${_record?.get(
        'result'
      )}`;
      window.location.href = url;
    } else {
      message.warn(intl.formatMessage({ id: 'lcr.components.desc.import.task.running', defaultMessage: '导入任务正在进行中' }));
    }
  };

  const renderResult = ({ record: _record }) => {
    return (
      <div style={{ color: '#2979ff', cursor: 'pointer' }} onClick={() => handleDownload(_record)}>
        {intl.formatMessage({ id: 'lcr.components.desc.import.click.download', defaultMessage: '点击下载' })}
      </div>
    );
  };

  function renderStatus({ value = '', text }) {
    const colorMap = {
      COMPLETED: '#1AB335',
      RUNNING: '#FFB400',
      PENDING: '#C9CDD4',
      FAILED: '#F34C4B',
      PART_FAILED: '#FADC19',
    };
    return <StatusTag mode="icon" color={colorMap[value]}>{text}</StatusTag>;
  }

  return (
    <div className="import-history-form">
      <Table
        pristine
        autoHeight
        dataSet={historyDataSet}
        queryBarProps={{
          title: intl.formatMessage({ id: 'lcr.components.desc.import.history', defaultMessage: '导入历史' }),
        }}
      >
        <Column name="file" renderer={renderName} />
        <Column name="templateName" />
        <Column name="createdByName" />
        <Column name="creationDate" />
        <Column name="status" renderer={renderStatus} />
        <Column name="result" renderer={renderResult} />
      </Table>
    </div>
  );
}

export default observer(ImportHistory);
