/* eslint-disable no-chinese/no-chinese */
import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Tabs } from 'choerodon-ui';
import Store from './stores';
import './index.less';

import UploadFile from './UploadFile';
import DownloadTemplate from './DownloadTemplate';

const MainView = () => {
  const context = useContext(Store);
  const { intl, prefixCls } = context;
  const upload = intl.formatMessage({ id: 'lcr.components.desc.batch.import.upload.file', defaultMessage: '上传文件' });
  const download = intl.formatMessage({ id: 'lcr.components.desc.batch.import.download.template', defaultMessage: '下载模板' });

  return (
    <div className={`${prefixCls}-main`}>
      <Tabs defaultActiveKey="1">
        <Tabs.TabPane tab={upload} key="1">
          <UploadFile />
        </Tabs.TabPane>
        <Tabs.TabPane tab={download} key="2">
          <DownloadTemplate />
        </Tabs.TabPane>
      </Tabs>
    </div>
  );
};

export default observer(MainView);
