import React, { useContext, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Select, Icon } from 'choerodon-ui/pro';
import _ from 'lodash';
import Store from './stores';

import './index.less';

const MainView = () => {
  const {
    searchDataSet,
    optionDataSet,
    current,
    name,
    prefixCls,
    textField = 'name',
    valueField = 'id',
    intl,
    config,
  } = useContext(Store);

  useEffect(() => {
    if (searchDataSet?.current && current) {
      searchDataSet.current.set('searchField', current.get(name));
    }
  }, [current?.get(name)]);

  async function handleSearch(value) {
    optionDataSet.setQueryParameter(`search_${textField}`, value && value.trim());
    handleClick();
  }

  const debounceSearch = _.debounce(handleSearch, 300);

  async function handleClick() {
    const { variableFilter, condition, queryLimitFlag } = config;

    // 由于查询会依赖其他字段，所以其他字段变更时，需要重新查询。
    // 目前按照每次点击重新查询来实现
    // 处理筛选变量，即依赖其他字段值进行查询
    const variableParams = {};
    variableFilter?.map(({ variable, relatedFieldCode }) => {
      variableParams[variable] = current && (current.get(relatedFieldCode)?.id || current.get(relatedFieldCode));
      return variable;
    });

    const queryData = {
      conditions: condition || [], // 过滤条件
      params: {
        ...(current?.toData() || {}), // 当前表单值，用于过滤条件
        __page_params: variableParams, // 筛选变量
      },
    };

    optionDataSet.setQueryParameter('queryData', queryData);
    // 添加判断逻辑
    if ((queryLimitFlag && optionDataSet.getQueryParameter(`search_${textField}`)) || !queryLimitFlag) {
      if (queryLimitFlag) {
        optionDataSet.pageSize = 50;
      }
      await optionDataSet.query();
    }
  }

  async function handleInput(e) {
    // 清空输入框时，清空字段值
    if (!e?.target?.value) {
      current.set(name, undefined);
    }
    await debounceSearch(e?.target?.value || '');
  }

  function handleSelect(r) {
    const newValue = (r && r.toData()) || {};
    // lov内部会默认将id置为实际需要保存的值，下拉的场景也需要处理
    current.set(name, {
      ...newValue,
      id: newValue[valueField],
    });
  }

  function searchMatcher(props) {
    const { text, record, textField: fieldCode } = props;
    return !text || record?.get(fieldCode)?.toLowerCase()?.indexOf(text?.trim()?.toLowerCase()) !== -1;
  }

  function handleChange() {
    if (searchDataSet.current && current) {
      const newValue = current.get(name) && current.get(name)[valueField] ? current.get(name)[valueField] : current.get(name);
      searchDataSet.current.set('searchField', newValue);
      handleSearch();
    }
  }

  /**
   * 搜索后失焦，清空搜索条件
   */
  function handleBlur() {
    optionDataSet.setQueryParameter(`search_${textField}`, '');
  }

  return (
    <Select
      className={`${prefixCls}-input`}
      searchable
      name="searchField"
      record={searchDataSet.current}
      searchMatcher={searchMatcher}
      onOption={({ record }) => ({
        onClick: () => handleSelect(record),
        disabled: record?.get('readonlyFlag'),
      })}
      onInput={handleInput}
      onClear={handleInput}
      onClick={handleClick}
      onChange={handleChange}
      onBlur={handleBlur}
      suffix={<Icon type="search" />}
      pagingOptionContent={intl.formatMessage({ id: 'lcr.components.desc.load.more', defaultMessage: '加载更多' })}
      placeholder={config?.placeHolder}
      disabled={current?.getField(name)?.disabled || config?.disabled}
      autoFocus={false}
    />
  );
};

export default observer(MainView);
