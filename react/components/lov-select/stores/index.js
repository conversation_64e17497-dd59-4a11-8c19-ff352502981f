import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import SearchDataSet from './SearchDataSet';
import OptionDataSet from './OptionDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId } },
      current,
      name,
      config,
    } = props;

    const {
      textField = 'name',
      valueField = 'id',
    } = config || {};

    const intlPrefix = 'lc.multiSelect';
    const prefixCls = 'lc-multiSelect';

    // 不要将 config 当做 deps， 因为每次都是新的，会导致ds 总是重新实例化，造成选项丢失
    const optionDataSet = useMemo(
      () => new DataSet(OptionDataSet({ current, config, tenantId, name })),
      [
        current,
        tenantId,
        name,
        current?.get(name),
        config.textField,
        config.valueField,
        config.relationLovId,
        config.variableFilter,
        config.condition,
        config.placeHolder,
        config.onlyLeafFlag,
      ],
    );
    const searchDataSet = useMemo(
      () => new DataSet(SearchDataSet({ optionDataSet, textField, valueField, current, name })),
      [textField, valueField, optionDataSet, current, name],
    );

    const value = {
      ...props,
      intl,
      intlPrefix,
      prefixCls,
      searchDataSet,
      optionDataSet,
      tenantId,
      current,
      name,
      textField,
      valueField,
      record: null,
      dataSet: null,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
