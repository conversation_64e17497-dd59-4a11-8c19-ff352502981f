import { unionBy } from 'lodash';

const OptionDataSet = ({ current, config, tenantId, name }) => {
  const {
    relationLovId,
    variableFilter,
    condition,
    textField = 'name',
    valueField = 'id',
    onlyLeafFlag = false,
  } = config;

  // 处理筛选变量，即依赖其他字段值进行查询
  const variableParams = {};
  variableFilter?.map(({ variable, relatedFieldCode }) => {
    variableParams[variable] = current && (current.get(relatedFieldCode)?.id || current.get(relatedFieldCode));
    return variable;
  });

  // 默认查询参数
  const defaultPostData = {
    conditions: condition || [], // 过滤条件
    params: {
      ...(current?.toData() || {}), // 当前表单值，用于过滤条件
      __page_params: variableParams, // 筛选变量
    },
  };

  const queryUrl = relationLovId ? `/lc/v1/engine/${tenantId}/options/${relationLovId}/queryWithCondition` : '';

  return {
    selection: 'single',
    autoQuery: false, // 每次点击下拉都会查询，这里关闭自动查询
    dataKey: 'content',
    pageSize: 30,
    transport: {
      read: ({ data, params }) => {
        // 本次查询参数
        const queryData = data.queryData || {};
        delete data.queryData;

        const postData = {
          ...defaultPostData, // 首次进入默认查询
          ...data, // 搜索参数search_
          ...queryData, // conditions
          params: {
            ...defaultPostData.params, // 首次进入当前表单数据
            ...queryData.params, // 当前表单最新数据
            ...data, // 搜索参数search_
          },
        };

        let dealParams = params;
        if (onlyLeafFlag) {
          dealParams = {
            ...params,
            treeFlag: true,
          };
        }

        return {
          url: queryUrl,
          method: 'post',
          data: postData,
          params: dealParams,
          transformResponse: (resp) => {
            let newData = {};
            try {
              newData = JSON.parse(resp);
            } catch (e) {
              newData = {};
            }
            const currentValue = current?.get(name);
            if (!currentValue) {
              return {
                ...newData,
                content: unionBy(newData?.content || [], valueField),
              };
            }
            const dataContent = unionBy(newData?.content || [], valueField);
            if (dataContent.length === 0) {
              return newData;
            }
            // 兼容不同类型的currentValue
            if (typeof currentValue === 'string') {
              // 如果不是第一页，则不再加入当前数据
              if (params.page > 0) {
                return {
                  ...newData,
                  content: [
                    ...dataContent?.filter(item => item[valueField] !== currentValue),
                  ],
                };
              }
              // 如果当前选项不在列表中，则根据选中数据加到列表中
              return {
                ...newData,
                content: [
                  {
                    [valueField]: currentValue,
                    [textField]: current?.get(`${name}:${textField}`),
                  },
                  ...dataContent?.filter(item => item[valueField] !== currentValue),
                ],
              };
            } else {
              // 如果不是第一页，则不再加入当前数据
              if (params.page > 0) {
                return {
                  ...newData,
                  content: [
                    ...dataContent?.filter(item => item[valueField] !== (currentValue.id || currentValue[valueField])),
                  ],
                };
              }
              // 如果当前选项不在列表中，则根据选中数据加到列表中
              return {
                ...newData,
                content: [
                  currentValue,
                  ...dataContent?.filter(item => item[valueField] !== (currentValue.id || currentValue[valueField])),
                ],
              };
            }
          },
        };
      },
    },
  };
};

export default OptionDataSet;
