const SearchDataSet = ({ optionDataSet, textField, valueField, current, name }) => {
  return {
    autoCreate: true,
    autoQuery: false,
    paging: true,
    dataKey: 'content',
    fields: [
      {
        name: 'searchField',
        type: 'object',
        textField,
        valueField,
        label: '',
        options: optionDataSet,
        ignore: 'always',
        defaultValue: current?.get(name),
        dynamicProps: {
          required: () => current?.getField(name)?.required,
        },
      },
    ],
  };
};

export default SearchDataSet;
