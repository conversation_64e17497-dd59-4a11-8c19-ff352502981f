import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import OcrDataSet from './OcrDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { tenantId } },
    } = props;

    const intlPrefix = 'lc.multiSelect';
    const prefixCls = 'lc-multiSelect';

    const ocrDataSet = useMemo(
      () => new DataSet(OcrDataSet()),
      [],
    );

    const value = {
      ...props,
      intl,
      intlPrefix,
      prefixCls,
      ocrDataSet,
      tenantId,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
