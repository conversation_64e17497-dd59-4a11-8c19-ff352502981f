import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';
import { Modal } from 'choerodon-ui/pro';
import ModalView from './ModalView';
import Store from './stores';

import './index.less';

function openOcrModal({ intl, tenantId, record, formDs, isLcRichText }) {
  Modal.open({
    title: intl.formatMessage({ id: 'lcr.components.desc.ocr.ticket', defaultMessage: '图片识别' }),
    children: (
      <ModalView
        intl={intl}
        tenantId={tenantId}
        record={record}
        formDs={formDs}
        isLcRichText={isLcRichText}
      />
    ),
    style: { width: '60%', height: '80%', zIndex: 1002 },
    className: 'orc-modal',
    okText: intl.formatMessage({ id: 'lcr.components.desc.ocr.ok.text', defaultMessage: '插入' }),
  });
}

const MainView = observer(() => {
  const {
    tenantId,
    intl,
    record,
    formDs,
    size = 14,
  } = useContext(Store);

  return (
    <Icon type="scanning-two" size={size} onClick={() => openOcrModal({ tenantId, intl, record, formDs })} />
  );
});

export { openOcrModal, MainView };
