import React, { useState, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';
import { Upload, message, Spin } from 'choerodon-ui/pro';
import { v4 as uuidv4 } from 'uuid';
import classnames from 'classnames';
import axios from 'axios';
import { getAccessToken } from '@zknow/utils';
import _ from 'lodash';

import './index.less';

const ModalView = observer((props) => {
  const {
    tenantId,
    intl,
    record,
    modal,
    formDs,
    isLcRichText,
  } = props;

  const [imgUrl, setImgUrl] = useState('');// 当前识别框图片url
  const [imgList, setImgList] = useState([]);// 多图图片信息数组
  const [success, setSuccess] = useState(false);
  const [scale, setScale] = useState(1);
  const [rotate, setRotate] = useState(0);
  const [json, setJson] = useState(null);// 接口识别数据
  const [jsonList, setJsonList] = useState([]);// 多图json数组
  const [bound, setBound] = useState(null);
  const [style, setStyle] = useState({});
  const [listBoxStyle, setListBoxStyle] = useState({});
  const [data, setData] = useState([]);
  const [currentId, setCurrentId] = useState('');// 当前jsonid
  const [imgLoaded, setImgLoaded] = useState(false);
  const [result, setResult] = useState([]);
  const [resultList, setResultList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [moving, setMoving] = useState(false);
  const [translate, setTranslate] = useState([0, 0]);
  const [point, setPoint] = useState(undefined);
  const imgRef = useRef();
  const listRef = useRef();

  // 设置c7nmodal的z-index,不被单据回复富文本组件全屏时盖住
  useEffect(() => {
    if (document.getElementsByClassName('orc-modal') && document.getElementsByClassName('orc-modal')[0] && document.getElementsByClassName('orc-modal')[0].parentNode && document.getElementsByClassName('orc-modal')[0].parentNode.parentNode) {
      document.getElementsByClassName('orc-modal')[0].parentNode.parentNode.style.zIndex = 1002;
    }
  }, [document.getElementsByClassName('orc-modal')]);

  // useEffect(() => {
  //   if (document.getElementsByClassName('orc-modal-wrapper-upload-box')[0]) {
  //     const fnName = getExploreName() === 'Firefox' ? 'DOMMouseScroll' : 'mousewheel';
  //     document.getElementsByClassName('orc-modal-wrapper-upload-box')[0].addEventListener(fnName, (event) => handleWheel(event));
  //   }
  // }, []);

  function getExploreName() {
    const userAgent = navigator.userAgent;
    if (userAgent.indexOf('Opera') > -1 || userAgent.indexOf('OPR') > -1) {
      return 'Opera';
    } else if (userAgent.indexOf('compatible') > -1 && userAgent.indexOf('MSIE') > -1) {
      return 'IE';
    } else if (userAgent.indexOf('Edge') > -1) {
      return 'Edge';
    } else if (userAgent.indexOf('Firefox') > -1) {
      return 'Firefox';
    } else if (userAgent.indexOf('Safari') > -1 && userAgent.indexOf('Chrome') === -1) {
      return 'Safari';
    } else if (userAgent.indexOf('Chrome') > -1 && userAgent.indexOf('Safari') > -1) {
      return 'Chrome';
    } else if (!!window.ActiveXObject || 'ActiveXObject' in window) {
      return 'IE>=11';
    } else {
      return 'Unkonwn';
    }
  }

  const uploadProps = {
    headers: {
      'Access-Control-Allow-Origin': '*',
      authorization: getAccessToken(),
      'Content-Type': 'application/octet-stream',
    },
    action: '',
    multiple: true,
    accept: ['.deb', '.png', '.jpg', 'image/*'],
    uploadImmediately: false,
    showUploadBtn: false,
    showPreviewImage: false,
  };

  function getObjectURL(file) {
    let url = null;
    if (window.createObjectURL !== undefined) {
      url = window.createObjectURL(file);
    } else if (window.URL !== undefined) {
      url = window.URL.createObjectURL(file);
    } else if (window.webkitURL !== undefined) {
      url = window.webkitURL.createObjectURL(file);
    }
    return url;
  }

  function change(list) {
    setLoading(true);
    const id = uuidv4();
    const reader = new FileReader();
    const resolveList = ['png', 'jpg', 'jpeg'];
    reader.readAsDataURL(list[0]);
    const serrchRes = list[0].name.split('.').pop();
    const resultFileType = resolveList.find((item) => {
      return item === serrchRes;
    });
    if (resultFileType === undefined) {
      setLoading(false);
      message.error(intl.formatMessage({ id: 'lcr.components.desc.ocr.no.suppot.file', defaultMessage: '请上传 "png", "jpg","jpeg" 类型的图片' }));
      return;
    }
    setSuccess(true);
    setImgUrl(getObjectURL(list[0]));
    imgList.forEach(v => {
      v.active = false;
    });
    setImgList([...imgList, { id, url: getObjectURL(list[0]), active: true }]);
    reader.onload = async (f) => {
      const res = await axios({
        url: `/ocr/v1/${tenantId}/ocrRecognize/textRecognizeBase64`,
        method: 'post',
        headers: {
          'Access-Control-Allow-Origin': '*',
          Authorization: getAccessToken(),
        },
        data: { imgBase64: [f.target.result] },
      });
      if (res && !res[0].faild) {
        setJson({ id, ...res[0].result });
        setCurrentId(id);
        setJsonList([...jsonList, { id, json: res[0].result }]);
        setData([]);
        if (formDs) {
          formDs.setState('imgUrl', imgUrl);
          formDs.setState('data', null);
          formDs.setState('bound', null);
          formDs.setState('json', null);
          formDs.setState('result', null);
        }
        setLoading(false);
      } else {
        message.error(intl.formatMessage({ id: 'lcr.components.desc.ocr.error', defaultMessage: '识别出错' }));
        setSuccess(false);
        setImgUrl('');
      }
    };
  }

  useEffect(() => {
    if (imgLoaded && json) {
      setTimeout(() => {
        onload();
      });
    }
  }, [imgLoaded, json]);

  useEffect(() => {
    if (formDs && formDs.getState('imgList') && formDs.getState('jsonList') && formDs.getState('jsonList')[0] && formDs.getState('bound') && formDs.getState('firstId')) {
      setImgUrl(formDs.getState('imgList')[0]?.url || '');
      const toolImgList = formDs.getState('imgList').map(v => {
        v.active = v.id === formDs.getState('imgList')[0].id;
        return v;
      });
      setImgList([...toolImgList]);
      setCurrentId(formDs.getState('firstId'));
      setSuccess(true);
      setImgLoaded(true);
      setStyle({
        zIndex: 99,
        width: formDs.getState('bound').width,
        height: formDs.getState('bound').height,
      });
      setJson({ ...formDs.getState('jsonList')[0]?.json });
      setJsonList([...formDs.getState('jsonList')]);
    }
  }, []);

  function onload() {
    const _bound = imgRef.current.getBoundingClientRect();
    setListBoxStyle({
      height: listRef.current.offsetWidth,
    });
    setBound({ ..._bound, height: imgRef.current.offsetHeight, width: imgRef.current.offsetWidth });
    setStyle({
      zIndex: 99,
      width: imgRef.current.offsetWidth,
      height: imgRef.current.offsetHeight,
    });
    recovery();
    startDrawSvg({ ..._bound, height: imgRef.current.offsetHeight, width: imgRef.current.offsetWidth });
  }

  function startDrawSvg(_bound) {
    const rate = json.rotated_image_width / _bound.width;
    const _data = [];
    if (json.data) {
      setData(json.data);
    } else {
      json.lines.map(item => {
        _data.push({
          text: item.text,
          polygons: item.position,
        });
        return item;
      });
      const _dataAf = [];
      _data.map(item => {
        const _polygons = [];
        item.polygons.map(v => {
          _polygons.push(v / rate);
          return v;
        });
        const _item = { ...item };
        _item.id = item.id || uuidv4();
        _item.checked = false;
        _item.text = item.text;
        _item.polygons = item.polygons;
        _item.polygonsScale = [..._polygons];
        _item.polygonsText = `${_polygons[0]} ${_polygons[1]}, ${_polygons[2]} ${_polygons[3]}, ${_polygons[4]} ${_polygons[5]}, ${_polygons[6]} ${_polygons[7]}`;
        _dataAf.push(_item);
        return item;
      });
      setData(_dataAf);
    }
  }

  useEffect(() => {
    const toolArr = resultList.map(item => item.result).flat();
    setResult([...toolArr]);
  }, [resultList]);

  function onSelect(item) {
    const _data = [...data];
    let textData = [];
    const index = _data.findIndex(v => v.id === item.id);
    if (index >= 0) {
      _data[index].checked = !_data[index].checked;
    }
    textData = _data?.filter(v => v.checked)?.map(vi => vi.text);
    const resIndex = resultList.findIndex(v => v.id === currentId);
    if (resIndex >= 0) {
      const toolResultArr = [...resultList];
      toolResultArr[resIndex] = { id: currentId, result: textData };
      setResultList([...toolResultArr]);
    } else {
      setResultList([...resultList, { id: currentId, result: textData }]);
    }
    setData([..._data]);
    const changeJsonList = jsonList.map(i => {
      if (i.id === currentId) {
        i.json.data = [..._data];
      }
      return i;
    });
    setJsonList([...changeJsonList]);
    if (typeof props.onChange === 'function') {
      props?.onChange(_data.filter(v => v.checked));
    }
  }

  async function handleOk() {
    try {
      if (!imgList.length) return true;
      if (record && formDs) {
        const text = formDs.get(0)?.get(record?.get('code')) || '';
        const newTextArr = result.map(i => {
          return `<p>${i}</p>`;
        });
        if (isLcRichText) {
          if (text === '' || text === '<p></p>') {
            formDs.get(0)?.set(record.get('code'), `${newTextArr.join('')}`);
          } else if (text.includes('<p data-json=')) {
            const [text1, text2] = text.split('<p data-json=');
              formDs.get(0)?.set(record.get('code'), `${text1}${newTextArr.join('')}<p data-json=${text2}`);
          } else {
              formDs.get(0)?.set(record.get('code'), `${text}\n${newTextArr.join('')}`);
          }
        } else {
          formDs.get(0)?.set(record.get('code'), `${text}\n${result.join('\n')}`);
        }
      } else if (formDs) {
        const newTextArr = result.map(i => {
          return `<p>${i}</p>`;
        });
        formDs.setState('value', newTextArr.join(''));
      }
      formDs.setState('imgList', [...imgList]);
      formDs.setState('bound', bound);
      const cacheJsonList = jsonList.map(item => {
        item.json.data = null;
        return item;
      });
      formDs.setState('jsonList', [...cacheJsonList]);
      formDs.setState('firstId', imgList[0].id);
    } catch (err) {
      return false;
    }
  }

  modal.handleOk(handleOk);
  modal.handleCancel(() => {
    setData([]);
    const toolArr = jsonList.map(item => {
      item.json.data = null;
      return item;
    });
    formDs.setState('jsonList', [...toolArr]);
    formDs.setState('firstId', currentId);
  });

  function reChoose() {
    document.getElementById('ocr-upload').click();
  }

  function dragImg(e) {
    handleDragDefault(e);
    if (e.dataTransfer.files) {
      change(e.dataTransfer.files);
    }
  }

  function handleDragDefault(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  function changeZoom(type) {
    if (!success) return null;
    if (type === 'in') {
      setScale(scale + 0.1);
    } else if (type === 'out' && scale > 0.2) {
      setScale(scale - 0.1);
    }
  }

  function handleRotate(type) {
    if (!success) return null;
    if (type === 'anticlockwise') {
      if (rotate === 360) {
        setRotate(90);
      } else {
        setRotate(rotate + 90);
      }
    } else if (type === 'clockwise') {
      if (rotate === -360) {
        setRotate(-90);
      } else {
        setRotate(rotate - 90);
      }
    }
  }

  function recovery() {
    setScale(1);
    setRotate(0);
  }

  function handleWheel(event) {
    event = event || window.event;
    let down = true; // 定义一个标志，当滚轮向下滚时，执行一些操作
    down = event.wheelDelta ? event.wheelDelta < 0 : event.detail > 0;
    if (down) {
      setScale(() => (parseFloat(scale) + 0.01).toFixed(2));
    } else if (scale === 0.01) {
      setScale(() => 0.01);
      return;
    } else {
      setScale(() => (parseFloat(scale) - 0.01).toFixed(2));
    }
    if (event.preventDefault) { /* FF 和 Chrome */
      event.preventDefault();// 阻止默认事件
    }
  }

  function onMouseDown(e) {
    setMoving(true);
    setPoint([e.clientX, e.clientY]);
  }

  function onMouseMove(e) {
    if (moving && point) {
      const tx = e.clientX - point[0];
      const ty = e.clientY - point[1];
      setTranslate([tx, ty]);
    }
  }

  function onMouseUp() {
    setMoving(false);
  }

  function changeImg(i) {
    setCurrentId(i.id);
    setImgUrl(i.url);
    const toolImgList = imgList.map(v => {
      v.active = v.id === i.id;
      return v;
    });
    setImgList([...toolImgList]);
    const currentJson = jsonList.find(item => item.id === i.id);
    setJson(currentJson.json);
    setScale(1);
    setRotate(0);
  }

  function deleteImg(i) {
    const toolImgArr = [...imgList];
    const toolJsonListArr = [...jsonList];
    const toolResultListArr = [...resultList];
    const deleteImgIndex = toolImgArr.findIndex(v => v.id === i.id);
    const deleteJsonIndex = toolJsonListArr.findIndex(v => v.id === i.id);
    const resIndex = toolResultListArr.findIndex(v => v.id === i.id);
    toolImgArr.splice(deleteImgIndex, 1);
    toolJsonListArr.splice(deleteJsonIndex, 1);
    toolResultListArr.splice(resIndex, 1);
    setImgList([...toolImgArr]);
    setJsonList([...toolJsonListArr]);
    setResultList([...toolResultListArr]);
    if (!toolImgArr.length) {
      setImgUrl('');
      setData([]);
      setSuccess(false);
    } else {
      setImgUrl(toolImgArr[0].url);
      setJson(toolJsonListArr[0].json);
    }
  }

  return (
    <div className="orc-modal-wrapper">
      <div className="orc-modal-wrapper-imglist">
        <div className="orc-modal-wrapper-imglist-btn" onClick={reChoose} ref={listRef}>
          <Icon type="plus" size={16} />
          <span>{intl.formatMessage({ id: 'lcr.components.desc.ocr.add', defaultMessage: '添加图片' })}</span>
        </div>
        <div className="orc-modal-wrapper-imglist-wrapper">
          {imgList && imgList.map(item => <div
            className={classnames({ 'img-item': true, 'img-item-active': item.active })}
            style={{ ...listBoxStyle, backgroundImage: `url(${item.url})`, backgroundSize: 'contain', backgroundRepeat: 'no-repeat', backgroundPosition: 'center' }}
          >
            <div className="img-item-mask">
              <Icon type="preview-open" size={24} onClick={() => changeImg(item)} />
              <Icon type="delete" size={24} onClick={() => deleteImg(item)} />
            </div></div>)}
        </div>
      </div>
      <div className="orc-modal-wrapper-upload">
        <div
          className={classnames({ 'orc-modal-wrapper-upload-box': true, 'justify-content-center': !success })}
          onDrop={dragImg}
          onDragEnter={handleDragDefault}
          onDragLeave={handleDragDefault}
          onDragOver={handleDragDefault}
          onMouseDown={onMouseDown}
          onMouseMove={onMouseMove}
          onMouseUp={onMouseUp}
        >
          {!success && <div className="orc-modal-wrapper-upload-box-text">{intl.formatMessage({ id: 'lcr.components.desc.ocr.info', defaultMessage: '点击添加图片或拖拽图片至此上传' })}</div>}
          {!success && <div className="orc-modal-wrapper-upload-box-btn" onClick={reChoose}>
            <Icon type="plus" size={16} />
            <span>{intl.formatMessage({ id: 'lcr.components.desc.ocr.add', defaultMessage: '添加图片' })}</span>
          </div>}
          {success && <img alt="" src={imgUrl} ref={imgRef} style={{ zIndex: '3', maxWidth: '90%', transform: `scale(${scale}) rotate(${rotate}deg) translateX(${translate[0]}px) translateY(${translate[1]}px`, transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s' }} onLoad={() => setImgLoaded(true)} />}
          {success && imgLoaded && data ? <svg
            style={{ ...style, transform: `scale(${scale}) rotate(${rotate}deg) translateX(${translate[0]}px) translateY(${translate[1]}px`, transition: 'all cubic-bezier(0.5, 0.5, 0.5, 0.5) 0s' }}
          >
            {data?.map(item => {
              return <polygon
                key={item.id}
                data-text={item.text}
                points={item.polygonsText}
                className={item.checked ? 'py py-checked' : 'py'}
                onClick={() => onSelect(item)}
              />;
            })}
          </svg> : null}
          <Upload className="hidden-upload" id="ocr-upload" {...uploadProps} onFileChange={change} />
        </div>
        <div className="ocr-tools">
          <Icon type="zoom-in" size={16} onClick={() => changeZoom('in')} />
          <Icon type="zoom-out" size={16} onClick={() => changeZoom('out')} />
          <Icon type="one-to-one" size={16} onClick={recovery} />
          <Icon type="undo" size={16} onClick={() => handleRotate('clockwise')} />
          <Icon type="redo" size={16} onClick={() => handleRotate('anticlockwise')} />
          <span onClick={reChoose}>{intl.formatMessage({ id: 'lcr.components.desc.ocr.rechoose', defaultMessage: '重新上传' })}</span>
        </div>
      </div>
      <div className="orc-modal-wrapper-result">
        <div className="orc-modal-wrapper-result-header">{intl.formatMessage({ id: 'lcr.components.desc.ocr.result', defaultMessage: 'OCR识别结果' })}</div>
        <div className="orc-modal-wrapper-result-content">
          {result.length ? result?.map(item => {
            return (
              <div>{item}</div>
            );
          }) : <div className="no-data">{intl.formatMessage({ id: 'lcr.components.desc.ocr.no.data', defaultMessage: '请先在左侧面板上传照片进行下一步操作噢~' })}</div>}
        </div>
      </div>
      <Spin spinning={loading} style={{ zIndex: loading ? '999' : '-1' }} tip={intl.formatMessage({ id: 'lcr.components.desc.ocr.ing', defaultMessage: '正在识别..' })} />
    </div>
  );
});

export default ModalView;
