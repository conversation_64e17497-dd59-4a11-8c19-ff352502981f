.orc-modal {
  .c7n-pro-modal-body {
    height: calc(100% - 120px);
    max-height: calc(100% - 120px) !important;
  }
  &-wrapper {
    display: flex;
    height: 100%;
    position: relative;
      &-imglist {
        width: 20%;
        padding-right: 24px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &-btn {
          width: 100%;
          display: flex;
          background-color: #fff;
          color: @primary-color;
          padding: 6px 10px;
          border-radius: 2px;
          align-items: center;
          justify-content: center;
          border: 1px solid #e9f1ff;
          cursor: pointer;
          &:hover {
            border: 1px solid @primary-color;
          }
          .i-icon {
            width: 16px;
            height: 16px;
          }
          .yqcloud-icon-park-wrapper {
            width: 16px;
            height: 16px;
          }
        }
        &-wrapper {
          height: calc(100% - 35px);
          width: 100%;
          overflow: auto;
          user-select: none;
          .img-item {
            width: 100%;
            height: 100px;
            border-radius: 4px;
            border: 1px solid #e8e8e8;
            margin: 8px 0;
            user-select: none;
            &-active {
              border: 1px solid @primary-color;
            }
            &:hover {
              .img-item-mask {
                display: flex;
                align-items: center;
                justify-content: space-around;
              }
            }
            &-mask {
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0.3);
              border-radius: 4px;
              display: none;
              .i-icon {
                color: #fff;
                cursor: pointer;
              }
              .yqcloud-icon-park-wrapper {
                color: #fff;
                cursor: pointer;
              }
            }
          }
        }
      }
      &-upload {
        width: 50%;
        position: relative;
        button {
          margin-top: 24px;
        }
        &-box {
          background: #f5f5f5;
          border: 1px dashed #bfbfbf;
          margin-right: 16px;
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          overflow: auto;
          height: 100%;
          user-select: none;
          &-text {
            font-size: 16px;
            color: #595959;
          }
          &-btn {
            display: flex;
            background-color: @primary-color;
            color: #fff;
            padding: 6px 16px;
            border-radius: 2px;
            align-items: center;
            margin-top: 24px;
            cursor: pointer;
            .i-icon {
              width: 16px;
              height: 16px;
            }
            .yqcloud-icon-park-wrapper {
              width: 16px;
              height: 16px;
            }
          }
          svg {
            position: absolute;
            .py {
              fill: rgba(255, 149, 0, 0.05);
              stroke: rgba(255, 149, 0, 1);
              stroke-width: 1px;
            }
            .py-checked {
              fill: rgba(255, 149, 0, 0.5);
              stroke: rgba(255, 149, 0, 1);
              stroke-width: 1px;
              cursor: pointer;
            }
          }
          .c7n-pro-upload {
            &:last-of-type {
              position: absolute;
              z-index: -1;
            }
          }
        }
        .ocr-tools {
          width: calc(100% - 16px);
          height: 40px;
          background: rgba(0, 0, 0, 0.5);
          border-radius: 4px 4px 0 0;
          display: flex;
          justify-content: center;
          align-items: center;
          position: absolute;
          bottom: 0;
          color: #fff;
          z-index: 999;
          .i-icon {
            height: 16px;
            width: 16px;
            margin: 8px;
            cursor: pointer;
            &:hover {
              color: @primary-color;
            }
          }
          .yqcloud-icon-park-wrapper {
            height: 16px;
            width: 16px;
            margin: 8px;
            cursor: pointer;
            &:hover {
              color: @primary-color;
            }
          }
          > span {
            cursor: pointer;
            user-select: none;
          }
        }
        .justify-content-center {
          justify-content: center;
        }
      }
      &-result {
        width: 30%;
        border: 1px solid #d9d9d9;
        &-header {
          height: 46px;
          border-bottom: 1px solid #ededed;
          padding-left: 16px;
          display: flex;
          align-items: center;
        }
        &-content {
          height: calc(100% - 46px);
          padding: 16px;
          font-style: 14px;
          color: #595959;
          line-height: 22px;
          overflow: auto;
          .no-data {
            height: 100%;
            display: flex;
            justify-content: center;
          }
        }
      }
      .c7n-spin {
        position: absolute;
        width: 200px;
        height: 46px;
        left: 50%;
        top: 30%;
        margin-left: -100px;
        z-index: 999;
        background-color: rgba(0, 0, 0, 0.75);
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
      }
  }
}
