import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import qs from 'qs';
import UserDataSet from './UserDataSet';
import KnowledgeDataSet from './KnowledgeDataSet';

const Store = createContext();
export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props) => {
    const {
      intl,
      children,
      AppState,
      AppState: { currentMenuType: { organizationId: tenantId } },
      businessObjectCode,
      ticketId,
      isReady,
      readyTagFlag,
      context,
      udmTenantId: udmTenantIdFromProps,
    } = props;
    // 上下游提单页面iframe会带udmTenantId
    const { udmTenantId: udmTenantIdFromSearch } = qs.parse(context?.history?.location?.search?.substring(1));
    const udmTenantId = udmTenantIdFromProps || udmTenantIdFromSearch;
    const prefixCls = 'lc-ckeditor';
    const intlPrefix = 'lc.wysiwyg';
    const userDataSet = useMemo(() => new DataSet(UserDataSet({ tenantId, businessObjectCode, ticketId, udmTenantId })), [businessObjectCode, ticketId, udmTenantId]);
    const knowledgeDataSet = useMemo(() => new DataSet(KnowledgeDataSet({ tenantId, udmTenantId })), [udmTenantId]);
    const emptyObj = useMemo(() => ({}), []);
    if (!sessionStorage?.getItem('mentionTenantId')) {
      sessionStorage?.setItem('mentionTenantId', tenantId);
    }
    const value = {
      ...props,
      prefixCls,
      userDataSet,
      knowledgeDataSet,
      tenantId,
      businessObjectCode,
      ticketId,
      intlPrefix,
      intl,
      AppState,
      overrideConfig: props.overrideConfig || emptyObj,
      udmTenantId,
    };

    return (
      <Store.Provider value={value}>
        {readyTagFlag && !isReady ? null : children}
      </Store.Provider>
    );
  }),
));
