const UserDataSet = ({
  tenantId,
  ticketId,
  businessObjectCode,
  udmTenantId,
}) => {
  const taskIdParams = ticketId ? `taskId=${ticketId}` : '';
  const businessObjectCodeParams = businessObjectCode ? `&businessObjectCode=${businessObjectCode}` : '';
  const udmParams = udmTenantId ? `&_udm=a0b923820dcc509a&udmTenantId=${udmTenantId}` : '';

  return {
    autoQuery: false,
    paging: true,
    pageSize: 10,
    fields: [
      {
        name: 'id',
        type: 'string',
      },
      {
        name: 'real_name',
        type: 'string',
      },
    ],
    queryFields: [{
      name: 'real_name',
      type: 'string',
    }],
    transport: {
      read: ({ data: { search_real_name: param } }) => {
        const search = param ? `?search_param=${encodeURIComponent(param)}&` : '?';
        return {
          url: `/iam/yqc/v1/${tenantId}/users/query/at/user${search}${taskIdParams}${businessObjectCodeParams}${udmParams}`,
          method: 'post',
          data: null,
        };
      },
    },
  };
};

export default UserDataSet;
