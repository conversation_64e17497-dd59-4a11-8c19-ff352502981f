const KnowledgeDataSet = ({ tenantId, udmTenantId }) => {
  const udmParma = udmTenantId ? `&_udm=a0b923820dcc509a&udmTenantId=${udmTenantId}` : '';
  return ({
    autoQuery: false,
    selection: false,
    paging: true,
    autoLocateFirst: false,
    totalKey: 'totalElements',
    pageSize: 10,
  
    transport: {
      read: ({ data: { queryText } }) => ({
        method: 'post',
        url: `/knowledge/v1/${tenantId}/know/element/mentionList?draftFlag=false${udmParma}`,
        data: {
          queryText,
          type: ['knowledge'],
          filters: [
            {
              key: 'fileType.keyword',
              value: 'FOLDER',
              containFlag: false,
            },
          ],
        },
      }),
    },
    fields: [
      { name: 'id', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'type', type: 'string' },
    ],
  }); 
};

export default KnowledgeDataSet;
