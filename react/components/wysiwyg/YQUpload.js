import { message } from 'choerodon-ui/pro';
import { getImageType } from '@/utils';

class Adapter {
  constructor(loader, options) {
    this.loader = loader;
    this.options = options;
  }

  upload() {
    return this.loader.file
      .then(file => new Promise((resolve, reject) => {
        this._initRequest();
        this._initListeners(resolve, reject, file);
        this._sendRequest(file);
      }));
  }

  abort() {
    if (this.xhr) {
      this.xhr.abort();
    }
  }

  _initRequest() {
    const xhr = new XMLHttpRequest();
    this.xhr = xhr;
    const { tokenFailureMessage } = this.options;
    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        if (xhr) {
          if (xhr.status === 401) {
            message.error(tokenFailureMessage);
          } else if (xhr.status === 200) {
            if (xhr.response.failed) {
              message.error(xhr.response.message);
            }
          }
        }
      }
    };

    xhr.open('POST', this.options.url, true);
    xhr.responseType = 'json';
  }

  _initListeners(resolve, reject) {
    const xhr = this.xhr;
    const loader = this.loader;

    xhr.addEventListener('error', () => {
      message.error(this.options.errorMessage);
      reject();
    });
    xhr.addEventListener('abort', () => reject());
    xhr.addEventListener('load', () => {
      const { transformResponse = (res) => res, yqUploadProgress = (res) => res } = this.options;
      const response = transformResponse(xhr.response);

      if (!response || response.failed) {
        yqUploadProgress(false);
        message.error(response.message);
        return reject();
      }

      const urls = response.url ? { default: response.url } : response.urls;

      resolve({
        ...response,
        urls,
      });
    });

    if (xhr.upload) {
      xhr.upload.addEventListener('progress', evt => {
        if (evt.lengthComputable) {
          loader.uploadTotal = evt.total;
          loader.uploaded = evt.loaded;
        }
        const { yqUploadProgress = (res) => res } = this.options;
        if (evt.total === evt.loaded) {
          setTimeout(() => {
            yqUploadProgress(false);
          }, 1000);
        } else {
          yqUploadProgress(!(evt.total === evt.loaded));
        }
      });
    }
  }

  async _sendRequest(file) {
    // Set headers if specified.
    const headers = this.options.headers || {};

    // Use the withCredentials flag if specified.
    const withCredentials = this.options.withCredentials || false;

    Object.keys(headers).forEach(headerName => {
      this.xhr.setRequestHeader(headerName, headers[headerName]);
    });

    this.xhr.withCredentials = withCredentials;

    // Prepare the form data.
    const data = new FormData();

    const { yqUploadProgress = (res) => res } = this.options;
    yqUploadProgress(true);
    const format = await getImageType(file);
    let fileName = file.name;
    if (format) {
      const fileNameList = file.name.split('.');
      fileNameList[fileNameList.length - 1] = format;
      fileName = fileNameList.join('.');
    }
    data.append('file', file, fileName);
    // Send the request.
    this.xhr.send(data);
  }
}

const YQUpload = (editor) => {
  const options = editor.config.get('YQUpload');
  editor.plugins.get('FileRepository').createUploadAdapter = (loader) => {
    const { yqUploadProgress = (res) => res } = options;
    yqUploadProgress(true);
    return new Adapter(loader, options);
  };
};

export default YQUpload;
