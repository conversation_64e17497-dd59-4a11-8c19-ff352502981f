const ImageView = (editor) => {
  const options = editor.config.get('ImageView') || {};
  const { handleImageClick } = options;
  editor.editing.view.document.on('mousedown', (eventInfo, eventData) => {
    const { domTarget } = eventData;
    if (domTarget.nodeName === 'IMG') {
      // 预览、禁用模式下直接点击查看图片
      if (editor.sourceElement.parentElement.classList.contains('ck-editor-preview') || editor.isReadOnly) {
        handleImageClick(domTarget.currentSrc);
        return true;
      }
      // 编辑模式下点击聚焦到图片，再次点击查看图片
      const parent = domTarget.parentNode;
      const resizer = parent.querySelector('.ck-widget__resizer');
      const { display } = window.getComputedStyle(resizer, null);
      if (display !== 'none') {
        handleImageClick(domTarget.currentSrc);
      }
    }
  });
};

export default ImageView;
