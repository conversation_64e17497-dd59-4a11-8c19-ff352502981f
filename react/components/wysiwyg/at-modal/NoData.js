/* eslint-disable no-chinese/no-chinese */
import React from 'react';

const NoData = () => {
  const svgStyle = {
    width: 80,
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
    margin: 'auto',
  };
  return <svg style={svgStyle} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 62 38.5">
    <g id="图层_2" data-name="图层 2">
      <g id="图层_1-2" data-name="图层 1">
        <ellipse id="椭圆形" fill="#f5f5f5" cx="31" cy="32.5" rx="31" ry="6" />
        <path fill="#fff" fillRule="evenodd" d="M18.94.5H41.12a2,2,0,0,1,1.47.65l10.35,11.3h0l.05,20a2,2,0,0,1-2,2H10a2,2,0,0,1-2-2H8l.05-20h0L17.4,1.22A2,2,0,0,1,18.94.5Z" />
        <path fill="#dbdbdb" d="M51,35H10a2.51,2.51,0,0,1-2.5-2.5l.06-20.19L17,.9A2.48,2.48,0,0,1,18.94,0H41.12A2.51,2.51,0,0,1,43,.81L53.44,12.25l.05,20.24A2.49,2.49,0,0,1,51,35ZM8.55,12.67,8.5,32.5A1.5,1.5,0,0,0,10,34H51a1.5,1.5,0,0,0,1.07-.44,1.46,1.46,0,0,0,.43-1.06l-.05-19.86L42.23,1.49A1.52,1.52,0,0,0,41.12,1H18.94a1.49,1.49,0,0,0-1.15.54Z" />
        <path fillRule="evenodd" fill="#fafafa" d="M8,12.5H18.82a2,2,0,0,1,2,2v2.37a2,2,0,0,0,2,2h0l14.73-.09a2,2,0,0,0,2-2l0-2.33a2,2,0,0,1,2-2H53v20a2,2,0,0,1-2,2H10a2,2,0,0,1-2-2v-20Z" />
        <path fill="#dbdbdb" d="M51,35H10a2.5,2.5,0,0,1-2.5-2.5v-20A.5.5,0,0,1,8,12H18.82a2.5,2.5,0,0,1,2.5,2.49v2.37a1.5,1.5,0,0,0,.44,1.06,1.64,1.64,0,0,0,1.06.44l14.74-.09a1.5,1.5,0,0,0,1.49-1.47l0-2.34A2.51,2.51,0,0,1,41.6,12H53a.5.5,0,0,1,.5.5v20A2.5,2.5,0,0,1,51,35ZM8.5,13V32.5A1.5,1.5,0,0,0,10,34H51a1.5,1.5,0,0,0,1.5-1.5V13H41.6a1.5,1.5,0,0,0-1.5,1.48l0,2.33a2.52,2.52,0,0,1-2.49,2.46l-14.73.09h0a2.51,2.51,0,0,1-2.51-2.49V14.5a1.5,1.5,0,0,0-1.5-1.5Z" />
      </g>
    </g>
  </svg>;
};
export default NoData;
