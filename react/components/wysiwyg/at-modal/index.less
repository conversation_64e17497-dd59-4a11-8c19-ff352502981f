.lc-page-loader-tab-right .lc-ckeditor-user-modal .c7n-tabs-bar .c7n-tabs-tab {
  padding: 0.12rem 0 0.13rem !important;
}

.lc-ckeditor {
  // 用户呀
  &-user {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &-modal {
      .c7n-tabs-bar {
        width: 5.2rem !important;
        padding-bottom: 0 !important;

        .c7n-tabs-tab {
          padding: 0.12rem 0 0.13rem !important;
        }
      }

      &-tabs {
        .c7n-tabs-nav-wrap {
          padding: 0;
          height: 0.46rem;
        }
      }

      &-close {
        position: absolute;
        top: 14px;
        right: 16px;
        z-index: 2;

        &-button {
          cursor: pointer;
        }
      }

      display: flex;
      padding: 0;
      flex-direction: column;
      width: 5.2rem;
      background-color: white;
      overflow: hidden;
      position: fixed;
      z-index: 999;
      border-radius: 0.02rem;
      border: 1px solid rgba(0, 0, 0, 0.05);
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);

      .c7n-pro-field-wrapper {
        padding: 0.1rem 0.05rem;
      }

      &-footer {
        display: flex;
        align-items: center;
        padding: 16px;
        border-top: solid 1px #e9e9e9;

        .footer-left {
          width: 370px;

          &-textfield {
            width: 100%;

            .c7n-pro-input {
              border: none;
            }
          }
        }

        .footer-right {
          display: flex;
          align-items: center;
          width: 100%;
          justify-content: flex-end;
        }
      }
    }

    &-list {
      width: 100%;
      overflow: auto;
      height: 2rem;
    }

    &-box {
      flex-direction: column;
      width: 100%;
      overflow: auto;
      // display: flex;
      display: block;

      .yq-rich-text-knowledge {
        padding: 12px 16px;

        &-right {
          flex: 0 0 calc(100% - 36px);
          width: calc(100% - 36px);

          .right-bottom {
            overflow: hidden;

            &-item {
              &:last-child {
                // 要考虑清楚，如果显示知识的更多信息，需要兼容样式问题，我没有更好的办法处理
                display: none !important;
              }
            }
          }
        }
      }
    }

    &-close {
      position: absolute;
      top: 0;
      right: 0;
      border: none !important;
      background: #fff;
      z-index: 999;

      .icon {
        font-size: 0.2rem !important;
      }
    }

    &-selected {
      background-color: rgba(41, 121, 255, 0.06);
    }

    &:hover {
      background-color: #f5f5f5;
      cursor: pointer;
    }

    &-left {
      height: 32px;
      width: 32px;
      margin-right: 8px;
    }

    &-right {
      width: 100%;

      .right-top {
        font-size: 14px;
        font-weight: 400;
        color: #12274d;
        line-height: 22px;
        margin-bottom: 2px;
      }

      .right-bottom {
        font-size: 12px;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 20px;
        display: flex;
        align-items: center;

        &-item {
          display: flex;
          align-items: center;
          margin-right: 16px;

          &-email {
            width: 185px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }

    &-extra {
      width: 16px;
      color: @primary-color;
    }
  }

  // 知识呀
  &-knowledge {
    padding: 12px 16px 12px 24px;
    display: flex;
    align-items: baseline;
    cursor: pointer;

    &-selected {
      background-color: rgba(41, 121, 255, 0.06);
    }

    &:hover {
      background-color: #f5f5f5;
      cursor: pointer;
    }

    &-left {
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }

    &-right {
      width: 100%;

      .right-top {
        font-size: 14px;
        font-weight: 400;
        color: #12274d;
        line-height: 22px;
        margin-bottom: 2px;

        word-break: break-all;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .right-bottom {
        font-size: 12px;
        font-weight: 400;
        color: #8c8c8c;
        line-height: 20px;
        display: flex;
        align-items: center;

        &-item {
          display: flex;
          align-items: center;
          margin-right: 16px;
        }
      }
    }

    &-extra {
      width: 16px;
      color: @primary-color;
    }
  }

  &-more-botton {
    width: 100%;
    color: @primary-color;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
  }

  .ql-image {
    max-width: 80%;
  }

  &-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(18, 39, 77, 0.65);
    padding-bottom: 10px;
    &-animation {
      display: flex;
      font-size: 0;
      position: relative;
      box-sizing: border-box;
      width: 54px;
      height: 18px;
      align-items: center;
      > div {
        position: relative;
        box-sizing: border-box;
        display: inline-block;
        float: none;
        background-color: currentColor;
        border: 0 solid currentColor;
        width: 3px;
        height: 3px;
        margin: 2px;
        border-radius: 100%;
        animation: ball-pulse-sync 0.6s infinite ease-in-out;
        &:nth-child(1){
          animation-delay: -0.14s;
        }
        &:nth-child(2){
          animation-delay: -0.07s;
        }
        &:nth-child(3){
          animation-delay: 0s;
        }
        @keyframes ball-pulse-sync {
          33% {
            transform: translateY(100%);
          }
        
          66% {
            transform: translateY(-100%);
          }
        
          100% {
            transform: translateY(0);
          }
        }
      }
      .la-dark {
        color: #333;
      }
      .la-sm {
        width: 26px;
        height: 8px;
      }
      .la-2x {
        width: 108px;
        height: 36px;
        > div {
          width: 20px;
          height: 20px;
          margin: 8px;
        }
      }
      .la-3x {
        width: 162px;
        height: 54px;
        > div {
          width: 30px;
          height: 30px;
          margin: 12px;
        }
      }
    }
  }
}
