@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.user {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.selected {
  background-color: rgba(41, 121, 255, 0.06);
}

.left {
  height: 32px;
  width: 32px;
  margin-right: 8px;
}

.right {
  width: calc(100% - 32px);
}

.top {
  font-size: 14px;
  font-weight: 400;
  color: @yq-text-8;
  line-height: 22px;
  margin-bottom: 2px;
  display: flex;
}

.tag {
  display: inline-block;
  max-width: 241px;
  margin-left: 8px;
  padding: 1px 8px;
  overflow: hidden;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  vertical-align: middle;
  color: ~'rgba(41, 121, 255, 1)';
  white-space: nowrap;
  text-overflow: ellipsis;
  background: ~'rgba(240, 248, 255, 1)';
  border-radius: 2px;
}

.udmCompany {
  font-size: 12px;
  font-weight: 400;
  color: #fd7d23;
  line-height: 20px;
  background: #fff7e9;
  margin-left: 4px;
  border-radius: 2px;
  height: 20px;
  display: inline-block;
  text-align: center;
  padding: 0 6px;
}

.oftenTag {
  color: #7816ff;
  background: #f5e8ff;
}

.bottom {
  font-size: 12px;
  font-weight: 400;
  color: #8c8c8c;
  line-height: 20px;
  width: 100%;
  overflow: hidden;
}

.item {
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 15px;
  line-height: 14px;
  margin: 4px 4px -3px 0;
  padding-right: 4px;
  color: @yq-text-6;
  font-size: 14px;
}

.border {
  border-right: 1px solid @yq-border-4;
}
