import React from 'react';
import { observer } from 'mobx-react-lite';
import { YqAvatar } from '@zknow/components';
import classnames from 'classnames';
import { inject } from 'mobx-react';
import AvatarTooltip from '@/components/avatar-tooltip';
import styles from './UserItem.module.less';

export default inject('HeaderStore')(observer((props) => {
  const {
    record,
    userDataSet,
    knowledgeDataSet,
    handleDoubleClick = () => { },
    HeaderStore,
    intl,
    tenantId,
  } = props;
  if (!record) return null;
  const { personCardConfig = [] } = HeaderStore?.getTenantPersonConfig || {};

  const isCurrent = userDataSet.current?.get('id') === record.get('id');
  const className = classnames({
    [styles.user]: true,
    [styles.selected]: isCurrent,
  });

  return (
    <div
      className={className}
      onClick={() => {
        userDataSet.current = record;
        knowledgeDataSet.current = undefined;
      }}
      onDoubleClick={() => handleDoubleClick({ record, type: 'user' })}
    >
      <div className={styles.left}>
        <AvatarTooltip id={record.get('id')} placement="leftTop">
          <YqAvatar src={record.get('image_url')} size={32}>
            {record.get('real_name')}
          </YqAvatar>
        </AvatarTooltip>
      </div>
      <div className={styles.right}>
        <div className={styles.top}>
          {record.get('real_name')}
          {(record.get('company_name') && record.get('from_tenant_id') !== tenantId) && <span className={styles.udmCompany}>{record.get('company_name')}</span>}
          {record.get('participantFlag') && <span className={styles.tag}>{intl.formatMessage({ id: 'lcr.components.desc.participant.flag', defaultMessage: '参与人' })}</span>}
          {record.get('dailyFlag') && <span className={`${styles.tag} ${styles.oftenTag}`}>{intl.formatMessage({ id: 'lcr.components.desc.daily.flag', defaultMessage: '经常@' })}</span>}
        </div>
        <div className={styles.bottom}>
          {(personCardConfig
            ?.slice?.(0, 3) || [])
            .filter(value => record?.get(value.nameField))
            .map(
              (value, index, arr) => (
                <div
                  className={classnames(styles.item, { [styles.border]: index !== arr.length - 1 })}
                  style={{ maxWidth: `${((100 / arr.length) - 1)}%` }}
                >
                  {record?.get(value.nameField)}
                </div>
              )
            )}
        </div>
      </div>
    </div>
  );
}));
