import React from 'react';
import { Icon } from '@zknow/components';

function KnowledgeIcon({ fileType = 'UNKNOW', expand = false, style, record, ...restProps }) {
  const unknow = { type: 'Notes', fill: '#6B7AD2' };
  const folderClose = { type: 'FolderClose', fill: '#FFC107' };
  const folderExpand = { type: 'FolderOpen', fill: '#FFC107' };
  const excel = { type: 'FileExcel', fill: '#4CAF50' };
  const doc = { type: 'FileWord', fill: '#03A9F4' };
  const pdf = { type: 'icon-wenjian-pdf_file-pdf', fill: '#F05743' };
  const ppt = { type: 'FilePdf', fill: '#EE7C2E' };
  const txt = { type: 'FileTxt', fill: '#576C7E' };
  const video = { type: 'videoFile', fill: '#1858d9' };
  const failed = { type: 'RectangleTear', fill: '#8C8C8C' };
  const jpg = { type: 'FileJpg', fill: '#F6B84E' };
  const gif = { type: 'FileGif', fill: '#9376BD' };
  const zip = { type: 'zip', fill: '#8B7C8F' };
  const png = { type: 'ImageFiles', fill: '#F6B84E' };

  const iconMap = {
    EXCEL: excel,
    DOCUMENT: doc,
    FOLDER: expand ? folderExpand : folderClose,
    DOCX: doc,
    DOC: doc,
    XLS: excel,
    XLSX: excel,
    PPT: ppt,
    PPTX: ppt,
    PDF: pdf,
    TXT: txt,

    MP4: video,
    AVI: video,
    RMVB: video,
    RM: video,
    FLV: video,
    MPG: video,

    JPG: jpg,
    GIF: gif,
    PNG: png,
    ZIP: zip,

    UNKNOW: unknow,
    FAILED: failed,
  };

  if (record) {
    fileType = record?.get('fileType')
    || record?.get('extraParam1')
    || record?.get('type');

    if (fileType === 'UPLOAD') {
      const fileName = record.get('name') || record.get('title');
      fileType = fileName?.split('.').pop()?.replace('<b class="yq-hlt">', '').replace('</b>', '');
    }
  }

  const { type, fill } = iconMap[fileType?.toUpperCase()] || unknow;

  return (
    <Icon
      type={type}
      fill={fill}
      theme="filled"
      color={fill}
      style={{ color: fill, ...style }}
      {...restProps}
    />
  );
}
export default KnowledgeIcon;
