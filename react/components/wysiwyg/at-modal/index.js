import React, { useContext, useState, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Spin, Button, Tabs, message, Tooltip } from 'choerodon-ui/pro';
import { Icon, Empty, ExternalComponent } from '@zknow/components';
import axios from 'axios';
import { useScrollToBottom } from 'use-scroll-to-bottom';
import UserItem from './user-item';
import NoData from './NoData';
import useReplaceState from '../../../hooks/useReplaceState';
import Store from '../stores';

import './index.less';

const TabPane = Tabs.TabPane;

const ModalWidth = 520;
const ModalMargin = 25;

const KnowledgeItem = (props) => {
  return (
    <ExternalComponent
      system={{
        scope: 'itsm',
        module: 'AtMentionKnowledgeCard',
      }}
      {...props}
    />
  );
};

const AtModal = (props) => {
  const { editor, handleChange } = props;
  const {
    userDataSet,
    prefixCls,
    knowledgeDataSet,
    tenantId,
    intl,
    atMotionKnowledgeFlag, // 服务配置-回复-控制@列表显示的配置
    atMotionPersonFlag,
  } = useContext(Store);
  const defaultActiveKey = useMemo(() => {
    if (atMotionPersonFlag) {
      return 'knowledge';
    }
    if (atMotionKnowledgeFlag) {
      return 'user';
    }
    return 'all';
  }, [atMotionPersonFlag, atMotionKnowledgeFlag]);
  const [currentTab, setCurrentTab] = useState(defaultActiveKey);
  const [searchValue, setSearchValue] = useState();
  const [loading, setLoading] = useState(false);
  const bounds = userDataSet.getState('bounds');

  const [userRef, userBottom] = useScrollToBottom();
  const [knowledgeRef, knowledgeBottom] = useScrollToBottom();

  useEffect(() => {
    async function query(dataSet) {
      if (dataSet.length !== dataSet.totalCount) {
        setLoading(true);
        await dataSet.queryMore(dataSet.currentPage + 1);
        setLoading(false);
      }
    }
    userBottom && query(userDataSet);
    knowledgeBottom && query(knowledgeDataSet);
  }, [userBottom, knowledgeBottom]);

  function listenUrlStateAndClose() {
    if (userDataSet) {
      userDataSet.setState('show', false);
    }
  }

  // 工作台切换 Tab 页的时候，需要关闭@弹窗
  useReplaceState(listenUrlStateAndClose);

  /**
   * 跟据 #zknow-app 元素进行定位
   * @returns {number}
   */
  function getTranslateX() {
    if (bounds && editor) {
      const targetLeft = bounds.left;
      const targetOffsetRight = window.innerWidth - targetLeft;
      const mouseRect = userDataSet.getState('mouseRect');
      const mouseOffsetLeft = mouseRect?.x;
      if (typeof mouseOffsetLeft === 'number') {
        const mouseOffsetRight = window.innerWidth - mouseOffsetLeft;
        if (mouseOffsetRight < ModalWidth) {
          return mouseOffsetLeft - ModalWidth;
        } else {
          return mouseOffsetLeft;
        }
      } else if (targetOffsetRight > ModalWidth || targetLeft < ModalWidth) {
        return targetLeft - ModalMargin;
      } else {
        return targetLeft - ModalWidth + ModalMargin;
      }
    }
  }

  function getTranslateY() {
    if (bounds && editor) {
      const atModalHeight = 327; // at弹出框的高度
      const bufferHeight = 20; // 缓冲高度
      const screenHeight = document.body.clientHeight; // 屏幕的高度
      const editorDom = editor.editing.view.getDomRoot();
      const editorDomTop = editorDom.getBoundingClientRect().top; // 当前富文本距离可视窗顶部的距离
      const lineHeight = 28; // 行高
      const mouseRect = userDataSet.getState('mouseRect');
      const mouseOffsetTop = mouseRect?.y;
      if (typeof mouseOffsetTop === 'number') {
        if (screenHeight - mouseOffsetTop > (atModalHeight + bufferHeight)) {
          return mouseOffsetTop + lineHeight;
        } else {
          return mouseOffsetTop - atModalHeight;
        }
      }
      if (/* at框框在下方 */ screenHeight - editorDomTop - bufferHeight > atModalHeight) {
        return editorDomTop + bufferHeight + lineHeight;
      }
      // at框框在上放
      return editorDomTop - atModalHeight;
    }
  }

  const handleClose = (needReplace) => {
    userDataSet.setState('show', false);
    // 关闭at框后添加一个空格防止继续弹出， 方法一
    // if (editor) {
    //   editor.editing.view.focus();
    //   editor.model.change(writer => {
    //     editor.model.insertContent(writer.createText(' '));
    //   });
    //   editor.editing.view.focus();
    // }

    // 方法二，替换＠字符
    if (editor && needReplace) {
      const paramLength = userDataSet.getState('param')?.length || 0;
      for (let i = 0; i <= paramLength; i++) {
        editor.execute('delete', { direction: 'backward' });
      }
      const text = `＠${userDataSet.getState('param')}`;
      // insert before input @
      editor.editing.view.focus();
      editor.model.change(writer => {
        editor.model.insertContent(writer.createText(text));
      });
      editor.editing.view.focus();
    }
  };

  useEffect(() => {
    const searchParam = userDataSet.getState('param');
    handleSearch({ target: { value: searchParam } });
  }, [userDataSet.getState('param')]);

  function handleSelect({ record, type = 'user', quoteKnowledgeFlag }) {
    const { model } = editor;
    const paramLength = userDataSet.getState('param')?.length || 0;
    for (let i = 0; i <= paramLength; i++) {
      editor.execute('delete', { direction: 'backward' });
    }
    const focus = model.document.selection.focus;
    // insert before input @
    const range = model.createRange(focus.getShiftedBy(0), focus.getShiftedBy(0));
    let sendName = record.get('real_name');
    let selectType = 'user';
    if (type === 'all') {
      if (userDataSet.current) {
        sendName = record.get('real_name');
        selectType = 'user';
      } else if (knowledgeDataSet.current) {
        sendName = record.get('titleNormal');
        selectType = 'knowledge';
      }
    } else {
      sendName = type === 'user' ? record.get('real_name') : record.get('titleNormal');
      selectType = type === 'user' ? 'user' : 'knowledge';
    }
    const companyData = record.get('company_name') ? { companyName: record.get('company_name'), fromtenantid: record.get('from_tenant_id') } : {};
    editor.execute('yqmention', {
      mention: {
        id: record.get('id'),
        name: sendName,
        type: selectType,
        tenantId,
        ...companyData,
      },
      text: sendName,
      range,
    });
    handleChange('atEvent', editor);
    // 直接引用知识前面加上@知识
    if (quoteKnowledgeFlag) {
      handleQuoteKnowledge({ record: knowledgeDataSet.current });
    }
    handleClose();
  }

  // 提及知识
  async function handleQuoteKnowledge({ record }) {
    if (!record) return null;
    const res = await axios.get(`/knowledge/v1/${tenantId}/know/document/getHTML/${record?.get('id')}`);
    if (res?.length > 10000) {
      message.warning(intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.mention.too.large', defaultMessage: '内容过多，无法直接引用，请直接“提及”' }));
      return null;
    }

    editor.execute('yqreference', {
      data: `<p>${res}</p>`,
    });
    handleChange('atEvent', editor);
    handleClose();

    return null;
  }

  function handleSearch(e) {
    const value = e.target.value;
    userDataSet.setQueryParameter('search_real_name', value);
    userDataSet.query();
    knowledgeDataSet.setQueryParameter('queryText', value);
    knowledgeDataSet.query();
  }

  // 渲染全部
  function renderAllList() {
    return (
      <TabPane
        tab={intl.formatMessage({ id: 'zknow.common.desc.all', defaultMessage: '全部' })}
        key="all"
      >
        <Spin dataSet={knowledgeDataSet}>
          <div className={`${prefixCls}-user-list`}>
            <div className={`${prefixCls}-user-box`}>
              {
                userDataSet.map(r => (
                  <UserItem
                    activeKey={currentTab}
                    record={r}
                    key={r.get('id')}
                    userDataSet={userDataSet}
                    knowledgeDataSet={knowledgeDataSet}
                    handleDoubleClick={handleSelect}
                    intl={intl}
                    tenantId={tenantId}
                  />
                ))
              }
              {
                knowledgeDataSet.map(r => (
                  <KnowledgeItem
                    activeKey={currentTab}
                    record={r}
                    key={r.get('id')}
                    userDataSet={userDataSet}
                    knowledgeDataSet={knowledgeDataSet}
                    handleDoubleClick={handleSelect}
                    intl={intl}
                  />
                ))
              }
              {
                knowledgeDataSet.length === 0 && userDataSet.length === 0 && <Empty />
              }
            </div>
          </div>
        </Spin>
      </TabPane>
    );
  }

  const renderUserList = () => {
    return (
      <TabPane
        tab={intl.formatMessage({ id: 'zknow.common.model.person', defaultMessage: '人员' })}
        key="user"
      >
        <Spin dataSet={userDataSet}>
          <div className={`${prefixCls}-user-list`}>
            <div className={`${prefixCls}-user-box`}>
              {
                userDataSet.map(r => (
                  <UserItem
                    activeKey={currentTab}
                    record={r}
                    key={r.get('id')}
                    userDataSet={userDataSet}
                    knowledgeDataSet={knowledgeDataSet}
                    handleDoubleClick={handleSelect}
                    intl={intl}
                    tenantId={tenantId}
                  />
                ))
              }
              {userDataSet.length === 0 && <Empty />}
            </div>
            {loading && <div className={`${prefixCls}-loading`}>
              {intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.loading', defaultMessage: '加载中' })}
              <div className={`${prefixCls}-loading-animation`}>
                <div />
                <div />
                <div />
              </div>
            </div>}
            <div ref={userRef} style={{ width: '100%', height: 10 }} />
          </div>
        </Spin>
      </TabPane>
    );
  };

  // 知识
  const renderKnowledgeList = () => {
    return (
      <TabPane
        tab={intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.knowledge', defaultMessage: '知识' })}
        key="knowledge"
      >
        <Spin dataSet={knowledgeDataSet}>
          <div className={`${prefixCls}-user-list`}>
            <div className={`${prefixCls}-user-box`}>
              {
                knowledgeDataSet.map(r => (
                  <KnowledgeItem
                    activeKey={currentTab}
                    record={r}
                    key={r.get('id')}
                    userDataSet={userDataSet}
                    knowledgeDataSet={knowledgeDataSet}
                    handleDoubleClick={handleSelect}
                    intl={intl}
                  />
                ))
              }{
                knowledgeDataSet.length === 0 && <NoData />
              }
            </div>
            {loading && <div className={`${prefixCls}-loading`}>
              {intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.loading', defaultMessage: '加载中' })}
              <div className={`${prefixCls}-loading-animation`}>
                <div />
                <div />
                <div />
              </div>
            </div>}
            <div ref={knowledgeRef} style={{ width: '100%', height: 10 }} />
          </div>
        </Spin>
      </TabPane>
    );
  };

  function getCurrentRecord() {
    if (currentTab === 'all') {
      return userDataSet.current || knowledgeDataSet.current;
    }
    if (currentTab === 'user') return userDataSet.current;
    return knowledgeDataSet.current;
  }

  function getDisabled() {
    if (currentTab === 'user') return !userDataSet.current;
    if (currentTab === 'knowledge') return !knowledgeDataSet.current;
    return !userDataSet.current && !knowledgeDataSet.current;
  }
  // 底部模块
  function renderModalFooter() {
    return (
      <div className={`${prefixCls}-user-modal-footer`}>
        <div className="footer-left">
          <TextField
            className="footer-left-textfield"
            placeholder={intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.at.placeholder', defaultMessage: '请输入搜索内容' })}
            value={searchValue}
            onChange={(value) => { setSearchValue(value); }}
            onEnterDown={(e) => handleSearch(e)}
          />
        </div>
        <div className="footer-right">
          <Button
            funcType="raised"
            color="primary"
            disabled={getDisabled()}
            onClick={() => handleSelect({
              type: currentTab,
              record: getCurrentRecord(),
            })}
          >
            {intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.mention', defaultMessage: '提及' })}
          </Button>
          {currentTab !== 'user' && knowledgeDataSet.current?.get('extraParam1') === 'DOCUMENT'
            && <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.reference', defaultMessage: '直接引用' })}>
              <Button onClick={() => {
                handleSelect({
                  type: currentTab,
                  record: getCurrentRecord(),
                  quoteKnowledgeFlag: true,
                });
              }}
              >
                {intl.formatMessage({ id: 'lcr.components.desc.lc.wysiwyg.reference', defaultMessage: '直接引用' })}
              </Button>
            </Tooltip>}
        </div>
      </div>
    );
  }

  return userDataSet.getState('show') ? (
    <div
      className={`${prefixCls}-user-modal`}
      style={
        userDataSet.getState('fullscreen') ? {
          position: 'fixed',
          top: 0,
          left: 0,
          zIndex: 1002,
          transform: `translate(${getTranslateX()}px, ${getTranslateY()}px)`,
        } : {
          transform: `translate(${getTranslateX()}px, ${0}px)`,
          top: `${getTranslateY()}px`,
          zIndex: 1004,
        }
      }
    >
      <div className={`${prefixCls}-user-modal-close`}>
        <Icon type="close" onClick={() => handleClose(true)} className={`${prefixCls}-user-modal-close-button`} />
      </div>
      <Tabs
        className={`${prefixCls}-user-modal-tabs`}
        defaultActiveKey={defaultActiveKey}
        tabBarGutter={16}
        activeKey={currentTab}
        onChange={(value) => setCurrentTab(value)}
      >
        {atMotionPersonFlag || atMotionKnowledgeFlag ? null : renderAllList()}
        {!atMotionPersonFlag && renderUserList()}
        {!atMotionKnowledgeFlag && renderKnowledgeList()}
      </Tabs>
      {renderModalFooter()}
    </div>
  ) : null;
};

export default observer(AtModal);
