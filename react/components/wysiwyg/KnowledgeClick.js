import qs from 'qs';

const KnowledgeClick = (editor) => {
  editor.editing.view.document.on('click', (eventInfo, eventData) => {
    const { domTarget } = eventData;
    if (domTarget.nodeName === 'SPAN' && domTarget.getAttribute('yqmention-type') === 'knowledge') {
      const { ticketId } = qs.parse(window.location.href.split('?').pop());
      const knowledgeId = domTarget.getAttribute('data-yqmention');
      const tenantId = domTarget.getAttribute('tenantId');
      const params = {
        tenantId,
        menu: 'knowledge',
        knowledgeId,
        sourceModule: 'TICKETS',
        sourceFunction: 'QUOTING_KNOWLEDGE',
        sourceId: ticketId,
      };
      const url = `${window.location.origin}/#/itsm/portal/knowledge?${qs.stringify(params)}`;
      window.open(url);
    }
  });
};

export default KnowledgeClick;
