import React, { useContext, useEffect, useCallback, useRef, useMemo, useState } from 'react';
import { observer } from 'mobx-react-lite';
import Rc<PERSON>iewer from '@hanyk/rc-viewer';
import { getAccessToken, getCookieToken } from '@zknow/utils';
import { CKEditor } from '@yqcloud/ckeditor5-react';
import YQEditor from '@yqcloud/ckeditor5';
import { message, Progress } from 'choerodon-ui/pro';
import { ExternalComponent, FileUploader, Icon, PortalReact, TranslateArea, LoadingGPT } from '@zknow/components';
import { debounce, uniq, isEmpty } from 'lodash';
import lodashGet from 'lodash/get';
import FileItem, { FileInfo } from '@/components/file-item';
import { getRichJson, getRichTextIsNull, isInIframe, transTextToHTML } from '@/utils';
import { getFileIcon, getFileName, getFileType } from '../editor-register/utils';
import YQUpload from './YQUpload';
import ImageView from './ImageView';
import HandleKnowledgeClick from './KnowledgeClick';
import AtModal from './at-modal';
import { openOcrModal } from '../ocr-modal/MainView';
import Store from './stores';
import './zh-cn.js';
import './index.less';
import { queryTranslateNormal, queryTranslateNormalAsync, getAiSummaryPoll, getAiSummaryAsync } from '@/service';
import GptLoading from '@/components/gpt-loading';

const noop = () => { };

const MiniToolBar = [
  'undo',
  'redo',
  'bold',
  'italic',
  'fontColor',
  'fontBackgroundColor',
  'bulletedList',
  'numberedList',
  'insertTable',
  'uploadImage',
  'link',
];

const RICH_TEXT_NULL_LIST = [
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\\n\"}]',
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\n\"}]',
  // eslint-disable-next-line no-useless-escape
  '[{\"insert\":\"\"}]',
  '[]',
  'null',
  '\u200b',
  null,
];

const FULLSCREEN = 'fullscreen';
const OCR = 'ocr';
const UPLOAD = 'upload';
const isSafari = navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');

const Wysiwyg = () => {
  const {
    name,
    tenantId,
    overrideConfig = {},
    data = '', // ckeditor自身格式数据
    htmlData = '', // 带有data-json的数据
    onReady = noop,
    onChange = noop,
    onBlur = noop,
    onFocus = noop,
    disabled,
    preview,
    minHeight: minHeightValue,
    maxHeight,
    userDataSet,
    placeholder,
    miniMode = true,
    ocrFlag,
    formDs,
    record,
    intl,
    isLcRichText = true,
    uploadFlag = false,
    atSignRef = {},
    autoFocus = false,
    AppState,
    udmTenantId,
    ticketTranslationFlag, // 目前工单翻译的设置是在服务配置中，和业务对象相关联的，所以先通过外部传参方式
    ticketTranslationConfig,
    ticketSolutionFlag,
    ticketSolutionConfig,
    atMotionKnowledgeFlag, // 服务配置-回复-控制@列表显示的配置
    atMotionPersonFlag,
    businessObjectId = '',
    ticketId,
    businessObjectCode,
    HeaderStore: { getTenantConfig: { enableChatGptFlag } },
  } = useContext(Store);
  const solutionRef = useRef(false); // 正在进行解决方案操作
  const solutionTimerRef = useRef();
  const solutionCountRef = useRef(30);

  const errorTranslateMsg = intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' });
  const countRef = useRef(30);
  const [translateLoading, setTranslateLoading] = React.useState(false);
  const [solutionLoading, setSolutionLoading] = React.useState(false);
  const [ckUploadFile, setCkUploadFile] = useState(null);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    return () => {
      countRef.current = -1;
      clearInterval(solutionTimerRef.current);
      solutionCountRef.current = -1;
    };
  }, []);

  // 在预览模式下 富文本最小高度为一行
  const minHeight = preview ? 35 + 13.2 : minHeightValue;

  const editorInstance = useRef();
  const imageViewRef = useRef(null);
  const initHeight = useRef(0);
  const currentHeight = useRef(0);

  useEffect(() => {
    if (initialized && editorInstance.current) {
      const editableElement = editorInstance.current?.ui?.view?.editable?.element;

      /**
       * 监听 copy 事件，目前富文本对 @ 的实现是通过，将英文的@替换为中文的＠来标识已经完成了提及
       *   问题是用户再次复制出来的时候，不会进行逆向转换，导致邮箱地址等失效
       *   通过拦截监听事件，在用户复制内容的时候进行替换
       */
      const handleCopy = (evt) => {
        // 获取选中的文本
        const selectedText = window.getSelection().toString();

        // 替换文本中的特殊字符，例如将 @ 替换为 #
        const modifiedText = selectedText.replace(/＠/g, '@');
        // 将修改后的文本放入剪贴板
        if (evt.clipboardData) {
          evt.clipboardData.setData('text/plain', modifiedText);
        }
      };

      // 绑定复制事件
      editableElement?.addEventListener('copy', handleCopy);

      return () => {
        editableElement?.removeEventListener('copy', handleCopy);
      };
    }
  }, [initialized]);

  function setInitHeight(v) {
    initHeight.current = v;
  }

  function setCurrentHeight(v) {
    currentHeight.current = v;
  }

  const startY = useRef(0);

  function setStartY(v) {
    startY.current = v;
  }

  // const [editorInstance, setEditor] = useState();
  // 草稿逻辑，如果是草稿，有数据，就在第一次加载后把草稿赋值进去
  // issues: yq-1358
  function setDraftData() {
    const hasDraft = formDs?.getState('__isDraft');
    if (hasDraft && data && editorInstance) {
      if (!editorInstance.current?.getData()) {
        editorInstance?.setData(data);
      }
    }
  }

  function setPreviewClass() {
    if (editorInstance.current) {
      const container = editorInstance.current.sourceElement.parentElement;
      if (container?.classList) {
        // 预览模式
        if (container && preview) {
          container.classList.add('ck-editor-preview');
        }
        if (container && !preview) {
          container.classList.remove('ck-editor-preview');
        }
      }
    }
  }

  function setEditor(v) {
    editorInstance.current = v;
    setPreviewClass();
    setDraftData();
  }

  const toolbar = useMemo(() => {
    const toolbars = [...MiniToolBar];
    if (uploadFlag) toolbars.push(UPLOAD);
    if (ocrFlag) toolbars.push(OCR);
    toolbars.push(FULLSCREEN);
    return toolbars;
  }, [uploadFlag, ocrFlag, preview]);
  const uploadRef = useRef();

  useEffect(() => {
    if (!initialized || !enableChatGptFlag || isInIframe() || preview || !businessObjectId) {
      return;
    }

    try {
      const editor = editorInstance.current;
      if (ticketTranslationFlag) {
        editor.config.set('translate', getTranslateConfig());
        editor.ui.view.toolbar.items.add(editor.ui.componentFactory.create('translate'));
      }
      const isSolutionField = (ticketSolutionConfig?.fields || []).some(field => field.code === name);
      if (ticketSolutionFlag && isSolutionField) {
        editor.config.set('solutions', getSolutionsConfig());
        editor.ui.view.toolbar.items.add(editor.ui.componentFactory.create('solutions'));
      }
    } catch (e) {
      window.console.error('Wysiwyg CKEditor ERROR:', e);
    }
  }, [initialized, ticketTranslationFlag, ticketSolutionFlag, businessObjectId, enableChatGptFlag]);

  useEffect(() => {
    setPreviewClass();
  }, [preview]);

  useEffect(() => {
    try {
      atSignRef.current = showAt;
    } catch {
      /* */
    }
  }, [editorInstance, atSignRef]);

  const showAt = () => {
    if (editorInstance?.current) {
      editorInstance?.current.editing.view.focus();
      editorInstance?.current.model.change(writer => {
        editorInstance?.current.model.insertContent(writer.createText('@'));
      });
      editorInstance?.current.editing.view.focus();
    }
  };

  const transformResponse = (response) => {
    const { fileKey = '' } = response;
    if (!fileKey.startsWith('http')) {
      return { url: `${window._env_.API_HOST}/hfle/yqc/v1/0/files/download-by-key?fileKey=${encodeURIComponent(fileKey)}&access_token=${getCookieToken() || AppState?.getAccessToken}` };
    } else {
      return { url: fileKey };
    }
  };

  function requiredValidate() {
    if (editorInstance.current && formDs && name && formDs?.current?.getField(name)?.required) {
      const container = editorInstance.current.sourceElement.parentElement;
      const value = formDs?.current?.get(name);
      if (typeof value === 'string') {
        if (value && !getRichTextIsNull(value)) {
          container.classList.remove('ck-editor-required');
        } else {
          container.classList.add('ck-editor-required');
        }
      } else if (!value || RICH_TEXT_NULL_LIST.includes(JSON.stringify(value))) {
        container.classList.add('ck-editor-required');
      }
    }
  }

  useEffect(() => {
    requiredValidate();
  }, [formDs?.current?.get(name)]);

  // 字段校验
  useEffect(() => {
    if (editorInstance.current && formDs && name) {
      const container = editorInstance.current.sourceElement.parentElement;
      const errorsInfo = formDs.getValidationErrors();
      if (errorsInfo.length) {
        if (errorsInfo[0].errors?.some(error => error?.field?.name === name) && getRichTextIsNull(formDs?.current?.get?.(name))) {
          container.classList.add('ck-editor-required');
        } else {
          container.classList.remove('ck-editor-required');
        }
      } else {
        container.classList.remove('ck-editor-required');
      }
    }
  }, [formDs?.getValidationErrors(), name, formDs]);

  const handleBlur = (_data) => {
    if (isSafari) {
      const currentData = editorInstance.current.getData();
      if (['<p>\u200b</p>'].includes(currentData)) {
        editorInstance.current.setData('');
      }
    }
    // 校验必填
    requiredValidate();
    onBlur(_data, editorInstance.current);
  };

  const handleFocus = () => {
    if (isSafari) {
      const currentData = editorInstance.current.getData();
      if (['<p></p>', ''].includes(currentData)) {
        editorInstance.current.setData('<p>\u200b</p>');
      }
    }
    onFocus();
  };

  const handleReady = (editor) => {
    setEditor(editor);
    if (!editor?.editing?.view) return;
    if (minHeight) {
      editor.editing.view.change((writer) => {
        writer.setStyle('min-height', `${minHeight}px`, editor.editing.view.document.getRoot());
      });
    }
    if (maxHeight) {
      editor.editing.view.change((writer) => {
        writer.setStyle('max-height', `${maxHeight}px`, editor.editing.view.document.getRoot());
      });
    }
    setInitHeight(editor.editing.view.getDomRoot()
      .getBoundingClientRect().height);
    const initY = Math.floor(editor.editing.view.getDomRoot()
      .getBoundingClientRect().bottom) - 9;
    setStartY(initY);
    if (autoFocus) {
      editor.editing.view.focus();
    }
    onReady(editor);
    setInitialized(true);
  };

  /**
   * 富文本变更
   * @param event
   * @param editor
   * @param file 上传附件key
   */
  const handleChange = (event, editor, file) => {
    onChange(event, editor, file);
  };

  const toggleFullscreen = (status) => {
    userDataSet.setState('fullscreen', status);
  };

  const handleMove = useCallback((e) => {
    const distance = e.clientY - startY.current;
    let newHeight = initHeight.current + distance;
    // 取消拖拽时最大长度限制
    if (newHeight >= maxHeight) {
      editorInstance.current.editing.view.change((writer) => {
        writer.setStyle(
          'max-height',
          undefined,
          editorInstance.current.editing.view.document.getRoot(),
        );
        setCurrentHeight(editorInstance.current.editing.view.getDomRoot()
          .getBoundingClientRect().height);
      });
    }

    if (newHeight <= minHeight) {
      if (initHeight.current === minHeight) {
        return;
      }
      newHeight = minHeight;
    }
    editorInstance.current.editing.view.change((writer) => {
      writer.setStyle(
        'height',
        `${newHeight}px`,
        editorInstance.current.editing.view.document.getRoot(),
      );
      setCurrentHeight(editorInstance.current.editing.view.getDomRoot()
        .getBoundingClientRect().height);
    });
  }, [editorInstance]);

  const handleResizeStart = () => {
    // setInitHeight(editorInstance.editing.view.getDomRoot().getBoundingClientRect().height);
    document.addEventListener('mousemove', handleMove);
    document.addEventListener('mouseup', () => {
      document.removeEventListener('mousemove', handleMove);
      setStartY(Math.floor(editorInstance.current.editing.view.getDomRoot()
        ?.getBoundingClientRect().bottom) - 9);
      setCurrentHeight(initHeight.current);
    });
  };

  // 计算拖拽按钮位置
  function getBottom(content) {
    let bottom = 0;
    try {
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const {
          audios = [],
          attachments = [],
        } = htmlObj;
        if (audios.length) {
          bottom += audios.length * 33 + 12;
        }
        if (attachments.length) {
          const length = getNewFileFlag() ? attachments.length + 1 : attachments.length;
          bottom += length * 33 + 12;
        }
      }
      return bottom;
    } catch {
      return bottom;
    }
  }

  // 渲染音频文件
  function renderAudioArea(content) {
    try {
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [] } = htmlObj;
        if (audios.length === 0) return null;
        return (
          <div className="reply-audio">
            {audios.map((i) => {
              return <ExternalComponent
                system={{
                  scope: 'itsm',
                  module: 'YqAudio',
                }}
                fileInfo={i}
              />;
            })}
          </div>
        );
      }
      return null;
    } catch {
      //
    }
  }

  // 校验文件大小
  function beforeUpload(file, fileLimit) {
    const fileSize = file.size / 1024 / 1024;
    setCkUploadFile(file);
    if (fileLimit && fileSize > fileLimit) {
      message.error(intl.formatMessage({
        id: 'lcr.components.desc.file.upload.limit',
        defaultMessage: '上传文件不能大于{limit}MB',
      }, { limit: fileLimit }));
      formDs?.setState('wysiwyg-uploading', false);
      return false;
    }
    formDs?.setState('wysiwyg-uploading', true);
    return true;
  }

  /**
   * 上传中的附件，显示上传进度
   * @type {React.FunctionComponent<object>}
   */
  const NewFile = observer(({
    flag,
    fileName,
    fileSize,
  }) => {
    if (!flag) {
      return null;
    }

    return (
      <div
        tabIndex="-1"
        onClick={(e) => e.stopPropagation()}
        className="new-file"
        style={{ position: 'relative' }}
      >
        <span style={{ width: '100%' }}>
          <Icon
            type={getFileIcon(getFileType(fileName)).iconType}
            theme="filled"
            fill={getFileIcon(getFileType(fileName)).color}
            size="16"
            className="lc-file-link"
          />
          <span
            style={{ width: '100%' }}
          >
            <FileInfo
              fileName={getFileName(fileName)}
              fileSize={fileSize}
            />
          </span>
        </span>
        <span style={{
          marginRight: 8,
          position: 'absolute',
          right: 8,
          top: 6,
          zIndex: 10,
        }}
        >
          <span style={{
            color: '#8C8C8C',
            marginRight: '.06rem',
            fontSize: '.12rem',
          }}
          >
            {`${Math.floor(formDs?.current?.getState(`progress-${ckUploadFile?.uid}`))}%`}
          </span>
          <span style={{
            position: 'relative',
            top: -2,
          }}
          >
            <Progress
              type="circle"
              percent={formDs?.current?.getState(`progress-${ckUploadFile?.uid}`)}
              width={18}
              format={() => ''}
            />
          </span>
        </span>
      </div>
    );
  });

  function getNewFileFlag() {
    return formDs?.current?.getState(`progress-${ckUploadFile?.uid}`)
      && formDs?.current?.getState(`progress-${ckUploadFile?.uid}`) !== 100;
  }

  function handleProgress(percent) {
    if (percent === 100) {
      setCkUploadFile(null);
      setTimeout(() => {
        formDs?.setState('wysiwyg-uploading', false);
      }, 1000);
    } else {
      formDs?.setState('wysiwyg-uploading', true);
    }
    formDs?.current?.setState(`progress-${ckUploadFile?.uid}`, percent);
  }

  const yqUploadProgress = (flag) => {
    formDs?.setState('wysiwyg-uploading', flag);
  };

  // 渲染附件
  const renderFileList = (content) => {
    try {
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { attachments = [] } = htmlObj;
        if (attachments.length === 0) return null;
        return (
          <div className="dynamic-box-fileList">
            {attachments.map((file) => (
              <FileItem
                file={file}
                intl={intl}
                onDelete={preview || disabled ? false : () => handleChange('deleteFile', editorInstance.current, file)}
              />
            ))}
          </div>
        );
      }
    } catch {
      //
    }
  };

  const KnowledgeClick = (editor) => {
    if (udmTenantId) {
      return () => {
      };
    } else {
      return HandleKnowledgeClick(editor);
    }
  };

  const getTranslateResult = async (uuid, callback) => {
    const res = await queryTranslateNormal(tenantId, uuid);
    if (countRef.current === -1) {
      setTranslateLoading(false);
      return false;
    }
    if (res?.failed) {
      callback(errorTranslateMsg, true);
      setTranslateLoading(false);
    } else if (res) {
      if (!isEmpty(res)) {
        callback(res, false);
      } else {
        callback(errorTranslateMsg, true);
      }
      setTranslateLoading(false);
    } else if (countRef.current > 0) {
      setTimeout(() => {
        getTranslateResult(uuid, callback);
      }, 3000);
    }
  };

  async function handleTranslate(isAllContent, value, targetLang, callback) {
    try {
      setTranslateLoading(true);
      const res = await queryTranslateNormalAsync({
        tenantId,
        businessObjectId,
        targetLang,
        message: value,
      });
      if (typeof res === 'string') {
        getTranslateResult(res, callback);
      } else {
        callback(errorTranslateMsg, true);
        setTranslateLoading(false);
      }
    } catch (err) {
      callback(errorTranslateMsg, true);
      setTranslateLoading(false);
    }
  }

  const getTranslateConfig = () => {
    if (ticketTranslationFlag) {
      const options = uniq(ticketTranslationConfig?.targetLanguageList)
        .map((item) => ({
          code: item,
          name: (ticketTranslationConfig?.languageValueList || []).find((i) => i.code === item)?.value || item,
        }));

      return {
        options,
        /**
         * @param noSelected 是否有选择
         * @param selectedText 选择的文本
         * @param lang 目标语言编码
         * @param callback 翻译后内容替换到目标的方法
         * @returns {Promise<void>}
         */
        translator: async (noSelected, selectedText, lang, callback) => {
          if (noSelected) {
            // 翻译全文
            const content = editorInstance.current?.getData();
            if (content) {
              handleTranslate(noSelected, content, lang, callback);
            }
          } else {
            handleTranslate(noSelected, selectedText, lang, callback);
          }
        },
      };
    }
    return {};
  };

  const pollQueryAiResult = (uuid, isNewUrl = false) => {
    solutionTimerRef.current = setInterval(() => {
      if (solutionCountRef.current > 0) {
        solutionCountRef.current -= 1;
        getAiSummaryPoll({ tenantId, uuid, isNewUrl }).then(resp => {
          if (resp && !resp?.failed) {
            const changedParams = lodashGet(resp, 'changedParams', {});
            Object.keys(changedParams).forEach(v => {
              const htmlStr = transTextToHTML(changedParams[v], 'p');
              formDs?.current.set(name, htmlStr);
            });
            setSolutionLoading(false);
            solutionRef.current = false;
            clearInterval(solutionTimerRef.current);
          }
        });
      } else {
        clearInterval(solutionTimerRef.current);
        setSolutionLoading(false);
        solutionRef.current = false;
      }
    }, 5 * 1000);
  };

  const queryAiUUID = (params) => {
    getAiSummaryAsync(params).then(result => {
      const uuid = typeof result === 'string' ? result : '';
      if (uuid) {
        pollQueryAiResult(uuid, params.isNewUrl);
      } else {
        setSolutionLoading(false);
        solutionRef.current = false;
      }
    }).catch(e => {
      setSolutionLoading(false);
      solutionRef.current = false;
    });
  };

  const prevGenResolution = () => {
    if (solutionRef.current === true) {
      return;
    }
    solutionRef.current = true;
    setSolutionLoading(true);
    solutionCountRef.current = 30;
  };

  // 回复生成解决方案
  function aiGenerateResolution(aiPromptId) {
    prevGenResolution();
    queryAiUUID({ tenantId, businessObjectId, aiPromptId, ticketId, isNewUrl: true });
  }

  // 业务需求，在富文本的 tool 上添加智能生产解决方案的入口
  const getSolutionsConfig = () => {
    if (ticketSolutionFlag && ticketId && businessObjectCode) {
      const {
        bestPromptTemplateFlag,
        generationPromptTemplateFlag,
        similarPromptTemplateFlag,
        knowledgePromptTemplateFlag,
        templateId,
      } = ticketSolutionConfig;
      const intelligentOption = ['reply', 'similar', 'best', 'knowledge'];
      const intelligentOptionFlag = {
        reply: generationPromptTemplateFlag,
        similar: similarPromptTemplateFlag,
        best: bestPromptTemplateFlag,
        knowledge: knowledgePromptTemplateFlag,
      };
      const intelligentFlag = intelligentOption.some(item => intelligentOptionFlag[item]);
      const options = [{
        code: 'reply',
        name: intl.formatMessage({ id: 'lcr.components.ticketSolution.popover.reply', defaultMessage: '智能生成方案' }),
      }];

      return intelligentFlag ? {
        options,
        translator: async (noSelected, selectedText, targetValue, callback) => {
          switch (targetValue) {
            case 'reply':
              aiGenerateResolution(templateId);
              break;
            default:
              break;
          }
        },
      } : {};
    }
    return {};
  };

  const getEditorConfig = () => ({
    toolbar: miniMode ? {
      items: toolbar,
      shouldNotGroupWhenFull: true,
    } : {
      shouldNotGroupWhenFull: true,
    },
    language: intl.locale.toLowerCase(),
    extraPlugins: [YQUpload, ImageView, KnowledgeClick],
    placeholder,
    link: {
      addTargetToExternalLinks: true,
    },
    YQUpload: {
      url: `${window._env_.API_HOST}/hfle/yqc/v1/${tenantId}/files/secret-multipart`,
      headers: {
        'Access-Control-Allow-Origin': '*',
        Authorization: getAccessToken() || AppState?.getAccessToken,
        'x-tenant-id': tenantId,
      },
      errorMessage: intl.formatMessage({
        id: 'lcr.components.desc.upload.failed',
        defaultMessage: '上传失败',
      }),
      tokenFailureMessage: intl.formatMessage({
        id: 'lcr.components.desc.token.failed',
        defaultMessage: '您的登录信息已过期，请刷新页面后重新登录.',
      }),
      transformResponse,
      yqUploadProgress,
    },
    ImageView: {
      handleImageClick: (src) => {
        const viewer = imageViewRef.current?.viewer;
        if (viewer) {
          viewer.element.firstChild.src = src;
          viewer.update();
          viewer.show();
        }
      },
    },
    YQMention: {
      showAt: debounce((editor, bounds, extra = {}) => {
        // 服务配置-回复-@隐藏
        if (atMotionKnowledgeFlag && atMotionPersonFlag) {
          return false;
        }
        try {
          if (sessionStorage.getItem('OPEN_SUPPORT_TENANT_FLAG') === 'true') return false;
        } catch (e) {
          // eslint-disable-next-line no-console
          console.log('sessionStorage not support');
        }
        const { param } = extra;
        userDataSet.setState('show', true);
        let mouseRect = {};
        try {
          // 获取鼠标聚焦位置距离视口顶部的距离
          mouseRect = document.getSelection()
            .getRangeAt(0)
            .getBoundingClientRect();
        } catch (e) {
          // eslint-disable-next-line no-console
          console.log(e);
        }
        userDataSet.setState('mouseRect', mouseRect);
        userDataSet.setState('bounds', bounds);
        userDataSet.setState('param', param);
      }, 300),
      hideAt: debounce(() => {
        userDataSet.setState('show', false);
      }, 500),
    },
    fullScreen: {
      toggleFullscreen,
    },
    ocr: {
      openOcrModal: () => {
        openOcrModal({
          formDs,
          record,
          intl,
          tenantId,
          isLcRichText,
        });
      },
    },
    upload: {
      uploadFile: () => {
        if (uploadRef.current) {
          uploadRef.current.click();
        }
      },
    },
    translate: getTranslateConfig(),
    solutions: getSolutionsConfig(),
    ...overrideConfig,
  });

  return (
    <div className="ck-editor-wrapper">
      {/* 使用传送门将 @ 提及弹框挂载在 <div id="zknow-app"> 节点下，这样 fixed 定位不在受未知影响  */}
      {userDataSet.getState('show')
        && <PortalReact><AtModal editor={editorInstance.current} handleChange={handleChange} /></PortalReact>}
      <RcViewer
        ref={imageViewRef}
        options={{
          toolbar: {
            zoomIn: { show: true },
            zoomOut: { show: true },
            oneToOne: { show: true },
            reset: { show: true },
            prev: { show: false },
            play: { show: true },
            next: { show: false },
            rotateLeft: { show: true },
            rotateRight: { show: true },
            flipHorizontal: { show: true },
            flipVertical: { show: true },
          },
        }}
        style={{ display: 'none' }}
      >
        <img alt="src" />
      </RcViewer>
      <div style={{ display: 'none' }}>
        <FileUploader
          overrideProps={{
            accept: '*',
            className: 'lc-wysiwyg-upload',
            beforeUpload: (file) => beforeUpload(file, 50),
            onSuccess: (res) => {
              if (res.failed) {
                record?.setState(`progress-${ckUploadFile}`, 100);
              }
            },
            onProgress: ({ percent }) => {
              return handleProgress(percent);
            },
            onError: (error, res) => {
              if (res.failed) {
                record?.setState(`progress-${ckUploadFile}`, 100);
              }
            },
          }}
          onChange={(response) => {
            if (!response?.failed) {
              handleChange('uploadFile', editorInstance.current, response);
            } else {
              message.error(response?.message);
              setCkUploadFile(null);
            }
          }}
          multiple={false}
          tenantId={tenantId}
        >
          <span ref={uploadRef} />
        </FileUploader>
      </div>
      {/* 增加一个块，避免 translate-spin 覆盖到 TranslateArea  */}
      <div className="yq-ck-editor-box">
        <CKEditor
          editor={YQEditor}
          config={getEditorConfig()}
          data={data?.replace(/(access_token=)[^\s&"]+/, `$1${getCookieToken() || AppState?.getAccessToken}`)}
          onReady={handleReady}
          onChange={handleChange}
          onBlur={handleBlur}
          onFocus={handleFocus}
          disabled={preview || disabled || translateLoading}
        />
        {translateLoading && (
          <div className="yq-plugin-translate-spin">
            <LoadingGPT message={intl.formatMessage({
              id: 'lcr.components.desc.translate.progress',
              defaultMessage: '翻译生成中...',
            })}
            />
          </div>
        )}
        {solutionLoading && (
          <div className="yq-plugin-solution-spin">
            <GptLoading
              title={intl.formatMessage({
                id: 'lcr.components.gptLoading.title',
                defaultMessage: '智能生成中',
              })}
              style={{
                minHeight: '100%',
                maxHeight: '100%',
                paddingTop: 10,
                paddingBottom: 10,
              }}
              titleStyle={{
                marginTop: 12,
                fontSize: 16,
              }}
              imageStyle={{
                width: 56,
                height: 56,
                flex: '0 0 56px',
              }}
            />
          </div>
        )}
      </div>
      {
        !preview && !userDataSet.getState('fullscreen') && (
          <div
            style={{
              bottom: getBottom(htmlData) || 3,
            }}
            className="ck-editor-resize"
            onMouseDown={handleResizeStart}
          />
        )
      }
      {!userDataSet.getState('fullscreen') && renderAudioArea(htmlData)}
      {!userDataSet.getState('fullscreen') && renderFileList(htmlData)}
      <div className="new-file-wrapper">
        <NewFile flag={getNewFileFlag()} fileName={ckUploadFile?.name} fileSize={ckUploadFile?.size} />
      </div>
      <TranslateArea
        name={name}
        intl={intl}
        formDataSet={formDs}
        message={intl.formatMessage({
          id: 'lcr.components.desc.translate.progress',
          defaultMessage: '翻译生成中...',
        })}
      />
    </div>
  );
};

export default observer(Wysiwyg);
