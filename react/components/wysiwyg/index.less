@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.ck-editor-preview {
  .ck-editor__top {
    display: none;
  }

  .ck-editor__main > div {
    border-color: transparent !important;
  }

  .ck-widget_selected {
    img {
      cursor: auto;
    }
  }

  .ck-editor__editable_inline {
    padding: 0 !important;
  }

  .ck-editor__editable_inline > :first-child {
    margin-top: 4px !important;
  }
}

.ck.ck-placeholder:before, .ck .ck-placeholder:before {
  color: @yq-text-4 !important
}

.ck-editor-required {
  .ck-editor__main > div {
    border: .01rem solid #f34c4b !important;
  }
}

.ck.ck-editor__editable:not(.ck-editor__nested-editable).ck-focused {
  border: #2979ff solid 1px !important;
}

.ck-editor-resize {
  border-style: solid;
  border-width: 0 0 10px 10px;
  border-color: transparent transparent #bfbfbf transparent;
  width: 0;
  height: 0;
  position: absolute;
  right: 3px;
  bottom: 3px;
  cursor: row-resize;
}

.ck-editor-wrapper {
  position: relative;

  .reply-audio {
    margin-top: 12px;
  }

  .dynamic-box-fileList {
    margin-top: 12px;
  }

  .yq-ck-editor-box {
    position: relative;
  }
}

.ck-focused {
  box-shadow: none !important;

  .ck-widget_selected {
    img {
      cursor: zoom-in;
    }
  }
}

.ck-toolbar {
  border-radius: 4px 4px 0 0 !important;
  border: 1px solid #d7e2ec !important;
  border-bottom-width: 0 !important;
  background-color: #fff !important;
}

.fullscreen .ck-sticky-panel__content {
  top: 0 !important;
  position: fixed;
  width: 100% !important;
}

.ck-toolbar__items {
  background: #fff !important;
}

.ck-content {
  border-radius: 0 0 4px 4px !important;
  border: 1px solid #d7e2ec !important;

  .image {
    width: 50%;
  }

  .image-inline {
    width: 50%;
  }
}

.ck-balloon-panel {
  border: 1px solid #e8e8e8 !important;
  box-shadow: none !important;
}

.ck-balloon-rotator__content {
  .ck-toolbar {
    border: 0 !important;
    background-color: #fff !important;
  }
}

.ck-body .ck.ck-balloon-panel .ck.ck-powered-by {
  display: none !important;
}

.new-file-wrapper {
  .new-file > span {
    display: flex;
    align-items: center;

    & > span {
      display: flex;
      align-items: center;
      top: -0.02rem;
    }
  }

  .lc-file-default-info {
    height: 0.28rem;

    .lc-file-default-name {
      display: inline-block;
      max-width: 2rem;
      height: 0.28rem;
      word-wrap: break-word;
      line-height: 0.28rem;
      font-size: 0.12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(18, 39, 77, 0.85);

      &-size {
        display: inline-block;
        max-width: 0.8rem;
        height: 0.28rem;
        word-wrap: break-word;
        margin-left: 0.12rem;
        margin-right: 0.12rem;
        line-height: 0.28rem;
        font-size: 0.12rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: rgba(18, 39, 77, 0.54);
      }
    }
  }
}

.yq-plugin-solution,
.yq-plugin-translate {
  .ck-list {
    max-height: 150px;
    overflow-y: auto;
  }
}

.yq-plugin-translate-spin {
  position: absolute;
  top: 40px;
  left: 0;
  margin: 2px;
  padding: 12px;
  width: calc(100% - 4px);
  height: calc(100% - 44px);
  background: #fff;
  z-index: 2;
}

.yq-plugin-solution-spin {
  position: absolute;
  top: 39px;
  left: 0;
  padding: 0;
  width: calc(100% - 1px);
  height: calc(100% - 39px);
  border-radius: 4px;
  background: #fff;
  z-index: 2;
  overflow: hidden;
}

// 智能生成解决方案的按钮样式定制化
.yq-plugin-solution {
  margin-left: auto !important;

  .ck-dropdown__panel {
    .ck.ck-button {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;

      &:active {
        border: none !important;
      }
    }
  }
}

.yq-plugin-solution-button {
  cursor: pointer !important;
  background: linear-gradient(135deg, #53ffc6 0%, #439cff 52%, #bb4bff 100%) !important;
  border: none !important;
  color: #fff !important;
  border-radius: 16px !important;
  display: inline-flex !important;
  padding-left: 1px !important;
  outline: none !important;

  .ck-icon {
    margin: 0 !important;
    margin-right: 4px !important;
    width: 28px;
    height: 28px;
  }

  .ck-button__label {
    padding-right: 4px !important;
    width: unset !important;
    font-weight: 500;
  }

  .ck-dropdown__arrow {
    display: none !important;
  }
}

// 所有提及的color必须是主题色，所以没必要再加 scope
.yqmention {
  color: @primary-color !important;
  cursor: pointer;
}
