export default ({ tenantId, ticketId, shortDescription, maxValue = 5 }) => {
  return {
    autoQuery: !!ticketId,
    selection: false,
    autoCreate: false,
    pageSize: 10,
    transport: {
      read: {
        url: `/itsm/v1/${tenantId}/ticket/repeat/${ticketId}?maxValue=${maxValue}&shortDescription=${shortDescription}`,
        method: 'get',
      },
    },
    fields: [
      { name: 'name', type: 'string' },
    ],
  };
};
