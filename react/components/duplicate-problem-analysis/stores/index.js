import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import DuplicateProblemAnalysisDataSet from './DuplicateProblemAnalysisDataSet';
import BusinessObjectFieldDataSet from '@/components/ui-action/stores/BusinessObjectFieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      children,
      AppState: { currentMenuType: { tenantId } },
      context,
      record,
    } = props;
    const formDataSet = context?.dsManager?.pageRef?.current?.formDataSet;
    const viewDataSet = context?.viewDataSet;
    const viewCode = viewDataSet.current?.get('code');
    const formConfig = viewDataSet?.current?.toData() || {};
    const { jsonData, businessObjectCode, businessObjectId } = formConfig;
    const ticketId = formDataSet?.get(0)?.get('id');
    const shortDescription = formDataSet?.get(0)?.get('short_description');
    const viewId = context?.dsManager?.viewId;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];
    const instanceId = context?.dsManager?.instanceId;
    const widgetConfig = record?.get('widgetConfig');
    const maxValue = record?.get('widgetConfig.maxRepeatedRecommendations');
    const prefix = 'lc-page-loader-DuplicateProblemAnalysis';

    const duplicateProblemAnalysisDataSet = useMemo(
      () => new DataSet(DuplicateProblemAnalysisDataSet({ tenantId, ticketId, shortDescription, maxValue })),
      [ticketId],
    );

    const businessObjectFieldDataSet = useMemo(() => new DataSet(BusinessObjectFieldDataSet({
      tenantId,
      businessObjectId,
    })), [tenantId, businessObjectId]);

    const value = {
      ...props,
      duplicateProblemAnalysisDataSet,
      tenantId,
      record,
      context,
      prefix,
      ticketId,
      viewId,
      instanceId,
      formDataSet,
      dsFieldList,
      businessObjectCode,
      businessObjectId,
      viewCode,
      viewDataSet,
      businessObjectFieldDataSet,
      widgetConfig,
      maxValue,
      shortDescription,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
