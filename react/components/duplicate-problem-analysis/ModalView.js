import React, { useMemo, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Spin } from 'choerodon-ui/pro';
import lodashGet from 'lodash/get';
import { getAiSummaryPoll, getAiSummaryAsync } from '@/service';
import { renderField, getActionFormDataSet, runAction } from '@/renderer/utils/utils.js';

const FormView = observer(({ modal, fieldCodeList, formDataSet,
  dsFieldList, businessObjectFieldDataSet,
  formConfigRecord,
  tenantId,
  viewCode,
  businessObjectCode,
  intl,
  aiMode,
  aiPromptId,
  ticketId,
  currentId,
  submitSuccess,
  record,
}) => {
  const transformDataSet = useMemo(() => getActionFormDataSet({
    fieldList: fieldCodeList,
    formDataSet,
    dsFieldList,
    businessObjectFieldDataSet,
    formConfigRecord,
    tenantId,
    intl,
  }), []);
  const timer = useRef();
  const count = useRef(30);

  modal.handleOk(async () => {
    if (transformDataSet.getState('loading')) {
      return false;
    }
    return runAction({
      transformDataSet,
      formDataSet,
      fieldCodeList,
      intl,
      submitSuccess: () => {
        setTimeout(() => {
          submitSuccess();
        }, 2000);
      },
    });
  });

  useEffect(() => {
    if (aiMode && currentId && aiPromptId) {
      transformDataSet.setState('loading', true);
      getAiSummaryAsync({ tenantId,
        businessObjectCode,
        aiPromptId,
        ticketId: currentId,
        data: {
          customParams: {
            originResolution: record.get('resolution') || '',
          },
        },
      }).then(result => {
        const uuid = typeof result === 'string' ? result : '';
        if (uuid) {
          timer.current = setInterval(() => {
            if (count.current === 0) {
              clearInterval(timer.current);
            } else {
              count.current -= 1;
              getAiSummaryPoll({ tenantId, uuid }).then(resp => {
                if (resp && !resp?.failed) {
                  const changedParams = lodashGet(resp, 'changedParams', {});
                  Object.keys(changedParams).forEach(v => {
                    transformDataSet?.current.set(v, changedParams[v]);
                  });
                  transformDataSet.setState('loading', false);
                  clearInterval(timer.current);
                }
              });
            }
          }, 5 * 1000);
        } else {
          transformDataSet.setState('loading', false);
        }
      });
    }
    return () => {
      clearInterval(timer.current);
      timer.current = null;
    };
  }, [aiMode, currentId, aiPromptId]);

  return (
    <Spin spinning={!!transformDataSet.getState('loading')}>
      <Form
        record={transformDataSet?.current}
        labelWidth="auto"
        className="lc-model-detail-form"
      >
        {fieldCodeList.map((i, index) => renderField({
          fieldItem: { field: i },
          formDs: transformDataSet,
          formDataSet,
          dsFieldList,
          intl,
          businessObjectFieldDataSet,
          viewCode,
          businessObjectCode,
          autoFocus: index === 0,
          tenantId,
        }))}
      </Form>
    </Spin>
  );
});
export default FormView;
