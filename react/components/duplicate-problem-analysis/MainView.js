import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Tooltip, TextField, message, Spin, Modal, Pagination } from 'choerodon-ui/pro';
import { Icon, Empty, StatusTag } from '@zknow/components';
import YanCopilot from '@/assets/images/duplicateProblem/yan-copilot-2.svg';
import LinkColor from '@/assets/images/duplicateProblem/link-color.svg';
import Stores from './stores';
import { relatedRepeatTicket, cancelRelatedRepeatTicket } from '@/service';
import ModalView from './ModalView';

import './index.less';

const MainView = () => {
  const {
    context,
    tenantId,
    record,
    prefix,
    duplicateProblemAnalysisDataSet,
    viewId,
    ticketId,
    formDataSet,
    dsFieldList,
    businessObjectCode,
    viewCode,
    viewDataSet,
    businessObjectFieldDataSet,
    widgetConfig,
    maxValue,
    shortDescription,
    intl,
  } = useContext(Stores);
  const { onJumpNewPage } = context;
  const name = record.get('name');
  const [search, setSearch] = useState(false);
  const [searchVal, setSearchVal] = useState(null);
  const [searchAllFlag, setSearchAllFlag] = useState(false);

  function handleChangeSearch(val) {
    setSearchVal(val);
    if (!val) {
      setSearch(false);
    }
  }

  async function handleSearchQuery() {
    duplicateProblemAnalysisDataSet.setQueryParameter('param', searchVal);
    duplicateProblemAnalysisDataSet.query();
  }

  const renderHeader = () => {
    // 此处的搜索是搜索智能分析结果，目前没有用
    if (search) {
      return <TextField
        prefix={<Icon type="icon-search" />}
        autoFocus
        clearButtonclassName={`${prefix}-header-input`}
        value={searchVal}
        onChange={handleChangeSearch}
        clearButton
        onBlur={handleSearchQuery}
        onEnterDown={handleSearchQuery}
      />;
    } else {
      return (
        <>
          <span className={`${prefix}-header-title`}>{name}</span>
          {/* <Icon theme="outline" type="search" className={`${prefix}-header-search-icon`} onClick={() => { setSearch(true); }} /> */}
        </>
      );
    }
  };

  function handleClickNumber(r) {
    onJumpNewPage({
      record: r,
      viewId,
    });
  }

  async function handleClickLink(i) {
    if (i?.get('relatedFlag')) {
      const res = await cancelRelatedRepeatTicket({ tenantId, linkId: i?.get('linkId') });
      if (!res?.failed) {
        duplicateProblemAnalysisDataSet.query();
        message.success(intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.disassociate.success' }));
      }
    } else {
      const res = await relatedRepeatTicket({ tenantId, ticketId, data: i.toData() });
      if (!res?.failed) {
        duplicateProblemAnalysisDataSet.query();
        message.success(intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.association.success' }));
      }
    }
  }

  async function handleReuseSolution(i) {
    await businessObjectFieldDataSet?.query();
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.generate.solution' }),
      children: (
        <ModalView
          fieldCodeList={['resolution']}
          formDataSet={formDataSet}
          dsFieldList={dsFieldList}
          businessObjectFieldDataSet={businessObjectFieldDataSet}
          formConfigRecord={viewDataSet?.current}
          tenantId={tenantId}
          viewCode={viewCode}
          businessObjectCode={businessObjectCode}
          intl={intl}
          aiMode
          aiPromptId={widgetConfig?.promptTemplateId}
          ticketId={ticketId}
          record={i}
          currentId={i?.get('id')}
        />
      ),
      style: { width: 800 },
    });
  }

  const rendererContentHeader = () => {
    if (searchAllFlag) {
      return <div className={`${prefix}-content-result-header`}>
        <span className={`${prefix}-content-result-header-title`}>{intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.search.title' })}</span>
      </div>;
    } else {
      return <div className={`${prefix}-content-result-header`}>
        <img src={YanCopilot} alt="" />
        <span className={`${prefix}-content-result-header-title`}>{intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.result' })}</span>
        <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.tooltips' })}>
          <Icon size={16} fill="#8C98B0" type="help" />
        </Tooltip>
      </div>;
    }
  };

  const renderContent = () => {
    if (!duplicateProblemAnalysisDataSet.length) {
      return <Empty renderDescription={intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.noData' })} />;
    } else {
      return (
        <div className={`${prefix}-content-result`}>
          {rendererContentHeader()}
          <div className={`${prefix}-content-result-list`}>
            {
                duplicateProblemAnalysisDataSet?.map(i => {
                  return (
                    <div className={`${prefix}-content-result-list-card`}>
                      <div className={`${prefix}-content-result-list-card-top`}>
                        <span className="shortDescription">
                          {i?.get('relatedFlag') && <img src={LinkColor} alt="" />}
                          {i?.get('shortDescription') || '-'}
                        </span>
                        <StatusTag name={i?.get('stateName')} color={i?.get('stateColor')}>{i?.get('stateName')}</StatusTag>
                      </div>
                      <div className={`${prefix}-content-result-list-card-bottom`}>
                        <span className="number" onClick={() => handleClickNumber(i)}>
                          <Icon className="number-icon" type="icon-document" theme="filled" size={16} fill="#2979FF" />
                          {i?.get('number')}
                        </span>
                        <span>
                          <span className="submitPerson">{intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.submitperson' })}{i?.get('submittedByName')}</span>
                          <span>{intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.assigneeperson' })}{i?.get('assigneePersonName')}</span>
                        </span>
                        <span className={`${prefix}-content-result-list-card-bottom-reuse`} onClick={() => handleReuseSolution(i)}>
                          <Icon className="link-icon" type="copy-one" theme="filled" size="16" fill="#2979FF" />
                          <span style={{ color: '#2979FF' }}>{intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.reuse' })}</span>
                        </span>
                        <span className={`${prefix}-content-result-list-card-bottom-link`} onClick={() => handleClickLink(i)}>
                          <Icon className="link-icon" type="LinkBreak" theme="filled" size="16" fill={i?.get('relatedFlag') ? '#F43636' : '#2979FF'} />
                          <span style={{ color: i?.get('relatedFlag') ? '#F43636' : '#2979FF' }}>{i?.get('relatedFlag') ? intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.cancelLink' }) : intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.link' })}</span>
                        </span>
                      </div>
                    </div>
                  );
                })
            }
          </div>
        </div>
      );
    }
  };

  async function handleSearch(value) {
    if (value) {
      duplicateProblemAnalysisDataSet.transport.read.url = `/itsm/v1/${tenantId}/ticket/repeat/search?excludeTicketId=${ticketId}&condition=${value}`;
      duplicateProblemAnalysisDataSet.transport.read.method = 'post';
      setSearchAllFlag(true);
    } else {
      duplicateProblemAnalysisDataSet.transport.read.url = `/itsm/v1/${tenantId}/ticket/repeat/${ticketId}?maxValue=${maxValue}&shortDescription=${shortDescription}`;
      duplicateProblemAnalysisDataSet.transport.read.method = 'get';
      setSearchAllFlag(false);
    }
    await duplicateProblemAnalysisDataSet.query();
  }

  // 这个搜索是在系统的全部单据搜索
  const renderSearch = () => {
    return <TextField onChange={handleSearch} clearButton className={`${prefix}-content-search`} placeholder={intl.formatMessage({ id: 'lcr.renderer.duplicateProblemAnalysis.search.tips', defaultMessage: '无相关结果？请输入关键字精准搜索' })} />;
  };

  return (
    <div className={prefix}>
      <div className={`${prefix}-header`}>
        {renderHeader()}
      </div>
      <Spin dataSet={duplicateProblemAnalysisDataSet}>
        <div className={`${prefix}-content`}>
          {renderSearch()}
          {renderContent()}
          {searchAllFlag && <Pagination dataSet={duplicateProblemAnalysisDataSet} className={`${prefix}-content-page`} />}
        </div>
      </Spin>
    </div>
  );
};

export default observer(MainView);
