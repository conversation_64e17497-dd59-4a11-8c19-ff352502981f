@import '~choerodon-ui/lib/style/themes/default';

.lc-page-loader-DuplicateProblemAnalysis {
    padding: 16px;
    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &-title {
            color: #12274D;
            font-weight: 500;
        }
        &-search {
            &-icon {
                padding: 0.08rem;
                color: @primary-color;
                background: @minor-color;
                border-radius: 0.04rem;
                line-height: 1;
                height: 32px;
                cursor: pointer;

                &:hover {
                    background-color: @primary-6;
                    color: @primary-1; 
                }
            }
        }
        &-input {
            width: 100%;
        }
        .c7n-pro-input-wrapper {
            width: 100%;
        }
    }
    &-content {
        &-result {
            margin-top: 16px;
            &-header {
                height: 33px;
                background: #EDF2FF;
                border-radius: 5px 5px 0px 0px;
                padding: 0 10px;
                display: flex;
                align-items: center;
                &-title {
                    font-weight: 500;
                    color: #12274d;
                    margin-right: 5px;
                }
                > img {
                    height: 22px;
                    width: 22px;
                    margin-right: 5px;
                }
            }
            &-list {
                padding: 12px;
                border: 1px solid #E5E6EB;
                border-top: 0;
                &-card {
                    border-bottom: 1px solid rgba(203, 210, 220, 0.5);
                    position: relative;
                    &-top {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 8px 0;
                        .shortDescription {
                            max-width: 70%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            font-weight: 500;
                            white-space: nowrap;
                            font-size: 14px;
                            > img {
                                width: 16px;
                                height: 16px;
                                margin-right: 4px;
                            }
                        }
                    }
                    &-bottom {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 10px;
                        font-size: 12px;
                        .number {
                            color: #2979ff;
                            cursor: pointer;
                        }
                        .number-icon {
                            margin-right: 4px;
                        }
                        &:last-child {
                            border: none;
                        }
                        .submitPerson {
                            margin-right: 16px;
                        }
                        &-link,&-reuse {
                            background: #fff;
                            display: none;
                            cursor: pointer;
                            position: absolute;
                            right: 0;
                            bottom: 8;
                            .link-icon {
                                margin-right: 10px;
                            }
                        }
                        &-reuse {
                            right: 88px;
                        }
                    }
                    &:hover {
                        .lc-page-loader-DuplicateProblemAnalysis-content-result-list-card-bottom {
                            &-link,&-reuse {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }
            }
        }
        &-search {
            width: 100%;
            margin-top: 12px;
        }
        &-page {
            display: flex;
            justify-content: flex-end;
            margin-top: 16px;
        }
    }
}
