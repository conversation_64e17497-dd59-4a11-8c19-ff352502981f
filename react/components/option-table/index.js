import React, { useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Table } from 'choerodon-ui/pro';
import { Button, Icon } from '@zknow/components';
import YQColor from '@/components/yq-color';

import './index.less';

const { Column } = Table;

const OptionTable = ({ fieldRecord, dataSet, modal, intl }) => {
  const tableRef = useRef();
  modal.handleOk(async () => {
    const res = await dataSet.validate();
    if (res) {
      fieldRecord.set('widgetConfig.options', dataSet.toData());
      return true;
    }
    return false;
  });

  modal.handleCancel(() => {
    dataSet.reset();
  });

  useEffect(() => {
    modal.update({
      bodyStyle: { height: 400 },
    });
  }, []);

  function handleEdit(record) {
    record.setState('editing', true);
  }

  function handleDelete(record) {
    record.dataSet.remove(record);
  }

  function handleSubmit(record) {
    record.setState('editing', false);
  }

  function handleCancel(record) {
    if (record.getState('isNew')) {
      record.dataSet.remove(record);
    } else {
      record.reset();
      record.setState('editing', false);
    }
  }

  function commands({ record }) {
    const btns = [];
    if (record.getState('editing')) {
      btns.push(
        <Button color="primary" funcType="raised" onClick={() => handleSubmit(record)}>
          {intl.formatMessage({ id: 'zknow.common.button.save', defaultMessage: '保存' })}
        </Button>,
        <Button funcType="raised" onClick={() => handleCancel(record)}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>,
      );
    } else {
      btns.push(
        <span className="option-table-btn" onClick={() => handleEdit(record)}>
          <Icon type="Write" />
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </span>,
        <span className="option-table-btn" onClick={() => handleDelete(record)}>
          <Icon type="Delete" />
          {intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}
        </span>,
      );
    }
    return [<span className="option-table-action">{btns}</span>];
  }

  function renderDragRow(props) {
    delete props.dragColumnAlign;
    return <Table.TableRow {...props} />;
  }

  function handleAddOption() {
    const newRecord = dataSet.create({
      color: '#2979FF',
    });
    newRecord.setState('isNew', true);
    newRecord.setState('editing', true);
    if (tableRef.current) {
      tableRef.current.element.scrollTo(0, 9999);
    }
  }

  return (
    <div className="option-table-table-container">
      <Table
        ref={tableRef}
        className="option-table-table"
        queryBar="none"
        dataSet={dataSet}
        onDragEnd={() => {}}
        labelLayout="float"
        autoHeight
        rowDragRender={{ renderClone: renderDragRow }}
        dragRow={!dataSet.find(record => record.getState('editing'))}
        filter={(record) => record.status !== 'delete'}
      >
        <Column name="meaning" editor={(record) => record.getState('editing')} />
        <Column name="value" editor={(record) => record.getState('editing')} />
        <Column
          name="color"
          renderer={({ record, value }) => (
            <YQColor
              name="color"
              record={record}
              preview={!record.getState('editing')}
              value={value}
              addonAfter
            />
          )}
        />
        <Column
          header={intl.formatMessage({ id: 'zknow.common.button.action', defaultMessage: '操作' })}
          align="center"
          renderer={commands}
        />
      </Table>
      <Icon
        type="AddOne"
        theme="filled"
        className="option-table-add"
        onClick={handleAddOption}
      />
    </div>
  );
};

export default observer(OptionTable);

/* externalize: OptionTable */
