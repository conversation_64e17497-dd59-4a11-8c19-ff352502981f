.option-table {
  &-btn {
    color: @primary-color;
    display: inline-flex;
    align-items: center;
    margin: 0 0.1rem;
    cursor: pointer;
    span {
      margin-right: 0.05rem;
    }
  }
  &-action {
    vertical-align: top;
  }
  &-add {
    border-radius: 50%;
    color: @primary-color;
    font-size: 0.21rem;
    cursor: pointer;
    margin-top: 0.2rem;
  }
  &-table {
    &-container {
      height: 100%;
    }

    .c7n-pro-table {
      border-radius: 0 !important;
    }

    .c7n-pro-table-content {
      border-radius: 0 !important;
    }

    .c7n-pro-table-cell-inner.c7n-pro-output {
      position: relative !important;
    }

    .yq-color {
      margin-top: -0.04rem;
    }
  }
}
