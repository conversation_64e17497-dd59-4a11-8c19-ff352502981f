import React from 'react';
import { Icon, ExternalComponent } from '@zknow/components';
import { EditorRegister } from '@zknow/utils';

const defaultValueEditor = (fieldProps, disabled, record, tenantId) => {
  return <ExternalComponent
    system={{
      scope: 'lcr',
      module: 'LcEditorUpload',
    }}
    defaultFlag
    fieldProps={fieldProps}
    disabled={disabled}
    record={record}
    tenantId={tenantId}
  />;
};

const preview = (props) => {
  const { parentKey, record, code } = props;
  const name = (parentKey ? `${parentKey}.${record.get('code')}` : record.get('code')) || code;
  return (
    <ExternalComponent
      system={{
        scope: 'lcr',
        module: 'LcEditorUpload',
      }}
      name={name}
      {...props}
    />
  );
};

EditorRegister.registry('Upload', {
  icon: <Icon type="InboxR" />,
  properties: [
    'fileFormat',
    'fileAccept',
    'liveUpdate',
    'fileDragUpload',
    'fileSizeLimit',
    'defaultValue',
  ],
  defaultValueEditor,
  configData: (intl) => ({
    name: intl.formatMessage({ id: 'lcr.components.desc.upload', defaultMessage: '附件' }),
    code: 'upload',
    description: '',
    maxLength: '',
    defaultValue: '',
    placeHolder: intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' }),
    widgetType: 'Upload',
    help: '',
    tag: 'Field',
    widgetConfig: {
      fileSizeLimit: undefined,
      fileFormat: 'single',
      fileAccept: '',
      fileDragUpload: false,
      liveUpdate: false,
    },
  }),
  preview,
});

export { defaultValueEditor, preview };
