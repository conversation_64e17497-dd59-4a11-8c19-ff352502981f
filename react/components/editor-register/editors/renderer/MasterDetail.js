import React from 'react';
import { withError<PERSON>ounda<PERSON>, Button } from '@zknow/components';
import { Lov, Output, Modal, Tooltip } from 'choerodon-ui/pro';
import LovSelect from '@/components/lov-select';
import PageLoader from '@/components/page-loader';
import InfoCard from '@/components/info-card';
import AvatarTooltip from '@/components/avatar-tooltip';

/**
 * 关联关系
 */
const Index = (props) => {
  const {
    record,
    readOnly,
    parentKey,
    formDs,
    tenantId,
    disabled,
    intl,
    context,
    pageRef,
    ...rest
  } = props;
  const onJumpNewPage = context?.onJumpNewPage;
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const viewConfig = record?.get('widgetConfig.lovToView');
  const bodyModalStyle = { padding: '0.16rem' };
  const modalKey = Modal.key();
  const relationLovId = record?.get('relationLovId');
  const previewFlag = record?.get('widgetConfig.previewFlag');
  const userCardFlag = record?.get('widgetConfig.userCardFlag');
  const help = record?.get('help');
  let modal;

  const field = formDs?.getField(name);
  if (field) {
    field.set('label', rest?.label);
    field.set('title', rest?.title);
  }

  if (readOnly) {
    return <Output name={name} {...rest} />;
  }

  async function openView(view = {}, dataSet, current, viewMode = 'MODIFY', defaultData) {
    const {
      viewSize,
      openType,
      viewId,
      viewName,
    } = view;
    const viewModalStyle = { width: Number(viewSize) };
    function getInstanceId() {
      return current.get(name)?.id;
    }
    if (openType === 'NEW') {
      if (onJumpNewPage) {
        onJumpNewPage({
          record,
          viewId,
          defaultData,
          instanceId: getInstanceId(),
        });
      } else {
        let newUrl = `/lc/engine/${viewId}`;
        if (current.get(name)) {
          newUrl += `/${getInstanceId()}`;
        }
        if (context?.viewId) {
          newUrl += `/${context?.viewId}`;
        }
        window.location.href = `${window.location.origin}/#${newUrl}?${window.location.href.split('?')[1] || ''}`;
        window.location.reload();
      }
    } else if (openType === 'NEWTAB') {
      let newUrl = `/lc/engine/${viewId}`;
      if (current.get(name)) {
        newUrl += `/${getInstanceId()}`;
      }
      if (context?.viewId) {
        newUrl += `/${context?.viewId}`;
      }
      window.open(`${window.location.origin}/#${newUrl}?${window.location.href.split('?')[1] || ''}`);
    } else if (openType === 'RIGHT' || openType === 'MIDDLE') {
      if (modal) {
        await modal.close();
      }
      modal = Modal.open({
        title: openType === 'MIDDLE' ? viewName : '',
        children: (
          <PageLoader
            instanceId={getInstanceId()}
            viewId={viewId}
            pageRef={pageRef}
            mode={viewMode}
            openType={openType}
            parentDataSet={dataSet}
            defaultData={defaultData}
          />
        ),
        key: `${modalKey}-${record.get('id')}`,
        drawer: openType === 'RIGHT',
        style: viewModalStyle,
        bodyStyle: bodyModalStyle,
        destroyOnClose: true,
        closeOnLocationChange: true,
        okButton: viewMode !== 'READONLY',
        cancelText: viewMode === 'READONLY' ? intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' }) : undefined,
      });
    }
    return true;
  }

  const viewButton = viewConfig?.viewId && (formDs?.current?.get(name)?.id || formDs?.current?.get(name))
    ? (
      <Button
        icon="share"
        color="#d0d0d0"
        onClick={() => openView(viewConfig, formDs, formDs?.current)}
        style={{ marginLeft: 8 }}
      />
    )
    : null;

  const displayMethod = record?.get('widgetConfig.displayMethod');

  const restConfig = displayMethod === 'DROP_DOWN_LIST'
    ? {
      viewMode: 'popup',
      popupSearchMode: 'single',
      searchMatcher: 'fuzzy_params_',
    }
    : {};

  function renderLov() {
    // 修复 UDM 的升级单下拉列表 LOV 选不到选项的问题
    //  场景：Modal 下的 iframe 嵌套，在 windows 小屏幕 100% 复现
    const trigger = window.parent !== window.self ? ['click'] : ['click', 'focus'];
    const queryLimitFlag = record?.get('widgetConfig.queryLimit') || false;
    if (displayMethod === 'DROP_DOWN') {
      const relationLovNameFieldCode = record.get('widgetConfig.relationLovNameFieldCode');
      const relationLovValueFieldCode = record.get('widgetConfig.relationLovValueFieldCode');
      const variableFilter = record.get('widgetConfig.variableFilter');
      const condition = record.get('widgetConfig.condition');
      const onlyLeafFlag = record.get('widgetConfig.onlyLeafFlag');
      const placeHolder = record.get('placeHolder');

      return (
        <LovSelect
          current={formDs?.current}
          name={name}
          help={help}
          config={{
            textField: relationLovNameFieldCode,
            valueField: relationLovValueFieldCode,
            relationLovId,
            variableFilter,
            condition,
            placeHolder,
            disabled,
            onlyLeafFlag,
            queryLimitFlag,
          }}
        />
      );
    }
    return (
      <Lov
        name={name}
        {...rest}
        {...restConfig}
        disabled={disabled}
        trigger={trigger}
        tableProps={{
          onRow: ({ record: current }) => ({
            isLeaf: current.get('isLeaf'),
          }),
          className: 'lc-lov-table',
          autoWidth: false,
          pagination: !queryLimitFlag,
        }}
      />
    );
  }

  return (
    <div className="lc-lov-wrapper lc-lov-link-btn" name={name}>
      {userCardFlag && previewFlag
        ? (
          <AvatarTooltip id={formDs?.current?.get(name)?.id || formDs?.current?.get(name)}>
            {renderLov()}
          </AvatarTooltip>
        )
        : (
          <Tooltip
            placement="bottomLeft"
            title={
                formDs?.current?.get(name) && previewFlag
                  ? (
                    <InfoCard
                      name={name}
                      title={rest.label}
                      intl={intl}
                      value={formDs?.current?.get(name)}
                      tenantId={tenantId}
                      record={record}
                      relationLovId={relationLovId}
                    />
                  )
                  : null
            }
            popupClassName="participants-card-tooltip"
          >
            {renderLov()}
          </Tooltip>
        )}
      {viewButton}
    </div>
  );
};

export default withErrorBoundary(Index);

/* externalize: LcEditorMasterDetail */
