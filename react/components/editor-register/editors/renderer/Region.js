import React from 'react';
import { Output } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import YqPcdPicker from '@/components/yq-pcd-picker';
import { getFieldDisabled } from '../../utils';

/**
 * 省市区级联
*/
const defaultValueEditor = ({ disabled, record }) => {
  const regionColumns = record?.get('widgetConfig.regionColumns');
  const defaultValue = record?.get('widgetConfig.regionValues') || [];
  return (
    <YqPcdPicker name="defaultValue" record={record} mode={regionColumns} defaultValue={defaultValue} readOnly={disabled} sider />
  );
};

const preview = ({ record, readOnly, parentKey, formDs, ...rest }) => {
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  let disabled = getFieldDisabled(record, formDs, name);
  const field = formDs?.getField(name);
  if (field) {
    field.set('label', rest?.label);
    field.set('title', rest?.title);
  }

  let currentRecord;
  let isForm = false;
  if (formDs?.current) {
    currentRecord = formDs?.current;
    disabled = getFieldDisabled(currentRecord, formDs, name);
    isForm = true;
  } else {
    currentRecord = record;
  }
  const realValue = currentRecord?.get(name);
  const regionColumns = record?.get('widgetConfig.regionColumns');
  if (readOnly) {
    return <Output name={name} {...rest} />;
  }
  return (
    <YqPcdPicker name={name} record={currentRecord} mode={regionColumns} isForm={isForm} readOnly={disabled} realValue={realValue} />
  );
};

const Index = observer((props) => {
  const { defaultFlag, ...rest } = props;
  if (defaultFlag) return defaultValueEditor({ ...rest });
  return preview({ ...rest });
});

export default withErrorBoundary(Index);

/* externalize: LcEditorRegion */
