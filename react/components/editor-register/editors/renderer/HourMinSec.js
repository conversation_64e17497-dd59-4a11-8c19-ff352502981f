import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Output, NumberField } from 'choerodon-ui/pro';
import { withErrorBoundary } from '@zknow/components';
import { getFieldDisabled } from '@/components/editor-register/utils';
import { dealDuration, durToDays } from '@/utils';

function Index(props) {
  const { record, formDs, readOnly, disabled: pageLoaderDisabled, parentKey, intl, context, ...rest } = props;
  const mode = record?.get('widgetConfig').durationMode;
  const unit = record?.get('widgetConfig').durationUnit;
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  let disabled = getFieldDisabled(record, formDs, name);

  useEffect(() => {
    try {
      // 特殊逻辑，回复编辑暂存的时长取暂存的数据，不取默认值。
      if (context?.disDurationDefaultVal) {
        return false;
      }
      // 时长的默认值按mode配置填充进表单
      if (formDs?.current && record?.get(name)) {
        formDs?.current?.set(record?.get(name));
        formDs?.current?.set(name, record?.get(name));
      } else if (formDs?.current?.get(name) && typeof formDs?.current?.get(name) !== 'object') {
        const time = durToDays(Number(formDs?.current?.get(name)), unit, mode);
        formDs?.current?.set(name, time);
      }
    } catch (error) {
      //
    }
  }, []);

  useEffect(() => {
    // 为了解决UI规则支持时长组件，必须要在脚本里返回包含: @days、@hours、minutes、@seconds 四种中任意一种的字符串；渲染时按照给的格式进行转换
    if (formDs?.current?.get(name) && typeof formDs?.current?.get(name) !== 'object' && formDs?.current?.get(name).toString().includes('@')) {
      const splitValue = formDs?.current?.get(name).split('@');
      const time = durToDays(Number(splitValue[0]), splitValue[1], mode);
      formDs?.current?.set(name, time);
    }
  }, [formDs?.current?.get(name)]);

  if (formDs?.current) {
    disabled = getFieldDisabled(formDs?.current, formDs, name);
  }

  const field = formDs?.getField(name);
  if (field) {
    field.set('label', rest?.label);
    field.set('title', rest?.title);
    field.set('help', rest?.help);
  }

  // 时分秒自动进位
  const handleChangeTime = (value, code) => {
    const minutesOrHoursValue = Math.floor(value / 60);
    const daysValue = Math.floor(value / 24);
    switch (code) {
      case 'seconds':
        if (value >= 60 && mode?.includes('minutes')) {
          record?.set(`${name}.seconds`, value % 60);
          handleChangeTime(record?.get(`${name}.minutes`) + minutesOrHoursValue, 'minutes');
        } else {
          record?.set(`${name}.${code}`, value);
        }
        break;
      case 'minutes':
        if (value >= 60 && mode?.includes('hours')) {
          record?.set(`${name}.minutes`, value % 60);
          handleChangeTime(record?.get(`${name}.hours`) + minutesOrHoursValue, 'hours');
        } else {
          record?.set(`${name}.${code}`, value);
        }
        break;
      case 'hours':
        if (value >= 60 && mode?.includes('days')) {
          record?.set(`${name}.hours`, value % 24);
          handleChangeTime(record?.get(`${name}.days`) + daysValue, 'days');
        } else {
          record?.set(`${name}.${code}`, value);
        }
        break;
      default:
        break;
    }
  };

  const renderContent = () => {
    const addProps = {
      step: 1,
      min: 0,
      style: { marginRight: 8, maxWidth: '60px' },
      disabled: disabled || pageLoaderDisabled,
      renderer: ({ value }) => (Number.isNaN(value) ? 0 : value),
    };
    return (
      <div className="yq-duration" name={name}>
        {
          mode?.includes('days') && (
            <>
              <NumberField
                name={`${name}.days`}
                {...addProps}
              />
              <span>
                {intl && intl.formatMessage({ id: 'zknow.common.model.day', defaultMessage: '天' })}
              </span>
            </>
          )
        }
        {
          mode?.includes('hours') && (
            <>
              <NumberField
                name={`${name}.hours`}
                {...addProps}
                onChange={(value) => handleChangeTime(value, 'hours')}
              />
              <span>
                {intl && intl.formatMessage({ id: 'zknow.common.model.hour', defaultMessage: '时' })}
              </span>
            </>
          )
        }
        {
          mode?.includes('minutes') && (
            <>
              <NumberField
                name={`${name}.minutes`}
                {...addProps}
                onChange={(value) => handleChangeTime(value, 'minutes')}
              />
              <span>
                {intl && intl.formatMessage({ id: 'zknow.common.model.minute', defaultMessage: '分' })}
              </span>
            </>
          )
        }
        {
          mode?.includes('seconds') && (
            <>
              <NumberField
                name={`${name}.seconds`}
                {...addProps}
                onChange={(value) => handleChangeTime(value, 'seconds')}
              />
              <span>
                {intl && intl.formatMessage({ id: 'zknow.common.model.second', defaultMessage: '秒' })}
              </span>
            </>
          )
        }
      </div>
    );
  };
  if (readOnly) {
    const value = formDs?.current?.get(name) || {};
    const output = dealDuration({ value, intl });
    return <div name={name} {...rest}><Output value={output} /></div>;
  }
  return renderContent();
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorHourMinSec */
