import React from 'react';
import _ from 'lodash';
import { observer } from 'mobx-react-lite';
import { Output, TextField } from 'choerodon-ui/pro';
import { withErrorBoundary, TranslateArea } from '@zknow/components';
import OcrModal from '@/components/ocr-modal';
import FieldAssocation from '@/components/field-association';
import PhrasesText from '@/renderer/text-common-phrases';
import useServiceSetting from '../../../../hooks/useServiceSetting';
import styles from './Input.module.less';
import TranslateSuffix from '../../../translate-suffix';
import { isInIframe } from '@/utils';
import { setComponentPhrases } from '@/service';

function Index(props) {
  const { record, formDs, intl, readOnly, parentKey, surveyFlag, ocrEnabledFlag, context, ...rest } = props;
  // ocrEnabledFlag 丢失
  const tenantOcrFlag = ocrEnabledFlag !== undefined ? ocrEnabledFlag : rest?.context?.HeaderStore?.getTenantConfig?.ocrEnabledFlag;
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const disabledFlag = record?.get('editType') === 'ALWAYS_NOT_EDIT';
  const businessObjectId = rest?.viewDataSet?.current?.get('businessObjectId');
  const businessObjectCode = rest?.viewDataSet?.current?.get('businessObjectCode');
  const historyInputFlag = record?.get('widgetConfig.historyInputFlag');

  const serviceSetting = useServiceSetting(businessObjectId, '', rest.tenantId, rest.AppState);

  const [translateLoading, setTranslateLoading] = React.useState(false);
  const [translateResult, setTranslateResult] = React.useState('');
  const [translateError, setTranslateError] = React.useState(false);

  if (readOnly) {
    return [
      <Output name={name} {...rest} />,
      <TranslateArea
        key={`${name}-translate`}
        name={name}
        formDataSet={formDs}
        intl={intl}
        message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
      />,
    ];
  }

  function handleSearch(value) {
    if (formDs?.current) {
      formDs?.current.set(name, value);
    }
  }

  const debounceSearch = _.debounce(handleSearch, 400);

  async function handleInput(e) {
    await debounceSearch(e?.target?.value || null);
  }

  // handleBlur，记录一下常用输入，需要调接口记录
  const handleBlur = () => {
    if (formDs?.current?.get(name) && historyInputFlag) {
      setComponentPhrases(rest.tenantId, record?.get('id'), formDs?.current?.get(name));
      record?.setState('updateListCount', (record?.getState('updateListCount') || 0) + 1);
    }
    handleChangeFocusFlag(record, false);
  };

  function renderSuffix() {
    if (surveyFlag) {
      return null;
    }
    if (disabledFlag) {
      return null;
    }
    if (!tenantOcrFlag) {
      return null;
    }
    if (record?.get('ocrFlag') && record?.getState('focusFlag')) {
      return <OcrModal record={record} intl={intl} formDs={formDs} />;
    }
    return null;
  }

  function renderMain() {
    const ticketTranslationFlag = serviceSetting?.ticketTranslationFlag && !isInIframe();
    const showTranslate = (serviceSetting?.ticketTranslationSettingVO?.fields || []).find(field => field.code === name);

    const fieldAssocation = context?.recommendField && name === context?.recommendField ? <FieldAssocation
      displayFlag={record?.getState('focusFlag')}
      type="search"
      fieldValue={formDs?.current.get(name)}
      businessObjectCode={businessObjectCode}
    /> : '';

    const phrasesText = historyInputFlag && <PhrasesText name={name} phrasesComponentId={record?.get('id')} phrasesValue={formDs?.current?.get(name)} formComponentType="TextField" changeFn={(text) => { formDs?.current?.set(name, text); }} updateListCount={record?.getState('updateListCount')} />;

    // 定时任务中（scheduleFlag）的执行任务为【服务项】时，渲染服务项视图，不要显示翻译
    return ticketTranslationFlag && showTranslate && !context?.scheduleFlag ? (
      <>
        <div className={styles.wrap}>
          <TextField
            key={name}
            name={name}
            className={record?.get('ocrFlag') ? 'lc-ocr-component' : ''}
            onFocus={() => handleChangeFocusFlag(record, true)}
            onBlur={handleBlur}
            suffix={renderSuffix()}
            {...rest}
            onInput={handleInput}
          />
          <TranslateSuffix
            businessObjectId={businessObjectId}
            tenantId={rest.tenantId}
            appState={rest.AppState}
            setTranslateLoading={setTranslateLoading}
            setTranslateError={setTranslateError}
            setTranslateResult={setTranslateResult}
            value={formDs?.current?.get(name)}
          />
        </div>
        {fieldAssocation}
        {phrasesText}
      </>
    ) : (
      <div className={styles.associationWrap}>
        <TextField
          key={name}
          name={name}
          className={record?.get('ocrFlag') ? 'lc-ocr-component' : ''}
          onFocus={() => handleChangeFocusFlag(record, true)}
          onBlur={handleBlur}
          suffix={renderSuffix()}
          {...rest}
          onInput={handleInput}
        />
        {fieldAssocation}
        {phrasesText}
      </div>
    );
  }

  /**
   * 增加翻译显示功能，触发翻译后，会给 formDs 添加自定义状态 TRANSLATE_STATE
   */
  return [renderMain(), <TranslateArea
    intl={intl}
    key={`${name}-translate`}
    name={name}
    formDataSet={formDs}
    loading={translateLoading}
    error={translateError}
    value={translateResult}
    message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
  />];
}

function handleChangeFocusFlag(record, target) {
  record?.setState('focusFlag', target);
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorInput */
