import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, TranslateArea } from '@zknow/components';
import { Output, TextArea } from 'choerodon-ui/pro';
import OcrModal from '@/components/ocr-modal';
import RichTextPreview from '@/renderer/rich-text-preview';
import FieldAssocation from '@/components/field-association';
import PhrasesText from '@/renderer/text-common-phrases';
import { setComponentPhrases } from '@/service';
import TranslateSuffix from '../../../translate-suffix';
import useServiceSetting from '../../../../hooks/useServiceSetting';
import styles from './Input.module.less';

import '../index.less';

function handleChangeFocusFlag(record, target) {
  record?.setState('focusFlag', target);
}

/**
 * 文本
 */

const Index = (props) => {
  const { record, formDs, readOnly, parentKey, intl, surveyFlag, ocrEnabledFlag, context, ...rest } = props;
  const tenantOcrFlag = ocrEnabledFlag !== undefined ? ocrEnabledFlag : rest?.context?.HeaderStore?.getTenantConfig?.ocrEnabledFlag;
  const rows = record?.get('widgetConfig.rows') || 4;
  const autoSize = record?.get('widgetConfig.autoSize') || false;
  const minRows = record?.get('widgetConfig.minRows') || 1;
  const maxRows = record?.get('widgetConfig.maxRows') || 4;
  const htmlFlag = record?.get('widgetConfig.htmlFlag') || false;
  const disabledFlag = record?.get('editType') === 'ALWAYS_NOT_EDIT';
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const help = record?.get('help');
  const businessObjectId = rest?.viewDataSet?.current?.get('businessObjectId');

  const serviceSetting = useServiceSetting(businessObjectId, '', rest.tenantId, rest.AppState);
  const historyInputFlag = record?.get('widgetConfig.historyInputFlag');

  const [translateLoading, setTranslateLoading] = React.useState(false);
  const [translateResult, setTranslateResult] = React.useState('');
  const [translateError, setTranslateError] = React.useState(false);
  // 更改成富文本预览
  function transformToHtml(text) {
    return (
      <RichTextPreview
        ocrFlag
        data={text}
        htmlData={text}
        preview
        minHeight={1}
        ticketId={formDs?.current?.get('id')}
      />
    );
  }
  if (htmlFlag) {
    return [
      <Output
        name={name}
        {...rest}
        tooltip="none"
        renderer={htmlFlag ? ({ text }) => transformToHtml(text) : ''}
      />,
      <TranslateArea
        key={`${name}-translate`}
        name={name}
        intl={intl}
        formDataSet={formDs}
        message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
      />,
    ];
  }
  if (readOnly) {
    return [
      <Output
        name={name}
        {...rest}
        tooltip="none"
      />,
      <TranslateArea
        key={`${name}-translate`}
        name={name}
        intl={intl}
        formDataSet={formDs}
        message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
      />,
    ];
  }

  const autoSizeConfig = autoSize ? { minRows, maxRows } : false;
  const fieldAssocation = context?.recommendField && name === context?.recommendField ? <FieldAssocation
    displayFlag={record?.getState('focusFlag')}
    type="search"
    fieldValue={formDs?.current.get(name)}
  /> : '';
  const phrasesText = historyInputFlag && <PhrasesText name={name} phrasesComponentId={record?.get('id')} phrasesValue={formDs?.current?.get(name)} formComponentType="TextArea" changeFn={(text) => { formDs?.current?.set(name, text); }} updateListCount={record?.getState('updateListCount')} />;

  // handleBlur，记录一下常用输入，需要调接口记录
  const handleBlur = () => {
    if (formDs?.current?.get(name) && historyInputFlag) {
      setComponentPhrases(rest.tenantId, record?.get('id'), formDs?.current?.get(name));
      record?.setState('updateListCount', (record?.getState('updateListCount') || 0) + 1);
    }
  };
  // 调查或者ocr不可用时只展示文本框
  if (!tenantOcrFlag || surveyFlag) {
    return <>
      <TextArea
        name={name}
        {...rest}
        resize="vertical"
        rows={rows}
        autoSize={autoSizeConfig}
        onMouseEnter={() => handleChangeFocusFlag(record, true)}
        onMouseLeave={() => handleChangeFocusFlag(record, false)}
        onBlur={handleBlur}
      />
      {fieldAssocation}
      {phrasesText}
    </>;
  }

  const renderMain = (className) => {
    const ticketTranslationFlag = serviceSetting?.ticketTranslationFlag;
    const showTranslate = (serviceSetting?.ticketTranslationSettingVO?.fields || []).find(field => field.code === name);

    return ticketTranslationFlag && showTranslate && !context?.scheduleFlag ? (
      <>
        <div className={styles.wrap}>
          <TextArea
            key={name}
            name={name}
            {...rest}
            className={className}
            style={{ flex: 1 }}
            resize="vertical"
            rows={rows}
            autoSize={autoSizeConfig}
            onBlur={handleBlur}
          />
          <TranslateSuffix
            businessObjectId={businessObjectId}
            tenantId={rest.tenantId}
            appState={rest.AppState}
            setTranslateLoading={setTranslateLoading}
            setTranslateError={setTranslateError}
            setTranslateResult={setTranslateResult}
            value={formDs?.current?.get(name)}
          />
        </div>
        {fieldAssocation}
        {phrasesText}
      </>
    ) : (
      <>
        <TextArea
          key={name}
          name={name}
          {...rest}
          className={className}
          style={{ width: '100%' }}
          resize="vertical"
          rows={rows}
          autoSize={autoSizeConfig}
          onBlur={handleBlur}
        />
        {fieldAssocation}
        {phrasesText}
      </>
    );
  };

  if (record?.get('ocrFlag') && !disabledFlag) {
    return (
      <div
        name={name}
        // eslint-disable-next-line react/no-unknown-property
        help={help}
        className="lc-ocr-component-wrapper"
        label={record?.get('name') || ''}
        onMouseEnter={() => handleChangeFocusFlag(record, true)}
        onMouseLeave={() => handleChangeFocusFlag(record, false)}
      >
        {renderMain('lc-ocr-component-wrapper-textArea')}
        <TranslateArea
          name={name}
          intl={intl}
          formDataSet={formDs}
          loading={translateLoading}
          error={translateError}
          value={translateResult}
          message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
          onBlur={handleBlur}
        />
        {record?.getState('focusFlag') && <OcrModal record={record} intl={intl} formDs={formDs} />}
        {phrasesText}
      </div>
    );
  }
  return [
    renderMain(),
    <TranslateArea
      key={`${name}-translate`}
      intl={intl}
      name={name}
      formDataSet={formDs}
      loading={translateLoading}
      error={translateError}
      value={translateResult}
      message={intl.formatMessage({ id: 'lcr.components.desc.translate.progress', defaultMessage: '翻译生成中...' })}
    />,
  ];
};

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorTextArea */
