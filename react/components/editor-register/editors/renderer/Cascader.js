import React from 'react';
import { Output } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import CascadeFields from '@/components/cascade-fields';

function Index(props) {
  const { defaultValueEditor, record, readOnly, parentKey, formDs, ...rest } = props;
  if (defaultValueEditor) {
    const fields = record?.get('widgetConfig.cascadeFields');
    const lookupCode = record?.get('widgetConfig.lookupCode')?.typeCode;
    return (
      <CascadeFields
        name="defaultValue"
        current={record}
        lookupCode={lookupCode}
        cascadeFields={fields}
        defaultFlag
      />
    );
  }

  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const fields = record?.get('widgetConfig.cascadeFields');
  const lookupCode = record?.get('widgetConfig.lookupCode');
  if (readOnly) {
    return <Output name={name} />;
  }

  return (
    <CascadeFields
      {...rest}
      current={formDs?.current}
      name={name}
      lookupCode={lookupCode}
      cascadeFields={fields}
    />
  );
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorCascader */
