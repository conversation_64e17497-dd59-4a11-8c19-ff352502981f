import React, { useState, useEffect } from 'react';
import classnames from 'classnames';
import isNil from 'lodash/isNil';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary, Icon } from '@zknow/components';

function strToBool(value) {
  if (value === 'false') {
    return false;
  } else if (value === 'true') {
    return true;
  } else if (value === '') {
    return null;
  }
  return value;
}

function Index(props) {
  const { formDs, record, readOnly, disabled: pageLoaderDisabled, parentKey, ...rest } = props;
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const likeText = record?.get('widgetConfig').likeText || record?.get('likeText');
  const dislikeText = record?.get('widgetConfig').dislikeText || record?.get('dislikeText');

  const current = formDs?.current;
  const [like, setLike] = useState(false);

  useEffect(() => {
    setLike(current?.get(name));
  }, [current]);

  const field = formDs?.getField(name);
  if (field) {
    field.set('label', rest?.label);
    field.set('title', rest?.title);
  }

  const addProps = (type) => {
    if (isNil(like) || like === '') {
      return {};
    }
    const f = type === 'good' ? like : !like;
    return {
      fill: f ? '#2979ff' : '#333',
      theme: f ? 'filled' : 'outline',
    };
  };

  const handleClick = (type) => {
    if (readOnly || pageLoaderDisabled || !current) return;
    let flag = like;
    flag = like === (type === 'good') ? null : type === 'good';
    if (current) {
      setLike(flag);
      current.set(name, flag ?? undefined);
    }
  };

  return (
    <span name={name} style={{ display: 'flex' }}>
      <div
        className={classnames({
          resolve: true,
          'resolve-active': like === true,
        })}
        onClick={() => handleClick('good')}
      >
        <div className="resolve-icon">
          <Icon type="goodTwo" size="16" {...addProps('good')} />
        </div>
        <div className="resolve-text">{likeText}</div>
      </div>
      <div
        className={classnames({
          resolve: true,
          'resolve-active': like === false,
        })}
        onClick={() => handleClick('bad')}
      >
        <div className="resolve-icon">
          <Icon type="badTwo" size="16" {...addProps('bad')} />
        </div>
        <div className="resolve-text">{dislikeText}</div>
      </div>
    </span>
  );
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorLike */
