import React from 'react';
import { observer } from 'mobx-react-lite';
import { Output } from 'choerodon-ui/pro';
import { withErrorBoundary, IconPicker, Icon } from '@zknow/components';

function Index(props) {
  const { defaultValueEditor, record, formDs, readOnly, disabled: pageLoaderDisabled, parentKey, intl, ...rest } = props;
  if (defaultValueEditor) {
    const iconType = record.get('widgetConfig.iconType');
    return (
      <IconPicker
        icons={iconType === 'ticket' || iconType === 'service' ? [iconType] : undefined}
        name="defaultValue"
        record={record}
        placement="bottomLeft"
      />
    );
  }

  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const iconType = record.get('widgetConfig.iconType');
  const field = formDs?.getField(name);
  if (field) {
    field.set('label', rest?.label);
    field.set('title', rest?.title);
  }
  if (readOnly) {
    return (
      <div name={name} {...rest} style={{ display: 'flex', alignItems: 'center' }}>
        <Icon type={formDs?.current?.get(name)} style={{ marginRight: 8 }} />
        <Output name={name} {...rest} />
      </div>
    );
  }

  return (
    <IconPicker
      icons={iconType === 'ticket' || iconType === 'service' ? [iconType] : undefined}
      disabled={pageLoaderDisabled}
      name={name}
      record={formDs.current}
      placement="bottomLeft"
    />
  );
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorIconPicker */
