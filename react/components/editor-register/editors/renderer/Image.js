import React from 'react';
import { observer } from 'mobx-react-lite';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@hanyk/rc-viewer';
import { message, Progress } from 'choerodon-ui/pro';
import { withErrorBoundary, FileShower, Icon, DragUploader } from '@zknow/components';
import { getFieldDisabled } from '../../utils';

function getFileUrl(file) {
  let url;
  if (window.createObjectURL) {
    url = window.createObjectURL(file);
  } else if (window?.URL?.createObjectURL) {
    url = window?.URL?.createObjectURL(file);
  } else if (window?.webkitURL?.createObjectURL) {
    url = window?.webkitURL?.createObjectURL(file);
  }
  return url;
}

function Index(props) {
  const { record, formDs, parentKey, intl, readOnly, disabled: modeDisabled, tenantId, ...rest } = props;
  const { code, name, placeHolder, widgetConfig, help } = record?.toData();
  const { fileSizeLimit, fileFormat } = widgetConfig || {};
  const fieldCode = (parentKey ? `${parentKey}.${code}` : code) || rest?.code;
  // DataSetManager中的disabled不生效
  let disabled = readOnly || modeDisabled;
  let imageViewRef;

  if (formDs?.current) {
    disabled = readOnly || getFieldDisabled(formDs?.current, formDs, fieldCode) || modeDisabled;
  }

  function beforeUpload(file, limit) {
    formDs?.current?.setState(`fileUrl-${fieldCode}`, getFileUrl(file));
    const fileSize = file.size / 1024 / 1024;
    if (limit && fileSize > limit) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.file.upload.limit', defaultMessage: '上传文件不能大于{limit}MB' }, { limit }));
      return false;
    }
    return true;
  }

  function handleCloseClick(e, fieldName, fileKey) {
    e.stopPropagation();
    const oldValue = JSON.parse(formDs.current?.get(fieldName));
    const newValue = JSON.stringify(oldValue.filter(key => key !== fileKey));
    formDs.current?.set(fieldName, newValue);
  }

  const PreviewImage = observer(({ fileKey }) => (
    <FileShower fileKey={fileKey}>
      {({ src }) => (
        <div
          className="lc-page-loader-upload-wrapper"
          style={{
            marginRight: '5px',
            marginBottom: '5px',
          }}
          onClick={(e) => {
            e.stopPropagation();
            const { viewer } = imageViewRef;
            if (viewer) {
              viewer.element.firstChild.src = src;
              viewer.update();
              viewer.show();
            }
          }}
        >
          <RcViewer
            ref={(ref) => {
              imageViewRef = ref;
            }}
            options={{
              toolbar: {
                zoomIn: { show: true },
                zoomOut: { show: true },
                oneToOne: { show: true },
                reset: { show: true },
                prev: { show: false },
                play: { show: true },
                next: { show: false },
                rotateLeft: { show: true },
                rotateRight: { show: true },
                flipHorizontal: { show: true },
                flipVertical: { show: true },
              },
            }}
            style={{ display: 'none' }}
          >
            <img alt="src" />
          </RcViewer>
          <img
            className="lc-page-loader-upload-img"
            src={src}
            alt={name}
          />
          {!disabled ? (
            <Icon
              type="CloseOne"
              theme="filled"
              className="lc-page-loader-upload-close"
              onClick={(e) => handleCloseClick(e, fieldCode, fileKey)}
            />
          ) : null}
        </div>
      )}
    </FileShower>
  ));

  function getImages() {
    const imageString = formDs?.current?.get(fieldCode);
    // 设计器图片，无论是否多图片，都存成数组
    let imageList = [];
    if (imageString) {
      try {
        imageList = JSON.parse(imageString).filter(item => !(item === null || item?.failed));
      } catch (e) {
        imageList = [imageString];
      }
    }
    if (imageList?.length !== formDs?.current?.getState(`fileLength-${fieldCode}`)) {
      formDs?.current?.setState(`progress-${fieldCode}`, 100);
    }
    formDs?.current?.setState(`fileLength-${fieldCode}`, imageList?.length);
    return imageList;
  }

  function renderImages() {
    const images = getImages();
    if (images === null) {
      formDs?.current?.setState(`progress-${fieldCode}`, 100);
      return null;
    }
    if (images || getNewFileFlag()) {
      return (
        <div
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            flexWrap: 'wrap',
          }}
        >
          {
            images.map((image, index) => {
              let fileKey = image;
              if (typeof image === 'object') {
                fileKey = image.fileKey;
              }
              if (fileKey === null) {
                return null;
              }
              return (
                <PreviewImage fileKey={fileKey} imageIndex={index} />
              );
            })
          }
          <div
            className="new-file-wrapper"
            style={{
              margin: '0 5px 5px 5px',
            }}
          >
            <NewFile flag={getNewFileFlag()} />
          </div>
        </div>
      );
    } else {
      return '';
    }
  }

  const field = formDs?.getField(code);
  if (field) {
    field.set('label', rest.label);
  }

  const NewFile = observer(({ flag }) => {
    if (!flag) {
      return null;
    }
    const url = formDs?.current?.getState(`fileUrl-${fieldCode}`);
    return (
      <div
        tabIndex="-1"
        onClick={(e) => e.stopPropagation()}
      >
        <span style={{ position: 'relative' }}>
          <img
            className="lc-page-loader-upload-img"
            src={url}
            alt=""
          />
          <Progress type="circle" percent={formDs?.current?.getState(`progress-${fieldCode}`)} width={20} format={() => ''} style={{ position: 'absolute', top: 'calc(50% - 10px)', left: 'calc(50% - 10px)' }} />
        </span>
      </div>
    );
  });

  function getNewFileFlag() {
    return formDs?.current?.getState(`progress-${fieldCode}`)
      && formDs?.current?.getState(`progress-${fieldCode}`) !== 100;
  }

  return (
    <div name={fieldCode} help={help}>
      <RcViewer
        ref={(ref) => {
          imageViewRef = ref;
        }}
        options={{
          toolbar: {
            zoomIn: { show: true },
            zoomOut: { show: true },
            oneToOne: { show: true },
            reset: { show: true },
            prev: { show: false },
            play: { show: true },
            next: { show: false },
            rotateLeft: { show: true },
            rotateRight: { show: true },
            flipHorizontal: { show: true },
            flipVertical: { show: true },
          },
        }}
        style={{ display: 'none' }}
      >
        <img alt="src" />
      </RcViewer>
      <DragUploader
        disabled={disabled}
        dragConfig={{
          // 何时显示 placeholder: 多文件且启用，单文件且启用且没值
          showPlaceHolder: () => !disabled && (fileFormat === 'multiple' || (getImages().length === 0 && fileFormat === 'single')),
          placeHolder: placeHolder || rest?.placeholder || intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' }),
          dragTip: intl.formatMessage({ id: 'lcr.components.desc.file.upload.drag.tip', defaultMessage: '拖拽文件至此上传' }),
          openFolder: intl.formatMessage({ id: 'lcr.components.desc.file.upload.open.folder', defaultMessage: '打开文件夹' }),
        }}
        name={fieldCode}
        record={formDs?.current}
        overrideProps={{
          ...rest,
          name: undefined,
          beforeUpload: (file) => beforeUpload(file, fileSizeLimit),
          multiple: fileFormat !== 'single',
          showPreviewImage: true,
          onProgress: ({ percent }) => {
            formDs?.current?.setState(`progress-${fieldCode}`, percent > 99 ? 99 : percent);
          },
        }}
        multiple
        tenantId={tenantId}
      >
        { renderImages() }
      </DragUploader>
    </div>
  );
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorImage */
