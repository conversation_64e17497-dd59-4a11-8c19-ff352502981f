/* eslint-disable jsx-a11y/control-has-associated-label */
import React, { Suspense } from 'react';
import { toJS } from 'mobx';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { withErrorBoundary } from '@zknow/components';
import { getEnv } from '@zknow/utils';
import { Form, Modal, Output } from 'choerodon-ui/pro';
import { getFieldDisabled, getFileName } from '../../utils';
import { deltaToHtmlStr, getRichJson, htmlToDelta, createRichHtml } from '@/utils';
import RichTextPreview from '@/renderer/rich-text-preview';
import FieldAssocation from '@/components/field-association';
import Wysiwyg from '@/components/wysiwyg';
import PhrasesText from '@/renderer/text-common-phrases';
import { setComponentPhrases } from '@/service';
import useServiceSetting from '../../../../hooks/useServiceSetting';

const modalKey = Modal.key();
const modalStyle = { width: 1200 };
const NORMAL_LINE_HEIGHT = 35;

/**
 * 长文本
 */

const updateData = ({ record, name, event, editor, file }) => {
  const htmlData = editor.getData();
  // 因为发现这里的数据格式变动，会导致光标不正确，所以对编辑器内部数据单独维护一份
  // record?.setState(`__ckeditor_${name}`, htmlData);
  // record?.setState(`__ckeditor_cache_${name}`, record.get(name));
  //  这里是新格式数据保存到后端
  const prevRichJson = getRichJson(record?.get(name)) || {};
  // PC 中的知识引用需要替换成兼容H5的quill格式的
  //   需要将知识提及的元素替换为 <a> 元素
  let content = editor.getData();
  if (content?.includes?.('yqmention-type="knowledge"')) {
    // 只给 H5 端使用的数据
    content = content.replace(/<span(?=.*?data-yqmention="([^"]+)")(?=.*?tenantid="([^"]+)")(?=.*?yqmention-type="knowledge")[^>]*>(.*?)<\/span>/g, (match, yqmention, tenantId, text) => {
      return `<a class="yqmention" href="${getEnv('MB_HOST')}/packageOther/pages/knowledge-detail/index?id=${yqmention}&noBreadcrumb=true&tenantId=${tenantId}">${text}</a>`;
    });
  }
  prevRichJson.quillData = htmlToDelta(content);
  if (event === 'uploadFile' && file) {
    if (!prevRichJson.attachments) {
      prevRichJson.attachments = [];
    }
    prevRichJson.attachments.push({
      ...file,
      size: file?.fileSize,
      name: getFileName(file?.fileKey),
    });
  }
  if (event === 'deleteFile' && file) {
    if (!prevRichJson.attachments) {
      prevRichJson.attachments = [];
    }
    prevRichJson.attachments = prevRichJson.attachments.filter(
      fileItem => (file.md5 && fileItem.md5 !== file.md5) || (file.fileKey && fileItem.fileKey !== file.fileKey)
    );
  }
  const richHtml = createRichHtml(htmlData, prevRichJson);
  record?.set(name, richHtml);
};

const handleChange = (record, name) => (event, editor, file) => {
  updateData({ record, name, event, editor, file });
};

const beforeGetData = (record, name) => {
  // 第一次加载后的就有编辑器的数据
  // if (record?.getState(`__ckeditor_${name}`) && record?.getState(`__ckeditor_cache_${name}`) === record?.get(name)) {
  //   return record?.getState(`__ckeditor_${name}`);
  // }

  // 非第一次加载没有__ckeditor
  if (typeof toJS(record?.get(name)) === 'object') {
    return deltaToHtmlStr(toJS(record?.get(name)));
  }

  let richHtml = record?.get(name);
  // 数据占位的空p会影响光标得干掉，编辑器内部去掉json结构
  try {
    if (record?.get(name)?.indexOf('<p data-json=') > -1) {
      richHtml = record?.get(name).substr(0, record?.get(name).indexOf('<p data-json='));
    }
  } catch {
    //
  }

  return richHtml;
};

const getAttachment = (record, name) => {
  // 非第一次加载没有__ckeditor
  if (typeof toJS(record?.get(name)) === 'object') {
    return deltaToHtmlStr(toJS(record?.get(name)));
  }

  return record?.get(name);
};

const defaultValueEditor = ({ props, disabled, record, tenantId, ticketTranslationFlag, ticketTranslationConfig }) => {
  const { intl } = props;
  const name = 'defaultValue';
  const minHeight = NORMAL_LINE_HEIGHT * 1 + 13.2;
  const RichText = observer(() => (
    <Form record={record} labelWidth="auto">
      <Suspense name="defaultValue">
        <Wysiwyg
          name={name}
          data={beforeGetData(record, name)}
          // 针对附件、录音的数据
          htmlData={record?.get(name)}
          minHeight={minHeight}
          isReadOnly={disabled}
          uploadFlag
          onChange={handleChange(record, name)}
          tenantId={tenantId}
          // ticketTranslationFlag={ticketTranslationFlag} // 有不确定因素，暂时不给支持
          // ticketTranslationConfig={ticketTranslationConfig}
        />
      </Suspense>
    </Form>
  ));
  function openModal() {
    const originValue = record.get('defaultValue');
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.default.value', defaultMessage: '默认值' }),
      children: <RichText />,
      className: 'lc-page-loader-wysiwyg-modal',
      key: modalKey,
      drawer: false,
      style: modalStyle,
      destroyOnClose: true,
      onOk: async () => {
        return true;
      },
      onCancel: () => {
        record.set('defaultValue', originValue);
        return true;
      },
    });
  }

  return (
    <Output
      name="defaultValue"
      renderer={() => (
        <span onClick={() => openModal(record)} className="lc-page-loader-wysiwyg">
          {intl.formatMessage({ id: 'lcr.components.desc.click.to.edit', defaultMessage: '点击编辑' })}
        </span>
      )}
    />
  );
};

const preview = (props) => {
  const {
    record,
    formDs,
    parentKey,
    readOnly, viewDataSet,
    disabled: modeDisabled, name: widgetFieldName,
    businessObjectCode, surveyFlag, ocrEnabledFlag, autoFocus,
    tenantId,
    ticketTranslationFlag, ticketTranslationConfig,
    ticketSolutionFlag, ticketSolutionConfig,
    ...rest } = props;
  const tenantOcrFlag = ocrEnabledFlag !== undefined ? ocrEnabledFlag : rest?.context?.HeaderStore?.getTenantConfig?.ocrEnabledFlag;
  const help = record?.get('help');
  const minRows = record?.get('widgetConfig.minRows') || record?.get('widgetConfig.rows') || 4;
  const minHeight = NORMAL_LINE_HEIGHT * minRows + 13.2;

  const maxRows = record?.get('widgetConfig.maxRows');
  // 
  const historyInputFlag = record?.get('widgetConfig.historyInputFlag');
  const maxHeight = maxRows ? NORMAL_LINE_HEIGHT * maxRows + 13.2 : undefined;

  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code') || widgetFieldName;
  let disabled = getFieldDisabled(record, formDs, name) || modeDisabled;

  if (formDs?.current) {
    disabled = getFieldDisabled(formDs?.current, formDs, name) || modeDisabled;
  }

  const fieldAssocation = rest?.context?.recommendField && name === rest?.context?.recommendField ? <FieldAssocation
    displayFlag={record?.getState('focusFlag')}
    type="search"
    fieldValue={formDs?.current.get(name)}
  /> : '';

  // 如果有详情 或者使用了草稿数据  就别赋值默认值了。
  if (formDs && formDs.current) {
    const isDetail = formDs?.current?.get('id');
    const hasDraft = formDs.getState('__isDraft');

    if (!isDetail && !hasDraft) {
      if (!formDs.current.get(name) && !formDs.getState(`${name}-init`)) {
        formDs.current.set(name, record?.get('defaultValue'));
      }
      formDs.setState(`${name}-init`, true);
    }

    // 用于判断是否由已有数据触发变更
    if (
      formDs.getState('sourceFrom') !== 'customer'
          && !formDs?.current?.getState(`__ckeditor_init_${name}`)
          && isDetail
          && formDs.current.get(name)
    ) {
          formDs?.current?.setState(`__ckeditor_init_${name}`, true);
    }
  }

  // 预览状态
  if (readOnly) {
    return (
      <Output
        name={name}
        help={help}
        renderer={() => (
          <RichTextPreview
            ocrFlag
            data={beforeGetData(formDs?.current, name)}
            dataAttachment={getAttachment(formDs?.current, name)}
            htmlData={formDs?.current?.get(name)?.includes?.('<p data-json=') ? formDs?.current?.get(name) : ''}
            preview
            minHeight={1}
            showExtraElement
            ticketId={formDs?.current?.get('id')}
            tenantId={tenantId}
          />
        )}
      />
    );
  }

  function extractTextFromHTMLDOM(htmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    return doc.body.textContent || doc.body.innerText;
  }

  // handleBlur，记录一下常用输入，需要调接口记录
  const handleBlur = () => {
    try {
      const stringValue = extractTextFromHTMLDOM(formDs?.current?.get(name));
      if (stringValue && historyInputFlag && formDs?.current?.get(name)) {
        setComponentPhrases(tenantId, record?.get('id'), formDs?.current?.get(name));
        record?.setState('updateListCount', (record?.getState('updateListCount') || 0) + 1);
      }
      record?.setState('focusFlag', false);
    } catch (error) {
      record?.setState('focusFlag', false);
    }
  };

  return (
    <Suspense name={name} help={help} fallback="">
      <Wysiwyg
        {...rest}
        name={name}
        // 针对编辑器自身的数据
        data={beforeGetData(formDs?.current, name)}
        // 针对附件、录音的数据
        htmlData={formDs?.current?.get(name)}
        onChange={handleChange(formDs?.current, name)}
        ocrFlag={(surveyFlag || !tenantOcrFlag) ? false : record?.get('ocrFlag')}
        record={record}
        formDs={formDs}
        disabled={disabled}
        minHeight={minHeight}
        maxHeight={maxHeight}
        ticketId={formDs?.current?.get('id')}
        readyTagFlag
        isReady={formDs?.length > 0}
        businessObjectCode={viewDataSet?.current?.get('businessObjectCode') || businessObjectCode}
        businessObjectId={viewDataSet?.current?.get('businessObjectId')}
        placeholder={record?.get('placeHolder')}
        miniMode={record?.get('widgetConfig.miniMode')}
        uploadFlag
        preview={readOnly}
        autoFocus={autoFocus}
        tenantId={tenantId}
        ticketTranslationFlag={ticketTranslationFlag}
        ticketTranslationConfig={ticketTranslationConfig}
        ticketSolutionFlag={ticketSolutionFlag}
        ticketSolutionConfig={ticketSolutionConfig}
        onBlur={handleBlur}
        onFocus={() => { record?.setState('focusFlag', true); }}
      />
      {fieldAssocation}
      {/* 输入历史（常用语） */}
      {historyInputFlag && <PhrasesText name={name} phrasesComponentId={record?.get('id')} phrasesValue={formDs?.current?.get(name)} formComponentType="RichText" changeFn={(text) => { formDs?.current?.set(name, text); }} updateListCount={record?.getState('updateListCount')} />}
    </Suspense>
  );
};

const Index = observer((props) => {
  const { defaultFlag, ...rest } = props;
  const businessObjectId = rest?.viewDataSet?.current?.get('businessObjectId');

  // 获取服务配置中的工单翻译的信息
  //  由于现在工单翻译的配置是在服务配置中与业务对象关联的，所以需要放到视图加载层获取
  const serviceSetting = useServiceSetting(businessObjectId, '', rest.tenantId, rest.AppState);
  const ticketTranslationFlag = serviceSetting?.ticketTranslationFlag;
  const ticketTranslationConfig = serviceSetting?.ticketTranslationSettingVO;
  const ticketSolutionFlag = serviceSetting?.intelligentSolutionFlag;
  const ticketSolutionConfig = serviceSetting?.intelligentSolutionSettingVO;

  if (defaultFlag) return defaultValueEditor({ ...rest, ticketTranslationFlag, ticketTranslationConfig });
  return preview({ ...rest, ticketTranslationFlag, ticketTranslationConfig, ticketSolutionFlag, ticketSolutionConfig });
});

export default withErrorBoundary(Index);

/* externalize: LcEditorWysiwyg */
