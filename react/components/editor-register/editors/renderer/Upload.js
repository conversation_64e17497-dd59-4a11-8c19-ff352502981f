import React, { useRef, useEffect, useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@hanyk/rc-viewer';
import { FileShower, FileUploader, Icon, YqPreview, DragUploader, Button, withErrorBoundary } from '@zknow/components';
import { getCookieToken } from '@zknow/utils';
import classnames from 'classnames';
import FileSaver from 'file-saver';
import { observer } from 'mobx-react-lite';
import copy from 'copy-to-clipboard';
import { message, Modal, Tooltip, Progress } from 'choerodon-ui/pro';
import { getFieldDisabled, getFileIcon, getFileType, getFileName } from '../../utils';

const modalKey = Modal.key();
const IMAGE_FORMAT_LIST = ['jpg', 'jpeg', 'png', 'gif'];

/**
 * URL输入框
 */

function transformAccept(fileAccept) {
  const acceptList = [];
  fileAccept?.split(',')?.forEach(accept => {
    if (accept) {
      acceptList.push(accept.startsWith('.') ? accept : `.${accept}`);
    }
  });
  return acceptList.length ? acceptList.join(',') : undefined;
}

function handleProgress(percent, record, name) {
  record?.setState(`progress-${name}`, percent > 99 ? 99 : percent);
  if (record?.getState(`cancel-${name}`)) {
    record?.setState(`progress-${name}`, 100);
    record?.setState(`cancel-${name}`, false);
    return 'abort';
  }
}
// 上传前处理
function beforeUpload(file, fileLimit, record, name, intl) {
  record?.setState(`file-${name}`, file);
  const fileSize = file.size / 1024 / 1024;
  if (fileLimit && fileSize > fileLimit) {
    message.error(intl.formatMessage({ id: 'lcr.components.desc.file.upload.limit', defaultMessage: '上传文件不能大于{limit}MB' }, { limit: fileLimit }));
    return false;
  }
  record?.setState(`uploading-${name}`, true);
  return true;
}
// 获取附件list
function getFile(record, name, pristineValue) {
  const file = pristineValue ? record?.getPristineValue(name) : record?.get(name);
  // 设计器附件，无论是否多附件，都存成数组
  let fileList = [];
  if (file) {
    try {
      fileList = JSON.parse(file).filter(item => !(item === null || item?.failed));
    } catch (e) {
      fileList = [file];
    }
  }
  if (fileList?.length !== record?.getState(`fileLength-${name}`)) {
    record?.setState(`progress-${name}`, 100);
  }
  record?.setState(`fileLength-${name}`, fileList?.length);
  return fileList;
}
// 渲染附件list
function renderFiles(record, fieldCode, File) {
  const files = getFile(record, fieldCode);
  
  if (files) {
    return files.map((file) => {
      let fileKey = file;
      let fileSize = 0;
      if (typeof file === 'object') {
        fileKey = file.fileKey;
        fileSize = file.fileSize;
      }
      if (fileKey === null) {
        record?.setState(`progress-${fieldCode}`, 100);
        return null;
      }
      return (
        <File
          fileKey={fileKey}
          fileSize={fileSize}
          udmTenantId={file?.udmTenantId}
        />
      );
    });
  }
  return '';
}
// 下载附件
function handleDownload(url, intl) {
  let newUrl;
  const token = getCookieToken();
  if (url.includes('?')) {
    newUrl = `${url}&access_token=${token}`;
  } else {
    newUrl = `${url}?access_token=${token}`;
  }
  if (navigator?.userAgent?.includes('wxwork')) {
    copy(newUrl);
    Modal.info({
      title: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips', defaultMessage: '附件地址已复制到剪切板' }),
      children: intl.formatMessage({ id: 'lcr.components.desc.wxwork.tips.detail', defaultMessage: '由于企业微信限制，需要您粘贴附件地址到浏览器中下载附件。' }),
    });
  } else {
    FileSaver.saveAs(newUrl, getFileName(url));
  }
}
// 预览附件
const previewFile = (fileKey, fileType, imageViewRef, intl, udmTenantId) => {
  if (IMAGE_FORMAT_LIST.includes(fileType)) {
    const src = fileKey?.startsWith('http') ? fileKey : `${window._env_.API_HOST}/hfle/yqc/v1/0/files/download-by-key?fileKey=${encodeURIComponent(fileKey)}`;
    const { viewer } = imageViewRef;
    if (viewer) {
      viewer.element.firstChild.src = src;
      viewer.update();
      viewer.show();
    }
  } else {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.components.desc.file.preview', defaultMessage: '附件预览' }),
      children: (
        <YqPreview
          fileKey={fileKey}
          udmTenantId={udmTenantId}
        />
      ),
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
    });
  }
};
// 删除附件
function handleDelete(fileKey, fileFormat, record, name) {
  if (fileFormat !== 'single') {
    const fileListStr = record?.get(name);
    try {
      const fileList = JSON.parse(fileListStr);
      const newFileList = fileList.filter(key => key.fileKey !== fileKey);
      if (newFileList.length === 0) {
        record?.set(name, '');
      } else {
        record?.set(name, JSON.stringify(newFileList));
      }
    } catch (e) {
      record?.set(name, '');
    }
  } else {
    record?.set(name, '');
  }
  record?.setState(`uploading-${name}`, false);
}
// 默认值渲染
const defaultValueEditor = ({ fieldProps, disabled, record, tenantId }) => {
  const { intl } = fieldProps;
  const { fileFormat, fileSizeLimit, fileAccept } = record?.get('widgetConfig') || {};
  const fieldCode = 'defaultValue';
  let imageViewRef;

  const FileInfo = observer((props) => {
    const { fileName, fileSize, fileKey, udmTenantId } = props;
    const size = fileSize / 1024 > 1024
      ? `${Number(fileSize / 1024 / 1024).toFixed(1)}MB`
      : `${Number(fileSize / 1024).toFixed(1)}KB`;
    return (
      <div className="lc-page-loader-upload-file-default-info">
        <Tooltip title={fileName} theme="dark">
          <div className="lc-page-loader-upload-file-default-name" onClick={() => previewFile(fileKey, getFileType(fileKey), imageViewRef, intl, udmTenantId)}>{fileName}</div>
        </Tooltip>
        <div className="lc-page-loader-upload-file-default-name-size">{`(${size})`}</div>
      </div>
    );
  });

  const File = observer((props) => {
    const { fileKey, fileSize, udmTenantId } = props;

    return (
      <FileShower
        fileKey={fileKey}
      >
        {({ src }) => (
          record && src ? (
            <div tabIndex="-1" onClick={(e) => e.stopPropagation()} className="lc-page-loader-upload-file" style={{ width: 235, position: 'relative' }}>
              <span>
                <Icon type="Link" className="lc-page-loader-upload-link" />
                <span><FileInfo
                  fileName={getFileName(src)}
                  fileSize={fileSize}
                  fileKey={fileKey}
                  udmTenantId={udmTenantId}
                /></span>
              </span>
              <span className="lc-page-loader-upload-action">
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })} theme="dark">
                  <Icon
                    type="Download"
                    size="14"
                    className="lc-page-loader-upload-icon"
                    onClick={() => handleDownload(src, intl)}
                  />
                </Tooltip>
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' })} theme="dark">
                  <Icon
                    type="PreviewOpen"
                    size="14"
                    onClick={() => previewFile(fileKey, getFileType(fileKey), imageViewRef, intl, udmTenantId)}
                    className="lc-page-loader-upload-icon"
                  />
                </Tooltip>
                {!disabled ? (
                  <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })} theme="dark">
                    <Icon
                      type="delete"
                      size="14"
                      onClick={() => handleDelete(fileKey, fileFormat, record, fieldCode)}
                      className="lc-page-loader-upload-icon"
                    />
                  </Tooltip>
                ) : null}
              </span>
            </div>
          ) : null
        )}
      </FileShower>
    );
  });

  function getNewFileFlag() {
    return record?.getState(`progress-${fieldCode}`) && record?.getState(`progress-${fieldCode}`) !== 100;
  }

  const NewFile = observer(({ fileName, fileSize, flag }) => {
    if (!flag) {
      return null;
    }
    return (
      <div
        tabIndex="-1"
        onClick={(e) => e.stopPropagation()}
        className={
          classnames({
            'lc-page-loader-upload-file': true,
            'lc-page-loader-upload-file-default': true,
            'lc-page-loader-upload-file-hover': getNewFileFlag(),
          })
        }
      >
        <span>
          <Icon type={getFileIcon(getFileType(fileName)).iconType} theme="filled" fill={getFileIcon(getFileType(fileName)).color} size="16" className="lc-page-loader-upload-link" />
          <span><FileInfo fileName={fileName} fileSize={fileSize} /></span>
        </span>
        <span style={{ marginRight: 8, position: 'relative' }}>
          <span style={{ color: '#8C8C8C', marginRight: '.06rem', fontSize: '.12rem' }}>
            {`${Math.floor(record?.getState(`progress-${fieldCode}`))}%`}
          </span>
          <span style={{ position: 'relative', top: -2 }}>
            <Progress type="circle" percent={record?.getState(`progress-${fieldCode}`)} width={18} format={() => ''} />
          </span>
          {
            getNewFileFlag() && (
              <div className="cancel-upload">
                <Icon type="close-one" theme="filled" size="20" fill="#8C8C8C" onClick={handleCancel} />
              </div>
            )
          }
        </span>
      </div>
    );
  });

  function cancelUpload() {
    record?.setState(`cancel-${fieldCode}`, true);
  }

  function handleCancel(e) {
    e.stopPropagation();
    cancelUpload();
  }

  return (
    <div name={fieldCode}>
      <RcViewer
        ref={(ref) => {
          imageViewRef = ref;
        }}
        options={{
          toolbar: {
            zoomIn: { show: true },
            zoomOut: { show: true },
            oneToOne: { show: true },
            reset: { show: true },
            prev: { show: false },
            play: { show: true },
            next: { show: false },
            rotateLeft: { show: true },
            rotateRight: { show: true },
            flipHorizontal: { show: true },
            flipVertical: { show: true },
          },
        }}
        style={{ display: 'none' }}
      >
        <img alt="src" />
      </RcViewer>
      <FileUploader
        disabled={disabled}
        name={fieldCode}
        record={record}
        fileSize
        overrideProps={{
          accept: transformAccept(fileAccept),
          className: 'lc-page-loader-upload',
          beforeUpload: (file) => beforeUpload(file, fileSizeLimit, record, fieldCode, intl),
          onSuccess: (res) => {
            if (res.failed) {
              record?.setState(`progress-${fieldCode}`, 100);
            }
          },
          onProgress: ({ percent }) => {
            return handleProgress(percent, record, fieldCode);
          },
          onError: (error, res) => {
            if (res.failed) {
              record?.setState(`progress-${fieldCode}`, 100);
            }
          },
          multiple: fileFormat !== 'single',
        }}
        multiple
        tenantId={tenantId}
      >
        <div onClick={(e) => getNewFileFlag() && e.stopPropagation()}>
          {!disabled && (fileFormat !== 'single' || getFile(record, fieldCode)?.length === 0) ? (
            <span
              className={
                classnames({
                  'lc-page-loader-upload-btn': true,
                  'lc-page-loader-upload-btn-ban': getNewFileFlag(),
                })
              }
            >
              {intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' })}
            </span>
          ) : null}
        </div>
        {renderFiles(record, fieldCode, File)}
        <div className="new-file-wrapper">
          <NewFile
            flag={getNewFileFlag()}
            fileName={record?.getState(`file-${fieldCode}`)?.name}
            fileSize={record?.getState(`file-${fieldCode}`)?.size}
          />
        </div>
      </FileUploader>
    </div>
  );
};
// 附件组件预览
const preview = ({ record, formDs, parentKey, intl, readOnly, disabled: modeDisabled, tenantId, ...rest }) => {
  const { code, widgetConfig, placeHolder, help } = record?.toData() || {};
  const { fileFormat = 'single', fileSizeLimit, fileAccept } = widgetConfig || {};
  const fullCode = (parentKey ? `${parentKey}.${code}` : code);
  let imageViewRef;
  // 如果外部表单为只读或者动作集（默认可编辑），则根据外部控制
  // DataSetManager中的disabled不生效，需要单独计算
  let disabled = readOnly || modeDisabled;

  if (formDs?.current) {
    disabled = readOnly || (modeDisabled === undefined ? getFieldDisabled(formDs?.current, formDs, fullCode) : modeDisabled);
  }

  const fieldCode = fullCode || rest.code;

  const FileInfo = observer((props) => {
    const { fileName, fileSize, fileKey, udmTenantId } = props;

    const infoRef = useRef();
    const [displayName, setDisplayName] = useState('');

    useEffect(() => {
      if (infoRef.current) {
        const { width } = getComputedStyle(infoRef.current);
        infoRef.current.style.width = width;
        setDisplayName(fileName);
      }
    }, []);

    const size = fileSize / 1024 > 1024
      ? `${Number(fileSize / 1024 / 1024).toFixed(1) || 0}MB`
      : `${Number(fileSize / 1024).toFixed(1) || 0}KB`;
    return (
      <div className="lc-page-loader-upload-file-info" onClick={() => previewFile(fileKey, getFileType(fileKey), imageViewRef, intl, udmTenantId)} ref={infoRef}>
        <Tooltip title={fileName} theme="dark">
          <span className="lc-page-loader-upload-file-name">{displayName}</span>
        </Tooltip>
        <span className="lc-page-loader-upload-file-name-size">{`(${size})`}</span>
      </div>
    );
  });

  const File = observer((props) => {
    const { fileKey, fileSize, udmTenantId } = props;
    return (
      <FileShower
        fileKey={fileKey}
      >
        {({ src }) => (
          formDs?.current && src ? (
            <div tabIndex="-1" onClick={(e) => e.stopPropagation()} className="lc-page-loader-upload-file" style={{ position: 'relative' }}>
              <span style={{ width: '100%', overflow: 'hidden' }}>
                <Icon type="Link" className="lc-page-loader-upload-link" />
                <span style={{ width: '100%' }}><FileInfo fileName={getFileName(fileKey)} fileSize={fileSize} fileKey={fileKey} udmTenantId={udmTenantId} /></span>
              </span>
              <span className="lc-page-loader-upload-action">
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview', defaultMessage: '预览' })} theme="dark">
                  <Icon
                    type="PreviewOpen"
                    size="14"
                    onClick={() => previewFile(fileKey, getFileType(fileKey), imageViewRef, intl, udmTenantId)}
                    className="lc-page-loader-upload-icon"
                  />
                </Tooltip>
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download', defaultMessage: '下载' })} theme="dark">
                  <Icon
                    type="Download"
                    size="14"
                    className="lc-page-loader-upload-icon"
                    onClick={() => handleDownload(src, intl)}
                  />
                </Tooltip>
                {!disabled ? (
                  <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })} theme="dark">
                    <Icon
                      type="delete"
                      size="14"
                      onClick={() => handleDelete(fileKey, fileFormat, formDs?.current, fieldCode)}
                      className="lc-page-loader-upload-icon"
                    />
                  </Tooltip>
                ) : null}
              </span>
            </div>
          ) : null
        )}
      </FileShower>
    );
  });

  const field = formDs?.getField(code);
  if (field) {
    field.set('label', rest.label);
  }

  function cancelUpload() {
    formDs?.current?.setState(`cancel-${fieldCode}`, true);
  }

  function handleCancel(e) {
    e.stopPropagation();
    cancelUpload();
  }

  /**
   * 上传中的附件，显示上传进度
   * @type {React.FunctionComponent<object>}
   */
  const NewFile = observer(({ flag, fileName, fileSize }) => {
    if (!flag) {
      return null;
    }

    return (
      <div
        tabIndex="-1"
        onClick={(e) => e.stopPropagation()}
        className={
          classnames({
            'lc-page-loader-upload-file': true,
            'lc-page-loader-upload-file-hover': getNewFileFlag(),
          })
        }
      >
        <span>
          <Icon type={getFileIcon(getFileType(fileName)).iconType} theme="filled" fill={getFileIcon(getFileType(fileName)).color} size="16" className="lc-page-loader-upload-link" />
          <span><FileInfo fileName={fileName} fileSize={fileSize} /></span>
        </span>
        <span style={{ marginRight: 8, position: 'relative' }}>
          <span style={{ color: '#8C8C8C', marginRight: '.06rem', fontSize: '.12rem' }}>
            {`${Math.floor(formDs?.current?.getState(`progress-${fieldCode}`))}%`}
          </span>
          <span style={{ position: 'relative', top: -2 }}>
            <Progress type="circle" percent={formDs?.current?.getState(`progress-${fieldCode}`)} width={18} format={() => ''} />
          </span>
          <div className="cancel-upload">
            <Icon type="close-one" theme="filled" size="20" fill="#8C8C8C" onClick={(e) => handleCancel(e)} />
          </div>
        </span>
      </div>
    );
  });

  function getNewFileFlag() {
    return formDs?.current?.getState(`progress-${fieldCode}`)
      && formDs?.current?.getState(`progress-${fieldCode}`) !== 100;
  }

  const showPlaceHolder = () => !disabled
    && (fileFormat === 'multiple' || (getFile(formDs?.current, fieldCode)?.length === 0 && fileFormat === 'single'));

  return (
    // eslint-disable-next-line react/no-unknown-property
    <div name={fieldCode} help={help}>
      <RcViewer
        ref={(ref) => {
          imageViewRef = ref;
        }}
        options={{
          toolbar: {
            zoomIn: { show: true },
            zoomOut: { show: true },
            oneToOne: { show: true },
            reset: { show: true },
            prev: { show: false },
            play: { show: true },
            next: { show: false },
            rotateLeft: { show: true },
            rotateRight: { show: true },
            flipHorizontal: { show: true },
            flipVertical: { show: true },
          },
        }}
        style={{ display: 'none' }}
      >
        <img alt="src" />
      </RcViewer>
      {
        widgetConfig?.fileDragUpload ? (
          <DragUploader
            dragConfig={{
              showPlaceHolder,
              onlyDragArea: true,
              placeHolder: placeHolder || rest?.placeholder || intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' }),
              dragTip: placeHolder || rest?.placeholder || intl.formatMessage({ id: 'lcr.components.desc.file.upload.drag.tip', defaultMessage: '拖拽文件至此上传' }),
              openFolder: intl.formatMessage({ id: 'lcr.components.desc.file.upload.open.folder', defaultMessage: '打开文件夹' }),
            }}
            disabled={disabled}
            name={fieldCode}
            record={formDs?.current}
            fileSize
            overrideProps={{
              ...rest,
              name: undefined,
              accept: transformAccept(fileAccept),
              className: 'lc-page-loader-upload',
              beforeUpload: (file) => beforeUpload(file, fileSizeLimit, formDs?.current, fieldCode, intl),
              onSuccess: (res) => {
                if (res.failed) {
                  formDs?.current?.setState(`progress-${fieldCode}`, 100);
                } else {
                  message.success(intl.formatMessage({ id: 'lcr.components.desc.upload.success', defaultMessage: '上传成功' }));
                }
              },
              onProgress: ({ percent }) => {
                return handleProgress(percent, formDs?.current, fieldCode);
              },
              onError: (error, res) => {
                if (res.failed) {
                  formDs?.current?.setState(`progress-${fieldCode}`, 100);
                }
              },
              multiple: fileFormat !== 'single',
            }}
            multiple
            tenantId={tenantId}
          >
            {renderFiles(formDs?.current, fieldCode, File)}
            <div className="new-file-wrapper">
              <NewFile
                flag={getNewFileFlag()}
                fileName={formDs?.current?.getState(`file-${fieldCode}`)?.name}
                fileSize={formDs?.current?.getState(`file-${fieldCode}`)?.size}
              />
            </div>
          </DragUploader>
        ) : (
          <FileUploader
            disabled={disabled}
            name={fieldCode}
            record={formDs?.current}
            fileSize
            overrideProps={{
              ...rest,
              name: undefined,
              accept: transformAccept(fileAccept),
              className: 'lc-page-loader-upload',
              beforeUpload: (file) => beforeUpload(file, fileSizeLimit, formDs?.current, fieldCode, intl),
              onSuccess: (res) => {
                if (res.failed) {
                  formDs?.current?.setState(`progress-${fieldCode}`, 100);
                }
              },
              onProgress: ({ percent }) => {
                return handleProgress(percent, formDs?.current, fieldCode);
              },
              onError: (error, res) => {
                if (res.failed) {
                  formDs?.current?.setState(`progress-${fieldCode}`, 100);
                }
              },
              multiple: fileFormat !== 'single',
            }}
            multiple
            tenantId={tenantId}
          >
            <span onClick={(e) => getNewFileFlag() && e.stopPropagation()}>
              {showPlaceHolder() ? (
                <Button
                  key="upload-btn"
                  icon="UploadTwo"
                  disabled={getNewFileFlag() || disabled}
                >
                  {placeHolder || rest?.placeholder || intl.formatMessage({ id: 'lcr.components.desc.please.upload', defaultMessage: '点击上传' })}
                </Button>
              ) : null}
            </span>
            {renderFiles(formDs?.current, fieldCode, File)}
            <div className="new-file-wrapper">
              <NewFile flag={getNewFileFlag()} fileName={formDs?.current?.getState(`file-${fieldCode}`)?.name} fileSize={formDs?.current?.getState(`file-${fieldCode}`)?.size} />
            </div>
          </FileUploader>
        )
      }
    </div>
  );
};

const Index = observer((props) => {
  const { defaultFlag, ...rest } = props;
  if (defaultFlag) return defaultValueEditor({ ...rest });
  return preview({ ...rest });
});

export default withErrorBoundary(Index);

/* externalize: LcEditorUpload */
