import React, { useRef, useEffect, useMemo } from 'react';
import { Alert } from 'choerodon-ui';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';

function Index(props) {
  const { record } = props;
  const alertType = record.get('widgetConfig.alertType');
  const alertMessage = record.get('widgetConfig.alertMessage');
  const Tip = () => {
    const alertRef = useRef();

    useEffect(() => {
      if (alertRef?.current) {
        const trRef = alertRef?.current?.parentElement?.parentElement?.parentElement;
        if (trRef && trRef.childNodes.length === 2) {
          trRef.childNodes[1].colSpan = 2;
          trRef.childNodes[0].remove();
        }
      }
    }, [alertRef?.current]);

    const messageRender = useMemo(() => {
      if (alertMessage.indexOf('\n') > -1) {
        return (
          <span>
            {alertMessage.split('\n').map(v => {
              return (
                <div>{v}</div>
              );
            })}
          </span>
        );
      }
      return alertMessage;
    }, [alertMessage]);

    return <div ref={alertRef}>
      <Alert
        message={messageRender || alertType}
        type={alertType && alertType?.toLowerCase()}
        showIcon
      />
    </div>;
  };

  // 包一层span是因为不选渲染label，隐藏时，下个字段的label会被隐藏（目前表单没法设置key）
  return <span><Tip /></span>;
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorAlert */
