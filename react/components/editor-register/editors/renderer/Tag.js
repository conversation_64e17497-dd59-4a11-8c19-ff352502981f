import React from 'react';
import { Output } from 'choerodon-ui/pro';
import { SelectTag, withErrorBoundary } from '@zknow/components';
import { getQueryString } from '@/utils';
import { getFieldDisabled } from '../../utils';
/**
 * 标签组件
 */

const Index = ({ record, readOnly, disabled: pageLoaderDisabled, parentKey, formDs, ...rest }) => {
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  if (readOnly || pageLoaderDisabled) {
    return <Output name={name} {...rest} />;
  }
  const tagGroupId = record.get('widgetConfig.tagGroupId');
  const tenantId = getQueryString('tenantId');
  const tagQuickAdd = record.get('widgetConfig.tagQuickAdd');
  const tagAddMode = record.get('widgetConfig.tagAddMode');
  const tagMultiFlag = record.get('widgetConfig.tagMultiFlag');
  const tagViewType = record.get('widgetConfig.tagViewType');
  const conditions = record.get('widgetConfig.condition');

  let disabled = getFieldDisabled(formDs?.current, formDs, name);

  if (formDs?.current) {
    disabled = getFieldDisabled(formDs?.current, formDs, name);
  }

  const ds = formDs.getField(name)?.options;
  if (formDs?.current && ds && !ds.length && !ds.getState(`${name}-init`)) {
    ds.setQueryParameter('params', formDs?.current?.toData());
    ds.query();
    ds.setState(`${name}-init`, true);
  }

  return (
    <SelectTag.SelectTag
      name={name}
      rest={rest}
      tagQuickAdd={tagQuickAdd}
      tagAddMode={tagAddMode}
      tagGroupId={tagGroupId}
      tenantId={tenantId}
      disabled={disabled}
      multiple={tagMultiFlag}
      tagViewType={tagViewType}
      tagDs={ds}
      formDs={formDs}
      conditions={conditions}
    />
  );
};

export default withErrorBoundary(Index);

/* externalize: LcEditorTag */
