import React from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import { getFieldDisabled } from '../../utils';
import YQColor from '@/components/yq-color';

function Index(props) {
  const { defaultValueEditor, record, formDs, readOnly, parentKey, ...rest } = props;
  if (defaultValueEditor) {
    return (
      <YQColor
        name="defaultValue"
        record={record}
        value={record.get('defaultValue')}
        addonAfter
      />
    );
  }

  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const { widgetConfig } = record.toData();
  let disabled = getFieldDisabled(record, formDs, name);

  if (formDs?.current) {
    disabled = getFieldDisabled(formDs?.current, formDs, name);
  }
  return (
    <YQColor
      name={name}
      record={formDs.current}
      preview={readOnly || disabled}
      value={formDs.current?.get(name)}
      addonAfter={widgetConfig?.showColorValue}
      {...rest}
    />
  );
}

export default withErrorBoundary(observer(Index));

/* externalize: LcEditorColor */
