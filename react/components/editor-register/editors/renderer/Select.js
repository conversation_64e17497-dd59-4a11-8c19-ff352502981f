import React, { useEffect } from 'react';
import { color as colorUtils } from '@zknow/utils';
import { withErrorBoundary } from '@zknow/components';
import { Output, TreeSelect, Select } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';

/**
 * 单选
 */

const defaultValueEditor = ({ disabled, record }) => {
  const cascadeFlag = record?.get('widgetConfig.cascadeFlag');
  function optionsFilter(node) {
    const parentFieldCode = record.get('widgetConfig.cascadeParentField');
    const parentValue = record.dataSet.find(item => item.get('code') === parentFieldCode)?.get('defaultValue');
    const parentId = node.dataSet.find(option => option.get('code') === parentValue)?.get('id');
    return node.get('parentId') === (parentFieldCode ? parentId : '0');
  }
  const config = {
    name: 'defaultValue',
    searchable: true,
    disabled,
    onOption: (({ record: node }) => {
      if (node?.children && record?.get('widgetConfig.onlyLeafFlag') && !cascadeFlag) {
        return {
          disabled: true,
        };
      }
      if (node && !node.get('enabledFlag') && record?.get('widgetConfig.dataSource') === 'lookup') {
        return {
          disabled: true,
        };
      }
      return {};
    }),
  };
  if (cascadeFlag) {
    return <Select {...config} optionsFilter={optionsFilter} />;
  }
  return <TreeSelect {...config} />;
};
const preview = ({ record, readOnly, parentKey, formDs, ...rest }) => {
  const name = parentKey ? `${parentKey}.${record?.get('code')}` : record?.get('code');
  const cascadeFlag = record?.get('widgetConfig.cascadeFlag');
  const cascadeParentField = record?.get('widgetConfig.cascadeParentField');
  if (readOnly) {
    return <Output name={name} {...rest} />;
  }

  /**
     * 展开 treeSelectNode 当前项
     */
  function handleExpandSelectTreeNode(element) {
    if (!element) return false;
    let parent = element;
    for (let i = 0; i < 6; i++) {
      if (parent?.className?.includes?.('tree-select-dropdown-menu-item')) {
        parent?.getElementsByClassName?.('c7n-tree-switcher')?.[0]?.click?.();
      }
      parent = parent.parentElement;
    }
    return false;
  }

  /**
     * 递归判断父级是否符合筛选
     * @param current
     * @param text 搜索条件
     * @param textField
     * @returns {boolean}
     */
  function searchParentMatcher(current, text, textField) {
    if (current.parent) {
      if (current.parent.get(textField).toLowerCase()?.includes(text.toLowerCase())) {
        return true;
      }
      return searchParentMatcher(current.parent, text, textField);
    }
    return false;
  }

  /**
     * 选项集自定义筛选
     * 怪异需求：如果某个节点符合搜索条件，则其子级默认全部符合条件
     * 实现逻辑：由于TreeSelect每个节点独立判断，所以这里递归判断父级是否符合筛选
     * @param props
     * @returns {boolean|*}
     */
  function searchMatcher(props) {
    const { text, record: current, textField } = props;
    if (searchParentMatcher(current, text, textField)) {
      return true;
    }
    return current?.get(textField).toLowerCase()?.includes(text.toLowerCase());
  }

  function renderOption(text, color) {
    return (
      <div
        className="lc-tree-select-renderer"
        style={{
          backgroundColor: color,
          color: colorUtils?.calColorShade(color) ? '#fff' : '#595959',
        }}
      >
        {text}
      </div>
    );
  }

  function renderer(data) {
    const { text, value, record: current } = data;
    if (!record?.get('widgetConfig.showColorFlag')) {
      return text;
    }
    const options = current?.getField(name)?.options;
    const currentOption = options?.find(option => option.get('code') === value);
    const color = currentOption?.get('color');
    if (color) {
      return renderOption(text, color);
    }
    return text;
  }

  function optionRenderer({ record: current }, _type) {
    const text = current.get('meaning');
    const color = current?.get('color');
    if (color && record?.get('widgetConfig.showColorFlag')) {
      return renderOption(text, color);
    }
    if (_type === 'Select') return text;
    return <div onClick={(e) => {
      if (current?.children?.length && record.get('widgetConfig.onlyLeafFlag')) {
        e.stopPropagation();
        handleExpandSelectTreeNode(e.target);
      }
    }}
    >{text}</div>;
  }

  function optionsFilter(node, nodes) {
    if (cascadeFlag) {
      const parentFieldCode = record.get('widgetConfig.cascadeParentField');
      const parentValue = formDs?.current.get(parentFieldCode);
      const parentId = node.dataSet.find(option => option.get('code') === parentValue)?.get('id');
      return node.get('enabledFlag') !== false ? node.get('parentId') === (parentFieldCode ? parentId : '0') : false;
    } else {
      return node.get('enabledFlag') !== false;
    }
  }

  // 问题详见生产工单 INC00025668
  const cascadeOptionsFilter = (node, nodes) => {
    if (cascadeParentField) {
      const cascadeParent = formDs?.current?.get(cascadeParentField);
      const parentField = formDs?.getField(cascadeParentField);
      // 注意级联父级的 options 的获取方式
      const parentId = (parentField?.options || []).find(option => option.get('code') === cascadeParent)?.get('id');
      return node.get('parentId') === (cascadeParentField ? parentId : '0');
    }
    return true;
  };

  if (cascadeFlag) {
    return (
      <Select
        name={name}
        {...rest}
        searchable
        searchMatcher={searchMatcher}
        // optionsFilter={cascadeOptionsFilter}
        onOption={(({ record: node }) => {
          if (node && !node.get('enabledFlag') && record?.get('widgetConfig.dataSource') === 'lookup') {
            return {
              disabled: true,
            };
          }
          return {};
        })}
        renderer={renderer}
        optionRenderer={(param) => optionRenderer(param, 'Select')}
      />
    );
  }

  return (
    <TreeSelect
      name={name}
      {...rest}
      searchable
      searchMatcher={searchMatcher}
      optionsFilter={optionsFilter}
      onOption={(({ record: node }) => {
        if (node?.children && record?.get('widgetConfig.onlyLeafFlag')) {
          return {
            disabled: true,
          };
        }
        if (node && !node.get('enabledFlag') && record?.get('widgetConfig.dataSource') === 'lookup') {
          return {
            disabled: true,
          };
        }
        return {
          isLeaf: !node?.children || !!node?.children?.find(n => !n.get('enabledFlag')),
        };
      })}
      renderer={renderer}
      optionRenderer={(param) => optionRenderer(param, 'TreeSelect')}
    />
  );
};

const Index = observer((props) => {
  const { defaultFlag, name, formDs, ...rest } = props;
  useEffect(() => {
    if (formDs?.getState('hasQueryFlag') || formDs?.current?.getState('initQueryFlag')) {
      const field = formDs.getField(name);
      const lookupUrl = field && field.props && field.props.get('lookupUrl') && field.props.get('lookupUrl')();
      if (lookupUrl?.includes('queryCodeWithCondition') || lookupUrl?.includes('queryValuesByParentCode')) {
        field.setLovPara('_t', new Date().valueOf());
      }
    }
  }, [formDs?.current?.get(name), formDs?.current?.get('id'), formDs?.current?.getState('approvalHistoryDynamicRefreshCount')]); // FIXME: 低代码详情刷新按钮点击需要重新请求选项集的接口
  if (defaultFlag) return defaultValueEditor({ name, formDs, ...rest });
  return preview({ name, formDs, ...rest });
});

export default withErrorBoundary(Index);

/* externalize: LcEditorSelect */
