@import '~choerodon-ui/lib/style/themes/default';

.lc-form-page-loader,.lc-form-right,.lc-model-detail-field-form,.lc-model-detail-form {
  .c7n-pro-field-output .c7n-pro-output-wrapper.c7n-pro-field.c7n-pro-output-disabled.c7n-pro-output {
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .lc-url-wrapper {
    display: flex;
  }

  .c7n-rate-star {
    .icon-star {
      font-size: inherit;
    }
    .yqcloud-icon-park-wrapper {
      font-size: inherit;
    }
  }

  .c7n-slider-with-marks {
    width: calc(100% - 0.2rem);
  }

  .yq-rich-text-disabled {
    margin-top: -0.05rem;
  }
}

.lc-page-loader-wysiwyg {
  color: @primary-color;
  cursor: pointer;

  &-modal {
    .quill {
      height: 1.7rem;
    }
  }
}

.rich-text-full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: .7rem;
  z-index: 1000;

  .quill {
    height: calc(100vh - 106px);
  }
}

.yq-duration {
  &>span {
    margin-right: 8px;
  }
}

.resolve {
  display: flex;
  justify-content: center;
  padding: 0 16px;
  height: 32px;
  margin-right: 12px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  &-active {
    color: @primary-color;
    border: 1px solid @primary-color;
  }
  &-text {
    height: 30px;
    line-height: 30px;
    margin-left: 12px;
  }
  &-icon {
    padding: 7px 0;
  }

  &:hover {
    cursor: pointer;
  }
}

.c7n-pro-modal-wrapper {
  .lc-ocr-component-wrapper-textArea {
    & + .yqcloud-icon-park-wrapper {
      right: 0 !important;
    }
  }
}

.lc-ocr-component {
  .c7n-pro-input-suffix {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &-wrapper {
    display: flex;
    position: relative;
    &-textArea {
      width: 100%;
      & + .yqcloud-icon-park-wrapper {
        padding: 8px;
        height: 26px;
        cursor: pointer;
        position: absolute;
        right: 0;
        z-index: 2;
      }
    }
  }
}

.lc-tree-select-renderer {
  position: relative;
  z-index: 2;
  font-size: 0.12rem;
  display: inline-flex;
  align-items: center;
  height: 0.22rem;
  // margin-top: 0.04rem;
  padding: 0 0.08rem;
  line-height: 0.22rem;
  vertical-align: middle;
  border-radius: 0.04rem;
  margin-bottom: 0.04rem;
  border: 0.01rem solid rgba(0, 0, 0, 0.02);
}

.c7n-pro-field-wrapper .lc-lov-link-btn {
  display: flex;
}

.lc-lov {
  &-wrapper {
    display: inline-block;
    width: 100%;
  }
  &-link-btn {
    display: flex;
    & .c7n-pro-select-wrapper {
      width: 100%;
    }
    & .c7n-pro-btn {
      padding: 0 8px;
      border: 0.01rem solid #e0e0e0;
    }
  }
  &-table {
    // 覆盖组件默认计算出来的值，防止撑不满
    width: 100% !important;
  }
}

.lc-url {
  &-wrapper {
    display: inline-flex;
    width: 100%;
    & .c7n-pro-input-wrapper {
      width: 100%;
    }
    & .c7n-pro-btn {
      padding: 0 8px;
      border: 0.01rem solid #e0e0e0;
    }
  }
}

.participants-card-tooltip {
  background-color: #fff;
  z-index: 100;
  .c7n-pro-tooltip-popup-inner {
    background: #fff !important;
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 0;
  }
  .c7n-pro-tooltip-popup-arrow {
    display: none;
    &-dark {
      display: none;
    }
  }
  &-main {
    width: 300px;
    .tooltip-top {
      padding: 16px 12px;
      display: flex;
      align-items: center;
      background-size: 100% 100%;
      .participants-card-avatar {
        margin-right: 12px;
        &-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #2196f3;
        }
      }
      .participants-card-name {
        font-size: 14px;
        font-weight: 500;
        color: #2b2d38;
        line-height: 22px;
        text-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
      }
    }
    .tooltip-content {
      display: table;
      padding: 12px 16px;
      &-row {
        display: table-row;
      }
      &-cell {
        display: table-cell;
        text-align: right;
        padding: 5px 0;
        padding-right: 16px;
        &-title {
          font-size: 14px;
          font-weight: 400;
          color: #6b7285;
          line-height: 32px;
          text-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
          white-space: nowrap;
        }
        &-value {
          font-size: 14px;
          font-weight: 400;
          color: #2b2d38;
          line-height: 22px;
          text-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
          text-align: left;
          word-break: break-word;
        }
      }
    }
  }
}

.lc-page-loader-upload {
  display: inline-block;
  width: 100%;
  &-wrapper {
    display: inline-block;
    position: relative;
    &:hover {
      .lc-page-loader-upload-close {
        visibility: visible;
      }
    }
  }
  &-close {
    visibility: hidden;
    position: absolute;
    top: -0.05rem;
    right: -0.05rem;
    cursor: pointer;
  }
  &-img {
    width: 0.72rem;
    height: 0.72rem;
    border: 0.01rem solid rgba(0, 0, 0, 0.15);
    border-radius: 0.04rem;
  }
  &-btn {
    margin-bottom: .04rem;
    color: @primary-color;
    font-size: .14rem;
    vertical-align: bottom;
    cursor: pointer;
    margin-top: 0.07rem;
    display: inline-block;
    line-height: 18px;
    &-ban {
      color: #8c8c8c;
      cursor: not-allowed;
    }
  }
  .c7n-upload-select-text {
    width: 100%;
  }
  .new-file-wrapper {
    position: relative;
    max-width: 5.6rem;
    &:hover .lc-page-loader-upload-file-hover {
      background: #f2f3f5;
      border: 1px solid #f2f3f5;
    }
  }
  &-file {
    display: flex;
    justify-content: space-between;
    border-radius: 0.04rem;
    padding: 0 .02rem;
    width: 100%;
    height: 28px;
    line-height: 0.32rem;
    margin-bottom: 0.05rem;
    border: 1px solid transparent;
    position: relative;
    top: 3px;
    .c7n-progress-circle-path {
      stroke: @primary-color;
    }
    &:focus, &:active {
      background: @minor-color;
      border: 1px solid @primary-color;
    }
    &-default {
      width: 235px;
    }

    &-default-info {
      height: .28rem;
    }

    &-info {
      height: .28rem;
      width: 100%;
    }

    &-default-name {
      display: inline-block;
      max-width: .8rem;
      height: .28rem;
      word-wrap: break-word;
      line-height: .28rem;
      font-size: .12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &-size {
        display: inline-block;
        max-width: .8rem;
        height: .28rem;
        word-wrap: break-word;
        margin-left: .12rem;
        margin-right: .12rem;
        line-height: .28rem;
        font-size: .12rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // color: #8c8c8c;
        color: #12274d;
        opacity: 0.54;
      }
    }

    &-name {
      display: inline-block;
      // width: calc(100% - 1.53rem);
      max-width: calc(100% - 1.53rem);
      height: .28rem;
      word-wrap: break-word;
      line-height: .28rem;
      font-size: .12rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &-size {
        display: inline-block;
        max-width: 2.28rem;
        height: .28rem;
        word-wrap: break-word;
        margin-left: .08rem;
        margin-right: .12rem;
        line-height: .28rem;
        font-size: .12rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // color: #8c8c8c;
        color: #12274d;
        opacity: 0.54;
      }
    }

    &>span {
      display: flex;
      align-items: center;
      &>span {
        display: flex;
        align-items: center;
        top: -0.02rem;
      }
    }
    &:hover {
      background: #f2f3f5;
      border: 1px solid #f2f3f5;

      .lc-page-loader-upload-icon {
        display: inline-flex !important;
      }

      .lc-page-loader-upload-link {
        color: @primary-color;
      }

      .lc-page-loader-upload-file-name {
        color: @primary-color;
      }

      .cancel-upload {
        opacity: 1;
      }
    }
    .cancel-upload {
      opacity: 0;
      display: flex;
      position: absolute;
      right: -1px;

      &:hover {
        cursor: pointer;
      }
    }
  }
  &-action {
    position: absolute;
    right: 0;
    top: calc(50% - 7px);
    z-index: 10;
    padding: 0 8px;
    background: #f2f3f5;
  }
  &-link {
    margin-right: 0.08rem;
  }
  &-icon.yqcloud-icon-park-wrapper {
    display: none !important;
    margin-right: 0.08rem;
    cursor: pointer;
  }
  &-icon:last-child {
    margin-right: 0;
  }
  &-icon {
    color: #12274d;
    opacity: 0.65;
  }
}

.upload-placeholder-disabled {
  cursor: not-allowed;
  color: #8c8c8c;
}

.lc-mobile-wrapper {
  display: flex !important;

  .lc-mobile-field {
    flex: 1;
  }

  .lc-mobile-field-btn {
    width: auto;
    height: 32px;
    margin-left: 4px;
    cursor: pointer;
  }
}

.lc-form-page-loader.view-type-UPDATE .url-btn.c7n-pro-field-label-output {
  padding: .06rem 0 .04rem .16rem;
}
