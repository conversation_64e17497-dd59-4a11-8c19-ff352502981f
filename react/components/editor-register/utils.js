/* eslint-disable no-useless-escape */

export function getFieldDisabled(record, formDs, code) {
  if (!code) return false;
  const field = formDs?.props?.fields?.find(e => e.name === code);
  return field ? field?.dynamicProps?.disabled({ formDs, record, name: code }) : false;
}

export function getFileType(fileKey) {
  if (!fileKey) return 'failed';
  const index = fileKey.lastIndexOf('.');
  return fileKey.slice(index + 1);
}

export function getFileIcon(type) {
  const others = { color: '#6B7AD2', iconType: 'icon-qita' };
  const iconList = [
    { type: 'doc', color: '#03A9F4', iconType: 'icon-word' },
    { type: 'docx', color: '#03A9F4', iconType: 'icon-word' },
    { type: 'xlsx', color: '#4CAF50', iconType: 'icon-xlsx' },
    { type: 'ppt', color: '#EE7C2E', iconType: 'icon-ppT' },
    { type: 'pptx', color: '#EE7C2E', iconType: 'icon-ppT' },
    { type: 'jpg', color: '#F6B84E', iconType: 'icon-tupian' },
    { type: 'jpeg', color: '#F6B84E', iconType: 'icon-tupian' },
    { type: 'txt', color: '#576C7E', iconType: 'FileTxt' },
    { type: 'zip', color: '#8B7C8F', iconType: 'zip' },
    { type: 'gif', color: '#9376BD', iconType: 'file-gif' },
    { type: 'png', color: '#F6B84E', iconType: 'icon-tupian' },
    { type: 'pdf', color: '#F05743', iconType: 'icon-PDF' },
    { type: 'failed', color: '#8C8C8C', iconType: '' },
  ];
  return iconList.find(item => item.type === type) || others;
}

export function getFileName(url) {
  let decodedUrl = '';
  try {
    decodedUrl = decodeURIComponent(url);
  } catch {
    decodedUrl = url;
  }
  const urlSplit = decodedUrl.split('@');
  if (decodedUrl) {
    const fileName = urlSplit[urlSplit.length - 1];
    return fileName?.split('&access_token')[0];
  } else {
    return url;
  }
}

export const RICH_TEXT_NULL_LIST = [
  '[{\"insert\":\"\\n\"}]',
  '[{\"insert\":\"\n\"}]',
  '[{\"insert\":\"\"}]',
  '[]',
  'null',
  null,
];
