import React, { useEffect } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';

export default inject('AppState')(observer((props) => {
  const {
    url,
    defaultValue,
    onChange,
  } = props;

  function handleChange(e) {
    if (onChange) {
      onChange(e.data);
    }
  }

  useEffect(() => {
    window.addEventListener('message', handleChange);

    return () => {
      window.removeEventListener('message', handleChange);
    };
  }, []);

  return (
    <iframe
      title="location"
      id="mapPage"
      allow="geolocation"
      width="100%"
      height="100%"
      frameBorder="0"
      scrolling="no"
      src={url}
    />
  );
}));
