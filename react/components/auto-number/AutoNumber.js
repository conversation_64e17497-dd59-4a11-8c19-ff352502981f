import React, { useReducer, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { v4 as uuidv4 } from 'uuid';
import moment from 'moment';
import classNames from 'classnames';
import { Icon, YqCodeMirror } from '@zknow/components';
import { Alert } from 'choerodon-ui';
import { TextField, Form, Dropdown, Menu, Select } from 'choerodon-ui/pro';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import Stores from './stores';

import './index.less';

const { Item } = Menu;
const { Option } = Select;

const AutoNumber = () => {
  const { dataSet, intl, previewFlag, disabled, extRecord, allowScript } = useContext(Stores);
  const [ignored, forceUpdate] = useReducer(x => x + 1, 0);
  const scriptFlag = dataSet.records.find(r => r.get('type') === 'script');

  function onDragEnd(target) {
    const { source, destination } = target;
    if (!destination) return false;
    const { index: tIndex } = destination;
    const { index: sIndex } = source;
    const moveRecord = dataSet.get(sIndex);
    const from = sIndex < tIndex ? tIndex + 1 : tIndex;
    dataSet.splice(from, 0, moveRecord);
    if (tIndex !== sIndex) {
      moveRecord.status = 'update';
    }
    // 更新字段，前端自用，用来触发 Dataset 的 update 事件
    moveRecord.set('__refresh', Date.now());
    // dataSet.current = source;
    forceUpdate();
  }

  function handleCreate(e) {
    const record = dataSet.create({ code: uuidv4(), type: e.key, format: e.key === 'date' ? 'yyyyMMdd' : undefined });
    record.set('__refresh', Date.now()); // 创建的时候触发更新事件，为外部record赋值
    forceUpdate();
  }

  function getValueName(type) {
    const type2Name = {
      digits: 'length',
      date: 'format',
      character: 'value',
    };
    return type2Name[type];
  }

  function getAddonAfter(r) {
    if (r.get('type') === 'digits') {
      return (
        <React.Fragment>
          <TextField record={r} name="start" disabled={disabled} addonBefore={intl.formatMessage({ id: 'lcr.components.desc.initial.number', defaultMessage: '起始流水' })} />
          <Select record={r} name="reset" disabled={disabled} addonBefore={intl.formatMessage({ id: 'lcr.components.desc.reset.frequency', defaultMessage: '重置频率' })}>
            <Option value="yyyy">{intl.formatMessage({ id: 'lcr.components.desc.reset.by.year', defaultMessage: '按年重置' })}</Option>
            <Option value="yyyyMM">{intl.formatMessage({ id: 'lcr.components.desc.reset.by.month', defaultMessage: '按月重置' })}</Option>
            <Option value="yyyyMMdd">{intl.formatMessage({ id: 'lcr.components.desc.reset.by.day', defaultMessage: '按天重置' })}</Option>
            <Option value="NONE">{intl.formatMessage({ id: 'lcr.components.desc.never.reset', defaultMessage: '永不重置' })}</Option>
          </Select>
        </React.Fragment>
      );
    }
  }

  function handleDelete(r) {
    // FIXME: 使用 delete 方法会导致整个弹框无法消除
    //  另外删除提醒也需要暂时去掉，conform的弹框也会影响数据
    const data = dataSet.toData();
    dataSet.loadData(data.filter(item => item.code !== r.get('code')));
    const refreshRecord = dataSet.get(0);
    refreshRecord?.set?.('__refresh', Date.now());
    forceUpdate();
  }

  function getLabelName(type) {
    const labelName = {
      digits: intl.formatMessage({ id: 'lcr.components.desc.serial.number', defaultMessage: '流水号：' }),
      date: intl.formatMessage({ id: 'lcr.components.desc.data', defaultMessage: '日期：' }),
      character: intl.formatMessage({ id: 'lcr.components.desc.fixed.character', defaultMessage: '固定字符：' }),
      script: intl.formatMessage({ id: 'lcr.components.desc.script', defaultMessage: '脚本：' }),
    };
    return labelName[type];
  }

  function getBeforeLabel(type) {
    const labelName = {
      digits: intl.formatMessage({ id: 'lcr.components.number.digits', defaultMessage: '位数' }),
      date: intl.formatMessage({ id: 'lcr.components.number.data', defaultMessage: '日期格式' }),
      character: intl.formatMessage({ id: 'lcr.components.number.character', defaultMessage: '固定值' }),
      script: intl.formatMessage({ id: 'lcr.components.number.script', defaultMessage: '脚本' }),
    };
    return labelName[type];
  }

  function renderItems() {
    return dataSet.records.filter(r => r.status !== 'delete').map(((r, index) => {
      const code = r.get('code');
      const Cmp = r.get('type') === 'date' ? Select : TextField;
      return (
        <Form key={code}>
          <Draggable key={code} draggableId={code} index={index}>
            {(provided) => (
              <div ref={provided.innerRef} {...provided.draggableProps}>
                <div
                  className={classNames('lc-auto-number-item', {
                    disabled,
                    editable: !disabled,
                  })}
                >
                  {disabled
                    ? null
                    : (
                      <span {...provided.dragHandleProps}>
                        <Icon type="Drag" className="lc-auto-number-drag" />
                      </span>
                    )}
                  <div className="lc-auto-number-item-label">{getLabelName(r.get('type'))}</div>
                  {r.get('type') === 'script'
                    ? (
                      <div className="lc-auto-number-script">
                        <YqCodeMirror
                          name="value"
                          mode="text"
                          record={r}
                          businessObjectId={extRecord.get('businessObjectId')?.id}
                          disabled={disabled}
                        />
                        <TextField
                          required
                          disabled={disabled}
                          addonBefore={intl.formatMessage({ id: 'lcr.components.desc.script.error', defaultMessage: '异常返回值' })}
                          record={r}
                          name="onException"
                          className="lc-auto-number-script-error"
                        />
                      </div>
                    ) : (
                      <Cmp
                        disabled={disabled}
                        addonAfter={getAddonAfter(r)}
                        addonBefore={getBeforeLabel(r.get('type'))}
                        colSpan={11}
                        record={r}
                        name={getValueName(r.get('type'))}
                      />
                    )}
                  {!disabled && (r.get('type') !== 'digits'
                    ? <Icon className="lc-auto-number-delete" onClick={() => handleDelete(r)} type="Delete" />
                    : <span className="lc-auto-number-delete-space" />
                  )}
                </div>
              </div>
            )}
          </Draggable>
        </Form>
      );
    }));
  }

  function getPreview() {
    return dataSet?.map(r => {
      switch (r.get('type')) {
        case 'script':
          return 'f(x)';
        case 'date':
          return moment().format(r?.get('format')?.toUpperCase());
        case 'character':
          return r.get('value');
        case 'digits': {
          // 预览要根据真实规则渲染
          const start = r.get('start') || 0;
          const len = r.get('length');
          if (len < 1) {
            return '0';
          } else if (!start || Number(start) === 1) {
            return `${'0'.repeat(len - 1)}1`;
          } else if (start.length === len) {
            return start;
          } else if (start.length > len) {
            // 从尾部截
            return start.slice(0, len);
          } else {
            return `${'0'.repeat(len - start.length)}${start}`;
          }
        }
        default: return 'x';
      }
    });
  }

  function renderCreationMenus() {
    return (
      <Menu onClick={(e) => handleCreate(e)}>
        <Item key="character">
          {intl.formatMessage({ id: 'lcr.components.desc.add.fixed.character', defaultMessage: '添加固定字符' })}
        </Item>
        <Item key="date">
          {intl.formatMessage({ id: 'lcr.components.desc.add.submit.date', defaultMessage: '添加提交日期' })}
        </Item>
        {allowScript
          ? (
            <Item key="script">
              {intl.formatMessage({ id: 'lcr.components.desc.add.script', defaultMessage: '添加脚本' })}
            </Item>
          ) : null}
      </Menu>
    );
  }

  if (previewFlag) {
    return getPreview();
  }

  return (
    <div className="lc-auto-number">
      {scriptFlag
        ? (
          <Alert
            style={{ margin: '4px' }}
            message={intl.formatMessage({ id: 'lcr.components.desc.lc.number.tips.script', defaultMessage: '您设置的规则内含脚本，请确认脚本是否有误；如果脚本出现异常，系统将显示配置的异常返回值' })}
            type="info"
            showIcon
          />
        ) : null}
      <DragDropContext
        onDragEnd={onDragEnd}
      >
        <Droppable droppableId="lc-draggable">
          {(provided) => (
            <div ref={provided.innerRef}>
              {renderItems()}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
      <div className="lc-auto-number-btn">
        <div className={disabled ? 'lc-auto-number-preview' : ''}>
          {intl.formatMessage({ id: 'lcr.components.desc.preview', defaultMessage: '预览：' })}
          {getPreview()}
        </div>
        {disabled
          ? null
          : (
            <Dropdown overlay={renderCreationMenus()}>
              <div className="lc-auto-number-btn-add">
                <Icon className="lc-auto-number-btn-icon" type="AddOne" />
                {intl.formatMessage({ id: 'lcr.components.desc.add.rule', defaultMessage: '添加规则' })}
              </div>
            </Dropdown>
          )}
      </div>
    </div>
  );
};

export default observer(AutoNumber);
