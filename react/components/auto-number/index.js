import React from 'react';
import { formatterCollections } from '@zknow/utils';
import { inject } from 'mobx-react';
import { StoreProvider } from './stores';
import AutoNumber from './AutoNumber';

export default inject('AppState')(formatterCollections({
  code: 'lcr.components',
})((props) => {
  return (
    <StoreProvider {...props}>
      <AutoNumber />
    </StoreProvider>
  );
}));

/* externalize: AutoNumber */
