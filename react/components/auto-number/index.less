.lc-auto-number {
  color: #12274d;

  .c7n-pro-field-wrapper {
    padding-left: 0 !important;
  }

  &-item {
    display: inline-flex;
    align-items: center;
    width: 100%;

    .lc-js-expression {
      width: calc(100% - 0.46rem);

      .yqcloud-icon-yq-wrapper {
        background: #fff;
      }
    }

    .c7n-pro-input-group{
     .c7n-pro-input-group-input:nth-child(2){
         width: 1rem;
      }
    }
    &.editable {
      padding: 0.08rem;
      background: #fafafa;
      border-radius: 0.04rem;
      border: 0.01rem solid #d9d9d9;
    }

    .c7n-pro-input-group-wrapper,
    .c7n-pro-select-group-wrapper {
      width: ~"calc(100% - 0.46rem)";
    }

    .c7n-pro-input-group-before,
    .c7n-pro-select-group-before {
      background: #fff;
      border-radius: 0.04rem;
      color: #595959;
    }

    .c7n-pro-input-group-input {
      .c7n-pro-input {
        border-radius: 0 0.04rem 0.04rem 0;
      }
    }

    .c7n-pro-input-group-after {
      padding-right: 0;

      .c7n-pro-select-group-wrapper {
        padding-left: 0.05rem;
      }
    }

    &.disabled {
      width: 100%;

      .c7n-pro-select-group-wrapper {
        width: 100%;
      }
    }

    &-label {
      width: 0.9rem;
      padding-left: 0.08rem;
    }
  }

  .c7n-pro-input-group-after {
    border: none;
    background-color: transparent;
  }

  &-drag {
    vertical-align: sub;
    margin-right: 0.05rem;
  }

  &-delete {
    color: #f83640;
    margin-left: 10px;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;
    text-align: center;
    vertical-align: middle;
    padding: 7px;
    background: white;
    cursor: pointer;

    &-space {
      width: 40px;
      margin-left: 0.08rem;
    }
    &-icon {
      color: #FD7D23;
      display: flex;
      align-items: center;
      &-text {
        font-weight: 500;
        color: #12274D;
        font-size: 0.16rem;
        margin-left: 0.08rem;
      }
    }
  }

  &-btn {
    &-icon {
      vertical-align: text-bottom;
      cursor: pointer;
      margin-right: 0.1rem;
      color: @primary-color;
    }

    &-add {
      color: @primary-color;
      margin-top: 0.1rem;
      margin-left: 0.08rem;
      cursor: pointer;
      width: 1rem;
    }
  }

  &-preview {
    margin-top: 0.1rem;
    padding-left: 0.1rem;
  }

  &-bottom {
    display: flex;
  }

  &-script {
    display: flex;
    width: calc(100% - 0.46rem);

    .lc-js-expression {
      width: 60%;
      margin-right: 10px;

      >.c7n-pro-input-wrapper {
        margin-right: 5px;
      }
    }

    >.c7n-pro-input-group-wrapper {
      width: 40%;
    }
  }
}
