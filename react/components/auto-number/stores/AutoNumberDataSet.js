import { DataSet } from 'choerodon-ui/pro';
import { v4 as uuidv4 } from 'uuid';

export default ({ initData, record: extRecord, fieldCode, stringFlag, intl }) => {
  const dateFormatOptionDs = new DataSet({
    data: [
      { meaning: '19701130', value: 'yyyyMMdd' },
      { meaning: '197011', value: 'yyyyMM' },
      { meaning: '1970', value: 'yyyy' },
      { meaning: '70', value: 'yy' },
      { meaning: '1970-11-30', value: 'yyyy-MM-dd' },
      { meaning: '1970-11', value: 'yyyy-MM' },
      { meaning: '1130', value: 'MMdd' },
      { meaning: '11', value: 'MM' },
      { meaning: '30', value: 'dd' },
    ],
  });

  function startValidator(value, name, record) {
    if (value && !/^[0-9]+$/.test(value)) {
      return intl.formatMessage({ id: 'lcr.components.model.auto.number.validator', defaultMessage: '起始流水只能包含数字' });
    }
  }

  return {
    autoQuery: false,
    selection: false,
    autoCreate: !initData,
    paging: false,
    primaryKey: 'code',
    data: initData || [],
    fields: [
      { name: 'type', type: 'string', defaultValue: 'digits' },
      { name: 'value', type: 'string' },
      { name: 'code', defaultValue: uuidv4(), type: 'string' },
      { name: 'length', type: 'number', defaultValue: 8 },
      {
        name: 'start',
        type: 'string',
        validator: startValidator,
        dynamicProps: {
          maxLength: ({ record }) => {
            return record.get('length') || 8;
          },
          defaultValue: ({ record }) => {
            return `${'0'.repeat((record.get('length') || 8) - 1)}1`;
          },
        },
      },
      { name: 'format', type: 'string', options: dateFormatOptionDs },
      { name: 'reset', defaultValue: 'NONE' },
      { name: '__refresh', ignore: 'always' },
      {
        name: 'onException',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'script',
        },
      },
    ],
    events: {
      update: ({ dataSet, name, record }) => {
        if (extRecord && fieldCode) {
          extRecord.set(fieldCode, stringFlag ? JSON.stringify(dataSet.toData()) : dataSet.toData());
        }
        if (name === 'length') {
          record.set('start', `${'0'.repeat((record.get('length') || 8) - 1)}1`);
        }
      },
      remove: ({ dataSet }) => {
        if (extRecord && fieldCode) {
          extRecord.set(fieldCode, stringFlag ? JSON.stringify(dataSet.toData()) : dataSet.toData());
        }
      },
      // indexChange: ({ dataSet }) => {
      //   if (extRecord && fieldCode) {
      //     extRecord.set(fieldCode, stringFlag ? JSON.stringify(dataSet.toData()) : dataSet.toData());
      //   }
      // },
    },
  };
};
