import React, { createContext, useMemo, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import AutoNumberDataSet from './AutoNumberDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: 'lcr.components' })(injectIntl(
  (props) => {
    const {
      children,
      defaultData,
      record,
      fieldCode,
      previewFlag = false,
      stringFlag = true,
      intl,
    } = props;

    let initData = false;
    if (defaultData) {
      try {
        const jsonData = JSON.parse(defaultData);
        if (Array.isArray(jsonData) && jsonData?.length > 0) {
          initData = jsonData;
        }
      } catch (e) {
        initData = false;
      }
    }

    const dataSet = useMemo(
      () => new DataSet(AutoNumberDataSet({ initData, record, fieldCode, stringFlag, intl })),
      [defaultData],
    );

    const value = {
      ...props,
      dataSet,
      previewFlag,
      extRecord: record,
      fieldCode,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
)));
