import React from 'react';
import { Tooltip } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';

import styles from './index.less';

const optionRender = ({ text, record }) => {
  return (
    <span className={styles.option_wrap}>
      {text}
      {record.get('help') ? (
        <Tooltip title={record.get('help')}>
          <Icon type="icon-help" className={styles.help_icon} />
        </Tooltip>
      ) : null}
    </span>
  );
};

export default optionRender;
