import React, { useRef, useState } from 'react';
import { TextField, CheckBox } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import noop from 'lodash/noop';
import { action } from 'mobx';
import Sortable from 'react-sortablejs';

const ChooseArea = (props) => {
  const {
    title,
    dataSet,
    intl,
    syncSortFields,
    canMoveFlag,
  } = props;
  // 使用ref来保存滚动位置
  const scrollRef = useRef(null);
  // 使用state来强制更新组件，但不影响滚动位置
  const [updateKey, setUpdateKey] = useState(0);
  const handleSelectAll = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', true);
    });
  };
  const handleSelectReverse = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', !record.get('isChoosen'));
    });
  };
  const handleClear = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', false);
    });
  };
  const onSortEnd = ({ oldIndex, newIndex }) => {
    // 不处理搜索状态下的排序
    if (dataSet.getState('searchResult')) {
      return;
    }

    // 保存当前滚动位置
    const scrollContainer = scrollRef.current;
    const scrollPosition = scrollContainer ? scrollContainer.scrollTop : 0;

    const [removed] = dataSet.splice(oldIndex, 1);
    dataSet.splice(newIndex, 0, removed);
    removed.status = 'update';

    // 同步排序字段到父组件
    syncSortFields();

    // 强制更新组件，但保持滚动位置
    setUpdateKey(prev => prev + 1);

    // 在下一个渲染周期恢复滚动位置
    setTimeout(() => {
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollPosition;
      }
    }, 0);
  };
  const handleSearch = (newValue) => {
    const filteredData = newValue === '' || !newValue ? null : dataSet.filter(item => item.get('label').toLowerCase().includes(newValue));
    dataSet.setState('searchResult', filteredData);
  };
  return (
    <div
      className="lc-choose-area"
    >
      <div className="title">
        <div className="title-info">{title}</div>
        <div className="title-count">{dataSet?.length || 0}</div>
      </div>
      <div className="button-area">
        <div className="button" onClick={handleSelectAll}>{intl.formatMessage({ id: 'zknow.common.button.selectAll', defaultMessage: '全选' })}</div>
        <div className="button" onClick={handleSelectReverse}>{intl.formatMessage({ id: 'lcr.components.desc.select.reverse', defaultMessage: '反选' })}</div>
        <div className="button" onClick={handleClear}>{intl.formatMessage({ id: 'zknow.common.button.clean', defaultMessage: '清除' })}</div>
      </div>
      <div className="fields">
        <TextField placeholder={intl.formatMessage({ id: 'lcr.components.desc.export.memory.transfer.search.tip', defaultMessage: '请输入名称搜索' })} className="search-text-field" onChange={handleSearch} valueChangeAction="input" />
        <div ref={scrollRef} className="fields-list-container" style={{ overflow: 'auto' }}>
          <Sortable
            key={updateKey}
            className="fields-list"
            onChange={noop}
            options={{
              animation: 150,
              fallbackOnBody: true,
              swapThreshold: 0.65,
              handle: '.field-item.move',
              onEnd: action(onSortEnd),
              // 添加以下配置，保持滚动位置
              scroll: true,
              scrollSensitivity: 30,
              scrollSpeed: 10,
            }}
          >
            {(dataSet.getState('searchResult') || dataSet.records).map(item => (
              <React.Fragment key={item?.get('objectFieldPath')}>
                <CheckBox
                  dataId={item?.get('objectFieldPath')}
                  name="fields"
                  checked={item.get('isChoosen')}
                  className={`field-item ${canMoveFlag ? 'move' : ''}`}
                  onChange={(v) => {
                    item.set('isChoosen', v);
                  }}
                >
                  {item?.get('label')}
                  {item?.get('calculatedFlag') && <span className="export-fx">fx</span>}
                </CheckBox>
              </React.Fragment>
            ))}
          </Sortable>
        </div>
      </div>
    </div>
  );
};

export default observer(ChooseArea);
