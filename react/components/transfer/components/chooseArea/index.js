import React, { useState } from 'react';
import { TextField, CheckBox } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import noop from 'lodash/noop';
import { action } from 'mobx';
import Sortable from 'react-sortablejs';

const ChooseArea = (props) => {
  const {
    title,
    dataSet,
    intl,
    syncSortFields,
    canMoveFlag,
  } = props;
  const handleSelectAll = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', true);
    });
  };
  const handleSelectReverse = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', !record.get('isChoosen'));
    });
  };
  const handleClear = () => {
    dataSet.records.forEach((record) => {
      record.set('isChoosen', false);
    });
  };
  function arrayMove(ds, oldIndex, newIndex) {
    const array = ds.toData();
    const [removed] = array.splice(oldIndex, 1);
    array.splice(newIndex, 0, removed);
    removed.status = 'update';
    return array;
  }
  const onSortEnd = ({ oldIndex, newIndex }) => {
    // 不处理搜索状态下的排序
    if (dataSet.getState('searchResult')) {
      return;
    }
    const [removed] = dataSet.splice(oldIndex, 1);
    dataSet.splice(newIndex, 0, removed);
    removed.status = 'update';

    // const newOrderedList = arrayMove(dataSet, oldIndex, newIndex);
    // dataSet.loadData(newOrderedList);
    syncSortFields();
  };
  const handleSearch = (newValue, oldValue) => {
    const filteredData = newValue === '' || !newValue ? null : dataSet.filter(item => item.get('label').toLowerCase().includes(newValue));
    dataSet.setState('searchResult', filteredData);
  };
  return (
    <div
      className="lc-choose-area"
    >
      <div className="title">
        <div className="title-info">{title}</div>
        <div className="title-count">{dataSet?.length || 0}</div>
      </div>
      <div className="button-area">
        <div className="button" onClick={handleSelectAll}>{intl.formatMessage({ id: 'zknow.common.button.selectAll', defaultMessage: '全选' })}</div>
        <div className="button" onClick={handleSelectReverse}>{intl.formatMessage({ id: 'lcr.components.desc.select.reverse', defaultMessage: '反选' })}</div>
        <div className="button" onClick={handleClear}>{intl.formatMessage({ id: 'zknow.common.button.clean', defaultMessage: '清除' })}</div>
      </div>
      <div className="fields">
        <TextField placeholder={intl.formatMessage({ id: 'lcr.components.desc.export.memory.transfer.search.tip', defaultMessage: '请输入名称搜索' })} className="search-text-field" onChange={handleSearch} valueChangeAction="input" />
        <Sortable
          // key={Date.now()}
          className="fields-list"
          onChange={noop}
          options={{
            animation: 150,
            fallbackOnBody: true,
            swapThreshold: 0.65,
            handle: '.field-item.move',
            onEnd: action(onSortEnd),
          }}
        >
          {(dataSet.getState('searchResult') || dataSet.records).map(item => (
            <>
              <CheckBox
                dataId={item?.get('objectFieldPath')}
                key={item?.get('objectFieldPath')}
                name="fields"
                checked={item.get('isChoosen')}
                className={`field-item ${canMoveFlag ? 'move' : ''}`}
                onChange={(v) => {
                  item.set('isChoosen', v);
                }}
              >
                {item?.get('label')}
                {item?.get('calculatedFlag') && <span className="export-fx">fx</span>}
              </CheckBox>
            </>
          ))}
        </Sortable>
      </div>
    </div>
  );
};

export default observer(ChooseArea);
