.lc-transfer-wrapper {
    display: flex;

    .transfer-buttons {
        display: flex;
        flex-direction: column;
        width: 48px;
        padding: 8px;
        justify-content: center;

        .c7n-pro-btn-wrapper {
            margin: 0 0 12px 0 !important;
        }
    }

    .lc-choose-area {
        width: 3rem;
        height: 3rem;
        border-radius: 4px;
        border: 1px solid rgba(203, 210, 220, 0.5);

        .title {
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(203, 210, 220, 0.5);
            padding: 8px 12px;
            justify-content: space-between;

            &-info {
                font-weight: 500;
            }

            &-count {
                font-weight: 400;
                color: rgb(203, 210, 220);
            }
        }

        .button-area {
            display: flex;
            align-items: center;
            border-bottom: 1px solid rgba(203, 210, 220, 0.5);

            .button {
                padding: 4px 12px;
                color: #2979ff;
                cursor: pointer;
            }
        }

        .fields {
            display: flex;
            flex-direction: column;
            padding: 8px 0;

            .search-text-field {
                padding: 0 .12rem;
            }

            .fields-list {
                display: flex;
                flex-direction: column;
                height: 1.9rem;
                overflow: overlay;

                .field-item {
                    padding: .06rem .12rem;

                    &:not(.move) {
                        cursor: pointer;
                    }

                    &:hover {
                        background-color: #F2F3F5;
                    }

                    .export-fx {
                        font-size: 0.12rem;
                        height: 0.2rem;
                        width: 0.2rem;
                        display: -ms-inline-flexbox;
                        display: inline-flex;
                        -ms-flex-pack: center;
                        justify-content: center;
                        border-radius: 0.02rem;
                        background-color: #F2F3F5;
                        border: 0.01rem solid rgba(203, 210, 220, 0.25);
                        margin-left: 0.12rem;
                    }
                }
            }
        }
    }
}