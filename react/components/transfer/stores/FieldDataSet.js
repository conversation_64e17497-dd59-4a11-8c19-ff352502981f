const FieldDataSet = ({ list }) => ({
  data: list,
  paging: false,
  fields: [
    {
      name: 'id',
      type: 'string',
    },
    {
      name: 'label',
      type: 'string',
    },
    {
      name: 'objectFieldId',
      type: 'string',
    },
    {
      name: 'objectFieldPath',
      type:'string',
    },
    {
      name: 'calculatedFlag',
      type:'boolean',
      ignore: 'always',
    },
    {
      name: 'isChoosen',
      type: 'boolean',
      defaultValue: false,
      ignore: 'always',
    }
  ],
});
export default FieldDataSet;
