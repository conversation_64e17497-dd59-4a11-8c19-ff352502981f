import React, { createContext, useCallback, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { formatterCollections } from '@zknow/utils';
import { observer } from 'mobx-react-lite';
import FieldDataSet from './FieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')(formatterCollections({ code: ['zknow.common', 'lcr.components'] })(injectIntl(observer((props) => {
  const {
    intl,
    children,
    AppState: { currentMenuType: { organizationId: tenantId, type } },
    exportDataSet,
    list,
    memoryList,
    sortField,
  } = props;
  function categorizeArrays() {
    const leftList = [];
    const rightList = [];
    const filterList = (memoryList?.length ? memoryList : sortField) || [];

    list.forEach(item => {
      if (filterList.includes(item.objectFieldPath)) {
        rightList.push({
          ...item,
          isChoosen: false,
        });
      } else {
        leftList.push({
          ...item,
          isChoosen: false,
        });
      }
    });

    // 创建一个对象数组，包含原始索引和对应的 rightList 中的值
    const indexedRightList = rightList.map((value, index) => ({ value, index }));

    // 根据 memoryList 的顺序对对象数组进行排序
    indexedRightList.sort((a, b) => {
      const indexA = filterList.indexOf(a.value.objectFieldPath);
      const indexB = filterList.indexOf(b.value.objectFieldPath);
      return indexA - indexB;
    });

    // 提取已排序的 rightList
    const sortedRightList = indexedRightList.map(item => item.value);

    return { leftList, sortedRightList };
  }
  const { leftList, sortedRightList } = useCallback;
  const leftFieldDataSet = useMemo(() => new DataSet(FieldDataSet({ tenantId, type, list: leftList })), [leftList]);
  const rightFieldDataSet = useMemo(() => new DataSet(FieldDataSet({ tenantId, type, list: sortedRightList })), [sortedRightList]);
  const value = {
    ...props,
    intl,
    tenantId,
    leftFieldDataSet,
    rightFieldDataSet,
    exportDataSet,
    type,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
}))));
