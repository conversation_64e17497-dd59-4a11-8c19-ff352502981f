import React, { useContext, useEffect } from 'react';
import { Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import ChooseArea from './components/chooseArea';
import store from './stores';
import './index.less';

const LcTransfer = () => {
  const {
    intl,
    leftFieldDataSet,
    rightFieldDataSet,
    exportDataSet,
  } = useContext(store);
  useEffect(() => {
    syncSortFields();
  }, [rightFieldDataSet?.length]);
  const syncSortFields = () => {
    const calculateFields = (rightFieldDataSet.toData() || []).filter(i => i.calculatedFlag).map(r => r.objectFieldPath);
    exportDataSet?.setState('sortFields', (rightFieldDataSet.toData() || []).map(r => r.objectFieldPath));
    exportDataSet?.current?.setState('calculatedFields', calculateFields);
    if (calculateFields?.length && exportDataSet?.current) {
      exportDataSet.current.set('asyncFlag', true);
    }
  };
  const handleTransfer = (direction) => {
    const targetDataSet = direction === 'toRight' ? rightFieldDataSet : leftFieldDataSet;
    const originDataSet = direction === 'toRight' ? leftFieldDataSet : rightFieldDataSet;
    const transferRecords = originDataSet.records.filter(r => r.get('isChoosen'));
    originDataSet.loadData(originDataSet.toData().filter(r => !r.isChoosen));
    transferRecords.forEach(r => r.set('isChoosen', false));
    targetDataSet.appendData(transferRecords);
  };
  return (
    <div
      className="lc-transfer-wrapper"
    >
      <ChooseArea title={intl.formatMessage({ id: 'lcr.components.desc.export.field.all', defaultMessage: '全部字段' })} intl={intl} dataSet={leftFieldDataSet} syncSortFields={syncSortFields} />
      <div className="transfer-buttons">
        <Button icon="right" onClick={() => handleTransfer('toRight')} />
        <Button icon="left" onClick={() => handleTransfer('toLeft')} />
      </div>
      <ChooseArea title={intl.formatMessage({ id: 'lcr.components.desc.export.field', defaultMessage: '导出字段' })} intl={intl} dataSet={rightFieldDataSet} syncSortFields={syncSortFields} canMoveFlag />
    </div>
  );
};

export default observer(LcTransfer);
