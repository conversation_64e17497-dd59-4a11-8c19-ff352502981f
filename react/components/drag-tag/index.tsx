import React, { useState } from 'react';
import { toJS } from 'mobx';
import { observer } from 'mobx-react-lite';
import { Icon, Button } from '@zknow/components';
import { Modal, Table } from 'choerodon-ui/pro';
import Sortable from 'react-sortablejs';
import { injectIntl } from 'react-intl';

import styles from './DragTag.less';

export interface DragTagProps {
  name: string,
  record: any,
  onRemove: () => {},
  lock: boolean,
  disabled: boolean,
  intl: any,
}

const sortableOption = {
  animation: 150,
  fallbackOnBody: true,
  swapThreshold: 0.65,
  tag: 'ul',
};

const Item = ({ data, onLock, onRemove, disabled }) => {
  return (
    <div className={styles.item}>
      <span className={styles.drag}>
        <Icon type="drag" fontSize={16} />
      </span>
      <span className={styles.label}>{data.name}</span>
      {onLock && <Icon type={data?.lock ? 'lock' : 'unlock'} className={styles.lock} onClick={!disabled && onLock} size={16} />}
      {onRemove && !disabled && <Icon type="CloseSmall" onClick={onRemove} />}
    </div>
  );
};

function getOptions(name, record) {
  return record?.getField(name)?.options
    || record?.dataSet.getField(name)?.options
    || record?.dataSet.props.fields.find(i => i.name === name)?.dynamicProps?.options();
}

const DragTag = observer((props: DragTagProps) => {
  const {
    name,
    record,
    lock = false,
    intl,
    disabled = false,
  } = props;
  const [_, forceRender] = useState<number>();
  const options = getOptions(name, record);

  const handleUpdate = (e) => {
    if (disabled) return;
    const { oldIndex, newIndex } = e;
    const sortList = record.get(name).slice();
    const drag = sortList[oldIndex];
    sortList.splice(oldIndex, 1);
    sortList.splice(newIndex, 0, drag);
    if (lock) {
      const current = sortList[newIndex];
      const next = sortList[newIndex + 1];
      const pre = sortList[newIndex - 1];
      if (next) {
        current.lock = (newIndex < oldIndex ? next : pre).lock;
      } else {
        !pre.lock && (current.lock = false);
      }
    }
    record.set(name, sortList);
    forceRender(Date.now());
  };

  const handleRemove = (index, item) => {
    if (disabled) return;
    const sortList = record.get(name);
    sortList.splice(index, 1);
    forceRender(Date.now());
  };

  const handleLock = (index, item) => {
    if (disabled) return;
    const sortList = record.get(name).slice();
    const oldLock = item.lock;
    sortList.forEach((field, _i) => {
      !oldLock && _i <= index && (field.lock = !oldLock);
      oldLock && _i >= index && (field.lock = !oldLock);
    });
    record.set(name, sortList);
    forceRender(Date.now());
  };

  const handleQuery = () => {
    let originData = options.getState('originData')?.slice();
    const currentData = toJS(record.get(name));
    if (!originData) {
      originData = options.toData();
      options.setState('originData', originData);
    } else {
      options.loadData(originData);
    }
    const { fuzzy_params: searchText } = options.queryParameter;
    if (searchText) {
      const newData = originData.filter(item => item.label?.includes(searchText)
        || item.name?.includes(searchText)
        || item.path?.includes(searchText)
        || item.objectFieldPath?.includes(searchText));
      if (newData.length > 0) {
        options.loadData([...currentData, ...toJS(newData)]);
      }
    } else {
      options.loadData(originData);
    }
  };

  const tableFilter = (item) => {
    return !(record?.get(name) || []).find(i => i.code === item.get('code'));
  };

  const handleAdd = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.import.select.field', defaultMessage: '选择字段' }),
      children: (<Table
        dataSet={options}
        filter={tableFilter}
        columns={[
          { name: 'name' },
          { name: 'code' },
        ]}
        queryBarProps={{
          onQuery: handleQuery,
        }}
      />),
      style: { width: 800 },
      bodyStyle: { padding: 0 },
      onOk: () => {
        const data = [
          ...(record?.get(name) || []),
          ...(options.selected.map(i => i.toData())),
        ];
        record.set(name, data);
      },
    });
  };

  return (
    <Sortable
      className={styles.container}
      onChange={(...arg) => {
        handleUpdate(arg?.[2]);
      }}
      options={{
        ...sortableOption,
        group: {
          name: `drag_tag_${name}`,
          pull: true,
        },
        handle: `.${styles.drag}`,
      }}
    >
      {(record?.get(name) || []).map((item, index) => {
        const label = options.find(i => i.get('code') === item.code)?.get('name');
        return <Item
          data={{
            ...item,
            name: label,
          }}
          disabled={disabled}
          onRemove={() => handleRemove(index, item)}
          onLock={lock ? () => handleLock(index, item) : null}
        />;
      })}
      <Button className={styles.add} onClick={handleAdd} icon="plus" funcType="raised" color="secondary" disabled={disabled} />
    </Sortable>
  );
});

export default injectIntl(DragTag);

/* externalize: DragTag */
