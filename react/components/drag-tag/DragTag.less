.container {
  display: flex;
  flex-wrap: wrap;
}
.drag {
  :global {
    .yqcloud-icon-park-wrapper {
      cursor: move !important;
    }
  }
  height: 16px;
}
.add {
  width: 32px;
}
.label {
  margin: 0 5px;
}
.item {
  display: flex;
  align-items: center;
  height: 32px;
  margin: 0 4px 4px 0;
  padding: 5px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.5);
  &:hover {
    background: #f7f8fa;
  }
  :global {
    .yqcloud-icon-park-wrapper {
      cursor: pointer;
    }
  }
}
.lock {
  margin-right: 8px;
}
.addModal {
  :global {
    .c7n-pro-modal-body {
      padding: 0;
    }
  }
}
