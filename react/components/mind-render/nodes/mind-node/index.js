import React, { useContext, useState, useCallback, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { Handle } from 'react-flow-renderer';
import { Icon } from '@zknow/components';
import axios from 'axios';
import { color as colorUtils } from '@zknow/utils';
import { message, Modal, Tooltip, TextArea } from 'choerodon-ui/pro';
import PageLoader from '@/components/page-loader';
import getLayoutElements from '../../layout';
import Store from '../../stores';

import './index.less';

const prefixCls = 'lc-component-mind-render-node';
const BOOLEAN_FIELDS = ['CheckBox', 'Switch'];

const MindNode = (props) => {
  const {
    id,
    data: {
      showFields = [{ value: '' }],
      parentId,
      path,
      level,
      color,
      dataId,
      isLeaf,
      queriedChildren,
    },
    sourcePosition = 'right',
    targetPosition = 'left',
  } = props;
  const {
    elementStore,
    deleteFlag,
    dragFlag,
    removeFlag,
    createFlag,
    createViewConfig,
    editFlag,
    viewViewConfig,
    relationFieldCode,
    showFieldId,
    businessObjectId,
    subBusinessObjectId,
    intl,
    intlPrefix,
    tenantId,
    selfRelationFieldId,
    selfRelationFieldCode,
  } = useContext(Store);
  const defaultLabel = BOOLEAN_FIELDS.includes(showFields[0].widgetType)
    ? `${showFields[0].fieldName}: ${intl.formatMessage({ id: showFields[0].value === '1' ? 'yes' : 'no' })}`
    : showFields[0].value;
  const [showAction, setShowAction] = useState(false);
  // 控制是否折叠
  const [collapse, setCollapse] = useState(false);
  // 用于控制拖拽样式
  const [dropContainer, setDropContainer] = useState(false);
  // 用于控制禁止拖拽放置样式
  const [dropDisable, setDropDisable] = useState(false);
  // 用于控制是否行内编辑
  const [editable, setEditable] = useState(false);
  const [labelText, setLabelText] = useState(defaultLabel);
  const editPageRef = useRef(null);
  const createPageRef = useRef(null);

  const handleNodeClick = () => {
    if (!editable) {
      elementStore.setCurrentNodeId(id);
      setShowAction(true);
    }
  };

  const handleDragStart = () => {
    elementStore.setDragNodeId(id);
  };

  const handleDragOver = (evt) => {
    const isParent = id === elementStore.getNodes.find(node => node.id === elementStore.getDragNodeId).data.parentId;
    if (path.includes(elementStore.getDragNodeId) || isParent) {
      evt.dataTransfer.effectAllowed = 'none';
      evt.dataTransfer.dropEffect = 'none';
      setDropDisable(true);
    } else {
      evt.preventDefault();
    }
  };

  const handleDragEnter = (evt) => {
    const isParent = id === elementStore.getNodes.find(node => node.id === elementStore.getDragNodeId).data.parentId;
    if (path.includes(elementStore.getDragNodeId) || isParent) {
      evt.dataTransfer.effectAllowed = 'none';
      evt.dataTransfer.dropEffect = 'none';
      setDropDisable(true);
    } else {
      setDropContainer(true);
    }
  };

  const handleDragLeave = () => {
    if (elementStore.getDragNodeId !== id) {
      setDropContainer(false);
      setDropDisable(false);
    }
  };

  const handleDrop = (evt) => {
    evt.preventDefault();
    const isParent = id === elementStore.getNodes.find(node => node.id === elementStore.getDragNodeId).data.parentId;
    if (path.includes(elementStore.getDragNodeId) || isParent) {
      evt.dataTransfer.effectAllowed = 'none';
      evt.dataTransfer.dropEffect = 'none';
      return;
    }
    const dragNodeId = elementStore.getDragNodeId;
    if (dragNodeId !== id) {
      elementStore.updateRelation(dataId, level).then(res => {
        if (res && res.failed) {
          message.error(res.message);
          setDropContainer(false);
        } else {
          elementStore.initElements();
        }
        setDropContainer(false);
      }).catch(e => {
        // eslint-disable-next-line
        console.log('request update node error', e.error);
      });
    }
  };

  const handleDragEnd = () => {
    setDropDisable(false);
  };

  const findChildElementIds = () => {
    const nodes = elementStore.getNodes.slice();
    const edges = elementStore.getEdges.slice();
    const targetNodes = [];
    const targetEdges = [];
    // find node
    nodes.forEach(el => {
      if (el.data.level > level) {
        if (el.data.path.includes(path) && el.data.path !== path) {
          targetNodes.push(el.id);
        }
      }
    });
    // find edge
    edges.forEach(el => {
      if (targetNodes.includes(el.target)) {
        targetEdges.push(el.id);
      }
    });
    return [...targetNodes, ...targetEdges];
  };

  const getChildrenNum = () => {
    const nodes = elementStore.getNodes.slice();
    const targetNodes = [];
    nodes.forEach(el => {
      if (el.data.level > level) {
        if (el.data.path.includes(path) && el.data.path !== path) {
          targetNodes.push(el.id);
        }
      }
    });
    return targetNodes.length;
  };

  const handleEdit = () => {
    Modal.open({
      title: viewViewConfig.viewName,
      children: (
        <PageLoader
          viewId={viewViewConfig.viewId}
          mode="MODIFY"
          instanceId={dataId}
          pageRef={editPageRef}
          openType={viewViewConfig.viewType}
        />
      ),
      drawer: viewViewConfig.viewType === 'RIGHT',
      style: { width: Number(viewViewConfig.viewSize) },
      destroyOnClose: true,
      onOk: async () => {
        const res = await editPageRef.current.formDataSet.submit();
        if (res && res.failed) {
          message.error(res.message);
          return false;
        } else {
          elementStore.initElements();
        }
      },
      onCancel: () => {
        editPageRef.current?.formDataSet?.reset();
      },
    });
  };

  const handleAddBro = () => {
    // add default value to new record
    const rootNode = elementStore.getNodes.find(el => el.data.level === 1);
    const parentNode = elementStore.getNodes.find(el => el.id === parentId);
    // 字段顺序不可改变，关联字段与自关联字段相同时，父级为 parentNode 而非 rootNode
    const defaultData = level > 2 ? {
      [relationFieldCode]: {
        [createViewConfig.relationLovValueFieldCode]: rootNode.data.dataId,
        [createViewConfig.relationLovNameFieldCode]: rootNode.data.label,
      },
      [selfRelationFieldCode]: {
        [createViewConfig.selfRelationLovValueFieldCode]: parentNode.data.dataId,
        [createViewConfig.selfRelationLovNameFieldCode]: parentNode.data.showFields[0].value,
      },
    } : {
      [relationFieldCode]: {
        [createViewConfig.relationLovValueFieldCode]: rootNode.data.dataId,
        [createViewConfig.relationLovNameFieldCode]: rootNode.data.label,
      },
    };
    Modal.open({
      title: createViewConfig.viewName,
      children: (
        <PageLoader
          viewId={createViewConfig.viewId}
          mode="CREATE"
          pageRef={createPageRef}
          defaultData={defaultData}
          openType={createViewConfig.viewType}
        />
      ),
      drawer: createViewConfig.viewType === 'RIGHT',
      style: { width: Number(createViewConfig.viewSize) },
      destroyOnClose: true,
      onOk: async () => {
        const valid = await createPageRef.current.formDataSet.validate();
        if (!valid) {
          return false;
        }
        try {
          const res = await createPageRef.current.formDataSet.submit();
          if (res && res.failed) {
            message.error(res.message);
            return false;
          }
          if (businessObjectId !== subBusinessObjectId) {
            elementStore.initElements();
          } else {
            elementStore.addNode(parentNode.id, parentNode.data.dataId, parentNode.data.path);
          }
        } catch (e) {
          message.error(e.message);
          return false;
        }
      },
      onCancel: () => {
        createPageRef.current.formDataSet.reset();
      },
    });
  };

  const handleLabelUpdate = (e) => {
    setEditable(false);
    if (dataId) {
      const value = e.target.value;
      if (!value || value === labelText) {
        setLabelText(defaultLabel);
        return;
      }
      const url = level === 1
        ? `/lc/v1/${tenantId}/mind-map/point/${businessObjectId}/${showFieldId}/${dataId}?value=${value}`
        : `/lc/v1/${tenantId}/mind-map/point/${subBusinessObjectId}/${showFields[0].id}/${dataId}?value=${value}`;
      axios
        .put(url)
        .then(res => {
          if (res && res.failed) {
            message.error(intl.formatMessage({ id: 'lcr.components.desc.saved.failed', defaultMessage: '保存失败' }));
            setLabelText(defaultLabel);
          } else {
            setLabelText(value);
            showFields[0].value = value;
            elementStore.initElements();
            message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
          }
        })
        .catch(error => {
          message.error(error.message);
          setLabelText(defaultLabel);
        });
    }
  };

  const handleLabelChange = (value) => {
    setLabelText(value);
  };

  const handleAddChild = () => {
    const rootNode = elementStore.getNodes.find(el => el.data.level === 1);
    const defaultData = level > 1 ? {
      [relationFieldCode]: {
        [createViewConfig.relationLovValueFieldCode]: rootNode.data.dataId,
        [createViewConfig.relationLovNameFieldCode]: rootNode.data.label,
      },
      [selfRelationFieldCode]: {
        [createViewConfig.selfRelationLovValueFieldCode]: dataId,
        [createViewConfig.selfRelationLovNameFieldCode]: labelText,
      },
    } : {
      [relationFieldCode]: {
        [createViewConfig.relationLovValueFieldCode]: rootNode.data.dataId,
        [createViewConfig.relationLovNameFieldCode]: rootNode.data.label,
      },
    };
    Modal.open({
      title: createViewConfig.viewName,
      children: (
        <PageLoader
          viewId={createViewConfig.viewId}
          mode="CREATE"
          pageRef={createPageRef}
          defaultData={defaultData}
          openType={createViewConfig.viewType}
        />
      ),
      drawer: createViewConfig.viewType === 'RIGHT',
      style: { width: Number(createViewConfig.viewSize) },
      destroyOnClose: true,
      onOk: async () => {
        const valid = await createPageRef.current.formDataSet.validate();
        if (!valid) {
          return false;
        }
        try {
          const res = await createPageRef.current.formDataSet.submit();
          if (res && res.failed) {
            message.error(res.message);
            return false;
          }
          if (businessObjectId !== subBusinessObjectId) {
            elementStore.initElements();
          } else if (queriedChildren) {
            elementStore.addNode(id, dataId, path);
          } else {
            handleQueryChildren(id, dataId, path);
          }
        } catch (e) {
          message.error(e.message);
          return false;
        }
      },
      onCancel: () => {
        createPageRef.current.formDataSet.reset();
      },
    });
  };

  const handleToggleClose = () => {
    const childrenIds = findChildElementIds();
    const nodes = elementStore.getNodes.slice();
    const edges = elementStore.getEdges.slice();
    [...nodes, ...edges].forEach(el => {
      if (childrenIds.includes(el.id)) {
        el.hidden = !collapse;
      }
    });
    elementStore.setNodes(nodes);
    elementStore.setEdges(edges);
    setCollapse(!collapse);
  };

  const handleQueryChildren = () => {
    elementStore.queryChildNodes(id, dataId, path);
  };

  const handleRemove = useCallback(() => {
    if (dataId) {
      elementStore.removeNode().then(res => {
        if (res && res.failed) {
          message.error(res.message);
        } else if (businessObjectId !== subBusinessObjectId) {
          elementStore.initElements();
        } else {
          const childrenIds = findChildElementIds();
          const nodes = elementStore.getNodes.slice();
          const edges = elementStore.getEdges.slice();
          const sourceEdgeIds = edges.filter(el => el.target === id).map(el => el.id);
          const newElements = [...nodes, ...edges].filter(el => ![...childrenIds, ...sourceEdgeIds, id].includes(el.id));
          newElements.forEach(el => {
            el.position = { x: 0, y: 0 };
          });
          const { nodes: newNodes, edges: newEdges } = getLayoutElements(newElements, 'LR');
          elementStore.setNodes(newNodes);
          elementStore.setEdges(newEdges);
        }
      }).catch(e => {
        // eslint-disable-next-line
        console.log('request remove node error', e.error);
      });
    }
  }, [elementStore.getNodes.slice(), elementStore.getEdges.slice()]);

  const handleDelete = useCallback(() => {
    if (dataId) {
      elementStore.deleteNode().then(res => {
        if (res && res.failed) {
          message.error(res.message);
        } else if (businessObjectId !== subBusinessObjectId) {
          elementStore.initElements();
        } else {
          const childrenIds = findChildElementIds();
          const nodes = elementStore.getNodes.slice();
          const edges = elementStore.getEdges.slice();
          const sourceEdgeIds = edges.filter(el => el.target === id).map(el => el.id);
          const newElements = [...nodes, ...edges].filter(el => ![...childrenIds, ...sourceEdgeIds, id].includes(el.id));
          newElements.forEach(el => {
            el.position = { x: 0, y: 0 };
          });
          const { nodes: newNodes, edges: newEdges } = getLayoutElements(newElements, 'LR');
          elementStore.setNodes(newNodes);
          elementStore.setEdges(newEdges);
        }
      }).catch(e => {
        // eslint-disable-next-line
        console.log('request delete node error', e.error);
      });
    }
  }, [elementStore.getNodes.slice(), elementStore.getEdges.slice()]);

  const hasAction = () => (
    (businessObjectId === subBusinessObjectId || level !== 1)
    && editFlag
  ) || (
      level !== 1
      && createFlag
    ) || (
      createFlag
      && (businessObjectId === subBusinessObjectId || level === 1)
    ) || (
      !isLeaf
      && collapse
    ) || (
      businessObjectId === subBusinessObjectId
      && !isLeaf
      && !queriedChildren
    ) || (
      !isLeaf
      && !collapse
      && queriedChildren
    ) || (
      removeFlag
      && level !== 1
    ) || (
      deleteFlag
      && level !== 1
    );

  return (
    <div className={`${prefixCls}-container`}>
      {
        id === elementStore.getCurrentNodeId && hasAction() && showAction && (
          <div className={`${prefixCls}-menu`}>
            {
              // 编辑按钮，业务对象和关联业务对象不同时根节点不允许编辑
              (businessObjectId === subBusinessObjectId || level !== 1)
              && editFlag
              && (
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}>
                  <Icon type="write" onClick={handleEdit} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 添加同级节点按钮
              level !== 1
              && createFlag
              && (
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.mind.node.add.bro.node', defaultMessage: '新建同级节点' })}>
                  <Icon type="add-item" onClick={handleAddBro} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              createFlag
              && (selfRelationFieldId || level === 1)
              && (
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.mind.node.add.child.node', defaultMessage: '新建子级节点' })}>
                  <Icon type="add-subset" onClick={handleAddChild} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 展开按钮
              !isLeaf
              && collapse
              && (
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.mind.node.expand', defaultMessage: '一键展开' })}>
                  <Icon type="circle-double-right" onClick={handleToggleClose} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 懒加载按钮
              selfRelationFieldId
              && !isLeaf
              && !queriedChildren
              && (
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.mind.node.expand', defaultMessage: '一键展开' })}>
                  <Icon type="circle-double-right" onClick={handleQueryChildren} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 收起按钮
              !isLeaf
              && !collapse
              && queriedChildren
              && (
                <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.lc.mind.node.collapse', defaultMessage: '一键收起' })}>
                  <Icon type="circle-double-left" onClick={handleToggleClose} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 移除按钮
              removeFlag
              && level !== 1
              && (
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.remove', defaultMessage: '移除' })}>
                  <Icon type="delete-mode" onClick={handleRemove} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
            {
              // 删除按钮
              deleteFlag
              && level !== 1
              && (
                <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}>
                  <Icon type="delete" onClick={handleDelete} className={`${prefixCls}-menu-button`} />
                </Tooltip>
              )
            }
          </div>
        )
      }
      <div className={`${prefixCls}-main`}>
        <div
          className={classnames(
            prefixCls,
            {
              [`${prefixCls}-draggable`]: dragFlag && level !== 1 && !editable,
              [`${prefixCls}-drop-container`]: dropContainer,
              [`${prefixCls}-drop-disable-container`]: dropDisable,
              [`${prefixCls}-level1`]: level === 1,
              [`${prefixCls}-level2`]: level === 2,
              [`${prefixCls}-noborder`]: level > 2,
              [`${prefixCls}-current`]: elementStore.getCurrentNodeId === id,
            },
          )}
          style={{
            borderColor: color && colorUtils.colorOpacityRGB(color, 0.34),
            backgroundColor: (color && colorUtils.colorOpacityRGB(color, 0.06)) || (level > 1 && '#f5f5f5'),
            color: color && colorUtils?.calColorShade(color) ? '#fff' : '#12274d',
          }}
          data-id={id}
          data-path={path}
          draggable={dragFlag && level !== 1 && !editable}
          onDragStart={handleDragStart}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onDragEnd={handleDragEnd}
          onClick={handleNodeClick}
        >
          <Handle
            type="target"
            position={targetPosition}
            id="left"
            style={{ visibility: 'hidden' }}
            isConnectable={false}
          />
          {
            editFlag && editable ? (
              <TextArea
                defaultValue={labelText}
                onBlur={handleLabelUpdate}
                onChange={handleLabelChange}
                className={`${prefixCls}-input`}
                autoFocus
                autoSize
              />
            ) : (
              <div
                className={`${prefixCls}-label`}
                onDoubleClick={() => {
                  if (!BOOLEAN_FIELDS.includes(showFields[0].widgetType)) {
                    setEditable(true);
                    setShowAction(false);
                  }
                }}
              >
                {labelText}
                {
                  showFields.length > 1 && (
                    <div className={`${prefixCls}-info`}>
                      {
                        showFields.slice(1).map(field => (
                          <span className={`${prefixCls}-info-item`} key={field.id}>
                            {BOOLEAN_FIELDS.includes(field.widgetType)
                              ? `${field.fieldName}: ${intl.formatMessage({ id: field.value === '1' ? 'yes' : 'no' })}`
                              : field.value}
                          </span>
                        ))
                      }
                    </div>
                  )
                }
              </div>
            )
          }
          <Handle
            type="source"
            position={sourcePosition}
            id="right"
            style={{ visibility: 'hidden' }}
            isConnectable={false}
          />
        </div>
        {
          id === elementStore.getCurrentNodeId
          && createFlag
          && (
            <div className={`${prefixCls}-extra`}>
              <div className={`${prefixCls}-line`} />
              <Icon
                type="add-one"
                onClick={handleAddChild}
                className={`${prefixCls}-extra-button`}
                theme="filled"
                fill="#2979ff"
              />
            </div>
          )
        }
        {
          // 默认显示子节点数，hover 显示展开按钮
          !isLeaf
          && collapse
          && id !== elementStore.getCurrentNodeId
          && (
            <div className={`${prefixCls}-extra`}>
              <div className={`${prefixCls}-line`} />
              <div className={`${prefixCls}-number`}>{getChildrenNum()}</div>
              <div className={`${prefixCls}-collapse-wrapper`}>
                <Icon
                  type="right-c"
                  onClick={handleToggleClose}
                  className={`${prefixCls}-collapse-button`}
                />
              </div>
            </div>
          )
        }
        {
          // hover 显示懒加载按钮
          id !== elementStore.getCurrentNodeId
          && !queriedChildren
          && (
            <div className={`${prefixCls}-extra`}>
              <div className={`${prefixCls}-collapse-wrapper`}>
                <div className={`${prefixCls}-line`} />
                <Icon
                  type="right-c"
                  onClick={handleQueryChildren}
                  className={`${prefixCls}-collapse-button`}
                />
              </div>
            </div>
          )
        }
        {
          // 收起按钮
          !collapse
          && id !== elementStore.getCurrentNodeId
          && getChildrenNum() !== 0
          && (
            <div className={classnames(`${prefixCls}-extra`, `${prefixCls}-collapse-wrapper`)}>
              <div className={`${prefixCls}-line`} />
              <Icon
                type="left-c"
                onClick={handleToggleClose}
                className={`${prefixCls}-collapse-button`}
              />
            </div>
          )
        }
      </div>
    </div>
  );
};

export default observer(MindNode);
