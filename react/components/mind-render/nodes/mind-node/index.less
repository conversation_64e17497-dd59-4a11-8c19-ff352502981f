@import "~choerodon-ui/lib/style/themes/default";

.lc-component-mind-render-node {
  text-align: 'center';
  font-weight: 400;
  max-width: 3.2rem;
  position: relative;
  cursor: default;
  border: 0.02rem transparent;

  &:hover {
    border: 0.02rem solid ~"rgba(122, 180, 255, 1)";
  }

  &-draggable {
    cursor: grab;
  }

  &-drop-container {
    cursor: default;
    background-color: gray !important;

    * {
      pointer-events: none;
    }
  }

  &-drop-disable-container {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &-level1 {
    border-radius: 0.05rem;
    border: 0.02rem solid ~"rgba(41, 121, 255, 1)";
    background-color: ~"rgba(41, 121, 255, 1)";
    color: #fff;
    padding: 0.06rem 0.1rem 0.06rem 0.1rem;
    font-size: 0.16rem;
  }

  &-level2 {
    border-radius: 0.05rem;
    background-color: #f5f5f5;
    color: #12274d;
    padding: 0.06rem 0.1rem 0.06rem 0.1rem;
    font-size: 0.16rem;
    border: 0.02rem solid #f5f5f5;
  }

  &-noborder {
    border-radius: 0.05rem;
    font-size: 0.16rem;
    padding: 0.06rem 0.1rem 0.06rem 0.1rem;
    background-color: #fff;
    border: 0.02rem solid #fff;
  }

  &-current {
    border-radius: 0.04rem;
    border: 0.02rem solid ~"rgba(41, 121, 255, 1)";
  }

  &-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      .lc-component-mind-render-node-collapse-wrapper {
        display: flex;
        align-items: center;
      }
      .lc-component-mind-render-node-number {
        display: none;
      }
    }
  }

  &-menu {
    background-color: #f7f7f7;
    display: flex;
    height: .4rem;
    flex-direction: row;
    align-items: center;
    cursor: default;
    padding: 0.04rem 0.08rem 0.04rem 0.08rem;
    position: absolute;
    top: -0.5rem;
    border-radius: 0.04rem;
    box-shadow: 0 3px 12px 0 rgba(0, 0, 0, 0.12);
    border: 1px solid #e8e8e8;

    &-button {
      width: 0.32rem;
      height: 0.32rem;
      border-radius: 0.04rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.16rem;
      margin-right: 0.04rem;
      margin-left: 0.04rem;
      color: #595959;

      &:hover {
        background-color: #e8e8e8;
      }
    }
  }

  &-main {
    display: flex;
    flex-direction: row;
  }

  &-input {
    background: transparent !important;
    font-size: .16rem;

    label {
      background: transparent;
    }

    textarea {
      border: none !important;
      background: transparent;
      font-size: .16rem;
    }
  }

  &-label {
    max-width: 3.2rem;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    font-size: .16rem;
  }

  &-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: .12rem;
    color: fade(@text-color, 80%);
  }

  &-extra {
    display: flex;
    flex-direction: row;
    align-items: center;
    background: white;

    &-button {
      color: ~"rgba(41, 121, 255, 1)";
      cursor: pointer;
      font-size: 0.22rem;
    }
  }

  &-line {
    width: 0.05rem;
    height: 0.02rem;
    background: ~"rgba(41, 121, 255, 1)";
    margin-right: 0.01rem;
  }

  &-number {
    width: 0.2rem;
    height: 0.2rem;
    border-radius: 0.1rem;
    border: 2px solid ~"rgba(41, 121, 255, 1)";
    color: ~"rgba(41, 121, 255, 1)";
    font-size: 0.12rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.01rem;
  }

  &-collapse {
    &-wrapper {
      display: none;
    }

    &-button {
      color: ~"rgba(41, 121, 255, 1)";
      cursor: pointer;
      font-size: 0.22rem;
    }
  }

  &-button {
    width: 0.15rem;
    height: 0.15rem;
    cursor: pointer;
  }
}
