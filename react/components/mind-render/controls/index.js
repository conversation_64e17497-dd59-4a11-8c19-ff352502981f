import React from 'react';
import { observer } from 'mobx-react-lite';
import { Controls, ControlButton, useStore, useStoreApi } from 'react-flow-renderer';
import { Icon } from '@zknow/components';
import { Tooltip } from 'choerodon-ui/pro';

const isInteractiveSelector = (s) => s.nodesDraggable && s.nodesConnectable && s.elementsSelectable;

const CustomControls = (props) => {
  const { intl, instanceRef } = props;

  const store = useStoreApi();
  const isInteractive = useStore(isInteractiveSelector);

  const handleZoomout = () => {
    instanceRef.current?.zoomOut();
  };

  const handleZoomin = () => {
    instanceRef.current?.zoomIn();
  };

  const handleFit = () => {
    instanceRef.current?.fitView();
  };

  const handleToggleLock = () => {
    store.setState({
      nodesDraggable: !isInteractive,
      nodesConnectable: !isInteractive,
      elementsSelectable: !isInteractive,
    });
  };

  return (
    <Controls showZoom={false} showFitView={false} showInteractive={false}>
      <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.zoomin', defaultMessage: '放大' })}>
        <ControlButton onClick={handleZoomin}>
          <Icon type="Plus" />
        </ControlButton>
      </Tooltip>
      <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.zoomout', defaultMessage: '缩小' })}>
        <ControlButton onClick={handleZoomout}>
          <Icon type="Minus" />
        </ControlButton>
      </Tooltip>
      <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.fit.view', defaultMessage: '适应' })}>
        <ControlButton onClick={handleFit}>
          <Icon type="full-screen" />
        </ControlButton>
      </Tooltip>
      <Tooltip title={intl.formatMessage({ id: isInteractive ? 'lock' : 'unlock' })}>
        <ControlButton onClick={handleToggleLock}>
          <Icon type={isInteractive ? 'Lock' : 'Unlock'} />
        </ControlButton>
      </Tooltip>
    </Controls>
  );
};

export default observer(CustomControls);
