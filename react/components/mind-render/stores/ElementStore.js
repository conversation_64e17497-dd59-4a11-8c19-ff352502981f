import { useLocalStore } from 'mobx-react-lite';
import axios from 'axios';
import { message } from 'choerodon-ui/pro';
import getLayoutElements from '../layout';

const fakeData = [
  {
    id: 'fake-1',
    position: { x: 0, y: 0 },
    dragHandle: '.lc-component-mind-render-node',
    type: 'mind',
    draggable: false,
    data: {
      showFields: [{ value: 'root' }],
      level: 1,
      path: 'fake-1',
      isLeaf: false,
      queriedChildren: true,
    },
  },
  {
    id: 'fake-2',
    position: { x: 0, y: 0 },
    dragHandle: '.lc-component-mind-render-node',
    type: 'mind',
    draggable: false,
    data: {
      showFields: [{ value: 'child1' }],
      level: 2,
      path: 'fake-1/fake-2',
      isLeaf: true,
    },
  },
  {
    id: 'fake-3',
    position: { x: 0, y: 0 },
    dragHandle: '.lc-component-mind-render-node',
    type: 'mind',
    draggable: false,
    data: {
      showFields: [{ value: 'child2' }],
      level: 2,
      path: 'fake-1/fake-3',
      isLeaf: true,
    },
  },
  {
    id: 'fake-1-fake-2',
    source: 'fake-1',
    target: 'fake-2',
    type: 'normal',
    style: { stroke: '#2979ff', strokeWidth: '0.02rem' },
  },
  {
    id: 'fake-1-fake-3',
    source: 'fake-1',
    target: 'fake-3',
    type: 'normal',
    style: { stroke: '#2979ff', strokeWidth: '0.02rem' },
  },
];

const ElementsStore = ({
  tenantId,
  businessObjectId,
  showFieldId,
  subBusinessObjectId,
  relationFieldId,
  subShowFieldIds,
  colorFieldPath,
  rootColorFieldPath,
  selfRelationFieldId,
  dataId,
  expandFlag,
  expandLayers,
}) => {
  return useLocalStore(() => ({
    nodes: [],

    setNodes(data) {
      this.nodes = data;
    },

    get getNodes() {
      return this.nodes.slice();
    },

    edges: [],

    setEdges(data) {
      this.edges = data;
    },

    get getEdges() {
      return this.edges.slice();
    },

    loading: true,

    setLoading(data) {
      this.loading = data;
    },

    get getLoading() {
      return this.loading;
    },

    async transformNodes(nodeData) {
      const edges = [];
      nodeData.forEach(ele => {
        ele.dragHandle = '.lc-component-mind-render-node';
        ele.type = 'mind';
        ele.draggable = false;
        ele.data.label = ele.data.showFields[0].value;
        // 有 selfRelationFieldId 并且不是叶子节点并且没有子节点才需要懒加载
        // 用于判断是否有请求查询过子节点，true 代表已经加载过
        const hasChildren = nodeData.some(x => x.data.parentId === ele.id);
        ele.data.queriedChildren = !(!ele.data.isLeaf && !hasChildren && selfRelationFieldId);
        if (ele.data.parentId && ele.data.parentId !== '0') {
          edges.push({
            id: `${ele.data.parentId}-${ele.id}`,
            source: ele.data.parentId,
            target: ele.id,
            type: 'normal',
            style: { stroke: '#2979ff', strokeWidth: '0.02rem' },
          });
        }
      });
      const { nodes, edges: edgeData } = getLayoutElements([...nodeData, ...edges], 'LR');
      // 延迟执行，确保 edge 的位置正确
      setTimeout(() => {
        this.setEdges(edgeData);
        this.setNodes(nodes);
        this.loading = false;
      }, 500);
    },

    reArrange(els) {
      const { nodes, edges } = getLayoutElements(els, 'LR');
      this.setEdges(edges);
      this.setNodes(nodes);
    },

    async initElements() {
      this.loading = true;
      // 预览时用假数据
      if (!dataId) {
        const { nodes, edges } = getLayoutElements(fakeData, 'LR');
        this.nodes = nodes;
        this.edges = edges;
        this.loading = false;
        return false;
      }
      // 非预览时请求数据
      const postData = {
        businessObjectId,
        showFieldId,
        subBusinessObjectId,
        relationFieldId,
        subShowFieldIds,
        colorFieldPath,
        rootColorFieldPath,
        selfRelationFieldId,
        dataId,
      };
      if (selfRelationFieldId) {
        postData.selfRelationFieldId = selfRelationFieldId;
      }
      await axios.post(`/lc/v1/${tenantId}/mind-map/query?deepQuery=${expandFlag === 'ALL_LAYERS' ? 'true' : 'false'}${expandFlag === 'SPECIFY_LAYERS' ? `&deep=${expandLayers}` : ''}&needRoot=true`, postData).then((res) => {
        if (res && res.failed) {
          message.error(res.message);
          this.loading = false;
          return false;
        } else {
          this.transformNodes(res);
        }
      }).catch(e => {
        // eslint-disable-next-line
        console.log('request element error', e);
        this.loading = false;
      });
    },

    queryChildNodes(targetNodeId, targetDataId, targetPath) {
      this.loading = true;
      axios.post(`/lc/v1/${tenantId}/mind-map/query?deepQuery=false&parentPath=${targetPath}&needRoot=false`, {
        businessObjectId,
        showFieldId,
        subBusinessObjectId,
        relationFieldId,
        subShowFieldIds,
        colorFieldPath,
        selfRelationFieldId,
        dataId: targetDataId,
      }).then(res => {
        // 添加新节点生成的边
        const edges = [];
        res.forEach(ele => {
          ele.dragHandle = '.lc-component-mind-render-node';
          ele.type = 'mind';
          ele.draggable = false;
          ele.data.parentId = targetNodeId;
          ele.data.queriedChildren = ele.data.isLeaf;
          edges.push({
            id: `${targetNodeId}-${ele.id}`,
            source: targetNodeId,
            target: ele.id,
            type: 'normal',
            style: { stroke: '#2979ff', strokeWidth: '0.02rem' },
          });
        });
        const newElements = [...this.nodes.slice(), ...this.edges.slice()];
        newElements.forEach(el => {
          if (el.id === targetNodeId) {
            el.data.queriedChildren = true;
          }
        });
        const { nodes, edges: edgeData } = getLayoutElements([...newElements, ...edges, ...res], 'LR');
        this.nodes = nodes;
        this.edges = edgeData;
        this.loading = false;
      });
    },

    addNode(targetNodeId, targetDataId, targetPath) {
      this.loading = true;
      axios.post(`/lc/v1/${tenantId}/mind-map/query?deepQuery=true&parentPath=${targetPath}&needRoot=false`, {
        businessObjectId,
        showFieldId,
        subBusinessObjectId,
        relationFieldId,
        subShowFieldIds,
        colorFieldPath,
        selfRelationFieldId,
        dataId: targetDataId,
      }).then(res => {
        const originNodeIds = this.nodes.map(el => el.data.dataId);
        const newNode = res.find(el => !originNodeIds.includes(el.data.dataId));
        newNode.position = { x: 0, y: 0 };
        newNode.dragHandle = '.lc-component-mind-render-node';
        newNode.type = 'mind';
        newNode.draggable = false;
        newNode.data.parentId = targetNodeId;
        newNode.data.queriedChildren = newNode.data.isLeaf;
        // 添加新节点生成的边
        const edge = {
          id: `${targetNodeId}-${newNode.id}`,
          source: targetNodeId,
          target: newNode.id,
          type: 'normal',
          style: { stroke: '#2979ff', strokeWidth: '0.02rem' },
        };
        const { nodes, edges } = getLayoutElements([...this.nodes.slice(), ...this.edges.slice(), edge, newNode], 'LR');
        nodes.forEach(el => {
          if (el.id === targetNodeId) {
            el.data.isLeaf = false;
          }
        });
        this.nodes = nodes;
        this.edges = edges;
        this.loading = false;
      });
    },

    deleteNode() {
      const currentNode = this.nodes.find(node => node.id === this.currentNodeId);
      return axios.delete(`/lc/v1/${tenantId}/mind-map/point/${currentNode.data.businessObjectId}/${currentNode.data.dataId}`);
    },

    removeNode() {
      const currentNode = this.nodes.find(node => node.id === this.currentNodeId);
      return axios.delete(`/lc/v1/${tenantId}/mind-map/line/${currentNode.data.businessObjectId}/${currentNode.data.dataId}/${currentNode.data.relationFieldId}`);
    },

    updateRelation(targetDataId, level) {
      const sourceNode = this.nodes.find(node => node.id === this.dragNodeId);
      let url;
      if (level === 1) {
        url = `/lc/v1/${tenantId}/mind-map/line/${subBusinessObjectId}/${sourceNode.data.dataId}/${relationFieldId}/${targetDataId}?selfRelationFieldId=${selfRelationFieldId}`;
      } else {
        url = `/lc/v1/${tenantId}/mind-map/line/${subBusinessObjectId}/${sourceNode.data.dataId}/${selfRelationFieldId}/${targetDataId}`;
      }
      return axios.put(url);
    },

    // 用来记录当前元素，显示元素操作菜单，删除等操作
    currentNodeId: '',

    setCurrentNodeId(data) {
      this.currentNodeId = data;
    },

    get getCurrentNodeId() {
      return this.currentNodeId;
    },

    dragNodeId: '',

    setDragNodeId(data) {
      this.dragNodeId = data;
    },

    get getDragNodeId() {
      return this.dragNodeId;
    },
  }));
};

export default ElementsStore;
