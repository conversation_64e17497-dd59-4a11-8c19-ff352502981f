import React, { createContext } from 'react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import ElementStore from './ElementStore';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  observer(props => {
    const {
      children,
      tenantId,
      businessObjectId,
      showFieldId,
      showFieldCode,
      subBusinessObjectId,
      relationFieldId,
      subShowFieldIds,
      colorFieldPath,
      rootColorFieldPath,
      selfRelationFieldId,
      selfRelationFieldCode,
      dataId,
      expandFlag = 'SPECIFY',
      expandLayers = 2,
    } = props;

    const prefixCls = 'lc-component-mind-render';
    const intlPrefix = 'lc.mind.node';

    const elementStore = ElementStore({
      tenantId,
      businessObjectId,
      showFieldId,
      showFieldCode,
      subBusinessObjectId,
      relationFieldId,
      subShowFieldIds,
      colorFieldPath,
      rootColorFieldPath,
      dataId,
      selfRelationFieldId,
      selfRelationFieldCode,
      expandFlag,
      expandLayers,
    });

    const value = {
      ...props,
      prefixCls,
      intlPrefix,
      elementStore,
    };
    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
