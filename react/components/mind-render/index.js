import React from 'react';
import { StoreProvider } from './stores';
import MainView from './MainView';

const MindRender = (props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
);

const toMindElements = (array) => {
  const nodes = array.map(r => ({
    id: r.id,
    data: {
      label: r.name,
      path: r.path,
      level: r.path.split('/').length,
      parentId: r.parentId,
    },
    position: {
      x: 0,
      y: 0,
    },
    dragHandle: '.lc-component-mind-render-node',
    type: 'mind',
  }));

  const edges = [];

  array.forEach(r => {
    if (r.parentId) {
      edges.push({
        id: `${r.parentId}-${r.id}`,
        source: r.parentId,
        target: r.id,
        type: 'smoothstep',
        style: { stroke: '#2979ff' },
      });
    }
  });
  return [...nodes, ...edges];
};

export default MindRender;
export { toMindElements };

/* externalize: MindRender */
