import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { getSmoothStepPath, getMarkerEnd, getEdgeCenter } from 'react-flow-renderer';
import { Icon } from '@zknow/components';
import Store from '../../stores';

import './index.less';

const foreignObjectSize = 20;

const NormalEdge = (props) => {
  const {
    id,
    sourceX,
    sourceY,
    targetX,
    targetY,
    sourcePosition,
    targetPosition,
    style = {},
    arrowHeadType,
    markerEndId,
    showButton,
    buttonIcon = 'Close',
    onClick = () => undefined,
  } = props;

  const { prefixCls } = useContext(Store);

  const edgePath = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 10,
  });

  const markerEnd = getMarkerEnd(arrowHeadType, markerEndId);

  const [edgeCenterX, edgeCenterY] = getEdgeCenter({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  return (
    <>
      <path
        id={id}
        style={style}
        className="react-flow__edge-path"
        d={edgePath}
        markerEnd={markerEnd}
      />
      {
        showButton && (
          <foreignObject
            width={foreignObjectSize}
            height={foreignObjectSize}
            x={edgeCenterX - foreignObjectSize / 2}
            y={edgeCenterY - foreignObjectSize / 2}
            className="edgebutton-foreignobject"
            requiredExtensions="http://www.w3.org/1999/xhtml"
          >
            <body className={`${prefixCls}-edge-button`}>
              <Icon
                type={buttonIcon}
                onClick={onClick}
                className={`${prefixCls}-edge-icon`}
              />
            </body>
          </foreignObject>
        )
      }
    </>
  );
};

export default observer(NormalEdge);
