import dagre from 'dagre';
import { isNode } from 'react-flow-renderer';

const getLayoutedElements = (elements, direction = 'TB') => {
  const isHorizontal = direction === 'LR';

  const dagreGraph = new dagre.graphlib.Graph({
    directed: true,
    multigraph: false,
    compound: true,
  });
  dagreGraph.setDefaultEdgeLabel(() => ({}));
  dagreGraph.setGraph({ rankdir: direction });

  elements.forEach((el) => {
    if (isNode(el)) {
      const width = 320;
      const height = el.height || 40;
      el.data.height = height;
      dagreGraph.setNode(el.id, { width, height });
    } else {
      dagreGraph.setEdge(el.source, el.target);
    }
  });

  dagre.layout(dagreGraph, {
    align: 'UL',
    nodesep: 60,
    ranker: 'tight-tree',
  });

  const nodes = [];
  const edges = [];

  elements.forEach(el => {
    if (isNode(el)) {
      const nodeWithPosition = dagreGraph.node(el.id);
      el.targetPosition = isHorizontal ? 'left' : 'top';
      el.sourcePosition = isHorizontal ? 'right' : 'bottom';

      // 节点的左右间距要保证一致，所以要根据父级节点的实际宽度计算x坐标位移
      const parent = elements.find(e => e.id === el.data.parentId);
      // 当前节点位移 = 父级的位移 + 父级节点的宽度 - 240（控制间距不要太大）
      const displacement = parent ? ((parent.displacement || 0) + (parent.width || 0) - 240) : 0;
      el.displacement = displacement;
      // 中间节点与初始节点居中对齐
      el.position = {
        x: nodeWithPosition.x - 160 + displacement,
        y: nodeWithPosition.y - el.data.height / 2.0,
      };
      nodes.push(el);
    } else {
      edges.push(el);
    }
  });
  return { nodes, edges };
};

export default getLayoutedElements;
