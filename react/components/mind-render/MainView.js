import React, { useContext, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import React<PERSON>low, { ReactFlowProvider } from 'react-flow-renderer';
import { MindNode } from './nodes';
import { NormalEdge } from './edges';
import Controls from './controls';
import Store from './stores';

import './index.less';

const NodeTypes = {
  mind: MindNode,
};

const EdgeTypes = {
  normal: NormalEdge,
};

const MainView = () => {
  const { elementStore, intl } = useContext(Store);

  const instanceRef = useRef(null);

  useEffect(() => {
    if (!elementStore.getLoading) {
      const instance = instanceRef.current;
      if (instance) {
        const { getNodes, getEdges } = instance;
        setTimeout(() => {
          elementStore.reArrange([...getNodes(), ...getEdges()]);
        }, 20);
        if (instance.getZoom() > 1) {
          instance.zoomTo(1);
        }
        setTimeout(() => {
          instance.fitView();
        }, 30);
      }
    }
  }, [elementStore.getLoading]);

  const handleInit = (instance) => {
    elementStore.initElements();
    instanceRef.current = instance;
    setTimeout(() => {
      if (instance.getZoom() > 1) {
        instance.zoomTo(1);
      }
    }, 500);
  };

  const handlePanClick = () => {
    elementStore.setCurrentNodeId('');
  };

  return (
    <ReactFlow
      onInit={handleInit}
      nodeTypes={NodeTypes}
      edgeTypes={EdgeTypes}
      onPaneClick={handlePanClick}
      nodes={elementStore.getNodes}
      edges={elementStore.getEdges}
      className="lc-mind"
      maxZoom={1}
      fitView
    >
      <Controls intl={intl} instanceRef={instanceRef} />
    </ReactFlow>
  );
};

export default observer(MainView);
