@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

@prefix-cls: yq-table;

.@{prefix-cls} {
  &-filter-advanced-query {
    display: flex;
    align-items: center;
    font-size: @font-size-base;
    margin: 0.04rem 0.08rem 0 0;

    span.@{prefix-cls}-advanced-query {
      height: 100%;
      color: @primary-color;
      display: flex;
      align-items: center;
      padding: 0 0.08rem;
      cursor: pointer;

      &-visible {
        background-color: @yq-primary-color-10;
        border-radius: @border-radius-base;
      }

      &-count {
        margin-left: 0.04rem;
        background: #2979ff;
        width: 0.2rem;
        height: 0.2rem;
        border-radius: 50%;
        color: #fff;
        text-align: center;
        line-height: 0.2rem;
        font-size: 0.125rem;
        transform: scale(.8);
      }

      .yqcloud-icon-park-wrapper {
        margin-right: 0.04rem;
      }

      &:focus,
      &:active,
      &:hover {
        background-color: @yq-primary-color-10;
        border-radius: @border-radius-base;
      }
    }
  }
}

.@{c7n-prefix}-popover {
  &.@{prefix-cls}-advanced-query-popover {
    min-width: 7.2rem;
    max-width: 9rem;
    padding-top: 0.01rem;
    z-index: 999;

    .c7n-popover-arrow {
      display: none;
    }

    .c7n-popover-title {
      color: #12274d;
      line-height: 0.22rem;
      padding: 0.12rem 0.16rem;
    }

    .c7n-popover-inner-content {
      padding: 0;
    }

    .advanced-query-content {
      max-height: 4rem;
      overflow: auto;
    }

    .advanced-query-filter {
      padding: 0.16rem 0.16rem 0 0.16rem;

      .conditionSelect-main {
        border-top: none !important;
        padding: 0 !important;
      }
    }

    .advanced-query-sort {
      margin-top: 0.12rem;
      padding: 0 0.16rem 0.16rem 0.16rem;
    }

    .advanced-query-operate {
      padding: 0.12rem 0 0.12rem 0;
      border-top: 0.01rem solid rgba(203, 210, 220, 0.5);
      text-align: center;
    }
  }
}
