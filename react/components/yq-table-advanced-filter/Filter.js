import React, { useState, useEffect, useContext } from 'react';
import axios from 'axios';
import { runInAction } from 'mobx';
import { inject } from 'mobx-react';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import { message } from 'choerodon-ui/pro';
import { Popover } from 'choerodon-ui';
import { Icon } from '@zknow/components';
import Content from './Content';
import Store from './stores';

import './index.less';

/**
 * 高级筛选
 */
const Filter = function QuickFilterButton() {
  const context = useContext(Store);
  const {
    resetCallback,
    AppState: { currentMenuType: { tenantId } },
    businessObjectId, filterId, filterListDataSet, intl, dataSet,
  } = context;
  const [visible, setVisible] = useState(false);
  const [fieldTableData, setTableFieldData] = useState([]);
  const [conditionCount, setConditionCount] = useState(0);

  const prefixCls = 'yq-table';

  /**
   * 字段加载修改为打开高级搜索时请求
   * @returns {Promise<void>}
   */
  async function loadFields() {
    // 当前对象字段已加载
    if (fieldTableData.length > 0) {
      return;
    }
    const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?conditionFlag=true`);
    if (res && res.failed) {
      message.error(res.message);
      setTableFieldData([]);
    } else {
      setTableFieldData(res);
    }
  }

  return (
    <div className={`${prefixCls}-filter-advanced-query`}>
      <Popover
        content={
          <Content
            intl={intl}
            filterId={filterId}
            businessObjectId={businessObjectId}
            fieldTableData={fieldTableData}
            setVisible={setVisible}
            dataSet={dataSet}
            filterListDataSet={filterListDataSet}
            setConditionCount={setConditionCount}
            resetCallback={resetCallback}
          />
        }
        popupClassName={`${prefixCls}-advanced-query-popover`}
        title={intl.formatMessage({ id: 'lcr.components.desc.table.set.advanced.filters', defaultMessage: '设置高级搜索条件' })}
        placement="bottom"
        trigger="click"
        visible={visible}
        autoAdjustOverflow
      >
        <span
          className={classNames(`${prefixCls}-advanced-query`, {
            [`${prefixCls}-advanced-query-visible`]: visible,
          })}
          onClick={(e) => {
            e.nativeEvent.stopImmediatePropagation();
            runInAction(async () => {
              await loadFields();
              setVisible(!visible);
            });
          }}
        >
          <Icon type={visible ? 'DoubleUp' : 'Filter'} />
          {visible ? intl.formatMessage({ id: 'lcr.components.desc.table.set.advanced.filters.away', defaultMessage: '收起搜索' }) : conditionCount ? intl.formatMessage({ id: 'lcr.components.desc.table.set.advanced.filters.unfold', defaultMessage: '展开高级搜索' }) : intl.formatMessage({ id: 'lcr.components.desc.table.set.advanced.filters.fold', defaultMessage: '高级搜索' })}
          {conditionCount && !visible
            ? <span className={`${prefixCls}-advanced-query-count`}>{conditionCount}</span>
            : ''}
        </span>
      </Popover>
    </div>
  );
};

export default inject('AppState')(observer(Filter));
