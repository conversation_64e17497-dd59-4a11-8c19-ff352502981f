import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import { v4 as uuidv4 } from 'uuid';
import { Button } from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';

/**
 * 高级筛选详情
 */
const Content = function QuickFilterButton(props) {
  const {
    businessObjectId, fieldTableData, setVisible, intl, resetCallback,
    dataSet, setConditionCount,
  } = props;
  const [disabled, setDisabled] = useState(true);
  const [condition, setCondition] = useState([{
    condition: 'AND',
    groupId: uuidv4(),
    filters: [{
      condition: 'AND',
      field: undefined,
      fieldValue: undefined,
      filter: undefined,
      filterUuid: uuidv4(),
    }],
  }]);
  const [order, setOrder] = useState([{ field: '', order: 'ASC', id: uuidv4() }]);

  /**
   * 筛选重置
   */
  function handleResetClick() {
    setDisabled(true);
    const resetCondition = [{
      condition: 'AND',
      groupId: uuidv4(),
      filters: [{
        condition: 'AND',
        field: undefined,
        fieldValue: undefined,
        filter: undefined,
        filterUuid: uuidv4(),
      }],
    }];
    const resetOrder = [{ field: '', order: 'ASC', id: uuidv4() }];
    setCondition(resetCondition);
    setConditionCount(0);
    dataSet.setQueryParameter('__condition', undefined);
    setOrder(resetOrder);
    dataSet.setQueryParameter('__orderBy', undefined);
  }

  if (resetCallback) {
    resetCallback(handleResetClick);
  }

  /**
   * 筛选
   */
  function handleFilterClick() {
    dataSet.setState('__CONDITIONSTATUS__', 'update');
    dataSet.query();
    setVisible(false);
  }

  return (
    <>
      <div className="advanced-query-content">
        <div className="advanced-query-filter">
          <ExternalComponent
            system={{
              scope: 'itsm',
              module: 'YqCondition',
            }}
            conditionData={condition}
            tableId={businessObjectId}
            fieldTableData={fieldTableData}
            onChange={(data) => {
              setCondition(data);
              setConditionCount(data.length);
              dataSet.setQueryParameter('__condition', JSON.stringify(data));
              setDisabled(false);
            }}
            sourceFrom="filter"
            isSider={false}
            isCascader
            conditionFlag
            canHideUser
            extraFieldsFlag
            groupConditionReadOnly
            hasVariableType={false}
            hasRecursiveList={['Select', 'Radio']}
          />
        </div>
        <div className="advanced-query-sort">
          <ExternalComponent
            system={{
              scope: 'itsm',
              module: 'SortCondition',
            }}
            conditionData={order}
            fieldTableData={fieldTableData}
            onChange={(data) => {
              setOrder(data);
              dataSet.setQueryParameter('__orderBy', JSON.stringify(data));
              setDisabled(false);
            }}
          />
        </div>
      </div>
      <div className="advanced-query-operate">
        <Button
          funcType="raised"
          color="primary"
          key="filter"
          onClick={handleFilterClick}
        >
          {intl.formatMessage({ id: 'lcr.components.model.filter', defaultMessage: '筛选' })}
        </Button>
        <Button
          key="reset"
          onClick={handleResetClick}
          disabled={disabled}
        >
          {intl.formatMessage({ id: 'zknow.common.button.reset', defaultMessage: '重置' })}
        </Button>
        <Button
          key="close"
          onClick={() => setVisible(false)}
        >
          {intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' })}
        </Button>
      </div>
    </>
  );
};

export default observer(Content);
