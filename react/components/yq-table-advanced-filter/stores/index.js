import React, { createContext } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';

const Store = createContext();
const MENUDATASET = '__MENUDATASET__';

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      dataSet,
    } = props;

    // 筛选器值集
    const filterListDataSet = dataSet.getState(MENUDATASET);
    const prefixCls = 'yq-table';

    const value = {
      ...props,
      prefixCls,
      tenantId,
      dataSet,
      filterListDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },)
));
