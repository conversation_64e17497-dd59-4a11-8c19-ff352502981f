export default ({ saveOptions, typeOptions, filterListDataSet, intl }) => {
  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: false,
    fields: [
      { name: 'saveMethod', options: saveOptions, defaultValue: 'NEW' },
      { name: 'name', type: 'string', maxLength: 20, required: true },
      { name: 'icon', type: 'string', required: true },
      { name: 'type', options: typeOptions, defaultValue: 'SELF' },
      {
        name: 'groupId',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'GROUP',
        },
        type: 'object',
        lovCode: 'GROUP',
        transformRequest: (value) => {
          return (value && value.id) || value;
        },
        transformResponse: (value, data) => {
          if (data.departmentId) {
            return {
              id: value,
              name: data.departmentName,
              groupName: data.departmentName,
            };
          }
          return null;
        },
      },
      {
        name: 'filterId',
        options: filterListDataSet,
        textField: 'name',
        valueField: 'id',
        dynamicProps: {
          required: ({ record }) => record.get('saveMethod') === 'OVERWRITE',
        },
      },
    ],
  };
};
