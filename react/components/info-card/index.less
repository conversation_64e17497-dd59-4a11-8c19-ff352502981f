
.user-card-tooltip {
  background-color: #ffffff;
  .c7n-pro-tooltip-popup-inner {
    background: #FFFFFF !important;
    box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 0px;
  }
  .c7n-pro-tooltip-popup-arrow {
    display: none;
    &-dark {
      display: none;
    }
  }
  &-main {
    width: 100%;
    min-width: 3rem;
    .tooltip-top {
      padding: 16px 12px;
      display: flex;
      align-items: center;
      background: url(../../renderer/participants/images/background.png) no-repeat;
      background-size: 100% 100%;
      .user-card-name {
        font-size: 14px;
        font-weight: 500;
        color: #2B2D38;
        line-height: 22px;
        text-shadow: 0px 3px 12px rgba(0, 0, 0, 0.12);
      }
    }
    .tooltip-content {
      display: table;
      padding: 12px 16px;
      .c7n-spin-container::before, .c7n-spin-container::after {
        display: none;
      }
      &-row {
        display: table-row;
      }
      &-cell {
        display: table-cell;
        text-align: right;
        padding: 5px 0px;
        padding-right: 16px;
        &-title {
          font-size: 14px;
          font-weight: 400;
          color: #6B7285;
          line-height: 32px;
          text-shadow: 0px 3px 12px rgba(0, 0, 0, 0.12);
          white-space: nowrap;
        }
        &-value {
          max-width: 2.4rem;
          font-size: 14px;
          font-weight: 400;
          color: #2B2D38;
          line-height: 22px;
          text-shadow: 0px 3px 12px rgba(0, 0, 0, 0.12);
          text-align: left;
        }
      }
    }
  }
}