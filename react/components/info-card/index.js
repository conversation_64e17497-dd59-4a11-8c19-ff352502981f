import React, { useEffect, useState } from 'react';
import { inject } from 'mobx-react';
import { Spin } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import classnames from 'classnames';

import './index.less';

export default inject('AppState')(observer((props) => {
  const {
    value,
    relationLovId,
    title,
    AppState: {
      currentMenuType: { tenantId },
    },
  } = props;
  const [loading, setLoading] = useState(true);
  const [items, setItems] = useState([]);
  const [data, setData] = useState({});

  async function getLovConfig() {
    try {
      const res = await axios.get(`/lc/v1/${tenantId}/object_options/id/${relationLovId}`);
      if (!res.failed) {
        return JSON.parse(res?.jsonData || '{}');
      }
      return [];
    } catch (e) {
      console.log(e);
    }
  }

  async function getDataById(field = 'id') {
    const _data = {};
    _data[field] = value?.id || value;
    try {
      const res = await axios.post(`/lc/v1/engine/${tenantId}/options/${relationLovId}/query`, _data);
      if (!res.failed && res.content.length) {
        return res.content[0];
      }
      return [];
    } catch (e) {
      console.log(e);
    }
  }

  useEffect(() => {
    (async function () {
      if (value || value.id) {
        const lovConfig = await getLovConfig();
        setItems(lovConfig?.layout || []);
        setData(await getDataById(lovConfig?.dataSource?.idField));
      }
    }());
    setLoading(false);
  }, [value, value?.id]);

  function renderCardTooltip() {
    const titleCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-title': true,
    });
    const valueCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-value': true,
    });

    return (
      <div className="user-card-tooltip-main">
        <div className="tooltip-top">
          <span className="user-card-name">{title}</span>
        </div>
        <div className="tooltip-content">
          <Spin spinning={loading} style={{ width: '100%' }}>
            {items.map(v => {
              if (v.props.displayFlag === false) {
                return null;
              }
              return (
                <div className="tooltip-content-row">
                  <div className={titleCLassName}>{v.props.label || v.name}</div>
                  <div className={valueCLassName}>{data[v.props.name] || '-'}</div>
                </div>
              );
            })}
          </Spin>
        </div>
      </div>
    );
  }

  return renderCardTooltip();
}));
