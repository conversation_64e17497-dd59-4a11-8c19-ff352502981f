import React from 'react';
import { observer } from 'mobx-react-lite';
import { Popover, Menu } from 'choerodon-ui';
import isEmpty from 'lodash/isEmpty';
import uniq from 'lodash/uniq';
import styles from './TranslateSuffix.module.less';
import { queryTranslateNormal, queryTranslateNormalAsync } from '../../service';
import useServiceSetting from '../../hooks/useServiceSetting';
import { isInIframe } from '@/utils';

function TranslateSuffix({ tenantId, businessObjectId, appState, setTranslateLoading, setTranslateResult, setTranslateError, value }) {
  const countRef = React.useRef(30); // 翻译的轮询
  const serviceSetting = useServiceSetting(businessObjectId, '', tenantId, appState);
  const [translateVisible, setTranslateVisible] = React.useState(false);

  React.useEffect(() => {
    return () => {
      countRef.current = -1;
    };
  }, []);

  const getTranslateResult = async (uuid) => {
    const res = await queryTranslateNormal(tenantId, uuid);
    if (countRef.current === -1) {
      setTranslateError(true);
      setTranslateLoading(false);
      return false;
    }
    if (res?.failed) {
      setTranslateError(true);
      setTranslateLoading(false);
    } else if (res) {
      if (!isEmpty(res)) {
        setTranslateResult(res);
      } else {
        setTranslateError(true);
      }
      setTranslateLoading(false);
    } else if (countRef.current > 0) {
      setTimeout(() => {
        getTranslateResult(uuid);
      }, 3000);
    }
  };

  async function handleTranslate(e) {
    const { key } = e;
    if (!value) {
      return false;
    }
    setTranslateLoading(true);
    setTranslateError(false);
    try {
      const res = await queryTranslateNormalAsync({ tenantId, businessObjectId, targetLang: key, message: value });
      if (typeof res === 'string') {
        getTranslateResult(res);
      } else {
        setTranslateError(true);
        setTranslateLoading(false);
      }
    } catch (err) {
      setTranslateError(true);
      setTranslateLoading(false);
    }
    setTranslateVisible(false);
  }
  function getPopoverContent() {
    return (
      <Menu
        onClick={handleTranslate}
        mode="vertical"
        selectedKeys={[]}
        className={styles.menu}
      >
        {uniq(serviceSetting?.ticketTranslationSettingVO?.targetLanguageList).map(item => (
          <Menu.Item key={item}>
            {(serviceSetting?.ticketTranslationSettingVO?.languageValueList || []).find(({ code }) => code === item)?.value || item}
          </Menu.Item>
        ))}
      </Menu>
    );
  }

  if (isInIframe()) return null;

  return (
    <Popover
      content={getPopoverContent()}
      trigger="hover"
      placement="bottomRight"
      overlayClassName={styles.intelPopover}
      getPopupContainer={el => el}
      visible={translateVisible}
      onVisibleChange={setTranslateVisible}
    >
      <span className={styles.suffix}>
        <img
          className={styles.suffixImg}
          src={`${window._env_.ICON_SERVER}/static/icon_translation.png`}
          alt="icon"
        />
      </span>
    </Popover>
  );
}

export default observer(TranslateSuffix);
