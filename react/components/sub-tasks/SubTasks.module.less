@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.renderer-sub-tasks {
  padding: 16px;

  .header {
    display: flex;
    justify-content: space-between;

    .left {
      display: flex;
      align-items: center;

      .line {
        width: 4px;
        height: 16px;
        border-radius: 2px;
        background-color: @primary-color;
      }

      .title {
        margin: 0 8px;
        font-weight: 500;
        font-size: 16px;
      }

      .icon {
        cursor: pointer;
      }

      .leftIcon {
        margin-right: 8px !important;
        color: @primary-color;
      }
    }
  }

  .contentEmpty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .progress {
      display: flex;
      align-items: baseline;
      width: 100%;
      margin: 12px 0;

      .progressGraph {
        flex: 1;

        :global {
          .c7n-progress-text {
            color: rgba(18, 39, 77, 0.85);
          }

          .c7n-progress-bg {
            background-color: #1ab335;
          }
        }
      }

      .progressText {
        color: rgba(18, 39, 77, 0.85);
        margin-left: 8px;
        display: flex;
        align-items: center;

        .completed {
          margin-left: 8px;
        }
      }
    }
  }

  .content {
    .progress {
      display: flex;
      align-items: center;
      width: 100%;
      margin: 12px 0;

      .progressGraph {
        flex: 1;

        :global {
          .c7n-progress-text {
            color: rgba(18, 39, 77, 0.85);
          }

          .c7n-progress-bg {
            background-color: #1ab335;
          }
        }
      }

      .progressText {
        color: rgba(18, 39, 77, 0.85);
        margin-left: 8px;
        display: flex;
        align-items: center;

        .completed {
          margin-left: 8px;
        }
      }
    }

    .item {
      position: relative;
      padding: 12px 0;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
      display: flex;
      align-items: center;
      justify-content: flex-start;
      flex-wrap: wrap;

      .title {
        margin-right: 8px;
        flex: 0 0 300px;
        width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .number {
        min-width: 100px;
        margin-right: 8px;
        flex: 0 0 auto;
        color: @primary-color;
        cursor: pointer;
      }

      .itemWrap {
        margin-right: 8px;
      }

      .itemIcon {
        margin-right: 8px;
      }

      &:hover {
        .rowBtn {
          display: block !important;
        }
      }

      :global {
        .c7n-pro-output {
          word-break: break-all;
        }
      }
    }

    .new {
      display: flex;
      align-items: center;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
      padding-bottom: 12px;

      .btnWrapper {
        display: flex;
        width: 200px;
        justify-content: space-between;
        margin-top: 12px;
      }

      .smallBtnWrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 0 0 64px;
        width: 64px;

        .iconBtn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          flex: 0 0 28px;
          width: 28px;
          height: 28px;
          color: #12274d;
          cursor: pointer;

          &:hover {
            color: @primary-color;
            background: @yq-primary-color-10;
            border-radius: 28px;
          }
        }
      }

      .group {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        :global {
          .c7n-pro-form-item-group-item {
            margin-top: 12px;
          }
        }
      }

      .newIcon {
        margin-top: 12px;
      }
    }

    .add {
      margin-top: 12px;
    }

    .input {
      width: 100px;
      margin-right: 4px;
    }

    .text {
      flex: 1;
      margin: 0 8px;
      padding-right: 8px;
    }

    .btn {
      min-width: 80px;
      margin-left: 8px;
    }
  }
}

.userItem {
  display: flex;
  align-items: center;

  .userText {
    margin-left: 8px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.defaultText {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(18, 39, 77, 0.45);
  border: 1px solid rgba(203, 210, 220, 0.65);
  margin: 8px 0;
  border-radius: 4px;
  font-size: 14px;
}

.rowBtn {
  display: none;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);
  right: 0;

  background: #fff;
}
