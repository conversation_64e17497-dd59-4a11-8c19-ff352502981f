import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { withErrorBoundary } from '@zknow/components';
import moment from 'moment';
import PageLoader from '@/components/page-loader';
import Store from './store';
import styles from './ShadowView.module.less';

const fieldTypeMapping = {
  Input: 'string',
  DateTime: 'dateTime',
  Date: 'date',
  MasterDetail: 'object',
};

const defaultDateFormat = {
  DateTime: 'YYYY-MM-DD HH:mm:ss',
  Date: 'YYYY-MM-DD',
  Time: 'HH:mm:ss',
};

/**
 * 子任务可以自定义列上的展示字段，如果单独为字段适配，是一个巨大的工程，相当于将表单渲染器迁移出来重新实现一份
 *   避免如此大的工作量，所以可以通过隐藏式的渲染一份子任务的新建视图，让其渲染出来 dataSet 后复用
 * @returns {Element}
 * @constructor
 */
function ShadowView() {
  const { shadowPageRef, subTaskCreateViewId, subTaskDataSet, subTaskColumns, createViewDataSet } = useContext(Store);

  if (!subTaskCreateViewId) {
    return null;
  }

  const formDataSetCreated = (dataSet) => {
    if (dataSet) {
      (subTaskColumns || []).forEach(column => {
        const targetField = dataSet.fields.get(column.code);
        if (targetField) {
          subTaskDataSet.addField(column.code, Object.fromEntries(targetField.props.toJS()));
        } else {
          // 新建视图中无拖入的字段，但是子任务的展示列中配了相应的字段
          const fieldProps = {
            name: column.code,
            label: column.name,
            type: fieldTypeMapping[column.widgetType],
            required: column.requiredType === 'ALWAYS_REQUIRED',
          };
          if (column.widgetType === 'DateTime' || column.widgetType === 'Date') {
            // 根据字段类型，个性化配置
            fieldProps.format = column.widgetConfig.format
              ?.replace('yyyy', 'YYYY')
              ?.replace('mm', 'MM')
              ?.replace('dd', 'DD')
              ?.replace(':MM', ':mm') || defaultDateFormat[column.widgetType];
            fieldProps.transformRequest = (value, record) => {
              return value ? moment(value).format(defaultDateFormat[column.widgetType]) : value;
            };
          } else if (column.widgetType === 'MasterDetail') {
            // LOV 暂不支持不在视图上的字段渲染
          }
          subTaskDataSet.addField(column.code, fieldProps);
        }
      });
    }
    subTaskDataSet.query();
  };

  const viewDataSetCreated = (record) => {
    // 视图上一些字段的特殊配置，例如日期选择器的【允许选择过去时间】
    if (record) {
      let allFields = [];
      (record.get('jsonData')?.sections || []).forEach((section) => {
        allFields = [...allFields, ...(section.fields || [])];
      });
      createViewDataSet.loadData(allFields);
    }
  };

  return (
    <div className={styles.wrap}>
      <PageLoader
        viewId={subTaskCreateViewId}
        pageRef={shadowPageRef}
        mode="CREATE"
        formDataSetCreated={formDataSetCreated}
        viewDataSetCreated={viewDataSetCreated}
        expressionFlag
        ticketFlag
        autoFocusDisabled
      />
    </div>
  );
}

export default withErrorBoundary(observer(ShadowView), { fallback: <span /> });
