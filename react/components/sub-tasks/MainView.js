import React, { useEffect, useState, useContext, useMemo, useRef, useCallback } from 'react';
import { observer } from 'mobx-react-lite';
import moment from 'moment';
import uuidv4 from 'uuid/v4';
import { useSize } from 'ahooks';
import { Form, Progress, TextField, DateTimePicker, Lov, Output, Modal, message, Tooltip } from 'choerodon-ui/pro';
import { Icon, Button, Empty, StatusTag } from '@zknow/components';
import Store from './store';
import Styles from './SubTasks.module.less';
import { ACTION, calculateConditions } from '@/components/page-loader/utils';
import PageLoader from '@/components/page-loader';

const { ItemGroup } = Form;
const modalKey = Modal.key();

const SubTasks = () => {
  const context = useContext(Store);
  const {
    AppState: {
      userInfo: { personId, person },
    },
    intl,
    formDataSet,
    tenantId,
    prefixCls,
    subTaskDataSet,
    themeColor,
    onJumpNewPage,
    mode,
    viewId,
    record: viewRecord,
    shadowPageRef,
    createViewDataSet,
    serviceItemId,
    fieldsDataSet,
  } = context;
  const lineType = useRef('');
  const createViewPageRef = useRef(null);
  const [addState, setAddState] = useState(true);
  const [progressValue, setProgressValue] = useState(0);
  const { subTaskViewId, subTaskColumns, subTaskCreateViewId } = viewRecord?.get('widgetConfig') || {};
  const ref = useRef();
  const componentsRef = useRef(null);
  const listCmpSize = useSize(componentsRef);

  useEffect(() => {
    // 子任务到达最终状态的比例
    if (subTaskDataSet && subTaskDataSet?.totalCount) {
      let finishState = 0;
      let total = 0;
      subTaskDataSet.forEach((i) => {
        if (i?.get('state_id:terminal_state_flag')) {
          finishState += 1;
        }
        if (i?.get('id')) {
          total += 1;
        }
      });
      if (total === 0) {
        setProgressValue(0);
      } else {
        const num = (finishState / total) * 100;
        setProgressValue(num);
      }
    }
  }, [subTaskDataSet?.totalCount]);

  const handleAdd = () => {
    setAddState(false);
    subTaskDataSet.create({});
    setTimeout(() => {
      ref?.current?.focus();
    }, 200);
  };

  const getPercent = () => {
    if (typeof progressValue === 'number') {
      return `${progressValue.toFixed(0)}%`;
    }
  };

  const openCreateView = (defaultData) => {
    if (!subTaskCreateViewId) {
      return;
    }
    Modal.open({
      key: modalKey,
      style: { width: 800 },
      children: (
        <PageLoader
          viewId={subTaskCreateViewId}
          pageRef={createViewPageRef}
          mode="CREATE"
          expressionFlag
          ticketFlag
          autoFocusDisabled
          showHeaderFlag={false}
          defaultData={defaultData}
        />
      ),
      onOk: async () => {
        try {
          const createDs = createViewPageRef.current?.formDataSet;
          const result = await createDs.validate();
          if (!result) {
            return false;
          }
          const data = createDs.current.toData();
          if (!data.state_id) {
            delete data.state_id;
          }
          const record = subTaskDataSet.create(data);
          const parentDraftFlag = formDataSet?.current?.get('draft_flag');
          record.set('draft_flag', !!parentDraftFlag);
          await subTaskDataSet.submit();
          await subTaskDataSet.query();
          setAddState(true);
          lineType.current = '';
        } catch (e) {
          message.error(e);
          return false;
        }
      },
    });
  };

  const renderRowButtons = (record) => {
    const handleSave = async () => {
      const shadowDs = shadowPageRef.current?.formDataSet;
      if (lineType.current !== 'editing') {
        if (shadowDs) {
          const currentData = record.toData();
          const shadowRecord = shadowDs.create(currentData);
          const result = await shadowRecord.validate(true);
          if (!result) {
            const lovFields = (subTaskColumns || []).filter((c) => c.widgetType === 'MasterDetail').map((c) => c.code);
            lovFields.forEach((f) => {
              if (currentData[f]) {
                currentData[f] = {
                  id: record.get(`${f}.id`),
                  name: record.get(`${f}.name`) || record.get(`${f}.real_name`),
                };
              }
            });
            openCreateView(currentData);
            setAddState(true);
            subTaskDataSet.reset();
            if (shadowDs) {
              shadowDs.loadData([]);
            }
            return false;
          }
        }
      }
      lineType.current = '';

      if (await subTaskDataSet.validate()) {
        const parentDraftFlag = formDataSet?.current?.get('draft_flag');
        record.set('draft_flag', !!parentDraftFlag);
        if (!serviceItemId) {
          await subTaskDataSet.submit();
          // query 会生成全新的 record，所以不需要给当前 record setState 了
          await subTaskDataSet.query();
        } else {
          record.setState('editing', false);
          record.set('id', uuidv4());
        }
        if (shadowDs) {
          shadowDs.loadData([]);
        }
        setAddState(true);
      } else if (ref?.current) {
        setTimeout(() => {
          ref?.current?.blur();
        }, 200);
      }
    };

    const handleCancel = () => {
      if (record.getState('editing')) {
        record.setState('editing', false);
      }
      if (serviceItemId) {
        subTaskDataSet.remove(record, true);
      } else {
        subTaskDataSet.reset();
      }
      setAddState(true);
    };
    return listCmpSize?.width > 600 ? (
      <div className={Styles.btnWrapper}>
        <Button color="primary" className={Styles.btn} onClick={handleSave}>
          {intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' })}
        </Button>
        <Button className={Styles.btn} onClick={handleCancel}>
          {intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}
        </Button>
      </div>
    ) : (
      <div className={Styles.smallBtnWrapper}>
        <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.ok', defaultMessage: '确定' })}><span className={Styles.iconBtn} onClick={handleSave}><Icon type="Check" size={16} /></span></Tooltip>
        <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}><span className={Styles.iconBtn} onClick={handleCancel}><Icon type="Close" size={16} /></span></Tooltip>
      </div>
    );
  };

  // 渲染新建列
  const rendererColumn = (i) => {
    if (!componentsRef?.current) {
      return null;
    }
    return (
      <div className={Styles.new} style={{ width: listCmpSize?.width - 30 }}>
        <Icon type="icon-zidingyi2" fill={themeColor} className={Styles.newIcon} />
        <Form dataSet={subTaskDataSet} record={i}>
          <ItemGroup className={Styles.group}>
            <TextField
              name="short_description"
              placeholder={intl.formatMessage({
                id: 'lcr.components.desc.renderer.sub.tasks.text',
                defaultMessage: '请输入子任务简述内容',
              })}
              ref={ref}
            />
            {(subTaskColumns || []).map((column) => {
              const { code, widgetType, name: fieldName } = column;
              switch (widgetType) {
                case 'Input':
                  return <TextField name={code} placeholder={fieldName || ''} />;
                case 'Date':
                case 'DateTime': {
                  const targetField = createViewDataSet.find(r => r.get('code') === code);
                  return (
                    <DateTimePicker
                      name={code}
                      min={targetField?.get('widgetConfig.passTimeFlag') ? undefined : moment()}
                      placeholder={fieldName || ''}
                    />
                  );
                }
                case 'MasterDetail': {
                  const displayMethod = fieldsDataSet.find(r => r.get('code') === code)?.get('widgetConfig.displayMethod');
                  return (
                    <Lov
                      name={code}
                      placeholder={fieldName || ''}
                      viewMode={displayMethod !== 'MODAL' ? 'popup' : undefined}
                      popupSearchMode="single"
                    />);
                }
                default:
                  return null;
              }
            })}
          </ItemGroup>
        </Form>
        {renderRowButtons(i)}
      </div>
    );
  };

  const getHidden = () => {
    const { createCondition, visibleAction, visibleCondition } = viewRecord?.get('widgetConfig') || {};
    const permissionFlag = viewRecord?.get('permissionFlag');

    const funcConfig = {
      personId,
      person,
      tenantId,
    };
    let hideFieldFlag = createCondition === 'ALWAYS_NOT_VISIBLE';
    if (createCondition === 'CONDITION') {
      if (permissionFlag !== false || mode === 'PREVIEW') {
        // 隐藏条件判断
        if (visibleAction === ACTION.HIDE) {
          hideFieldFlag = calculateConditions(null, null, null, formDataSet?.current, visibleCondition, funcConfig);
        }
        // 显示条件判断
        if (visibleAction === ACTION.SHOW && visibleCondition?.length) {
          hideFieldFlag = !calculateConditions(null, null, null, formDataSet?.current, visibleCondition, funcConfig);
        }
      } else {
        // 当无权限时，不再进行UI规则判断
        hideFieldFlag = true;
      }
    }
    return hideFieldFlag;
  };

  // 子任务的新建按钮受控，逻辑和显隐控制一致
  const renderAddButton = (primary) => {
    const hideFieldFlag = getHidden();
    const hasEditingRow = subTaskDataSet.find((r) => r.getState('editing'));

    if (hideFieldFlag || hasEditingRow) {
      return null;
    }

    if (primary) {
      return (
        <Button color="primary" onClick={handleAdd}>
          {intl.formatMessage({ id: 'lcr.components.desc.renderer.sub.tasks.new', defaultMessage: '新建子任务' })}
        </Button>
      );
    } else {
      return addState ? (
        <Button icon="plus" className={Styles.add} onClick={handleAdd} color="secondary">
          {intl.formatMessage({ id: 'lcr.components.desc.renderer.sub.tasks.new', defaultMessage: '新建子任务' })}
        </Button>
      ) : null;
    }
  };

  function renderEmpty() {
    const noSubTaskRecord = subTaskDataSet?.status === 'ready' && subTaskDataSet?.length === 0;
    // 增加服务项配置的控制
    const defaultConfig = viewRecord?.get('widgetConfig.subTaskDefaultConfig');

    if (defaultConfig === 'hideDefaultImg') {
      return null;
    } else if (defaultConfig === 'showDefaultImg' && noSubTaskRecord) {
      const emptySize = `${viewRecord?.get('widgetConfig.subTaskDefaultLayout') || 80}px`;
      return <Empty innerStyle={{ width: emptySize, height: emptySize }} type="empty" style={{ padding: '38px 0 20px 0' }} />;
    } else if (defaultConfig === 'showDefaultText' && noSubTaskRecord) {
      return <div className={Styles.defaultText}>{viewRecord?.get('widgetConfig.subTaskDefaultText')}</div>;
    } else if (noSubTaskRecord) {
      return <Empty innerStyle={{ width: 80, height: 80 }} type="empty" style={{ padding: '38px 0 20px 0' }} />;
    }
    return null;
  }

  function renderEditButton(record) {
    const lineEditFlag = viewRecord?.get('widgetConfig.subTaskEditFlag');
    const hideFieldFlag = getHidden();
    const hasNewRow = subTaskDataSet.find((r) => !r.get('id'));
    const hasEditingRow = subTaskDataSet.find((r) => r.getState('editing'));
    // 有新建行或正在编辑行或不符合视图配置的规则
    if (hasNewRow || hasEditingRow || hideFieldFlag || !lineEditFlag) {
      return null;
    }

    return (
      <div className={Styles.rowBtn}>
        <Button
          color="primary"
          funcType="flat"
          onClick={() => {
            lineType.current = 'editing';
            record.setState('editing', true);
          }}
        >
          {intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}
        </Button>
      </div>
    );
  }

  function renderDisplayCols(i) {
    return (
      <div key={i.get('id')} className={Styles.item} style={{ width: listCmpSize?.width - 30 }}>
        <Icon type="icon-zidingyi2" className={Styles.itemIcon} fill={themeColor} />
        {!serviceItemId && (
          <span
            className={Styles.number}
            onClick={() => onJumpNewPage({
              record: i,
              viewId: subTaskViewId,
              subParentViewId: viewId,
            })}
          >
            {i.get('number')}
          </span>
        )}
        <span className={Styles.title}>{i.get('short_description')}</span>
        {i.get('state_id:name') && (
          <span className={Styles.itemWrap}>
            <StatusTag color={i.get('state_id:color') || themeColor}>{i.get('state_id:name')}</StatusTag>
          </span>
        )}
        {(subTaskColumns || []).map(col => renderCustomerField(col, i))}
        {renderEditButton(i)}
      </div>
    );
  }

  function renderCustomerField(col, i) {
    if (col.code === 'priority_id') {
      const name = i.get('priority_id:name') || i.get('priority_id.name');
      const color = i.get('priority_id:color') || i.get('priority_id.color');
      return name ? (
        <span className={Styles.itemWrap}>
          <StatusTag
            color={color || themeColor}
            mode="icon"
          >{name}</StatusTag></span>
      ) : null;
    }
    return <span className={Styles.itemWrap}><Output record={i} name={col.code} /></span>;
  }

  return subTaskDataSet?.totalCount > 0 ? (
    <div className={Styles[prefixCls]} ref={componentsRef}>
      <div className={Styles.header}>
        <div className={Styles.left}>
          <span className={Styles.line} />
          <span className={Styles.title}>
            {viewRecord?.get('name') || intl.formatMessage({ id: 'lc.common.component.SubTasks', defaultMessage: '子任务' })}
          </span>
        </div>
      </div>
      <div className={Styles.content}>
        <div className={Styles.progress}>
          <Progress value={progressValue} className={Styles.progressGraph} showInfo={false} />
          <span className={Styles.progressText}>
            {getPercent()}
            <span className={Styles.completed}>{intl.formatMessage({ id: 'lcr.components.model.completed', defaultMessage: '已完成' })}</span>
          </span>
        </div>
        {subTaskDataSet?.map((i) => ((i.getState('editing') || !i.get('id')) ? rendererColumn(i) : renderDisplayCols(i)))}
        {renderAddButton()}
      </div>
    </div>
  ) : (
    <div className={Styles[prefixCls]} ref={componentsRef}>
      <div className={Styles.header}>
        <div className={Styles.left}>
          <span className={Styles.line} />
          <span className={Styles.title}>{viewRecord?.get('name') || intl.formatMessage({ id: 'lc.common.component.SubTasks', defaultMessage: '子任务' })}</span>
        </div>
        {renderAddButton(true)}
      </div>
      <div className={Styles.contentEmpty}>
        <div className={Styles.progress}>
          <Progress value={0} className={Styles.progressGraph} showInfo={false} />
          <span className={Styles.progressText}>
            {getPercent()}
            <span className={Styles.completed}>{intl.formatMessage({ id: 'lcr.components.model.completed', defaultMessage: '已完成' })}</span>
          </span>
        </div>
        {renderEmpty()}
      </div>
    </div>
  );
};

export default observer(SubTasks);
