const SubTaskDataSet = ({
  instanceId,
  tenantId,
  businessObjectId,
  subTaskBusinessObject,
  subTaskBusinessObjectCode,
  subTaskColumns,
  subTaskCreateViewId,
  serviceItemId,
}) => {
  const customerFieldsCode = (subTaskColumns || []).map((col) => (col.code));
  const subTaskBOCode = subTaskBusinessObject?.code || subTaskBusinessObjectCode;
  const urlPrefix = !serviceItemId && instanceId && businessObjectId && subTaskBOCode ? `/itsm/v1/${tenantId}/subtasks/${businessObjectId}/${instanceId}/` : '';

  return {
    autoQuery: !subTaskCreateViewId, // 如果没有配置子任务的【新建视图】，那么就不需要等到 shadowView 加载，直接请求即可
    paging: false,
    transport: {
      read: {
        url: urlPrefix ? `${urlPrefix}query?subBusinessObjectCode=${subTaskBOCode}&subTaskFields=${customerFieldsCode.join(',')}` : '',
        method: 'post',
      },
      create: ({ data: [data] }) => {
        if (data?.assignee_person_id?.id) {
          data.assignee_person_id = data.assignee_person_id.id;
        }
        if (data?.priority_id?.id) {
          data.priority_id = data.priority_id.id;
        }
        return {
          url: urlPrefix ? `${urlPrefix}submit?subBusinessObjectCode=${subTaskBOCode}` : '',
          method: 'post',
          data,
        };
      },
      update: ({ data: [data] }) => {
        if (data?.assignee_person_id?.id) {
          data.assignee_person_id = data.assignee_person_id.id;
        }
        if (data?.priority_id?.id) {
          data.priority_id = data.priority_id.id;
        }
        return {
          url: urlPrefix ? `${urlPrefix}submit?subBusinessObjectCode=${subTaskBOCode}` : '',
          method: 'post',
          data,
        };
      },
    },
    fields: [
      {
        name: 'short_description',
        required: true,
      },
    ],
  };
};

export default SubTaskDataSet;
