import React, { createContext, useEffect, useMemo, useRef } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import SubTaskDataSet from './SubTaskDataSet';
import CreateViewDataSet from './CreateViewDataSet';
import BusinessObjectFieldDataSet from './BusinessObjectFieldDataSet';

const Store = createContext();
export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      pageRef,
      viewDataSet,
      record,
      instanceId,
      HeaderStore: {
        getTenantConfig: {
          themeColor = '#2979ff',
        },
      },
    } = props;
    const shadowPageRef = useRef(null);
    const prefixCls = 'renderer-sub-tasks';
    const { businessObjectCode, businessObjectId, id: viewId, viewType } = viewDataSet?.current?.toData() || {};
    const { subTaskBusinessObject, subTaskBusinessObjectCode, subTaskCreateViewId, subTaskColumns } = record?.get('widgetConfig') || {};
    const formDataSet = pageRef?.current?.formDataSet;
    let serviceItemId = props?.serviceItemId;

    if (viewType === 'INSERT') {
      serviceItemId = true;
    }

    const fieldsDataSet = useMemo(() => new DataSet(BusinessObjectFieldDataSet({ tenantId, businessObjectId: subTaskBusinessObject?.id })), [subTaskBusinessObject]);
    const createViewDataSet = useMemo(() => new DataSet(CreateViewDataSet()), []);
    const subTaskDataSet = useMemo(() => new DataSet(SubTaskDataSet({
      instanceId,
      tenantId,
      intl,
      businessObjectId,
      subTaskBusinessObject,
      subTaskBusinessObjectCode,
      subTaskColumns,
      subTaskCreateViewId,
      serviceItemId,
    })), [instanceId, businessObjectCode, tenantId, businessObjectId, subTaskBusinessObjectCode, subTaskBusinessObject, subTaskCreateViewId]);

    useEffect(() => {
      // 在服务项提单中使用，需要将子任务 dataset 抛出去
      if (serviceItemId && pageRef?.current) {
        pageRef.current.subTaskDataSet = subTaskDataSet;
        pageRef.current.subTaskBusinessObject = subTaskBusinessObject;
      }
      // 提单草稿数据初始化
      if (pageRef?.current?.subTaskShouldLoadData?.length) {
        subTaskDataSet.loadData(pageRef.current.subTaskShouldLoadData);
        pageRef.current.subTaskShouldLoadData = null;
      }
    }, [serviceItemId, subTaskDataSet]);

    const value = {
      ...props,
      prefixCls,
      tenantId,
      intl,
      pageRef,
      subTaskDataSet,
      themeColor,
      viewId,
      formDataSet,
      businessObjectCode,
      subTaskCreateViewId,
      shadowPageRef,
      subTaskColumns,
      createViewDataSet,
      subTaskBusinessObject,
      serviceItemId,
      fieldsDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
