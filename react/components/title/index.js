import React from 'react';
import classNames from 'classnames';
import { Icon } from '@zknow/components';

import './index.less';

const Title = (props) => {
  const {
    title, onClick, visibleFlag, extra = null,
    rightComponent = null, className, sectionFlag,
  } = props;
  return (
    <div
      className={classNames('lc-title', {
        'lc-title-section': sectionFlag,
      }, className)}
      onClick={onClick}
    >
      {sectionFlag ? <div className="lc-title-prefix" /> : null}
      <div className="lc-title-left">
        {title}
        {onClick ? <Icon className="lc-title-icon" theme="filled" type={visibleFlag ? 'UpOne' : 'DownOne'} /> : null}
        <div className="lc-title-extra">
          {extra}
        </div>
      </div>
      <div className="lc-title-right">
        {rightComponent}
      </div>
    </div>
  );
};

export default Title;

/* externalize: Title */
