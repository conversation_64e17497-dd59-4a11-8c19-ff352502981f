/**
 * Created by wml on 03/16/2021
 * 颜色选择器
 * @param props
 */

import React, { useCallback } from 'react';
import { SketchPicker, GithubPicker } from 'react-color';
import ReactDom from 'react-dom';
import reactCSS from 'reactcss';
import './index.less';

class YQColor extends React.Component {
  constructor(props) {
    super(props);
    const { 
      value, 
      name, 
      addonAfter, 
      record,
    } = this.props;
    this.state = {
      displayColorPicker: false,
      addonAfter: props.addonAfter,
      // eslint-disable-next-line react/no-unused-state
      color: value || record?.get(name || 'color') || '#2196f3',
    };
  }

  handleClick = (e) => {
    const { type, preview, mode } = this.props;
    let colorPickerHeight = 309;
    let colorPickerWidth = 270;
    if (mode === 'Github') {
      colorPickerWidth = 212;
      colorPickerHeight = 47;
    }
    if (type === 'text' || preview) return;
    const { top, left } = e.currentTarget.getBoundingClientRect();

    if (window.innerHeight - top < colorPickerHeight) {
      this.setState({
        topHeight: top - colorPickerHeight + 24 > 0 ? top - colorPickerHeight + 24 : 0,
      });
    } else {
      this.setState({
        topHeight: top,
      });
    }
    if (window.innerWidth - left < colorPickerWidth) {
      this.setState({
        leftHeight: window.innerWidth - colorPickerWidth - 24,
      });
    } else {
      this.setState({
        leftHeight: left,
      });
    }
    this.setState(prev => ({ displayColorPicker: !prev.displayColorPicker }));
  };

  handleClose = () => {
    this.setState({ displayColorPicker: false });
  };

  handleChange = (color) => {
    const { onChange, disabled, name, record } = this.props;
    if (!disabled) {
      this.setState({ color: color.rgb });
      const HEX_VALUE = this.hexIfy16(`rgba(${color.rgb.r},${color.rgb.g},${color.rgb.b},${color.rgb.a})`).toUpperCase();
      if (record) {
        record.set(name || 'color', HEX_VALUE);
      }
      if (onChange) {
        onChange({
          rgba: `rgba(${color.rgb.r},${color.rgb.g},${color.rgb.b},${color.rgb.a})`,
          hex: HEX_VALUE,
        });
      }
    }
  };

  // rgb/rgba转16进制颜色编码
  hexIfy16 = (color) => {
    const values = color
      .replace(/rgba?\(/, '')
      .replace(/\)/, '')
      .replace(/[\s+]/g, '')
      .split(',');
    const a = parseFloat(values[3] || 1);
    const r = Math.floor(a * parseInt(values[0], 10) + (1 - a) * 255);
    const g = Math.floor(a * parseInt(values[1], 10) + (1 - a) * 255);
    const b = Math.floor(a * parseInt(values[2], 10) + (1 - a) * 255);
    // const r = Math.floor(parseInt(values[0], 10)); // 舍弃透明度
    // const g = Math.floor(parseInt(values[1], 10));
    // const b = Math.floor(parseInt(values[2], 10));
    return `#${
       (`0${ r.toString(16)}`).slice(-2)
       }${(`0${ g.toString(16)}`).slice(-2)
       }${(`0${ b.toString(16)}`).slice(-2)}`;
  };

  render() {
    const { topHeight, leftHeight } = this.state;
    const { type, preview, width = '250px', name, record, popoverStyle = () => {} } = this.props;
    const styles = reactCSS({
      default: {
        color: {
          width: '0.14rem',
          height: '0.14rem',
          borderRadius: '0.02rem',
        },
        swatch: {
          padding: '0.04rem',
          background: '#fff',
          borderRadius: '1px',
          boxShadow: '0 0 0 0.01rem rgba(0,0,0,.1)',
          display: 'inline-block',
          // eslint-disable-next-line no-nested-ternary
          cursor: type === 'text' ? 'text' : preview ? 'unset' : 'pointer',
          verticalAlign: 'middle',
          marginRight: '0.1rem',
          // eslint-disable-next-line no-nested-ternary
          marginTop: type === 'text' ? 'unset' : '0.05rem',
        },
        popover: {
          top: topHeight,
          left: leftHeight,
          position: 'fixed',
          zIndex: '1000',
          ...popoverStyle({ topHeight, leftHeight }),
        },
        cover: {
          position: 'fixed',
          top: '0px',
          right: '0px',
          bottom: '0px',
          left: '0px',
          marginLeft: '1rem',
        },
        defaultView: {
          width: '250px',
          marginLeft: '0.2rem',
        },
      },
    });

    const renderColorPicker = () => {
      const { mode = 'Sketch' } = this.props;
      if (mode === 'Sketch') {
        return (
          <SketchPicker
            width={width}
            color={this.state.color}
            onChange={this.handleChange}
          />
        );
      } else if (mode === 'Github') {
        return (
          <GithubPicker
            width="212px"
            color={this.state.color}
            colors={[
              '#EBEBEB', '#BFBFBF', '#FFCCC7', '#FFD8BF', '#FFF1B8', '#C0F7BE', '#B5F5EC', '#BAE7FF',
              '#D9D9D9', '#8C8C8C', '#FF7875', '#FF9C6E', '#FFD666', '#91DE64', '#5CDBD3', '#69C0FF',
            ]}
            onChangeComplete={this.handleChange}
            triangle="top-left"
          />
        );
      }
    };

    const yqcolorModal = (
      <div id="yqColor" style={styles.popover}>
        <div style={styles.cover} onClick={this.handleClose} />
        {renderColorPicker()}
      </div>
    );

    return (
      <div className="yq-color" style={{ position: 'relative' }}>
        <div style={styles.swatch} onClick={this.handleClick}>
          <div style={{ background: record?.get(name || 'color'), ...styles.color }} />
        </div>
        {this.state.addonAfter && <span style={{ verticalAlign: 'sub' }}>{record?.get(name || 'color')}</span>}
        {(!(type === 'text' || preview) && this.state.displayColorPicker) && (ReactDom.createPortal(yqcolorModal, document.getElementsByTagName('body')[0]))}
      </div>
    );
  }
}
export default YQColor;

/* externalize: YQColor */
