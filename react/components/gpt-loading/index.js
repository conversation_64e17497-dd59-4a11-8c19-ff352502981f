import React from 'react';
import styles from './GPTLoading.module.less';

/**
 * 智能生成动画
 * @returns {JSX.Element}
 * @constructor
 */
export default function GptLoading({ title, description, style = null, titleStyle = null, imageStyle = null }) {
  return (
    <div className={styles.wrap} style={style}>
      <div className={styles.imgWrap} style={imageStyle}>
        <img className={styles.img} src={`${window._env_.ICON_SERVER}/static/intelligent-loading-min.gif`} alt="loading" />
      </div>
      {title && (<div className={styles.title} style={titleStyle}>{title}</div>)}
      {description && (<div className={styles.description}>{description}</div>)}
    </div>
  );
}
