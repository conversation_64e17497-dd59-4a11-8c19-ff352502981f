export default ({ intl, initData }) => {
  const fileName = intl.formatMessage({ id: 'lcr.components.model.download.history.file', defaultMessage: '文件' });
  const fileSize = intl.formatMessage({ id: 'lcr.components.model.download.history.size', defaultMessage: '文件大小' });

  return {
    autoQuery: false,
    selection: 'multiple',
    autoCreate: false,
    paging: false,
    data: initData || [],
    fields: [
      { name: 'fileName', type: 'string', label: fileName },
      { name: 'fileKey', type: 'string' },
      { name: 'fileSize', type: 'string', label: fileSize },
    ],
    queryFields: [
      { name: 'fileName', type: 'string', label: fileName },
    ],
  };
};
