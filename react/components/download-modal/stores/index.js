import React, { createContext, useMemo, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import AttachmentDataSet from './AttachmentDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      children,
      attachments = [],
      intl,
      AppState: { currentMenuType: { tenantId } },
    } = props;

    function getFileName(fileKey) {
      const nameStrList = fileKey?.split('@');
      return nameStrList?.length ? nameStrList[nameStrList.length - 1] : fileKey;
    }

    /**
     * 处理附件数据，兼容单/多文件
     * @param attachmentList
     * @returns {Array}
     */
    function getFileList(attachmentList) {
      let fileList = [];
      attachmentList.map(attachment => {
        const fileKey = attachment;
        const fileSize = 0;
        try {
          if (typeof attachment === 'string') {
            const jsonData = JSON.parse(attachment);
            if (Array.isArray(jsonData)) {
              fileList = fileList.concat(getFileList(jsonData));
            } else if (typeof jsonData === 'object') {
              fileList.push({
                fileName: getFileName(jsonData.fileKey),
                fileKey: jsonData.fileKey,
                fileSize: jsonData.fileSize,
              });
            }
          } else if (typeof attachment === 'object') {
            fileList.push({
              fileName: getFileName(attachment.fileKey),
              fileKey: attachment.fileKey,
              fileSize: attachment.fileSize,
            });
          }
        } catch (e) {
          fileList.push({
            fileName: getFileName(fileKey),
            fileKey,
            fileSize,
          });
        }
        return attachment;
      });
      return fileList;
    }

    const initData = getFileList(attachments);
    const dataSet = useMemo(
      () => new DataSet(AttachmentDataSet({ intl, initData })),
      [],
    );

    const value = {
      ...props,
      dataSet,
      tenantId,
    };
    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
