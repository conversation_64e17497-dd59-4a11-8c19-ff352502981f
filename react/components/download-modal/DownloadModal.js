import React, { useContext, useState, useEffect } from 'react';
import FileSaver from 'file-saver';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { SelectBox, Table } from 'choerodon-ui/pro';
import { <PERSON><PERSON>hower, Button, TableHoverAction } from '@zknow/components';
import Stores from './stores';

import './index.less';

const { Option } = SelectBox;
const { Column } = Table;

const DraggableForm = () => {
  const { dataSet, intl, modal, tenantId, fileName } = useContext(Stores);
  const [type, setType] = useState('all');

  modal.handleOk(async () => {
    const fileKeys = dataSet.selected?.map(record => record.get('fileKey'));
    try {
      const res = await axios.post(
        `/hfle/yqc/v1/${tenantId}/files/batch-download/file-keys`,
        fileKeys,
        { responseType: 'arraybuffer' },
      );
      if (res && !res?.failed) {
        const blob = new Blob([res], { type: 'application/zip' });
        FileSaver.saveAs(blob, `${fileName}.zip`);
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  });

  useEffect(() => {
    if (dataSet) {
      dataSet.selectAll();
    }
  }, [dataSet]);

  function handleChange(value) {
    setType(value);
    if (value === 'all') {
      dataSet.selectAll();
    } else {
      dataSet.unSelectAll();
    }
  }

  function handleDownload(url, name) {
    FileSaver.saveAs(url, name);
  }

  function renderDownloadBtn({ record }) {
    return (
      <TableHoverAction
        record={record}
        actions={[
          {
            element: (
              <FileShower
                fileKey={record.get('fileKey')}
              >
                {({ src }) => (
                  <Button
                    onClick={() => handleDownload(src, record.get('fileName'))}
                    color="primary"
                    icon="Download"
                  >
                    {intl.formatMessage({ id: 'lcr.components.desc.import.click.download', defaultMessage: '点击下载' })}
                  </Button>
                )}
              </FileShower>
            ),
          },
        ]}
      />
    );
  }

  function renderSize({ record }) {
    const fileSize = record.get('fileSize');
    return fileSize / 1024 > 1024
      ? `${Number(fileSize / 1024 / 1024).toFixed(1)}MB`
      : `${Number(fileSize / 1024).toFixed(1)}KB`;
  }

  return (
    <div className="download-modal">
      <div className="download-modal-selectbox">
        <SelectBox onChange={handleChange} defaultValue="all">
          <Option value="all">{intl.formatMessage({ id: 'lcr.components.desc.download.all', defaultMessage: '下载所有附件' })}</Option>
          <Option value="select">{intl.formatMessage({ id: 'lcr.components.desc.download.select', defaultMessage: '下载部分附件' })}</Option>
        </SelectBox>
      </div>
      {type === 'select'
        ? (
          <Table
            dataSet={dataSet}
            style={{ height: 400 }}
            pristine
          >
            <Column name="fileName" />
            <Column name="fileSize" renderer={renderSize} />
            <Column width={50} renderer={renderDownloadBtn} />
          </Table>
        ) : null}
    </div>
  );
};

export default observer(DraggableForm);
