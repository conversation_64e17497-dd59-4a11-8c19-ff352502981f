import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { message, TextField, Form } from 'choerodon-ui/pro';
import { IconPicker, ExternalComponent, Title } from '@zknow/components';
import DragTag from '@/components/drag-tag';

function FilterContent(props) {
  const {
    AppState: { currentMenuType: { tenantId } },
    businessObjectId, record, intl,
  } = props;
  const [fieldTableData, setTableFieldData] = useState([]);
  const [condition, setCondition] = useState([]);
  const [order, setOrder] = useState([]);

  useEffect(() => {
    setCondition(JSON.parse(record?.get('condition') || '[]'));
    setOrder(record?.get('orderBy') ? JSON.parse(record?.get('orderBy')) : [{ field: '', order: 'ASC', id: uuidv4() }]);
  }, []);

  useEffect(() => {
    async function loadFields() {
      const res = await axios.get(`/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?conditionFlag=true`);
      if (res && res.failed) {
        message.error(res.message);
        setTableFieldData([]);
      } else {
        setTableFieldData(res);
      }
    }
    if (businessObjectId) {
      loadFields();
    }
  }, [businessObjectId]);

  return (
    <>
      <Form
        record={record}
        header={intl.formatMessage({ id: 'zknow.common.desc.basic.information', defaultMessage: '基本信息' })}
        labelLayout="horizontal"
        labelWidth={75}
        columns={2}
      >
        <TextField
          name="name"
          label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })}
        />
        <IconPicker
          name="icon"
          record={record}
          type={record?.get('searchIcon')}
          label={intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' })}
        />
      </Form>
      <Form
        record={record}
        header={intl.formatMessage({ id: 'lcr.components.desc.table.quick.filter', defaultMessage: '快速筛选' })}
        labelLayout="horizontal"
        labelWidth={75}
        columns={1}
      >
        <DragTag name="personalFilterEditor" record={record} />
        <DragTag name="personalColumnEditor" record={record} lock />
      </Form>
      {/* 高级筛选 */}
      <Title title={intl.formatMessage({ id: 'lcr.components.desc.table.advanced.filter', defaultMessage: '高级搜索' })} />
      <ExternalComponent
        system={{
          scope: 'itsm',
          module: 'YqCondition',
        }}
        conditionData={condition}
        tableId={businessObjectId}
        fieldTableData={fieldTableData}
        onChange={(data) => {
          setCondition(data);
          record.set('condition', JSON.stringify(data));
        }}
        sourceFrom="filter"
        isSider={false}
        isCascader
        conditionFlag
        canHideUser
        extraFieldsFlag
        hasVariableType={false}
        hasRecursiveList={['Select', 'Radio']}
      />
      {/* 字段排序 */}
      <Title className="mt-18" title={intl.formatMessage({ id: 'lcr.components.desc.table.field.sort', defaultMessage: '字段排序' })} />
      <ExternalComponent
        system={{
          scope: 'itsm',
          module: 'SortCondition',
        }}
        hiddenTitle
        conditionData={order}
        fieldTableData={fieldTableData}
        onChange={(data) => {
          setOrder(data);
          record.set('orderBy', JSON.stringify(data));
        }}
      />
    </>
  );
}

export default inject('AppState')(observer(FilterContent));
