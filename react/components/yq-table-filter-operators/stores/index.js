import React, { createContext, useMemo, useCallback } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import FilterDataSet from './FilterDataSet';

const Store = createContext();
const MENUDATASET = '__MENUDATASET__';
const OPTIONDATASET = '__OPTIONDATASET__';

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      dataSet,
      tableConfig,
      tableFilterFields,
      intl,
      columnDataSet,
    } = props;

    const getFieldsOptions = useCallback((key) => {
      const data = (tableConfig.toData() || {})?.[key];
  
      return new DataSet({
        data: data?.filter(i => i.code).map(i => {
          const columnData = columnDataSet.find(r => r.get('objectFieldPath') === i?.code);
          return { code: i.code, name: i.name || columnData?.get('label') };
        }) || [],
        fields: [
          { name: 'name', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
          { name: 'code', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
        ],
        paging: false,
      });
    }, []);

    // 筛选器值集
    const filterListDataSet = dataSet.getState(MENUDATASET);
    if (filterListDataSet && !filterListDataSet?.getField('personalFilterEditor')) {
      const filterFields = tableFilterFields
        .filter(v => v.code).slice()
        .filter(v => v.fuzzyFlag === undefined || v.quickFlag !== undefined);
      // 筛选器personalFilter编辑字段
      filterListDataSet.addField('personalFilterEditor', {
        label: intl.formatMessage({ id: 'lcr.components.desc.table.quick.filter', defaultMessage: '快速筛选' }),
        options: new DataSet({ 
          data: filterFields,
          fields: [
            { name: 'name', label: intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' }) },
            { name: 'code', label: intl.formatMessage({ id: 'zknow.common.model.code', defaultMessage: '编码' }) },
          ],
          paging: false,
        }),
        ignore: 'always',
        transformResponse: (_, data) => {
          const value = data.personalFilter;
          return value && JSON.parse(value).map(i => {
            return {
              code: i.fieldName,
              value: i.value,
            };
          });
        },
      });
      filterListDataSet.addField('personalColumnEditor', {
        label: intl.formatMessage({ id: 'lcr.components.desc.table.quick.filter.column', defaultMessage: '列表字段' }),
        options: getFieldsOptions('fields'),
        ignore: 'always',
        transformResponse: (_, data) => {
          const value = JSON.parse(data.personalColumn || '{}');
          return getFieldsOptions('fields')
            .map(i => ({
              code: i.get('code'),
              lock: value?.[i.get('code')]?.lock,
              sort: value?.[i.get('code')]?.sort,
            }))
            .filter(key => !value?.[key.code]?.hidden)
            .sort((a, b) => (a?.sort ?? Number.MAX_SAFE_INTEGER) - (b?.sort ?? Number.MAX_SAFE_INTEGER));
        },
      });
    }
    const prefixCls = 'yq-table';

    const saveOptions = useMemo(() => new DataSet({
      data: [
        { meaning: intl.formatMessage({ id: 'zknow.common.button.create', defaultMessage: '新建' }), value: 'NEW' },
        { meaning: intl.formatMessage({ id: 'lcr.components.desc.overwrite', defaultMessage: '覆盖已有筛选器' }), value: 'OVERWRITE' },
      ],
    }), []);

    const typeOptions = useMemo(() => new DataSet({
      data: [
        { meaning: intl.formatMessage({ id: 'lcr.components.desc.self', defaultMessage: '本人' }), value: 'SELF' },
        { meaning: intl.formatMessage({ id: 'lcr.components.desc.specific.user.group', defaultMessage: '特定人员组' }), value: 'GROUP' },
      ],
    }), []);

    const filterDataSet = useMemo(() => new DataSet(FilterDataSet({
      saveOptions, typeOptions, tenantId, intl,
    })), []);

    const value = {
      ...props,
      prefixCls,
      tenantId,
      dataSet,
      filterDataSet,
      filterListDataSet,
      MENUDATASET,
      OPTIONDATASET,
      getFieldsOptions,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  })
));
