import axios from 'axios';
import { message } from 'choerodon-ui/pro';

export default ({ saveOptions, typeOptions, tenantId, intl }) => {
  async function validator(name, code, record) {
    try {
      const result = await axios.get(`/lc/v1/${tenantId}/formFilters/check/${record.get('viewId')}?name=${name}`);
      if (result && !result.failed) {
        return true;
      } else {
        message.error(intl.formatMessage({ id: 'lcr.components.model.filter.name.unique', defaultMessage: '名称重复，请重新输入!' }));
        return intl.formatMessage({ id: 'lcr.components.model.filter.name.unique', defaultMessage: '名称重复，请重新输入!' });
      }
    } catch (e) {
      return false;
    }
  }

  return {
    paging: false,
    autoQuery: false,
    autoLocateFirst: true,
    fields: [
      { name: 'saveMethod', options: saveOptions, defaultValue: 'NEW' },
      { name: 'name', type: 'string', maxLength: 20, required: true },
      { name: 'icon', type: 'string', required: true },
      { name: 'type', options: typeOptions, defaultValue: 'SELF' },
      {
        name: 'groupId',
        dynamicProps: {
          required: ({ record }) => record.get('type') === 'GROUP',
        },
        type: 'object',
        lovCode: 'MY_GROUP',
        transformRequest: (value) => {
          return (value && value.id) || value;
        },
        transformResponse: (value, data) => {
          if (data.id) {
            return {
              id: value,
              name: data.name,
            };
          }
          return null;
        },
      },
    ],
    transport: {
      read: ({ data }) => {
        const { filterId } = data;
        return {
          url: `/lc/v1/${tenantId}/formFilters/portal/${filterId}`,
          method: 'get',
        };
      },
    },
  };
};
