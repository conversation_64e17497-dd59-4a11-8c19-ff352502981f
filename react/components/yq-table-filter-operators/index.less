@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.yqc-sidebar-action {
  &-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: .2rem;
    height: .2rem;
    background: #f2f3f5;
    color: #12274d;
    border-radius: .02rem;
    cursor: pointer;

    &:hover {
      color: @primary-color;
      background: @yq-primary-color-10;
    }
  }

  &-popup {
    padding: .04rem 0;
    width: 1.1rem;
    border-radius: .02rem;
    box-shadow: 0 2px 8px 0 rgba(129, 137, 153, .32);
  }

  &-ul {
    padding: 0;
    margin: 0;
    width: 100%;
    list-style: none;
    border-bottom: @border-width-base @border-style-base #e8e8e8;

    &:last-child {
      border-bottom: none;
    }
  }

  &-li {
    padding: 0;
    margin: 0;
    width: 100%;
    height: .32rem;
  }

  &-item {
    display: flex;
    align-items: center;
    padding: 0 .12rem;
    height: 100%;
    overflow: hidden;
    color: #12274d;
    cursor: pointer;

    &:hover {
      background: #f2f3f5;
    }

    &-text {
      margin-left: .08rem;
      font-size: .14rem;
    }
  }
}

.yqc-form-title {
  padding: 0 0 0 0.08rem;
  border-bottom: none;
  margin: 0.16rem 0 0.08rem;
  color: #12274d;
  font-weight: 500;
  font-size: .14rem;
  line-height: .14rem;
  border-left: 0.03rem solid @primary-color;
}

.mt-18 {
  margin-top: 18px;
}
