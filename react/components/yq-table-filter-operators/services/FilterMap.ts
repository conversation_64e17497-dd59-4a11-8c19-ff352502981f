import axios from 'axios';
/**
 * 创建筛选器
 * @param {string} tenantId 租户id
 * @param {string} data 筛选器
 */
function createFilter(tenantId: string, data: any) {
  return axios.post(`/lc/v1/${tenantId}/formFilters/createBySelf`, data);
}

/**
 * 更新筛选器
 * @param {string} tenantId 租户id
 * @param {string} data 筛选器
 */
function updateFilter(tenantId: string, data: any) {
  return axios.put(`/lc/v1/${tenantId}/formFilters/updateBySelf`, data);
}

/**
 * 删除筛选器
 * @param tenantId
 * @param id
 */
function deleteFilter(tenantId: string, id: string) {
  return axios({
    url: `/lc/v1/${tenantId}/formFilters/deleteBySelf/${id}`,
    method: 'delete',
  });
}

export { createFilter, updateFilter, deleteFilter };
