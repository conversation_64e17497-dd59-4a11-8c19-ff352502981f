import React from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { Form, SelectBox, TextField, Lov } from 'choerodon-ui/pro';
import { IconPicker } from '@zknow/components';

function FilterSave(props) {
  const { record, intl, onlyCreate, hasPermission } = props;

  return (
    <Form record={record} labelLayout="horizontal" labelWidth={90}>
      {onlyCreate || !hasPermission
        ? ''
        : (
          <SelectBox
            name="saveMethod"
            label={intl.formatMessage({ id: 'lcr.components.desc.save.method', defaultMessage: '另存方式' })}
          />
        )}
      {record.get('saveMethod') === 'NEW'
        ? (
          <TextField
            name="name"
            label={intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' })}
          />
        ) : null}
      {record.get('saveMethod') === 'NEW'
        ? (
          <IconPicker
            name="icon"
            record={record}
            type={record?.get('searchIcon')}
            label={intl.formatMessage({ id: 'zknow.common.model.icon', defaultMessage: '图标' })}
          />
        ) : null}
      {record.get('saveMethod') === 'NEW'
        ? (
          <SelectBox
            name="type"
            label={intl.formatMessage({ id: 'zknow.common.model.type', defaultMessage: '类型' })}
          />
        ) : null}
      {record.get('type') === 'GROUP'
        ? (
          <Lov
            name="groupId"
            label={intl.formatMessage({ id: 'lcr.components.desc.specific.user.group', defaultMessage: '特定人员组' })}
          />
        ) : null}
    </Form>
  );
}

export default inject('AppState')(observer(FilterSave));
