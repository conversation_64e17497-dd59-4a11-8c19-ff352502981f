import React, { useContext, useMemo, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { inject } from 'mobx-react';
import { Dropdown, Modal, message, Button } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { createFilter, updateFilter, deleteFilter } from './services';
import FilterContent from './FilterContent';
import FilterSave from './FilterSave';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();
const copyModalKey = Modal.key();

function stopPropagation(e) {
  e.stopPropagation();
}

function MenuItem({ onClick, name, itemId, icon }) {
  function handleClick(e) {
    if (typeof onClick === 'function') {
      onClick(itemId);
    }
  }
  return (
    <li className="yqc-sidebar-action-li">
      <span className="yqc-sidebar-action-item" onClick={handleClick}>
        <Icon type={icon} fontSize={16} />
        <span className="yqc-sidebar-action-item-text">{name}</span>
      </span>
    </li>
  );
}

function MenuGroup({ items }) {
  return (
    <ul className="yqc-sidebar-action-ul">
      {items.map(({ id, name, onClick, icon }) => (
        <MenuItem
          key={id}
          itemId={id}
          name={name}
          icon={icon}
          onClick={onClick}
        />
      ))}
    </ul>
  );
}

function Operators() {
  const context = useContext(Store);
  const {
    AppState: { userInfo: { personId } }, intl, filterId,
    businessObjectId, filterListDataSet, filterDataSet, addFlag,
    tableFilterNotice, saveCallback, dataSet, MENUDATASET, OPTIONDATASET, tenantId,
    setFilterId, getFieldsOptions,
  } = context;
  const modalStyle = useMemo(() => ({ width: 870 }), []);
  const copyModalStyle = useMemo(() => ({ width: 520 }), []);
  const filterRecord = filterListDataSet?.find(r => r.get('id') === filterId) || filterListDataSet?.get(0);

  useEffect(() => {
    const loadFilter = async () => {
      filterDataSet.setQueryParameter('filterId', filterId);
      await filterDataSet.query();
    };
    if (filterId) {
      loadFilter();
    }
  }, []);

  function updatePersonalFilter(currentFilter) {
    const currentData = currentFilter?.toData() || {};
    const personalFilter = JSON.parse(currentData?.personalFilter || '[]') || [];
    const personalFilterEditor = currentData.personalFilterEditor || [];
    const newPersonalFilter = personalFilterEditor.map(i => {
      const originItem = personalFilter.find(f => f.fieldName === i.code);
      return {
        fieldName: i.code,
        value: originItem?.value || '',
      };
    });
    const personalColumnEditor = currentData.personalColumnEditor || [];
    const newPersonalColumn = personalColumnEditor?.reduce((pre, cur, index) => {
      pre[cur.code] = {
        sort: index,
        lock: cur.lock,
      };
      return pre;
    }, {});
    if (personalColumnEditor?.length && newPersonalColumn) {
      getFieldsOptions('fields').forEach(i => {
        if (!newPersonalColumn[i.get('code')]) {
          newPersonalColumn[i.get('code')] = { hidden: true, lock: false };
        }
      });
    }
    currentFilter?.set('personalFilter', JSON.stringify(newPersonalFilter));
    currentFilter?.set('personalColumn', JSON.stringify(newPersonalColumn));
  }

  function editFilterModal() {
    filterRecord.init('condition', filterDataSet?.current?.get('condition'));
    Modal.open({
      children: <FilterContent businessObjectId={businessObjectId} record={filterRecord} intl={intl} />,
      key: modalKey,
      destroyOnClose: true,
      movable: true,
      title: intl.formatMessage({ id: 'lcr.components.desc.table.filter.edit', defaultMessage: '编辑筛选器' }),
      drawer: false,
      style: modalStyle,
      onOk: async () => {
        const validate = await filterRecord.validate();
        if (validate) {
          setFilterId('');
          try {
            const currentFilter = filterListDataSet.find(r => r.get('id') === filterId) || filterListDataSet?.get(0);
            updatePersonalFilter(currentFilter);
            const res = await updateFilter(tenantId, currentFilter.toData());
            if (res && res.failed) {
              message.error(res.message);
            } else {
              const filterData = await filterListDataSet.query();
              const optionDataSet = dataSet.getState('__OPTIONDATASET__');
              optionDataSet.loadData(filterData);
              dataSet.query();
              filterChangeCallback(tableFilterNotice);
              setFilterId(filterId);
              return true;
            }
          } catch (e) {
            setFilterId(filterId);
            return false;
          }
        }
        setFilterId(filterId);
        return false;
      },
      onCancel: () => {
        filterRecord.reset();
      },
      footer: (okBtn, cancelBtn) => {
        return [
          addFlag ? (
            <Button onClick={() => copyFilterModal('saveAs')} color="primary">
              {intl.formatMessage({ id: 'lcr.components.desc.save.as', defaultMessage: '另存为' })}
            </Button>
          ) : undefined,
          okBtn,
          cancelBtn,
        ];
      },
      okText: intl.formatMessage({ id: 'lcr.components.desc.overwrite.btn', defaultMessage: '保存覆盖' }),
    });
  }

  async function handleSave(copyRecord, tableFilterId, filterData, type) {
    const listDataSet = filterListDataSet || dataSet.getState(MENUDATASET);
    const optionDataSet = dataSet.getState(OPTIONDATASET);
    const menuDataSet = dataSet.getState(MENUDATASET);
    const currentFilter = type === 'copy' ? filterDataSet.find(r => r.get('id') === (tableFilterId || filterId)) || listDataSet.get(0) : listDataSet.find(r => r.get('id') === (tableFilterId || filterId)) || listDataSet.get(0);
    try {
      let res;
      setFilterId('');
      const condition = currentFilter.get('condition');
      const orderBy = currentFilter.get('orderBy');
      const saveMethod = copyRecord.get('saveMethod');
      if (saveMethod === 'NEW') {
        const groupId = copyRecord.get('groupId')?.id || copyRecord.get('groupId');
        const newData = {
          ...currentFilter.toData(),
          ...filterData,
          personalFilterEditor: undefined,
          id: null,
          name: copyRecord.get('name'),
          icon: copyRecord.get('icon'),
          userId: copyRecord.get('type') === 'SELF' ? personId : null,
          groupId: copyRecord.get('type') === 'GROUP' ? groupId : null,
          defaultFlag: false,
          condition,
          // 高级筛选设置了排序，则以高级筛选为主
          orderBy: dataSet.getQueryParameter('__orderBy') || orderBy,
          // 高级筛选，后端会将mergeCondition合并到condition中
          mergeCondition: dataSet.getQueryParameter('__condition'),
        };
        res = await createFilter(tenantId, newData);
      } else {
        res = await updateFilter(tenantId, {
          ...currentFilter.toData(),
          ...filterData,
          condition,
          // 高级筛选设置了排序，则以高级筛选为主
          orderBy: dataSet.getQueryParameter('__orderBy') || orderBy,
          // 高级筛选，后端会将mergeCondition合并到condition中
          mergeCondition: dataSet.getQueryParameter('__condition'),
        });
      }
      if (res && res.failed) {
        message.error(res.message);
      } else {
        filterChangeCallback(tableFilterNotice);
        const listData = await listDataSet.query();
        if (saveMethod === 'NEW') {
          if (optionDataSet) {
            optionDataSet.loadData(listData);
          }
          if (menuDataSet) {
            menuDataSet.loadData(listData);
          }
          dataSet.setQueryParameter('__condition', currentFilter.get('condition'));
          dataSet.setQueryParameter('__orderBy', currentFilter.get('orderBy'));
        }
      }
      setFilterId(filterId);
      dataSet.setState('__SEARCHTEXT__', '');
      dataSet.setQueryParameter('fuzzy_params_', '');
      dataSet.setState('__CONDITIONSTATUS__', 'sync');
      dataSet.query();
      return true;
    } catch (e) {
      setFilterId(filterId);
      message.error(intl.formatMessage({ id: 'lcr.components.desc.save.failed', defaultMessage: '保存失败' }));
    }
  }

  function copyFilterModal(type = 'copy', tableFilterId, filterData) {
    const listDataSet = filterListDataSet || dataSet.getState(MENUDATASET);
    const currentFilter = type === 'copy' ? filterDataSet?.current : listDataSet.find(r => r.get('id') === (tableFilterId || filterId)) || listDataSet.get(0);
    updatePersonalFilter(currentFilter);
    const filterName = type === 'save'
      ? currentFilter.get('name')
      : `${currentFilter.get('name')}-${intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' })}`;
    const copyRecord = filterDataSet.create({
      name: filterName,
      icon: currentFilter.get('icon'),
      viewId: currentFilter.get('viewId'),
    });
    const hasPermission = currentFilter?.get('userId') === personId || currentFilter?.get('groupOwnerId') === personId;
    Modal.open({
      children: (
        <FilterSave
          record={copyRecord}
          intl={intl}
          hasPermission={hasPermission}
          onlyCreate={type !== 'save'}
        />
      ),
      key: copyModalKey,
      destroyOnClose: true,
      movable: true,
      title: intl.formatMessage({ id: `table_filter_${type}` }),
      drawer: false,
      style: copyModalStyle,
      onOk: () => handleSave(copyRecord, tableFilterId, filterData, type),
    });
  }

  function deleteFilterModal() {
    Modal.confirm({
      title: intl.formatMessage({ id: 'lcr.components.desc.table.filter.delete', defaultMessage: '删除筛选器' }),
      children: (
        <div className="confirm-modal-content">
          {intl.formatMessage({ id: 'lcr.components.desc.table.filter.delete.tips', defaultMessage: '确定删除筛选器？删除后无法恢复。' })}
        </div>
      ),
      onOk: async () => {
        try {
          const res = await deleteFilter(tenantId, filterRecord.get('id'));
          if (res && res.failed) {
            message.error(res.message);
          } else {
            filterChangeCallback(tableFilterNotice);
            filterListDataSet.query();
          }
          return true;
        } catch (e) {
          message.error(intl.formatMessage({ id: 'lcr.components.desc.delete.failed', defaultMessage: '删除失败' }));
        }
      },
    });
  }

  if (saveCallback) {
    saveCallback(copyFilterModal);
  }

  const editBtn = {
    id: 'edit',
    icon: 'edit-two',
    name: intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' }),
    onClick: () => {
      editFilterModal();
    },
  };

  const deleteBtn = {
    id: 'delete',
    icon: 'delete',
    name: intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' }),
    onClick: () => {
      deleteFilterModal();
    },
  };

  const copyBtn = {
    id: 'copy',
    icon: 'copy',
    name: intl.formatMessage({ id: 'zknow.common.button.copy', defaultMessage: '复制' }),
    onClick: () => {
      copyFilterModal();
    },
  };

  let operators = [];
  const isOwner = filterRecord?.get('createdBy') === personId || filterRecord?.get('groupOwnerId') === personId;
  if (isOwner && addFlag) {
    operators = [[editBtn, copyBtn], [deleteBtn]];
  } else if (isOwner) {
    operators = [[editBtn], [deleteBtn]];
  } else if (addFlag) {
    operators = [[copyBtn]];
  }

  function getMenu() {
    return (
      <div className="yqc-sidebar-action-content">
        {operators.map(group => <MenuGroup key={uuidv4()} items={group} />)}
      </div>
    );
  }

  if (!operators.length) {
    return null;
  }

  return (
    <Dropdown
      overlay={getMenu()}
      trigger="click"
      popupClassName="yqc-sidebar-action-popup"
      getPopupContainer={() => document.getElementById('__TaskCenterSidebar')}
    >
      <div className="yqc-sidebar-action-btn" onClick={stopPropagation}>
        <Icon type="more-one" fontSize={18} />
      </div>
    </Dropdown>
  );
}

export default inject('AppState')(Operators);

function filterChangeCallback(fn) {
  if (typeof fn === 'function') {
    fn();
  }
}
