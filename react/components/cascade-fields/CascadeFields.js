import React, { useContext, useEffect } from 'react';
import { Cascader } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { StoreProvider } from './stores';
import Store from './stores';

import styles from './CascadeField.module.less';

const CascadeFields = () => {
  const {
    fieldDs,
    name,
    help,
    current,
    cascadeFields,
    defaultFlag,
  } = useContext(Store);

  useEffect(() => {
    if (current?.get(name) && !defaultFlag) {
      // 设置默认值
      setFiedValues(current.get(name), 'init');
    }
  }, []);

  function setFiedValues(value, func) {
    fieldDs?.current.set('field', value);
    if (cascadeFields?.length) {
      cascadeFields.forEach((item, index) => {
        current[func](item.value, value?.[index] || null);
      });
    }
  }

  function handleChange(value) {
    current.set(name, value);
    !defaultFlag && setFiedValues(value, 'set');
  }

  return (
    // eslint-disable-next-line react/no-unknown-property
    <div name={name} help={help}>
      <Cascader
        name="field"
        dataSet={fieldDs}
        onChange={handleChange}
        className={styles.cascader}
        disabled={current?.dataSet.getField(name)?.disabled}
      />
    </div>
  );
};

export default observer((props) => (
  <StoreProvider {...props}>
    <CascadeFields />
  </StoreProvider>
));
