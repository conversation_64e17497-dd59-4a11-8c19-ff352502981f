export interface ListItem {
  parentId:string,
  id: string,
  __level: number
}

export function getCascadeList(list:Array<ListItem> = []) {
  // 给选项集的父子层级添加__level参数
  list.forEach && list.forEach(item => {
    if (item.parentId === '0') { 
      item.__level = 0;
    } else {
      let level = 0;
      let current = item;
      while (current.parentId !== '0') {
        level += 1;
        // @ts-ignore
        // eslint-disable-next-line no-loop-func
        current = list.find(_item => _item.id === current.parentId) || { parentId: '0' };
      }
      item.__level = level;
    }
  });
  
  return list;
}
