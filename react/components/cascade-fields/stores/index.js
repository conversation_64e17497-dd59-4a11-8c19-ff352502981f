import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { FieldDataSet, OptionsDataSet } from './dataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(observer((props) => {
  const {
    intl,
    children,
    AppState: { currentMenuType: { organizationId: tenantId } },
    cascadeFields,
    lookupCode,
    current,
    name,
  } = props;

  const optionsDs = useMemo(
    () => new DataSet(OptionsDataSet({ tenantId, lookupCode, cascadeFields })),
    [lookupCode, cascadeFields?.length],
  );

  const fieldDs = useMemo(
    () => new DataSet(FieldDataSet({ current, name, optionsDs })),
    [lookupCode, cascadeFields?.length]
  );

  const value = {
    ...props,
    intl,
    tenantId,
    optionsDs,
    fieldDs,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
})));
