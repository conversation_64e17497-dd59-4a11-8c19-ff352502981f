import { getCascadeList } from '../utils';

export const OptionsDataSet = ({ tenantId, lookupCode, cascadeFields }) => {
  return {
    autoQuery: true,
    paging: false,
    selection: 'mutiple',
    parentField: 'parentId',
    idField: 'id',
    transport: {
      read: () => {
        return {
          url: lookupCode ? `/hpfm/v1/${tenantId === 0 ? '' : `${tenantId}/`}lookup/queryByCode/all?lookupTypeCode=${lookupCode}` : '',
          method: 'get',
          transformResponse: (data) => {
            let originData = [];
            if (typeof data === 'string') {
              try {
                originData = JSON.parse(data);
              } catch (e) {
                return data;
              }
            } else {
              originData = data;
            }
            originData = getCascadeList(originData)
              ?.filter?.(item => item.__level <= (cascadeFields || []).length - 1);
            return {
              content: originData.map && originData.map(_d => ({
                ..._d,
                meaning: _d.value,
                value: _d.code,
              })),
            };
          },
        };
      },
    },
  };
};

export const FieldDataSet = ({ optionsDs, current, name }) => {
  return {
    autoCreate: true,
    autoQuery: false,
    paging: true,
    fields: [
      {
        name: 'field',
        label: '',
        options: optionsDs,
        ignore: 'always',
        defaultValue: current?.get(name),
        dynamicProps: {
          required: () => {
            return current?.dataSet.getField(name)?.required;
          },
        },
      },
    ],
  };
};
