import React, { useMemo, useState } from 'react';
import { Select, DataSet } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { intlShape } from 'react-intl';
import Record from 'choerodon-ui/dataset/data-set/Record';

import styles from './CascadeField.module.less';

export interface CascadeFieldsConfigProps {
  name: string,
  record: Record,
  businessObjectId: string,
  AppState: any,
  intl: intlShape,
}

const CascadeFieldsConfig: React.FC<CascadeFieldsConfigProps> = inject('AppState')(observer((({
  record, name, businessObjectId, AppState: { currentMenuType: { tenantId } }, intl,
}) => {
  const lookupCode: { typeCode?: string } = record.get('widgetConfig.lookupCode') || { typeCode: '' };
  const [values, setValues] = useState();

  if (!record.get(name)) {
    record.init(name, []);
  }
  const options: DataSet = useMemo(() => new DataSet({
    autoQuery: true,
    paging: false,
    selection: false,
    transport: {
      read: () => ({
        url: `/lc/v1/${tenantId === 0 ? '' : `${tenantId}/`}object_fields/all/${businessObjectId}?includeWidgetType=Select`,
        method: 'get',
      }),
    },
  }), []);

  function handleAddField() {
    const _fields = record.get(name) || [];
    _fields.push({ key: _fields.length });
    record.set(name, _fields);
  }

  function handleChange(value, key) {
    const _fields = record.get(name);
    _fields.find(item => item.key === key).value = value;
    record.set(name, _fields);
    setValues(_fields.map(v => v.value).join(','));
  }

  function handleDelete() {
    const _fields = record.get(name);
    _fields.pop();
    record.set(name, _fields);
  }

  function getOptionDisabled(option) {
    return record.get(name).find(item => item.value === option.get('code'));
  }

  function getDeleteBtnDisabled(index) {
    return index !== ((record.get(name) || []).length - 1);
  }

  function getAddBtnDisabled() {
    return !lookupCode.typeCode;
  }

  const selectFields = useMemo(() => {
    return (record.get(name) || [])
      .sort((a, b) => a.key - b.key)
      .map((field, index) => (
        <div className={styles.field}>
          <Select onChange={(value) => handleChange(value, field.key)} value={field.value} key={`${name}_${field.key}`}>
            {options
              .filter(option => {
                return option.get('widgetConfig.lookupCode') === lookupCode.typeCode;
              })
              .map(option => (
                <Select.Option value={option.get('code')} disabled={getOptionDisabled(option)}>{option.get('name')}</Select.Option>
              ))}
          </Select>
          <Button
            icon="delete"
            onClick={handleDelete}
            disabled={getDeleteBtnDisabled(index)}
            className={styles.button}
          />
        </div>
      ));
  }, [values, options.length, record.get(name).length]);

  return (
    // @ts-ignore
    <div name={name}>
      {selectFields}
      <Button
        icon="plus"
        funcType="flat"
        onClick={handleAddField}
        disabled={getAddBtnDisabled()}
      >
        {intl.formatMessage({ id: 'lcr.components.desc.field.add', defaultMessage: '添加字段' })}
      </Button>
    </div>
  );
})));

export default CascadeFieldsConfig;

/* externalize: CascadeFieldsConfig */
