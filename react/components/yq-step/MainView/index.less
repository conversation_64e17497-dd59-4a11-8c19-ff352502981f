@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

@floatWidth: 20px;

.step-renderer {
  .flex-center {
    display: flex;
    align-items: center;
  }
  overflow-x: scroll;
  padding: 16px 16px 8px 16px;
  text-align: center;
  &-main {
    display: inline-flex;
    align-items: center;
  }
  &-item {
    min-width: calc(155px -  @floatWidth);
    .flex-center;
    &-last {
      min-width: unset;
    }
    .item-left {
      .flex-center;
      &-number {
        width: 24px;
        height: 24px;
        background: #fff;
        border: 1px solid #939aaa;
        font-size: 14px;
        font-weight: 500;
        color: #939aaa;
        line-height: 24px;
        border-radius: 50%;
        margin-right: 8px;
        .flex-center;
        justify-content: center;
        &-done {
          background: fade(#1ab335, 20%);
          color: #1ab335;
          border: solid 1px #1ab335;
        }
        &-inProgress {
          background: @primary-color;
          color: #fff;
          border: unset;
        }
      }
      &-name {
        font-size: 12px;
        font-weight: 400;
        color: #2b2d38;
        line-height: 20px;
        white-space: nowrap;
        flex: 1;
      }
    }
    // 线
    .item-line {
      height: 1px;
      background-color: #e8e8e8;
      // border: solid 1px #C0C6D2;
      margin: 0 12px;
      min-width: calc(75px - @floatWidth);
      &-last {
        display: none;
        width: 0;
      }
    }
  }
}

// 骨架屏
.step-skeleton {
  display: flex;
  align-items: center;
  padding: 8px 100px;
  &-avatar {
    width: 32px;
    margin-right: 20px;
  }
  &-input {
    height: 2px;
    width: calc(100% - 32px);
    span.c7n-skeleton-input.step-skeleton-input {
      height: 2px;
      width: calc(100% - 32px);
    }
  }
}
