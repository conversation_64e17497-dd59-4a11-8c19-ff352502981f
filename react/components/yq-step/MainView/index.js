/* eslint-disable react/no-danger */
import React, { useState, useCallback, useContext, useEffect } from 'react';
import { Icon } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import SkeletonRenderer from './SkeletonRenderer';
import Store from '../stores';
import './index.less';

const YqStep = observer(() => {
  const context = useContext(Store);
  const {
    prefixCls,
    stepsDataSet,
  } = context;

  const renderMain = () => {
    const steps = stepsDataSet.map((i, index) => {
      const item = i.toData();
      const itemClassName = classNames({
        [`${prefixCls}-item`]: true,
        [`${prefixCls}-item-last`]: index === stepsDataSet?.length - 1,
      });
      const numberClassName = classNames({
        'item-left-number': true,
        'item-left-number-notStarted': item.status === 'AFTER',
        'item-left-number-inProgress': item.status === 'CURRENT',
        'item-left-number-done': item.status === 'BEFORE',
      });
      const lineClassName = classNames({
        'item-line': true,
        'item-line-last': index === stepsDataSet?.length - 1,
      });
      return (
        <div className={itemClassName}>
          <div className="item-left">
            <div className={numberClassName}>{item.status === 'BEFORE' ? <Icon type="check" style={{ fontSize: '14px' }} /> : index + 1}</div>
            <div className="item-left-name">{item.name}</div>
          </div>
          <div className={lineClassName} />
        </div>
      );
    });
    return (
      <div className={`${prefixCls}`} id={`${prefixCls}`}>
        <div className={`${prefixCls}-main`}>
          {steps}
        </div>
      </div>
    );
  };

  if (stepsDataSet?.status !== 'ready') return <SkeletonRenderer />;

  return renderMain();
});

export default YqStep;
