/* eslint-disable react/no-danger */
import React from 'react';
import { observer } from 'mobx-react-lite';
import { Skeleton } from 'choerodon-ui/pro';
import './index.less';

const Skeleton<PERSON>enderer = observer(() => {
  return (
    <div className="step-skeleton">
      <Skeleton.Avatar className="step-skeleton-avatar" active size="default" shape="circle" />
      <Skeleton.Input className="step-skeleton-input" active />
      <Skeleton.Avatar className="step-skeleton-avatar" active size="default" shape="circle" />
      <Skeleton.Input className="step-skeleton-input" active />
      <Skeleton.Avatar className="step-skeleton-avatar" active size="default" shape="circle" />
    </div>
  );
});

export default SkeletonRenderer;
