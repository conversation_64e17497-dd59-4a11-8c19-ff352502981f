/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021年12月13日 11:37:41
 * @Description: 步骤条
 */

import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { StoreProvider } from './stores';
import MainView from './MainView';

export default inject('AppState')(observer((props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
)));

/* externalize: YqStep */
