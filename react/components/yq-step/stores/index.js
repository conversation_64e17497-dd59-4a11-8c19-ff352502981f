import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
      stepsDataSet,
    } = props;
    const prefixCls = 'step-renderer';

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      stepsDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },)
));
