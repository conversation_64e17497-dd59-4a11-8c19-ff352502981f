import React from 'react';
import { observer } from 'mobx-react-lite';
import { Button, Icon } from '@zknow/components';
import classNames from 'classnames';

import './index.less';

function ActionButton({ record, prefixCls, showIcon = false, onClick, disabled = false, loading = false, isFirst = false }) {
  if (!record) {
    return null;
  }

  const name = record.get('name');
  const icon = record.get('icon');
  const code = record.get('code');

  const buttonClass = classNames({
    [`${prefixCls}-button`]: true,
    [`${prefixCls}-button-hasIcon`]: showIcon,
  });

  async function handleClick() {
    if (typeof onClick === 'function') {
      await onClick(record);
    }
  }

  return (
    <Button
      disabled={disabled}
      loading={loading}
      funcType="raised"
      // color={isFirst ? 'primary' : 'secondary'}
      color={showIcon ? 'primary' : 'secondary'}
      icon={icon}
      id={code}
      onClick={handleClick}
      className={buttonClass}
    >
      <span className={`${prefixCls}-dropdown-name`}>{name}</span>
      {showIcon && (
        <Icon
          className={`${prefixCls}-dropdown-icon`}
          type="down"
          onClick={stopPropagation}
        />
      )}
    </Button>
  );
}

export default observer(ActionButton);

function stopPropagation(e) {
  e.stopPropagation();
}
