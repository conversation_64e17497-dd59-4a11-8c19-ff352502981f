@import "~choerodon-ui/lib/style/themes/default";

.itsm-ui-action {
  &-button-hasIcon {
    padding-right: 0;

    &:hover {
      .itsm-ui-action-dropdown-icon {
        border-color: @primary-color;
        border-left-color: rgba(255, 255, 255, .54) !important;
      }
    }
  }

  &-dropdown-name {
    display: inline-block;
    vertical-align: middle;
  }

  &-dropdown-icon {
    height: .32rem;
    width: .32rem;
    align-items: center;
    justify-content: center;
    border-left: solid 1px @minor-color;
    margin-left: 8px;
    padding: 0;
  }
}
