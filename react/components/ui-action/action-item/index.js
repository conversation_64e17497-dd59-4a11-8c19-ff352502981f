import React from 'react';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';

import './index.less';

function ActionItem({ record, prefixCls, onClick }) {
  const itemIcon = record.get('icon');
  const itemName = record.get('name');

  function handleClick() {
    if (typeof onClick === 'function') {
      onClick(record);
    }
  }

  return (
    <div
      className={`${prefixCls}-item`}
      onClick={handleClick}
    >
      <Icon style={{ marginRight: '8px' }} type={itemIcon} />
      {itemName}
    </div>
  );
}

export default observer(ActionItem);
