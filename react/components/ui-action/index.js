import React from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';
import { withErrorBoundary } from '@zknow/components';
import { StoreProvider } from './stores';
import UIAction from './UIAction';

const IntlUIAction = (props) => {
  return (
    <StoreProvider {...props}>
      <UIAction />
    </StoreProvider>
  );
};

export default inject('AppState')(
  formatterCollections({ code: ['lcr.components', 'lcr.renderer'] })(
    observer(withErrorBoundary(IntlUIAction, { fallback: <span /> }))
  )
);

/* externalize: UIAction */
