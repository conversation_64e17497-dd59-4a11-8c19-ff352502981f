export default ({ tenantId }) => {
  const urlPrefix = `/knowledge/v1/${tenantId}/kb_labels/knowledge`;
  return {
    autoQuery: true,
    selection: false,
    autoCreate: false,
    paging: true,
    pageSize: 25,
    dataKey: 'content',
    primaryKey: 'id',
    transport: {
      read: () => {
        return {
          url: `${urlPrefix}`,
          method: 'get',
        };
      },
      create: ({ data: [data] }) => ({
        url: urlPrefix,
        method: 'post',
        data,
      }),
    },
  };
};
