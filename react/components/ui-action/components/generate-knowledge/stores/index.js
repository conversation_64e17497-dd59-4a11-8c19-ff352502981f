import React, { createContext, useMemo, useState, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { withRouter } from 'react-router-dom';
import axios from 'axios';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import LabelDataSet from './LabelDataSet';
import GenKnoDataSet from './GenKnoDataSet';
import MetadataDataSet from '@/renderer/transform-knowledge/stores/MetadataDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = withRouter(
  inject('AppState', 'HeaderStore')(observer((props) => {
    const {
      children,
      AppState: {
        currentMenuType: { tenantId },
      },
      AppState,
      intl,
      ticketId,
      businessObjectId,
      HeaderStore: {
        getTenantConfig: { themeColor = '#2979ff' },
      },
      modal,
    } = props;
    const [defaultData, setDefaultData] = useState({});
    const [knowledgeFieldRequired, setKnowledgeFieldRequired] = useState([]);

    const getDataWithCache = async () => {
      const cache = AppState?.customConfig[`${businessObjectId}-${ticketId}`];
      if (cache) {
        return cache;
      }
      if (!ticketId || !tenantId) return;
      const res = await axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`);
      AppState?.setCustomConfig(businessObjectId, res);
      return res;
    };

    const initConfig = async () => {
      const res = await getDataWithCache();
      try {
        if (res?.knowledgeSettingVO) {
          res.knowledgeSettingVO.useDefaultValueFlag = JSON.parse(res.knowledgeSetting)?.useDefaultValueFlag;
        }
      } catch {
        //
      }
      const { knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag, knowledgeDefaultVO = {} } = res;
      setKnowledgeFieldRequired(knowledgeSettingVO?.required || []);
      // 是否使用默认值
      if (knowledgeSettingVO?.useDefaultValueFlag) {
        setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag });
      } else {
        // 如果不使用默认值，后端就会查询出用户上次输入的值，存放在knowledgeDefaultVO中
        setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO: knowledgeDefaultVO, transformFlag, modifyDefaultFlag });
      }
    };

    useEffect(() => {
      if (ticketId && businessObjectId) {
        initConfig(ticketId, businessObjectId);
      }
    }, [ticketId, businessObjectId]);

    const intlPrefix = 'lc.components.ticket.genKnowledge';
    const prefixCls = 'transform-knowledge';
    const intlText = (id) => intl.formatMessage({ id: `${intlPrefix}.${id}` });

    const labelDataSet = useMemo(() => new DataSet(LabelDataSet({ intl, tenantId })), []);

    const metadataDataSet = useMemo(() => new DataSet(MetadataDataSet()), []);
    const genKnoDataSet = useMemo(
      () => new DataSet(GenKnoDataSet({ intl, intlPrefix, tenantId, labelDataSet, knowledgeFieldRequired, metadataDataSet })),
      [tenantId, labelDataSet, knowledgeFieldRequired]
    );

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      intlText,
      labelDataSet,
      genKnoDataSet,
      defaultData,
      intl,
      themeColor,
      businessObjectId,
      modal,
      metadataDataSet,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  }))
);
