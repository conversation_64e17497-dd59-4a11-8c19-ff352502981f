import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, intlPrefix, labelDataSet, knowledgeFieldRequired, metadataDataSet, tenantId }) => {
  const yes = intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' });
  const no = intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' });
  const name = intl.formatMessage({ id: 'zknow.common.model.title', defaultMessage: '标题' });
  const space = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.space', defaultMessage: '选择空间' });
  const folder = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.folder', defaultMessage: '文件夹' });
  const label = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.label', defaultMessage: '标签' });
  const template = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.template', defaultMessage: '选择模板' });
  const templatePlaceholder = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.template.placeholder', defaultMessage: '选择知识模板' });
  const origin = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.origin', defaultMessage: '同源是否覆盖' });
  const originHelp = intl.formatMessage({ id: 'lcr.components.model.lc.components.ticket.gen.knowledge.origin.help', defaultMessage: '同一知识空间，同一文件夹, 同一单据视为同源知识' });

  const originOptions = new DataSet({
    data: [{ text: yes, value: true }, { text: no, value: false }],
  });

  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
        required: true,
        maxLength: 100,
      },
      {
        name: 'spaceIdLov',
        type: 'object',
        label: space,
        lovCode: 'KB_SPACE',
        required: true,
      },
      {
        name: 'folderIdLov',
        label: folder,
        lovCode: 'KB_FOLDER',
        type: 'object',
        textField: 'path',
        required: knowledgeFieldRequired.includes('folder'),
        dynamicProps: {
          disabled: ({ record }) => !record?.get('spaceIdLov'),
          lovPara: ({ record }) => {
            return { spaceId: record?.get('spaceIdLov')?.id };
          },
        },
      },
      {
        name: 'labels',
        label,
        type: 'string',
        multiple: true,
        textField: 'name',
        valueField: 'name',
        options: labelDataSet,
        required: knowledgeFieldRequired.includes('labels'),
      },
      {
        name: 'templateName',
        label: template,
        placeholder: templatePlaceholder,
        type: 'object',
        required: true,
      },
      {
        name: 'sourceCoveredFlag',
        label: origin,
        help: originHelp,
        type: 'boolean',
        textField: 'text',
        valueField: 'value',
        options: originOptions,
      },
    ],
    events: {
      update: async ({ record, name: _name, value }) => {
        if (_name === 'spaceIdLov') {
          record.set('folderIdLov', null);
          if (value?.id) {
            metadataDataSet.transport.read.url = `/knowledge/v1/${tenantId}/kb_metadata_fields/space/${value.id}/all?enabledFlag=true`;
            metadataDataSet.query();
          }
        }
      },
    },
  };
};
