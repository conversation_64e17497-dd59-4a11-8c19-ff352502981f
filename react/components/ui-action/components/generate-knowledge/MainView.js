import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import GenKnowledgeForm from '../../../../renderer/transform-knowledge/GenKnowledgeForm';
import Store from './stores';

export default observer(() => {
  const {
    genKnoDataSet,
    intlText,
    businessObjectId,
    tenantId,
    ticketId,
    history,
    intl,
    prefixCls,
    intlPrefix,
    themeColor,
    newPageFlag,
    modal,
    name,
    onCreateSuccessCallback = () => {},
    uiActionBeforeFlag,
    onCancelCreateSuccess = () => {},
    AppState,
    metadataDataSet,
  } = useContext(Store);
  const [defaultData, setDefaultData] = useState({});

  const getDataWithCache = async () => {
    const cache = AppState?.customConfig[`${businessObjectId}-${ticketId}`];
    if (cache) {
      return cache;
    }
    if (!ticketId || !tenantId) return;
    const res = await axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`);
    AppState?.setCustomConfig(businessObjectId, res);
    return res;
  };

  const initConfig = async () => {
    const res = await getDataWithCache();
    try {
      if (res?.knowledgeSettingVO) {
        res.knowledgeSettingVO.useDefaultValueFlag = JSON.parse(res.knowledgeSetting)?.useDefaultValueFlag;
      }
    } catch {
      //
    }
    const { knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag, knowledgeDefaultVO = {} } = res;
    // 是否使用默认值
    if (knowledgeSettingVO?.useDefaultValueFlag) {
      setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag });
    } else {
      // 如果不使用默认值，后端就会查询出用户上次输入的值，存放在knowledgeDefaultVO中
      setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO: knowledgeDefaultVO, transformFlag, modifyDefaultFlag });
    }
  };

  useEffect(() => {
    if (ticketId && businessObjectId) {
      initConfig(ticketId, businessObjectId);
    }
  }, [ticketId, businessObjectId]);

  useEffect(() => {
    if (genKnoDataSet) {
      const _record = genKnoDataSet?.create({ name });
      genKnoDataSet.current = _record;
      initDefaultData(_record);
    }
  }, [genKnoDataSet]);

  const initDefaultData = (_record) => {
    const {
      knowledgeSettingFlag,
      knowledgeSettingVO,
    } = defaultData;
    // 服务配置应用
    if (knowledgeSettingFlag) {
      try {
        const { labels, spaceId, spaceName, templateId, templateName, folderId, folderName, sourceCoveredFlag = true } = knowledgeSettingVO || {};

        labels && _record?.set('labels', labels);

        templateId && _record?.set('templateName', { name: templateName, id: templateId });

        spaceId && _record?.set('spaceIdLov', { name: spaceName, id: spaceId });

        folderId && _record?.set('folderIdLov', { path: folderName, name: folderName, id: folderId });

        _record?.setState('defaultSourceCoveredFlag', sourceCoveredFlag);
      } catch (e) {
        // console.log(e);
      }
    } else {
      _record?.setState('defaultSourceCoveredFlag', true);
    }
  };

  if (genKnoDataSet && genKnoDataSet.current) {
    return (
      <GenKnowledgeForm
        dataSet={genKnoDataSet}
        metadataDataSet={metadataDataSet}
        intlText={intlText}
        businessObjectId={businessObjectId}
        originBusinessObjectId={businessObjectId}
        tenantId={tenantId}
        ticketId={ticketId}
        history={history}
        intl={intl}
        prefixCls={prefixCls}
        intlPrefix={intlPrefix}
        themeColor={themeColor}
        newPageFlag={newPageFlag}
        modal={modal}
        onCreateSuccessCallback={onCreateSuccessCallback}
        uiActionBeforeFlag={uiActionBeforeFlag}
        onCancelCreateSuccess={onCancelCreateSuccess}
      />
    );
  }
});
