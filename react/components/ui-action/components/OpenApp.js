import { axios } from '@yqcloud/apps-master';
import { Modal } from 'choerodon-ui/pro';
import qs from 'qs';
import React, { useEffect } from 'react';
import styles from './OpenApp.module.less';

const useOpenAppModal = async (props) => {
  const { btn, tenantId, tenantCode, openAppId, ticketId, openFieldMappingId, intl, businessObjectCode } = props;
  return new Promise((resolve) => {
    const handleOpenAppModal = async () => {
      const c7nTriggerType = btn.get('c7nTriggerType');
      const message = await axios.get(`/itsm/v1/${tenantId}/c7n_ticket/mapping/${openAppId}/${ticketId}?businessObjectCode=${businessObjectCode}${openFieldMappingId ? `&fieldMappingId=${openFieldMappingId}` : ''}`);
      const userInfo = await axios.get(`/iam/v1/${tenantId}/choerodon_user/token/${openAppId}`);
      const thirdInfo = await axios.get(`/iam/yqc/${tenantId}/open_apps/${openAppId}`);
      if (!userInfo.failed) {
        const { userAccessToken } = userInfo;
        const { jsonConfig } = thirdInfo;
        const { domainUrl } = JSON.parse(jsonConfig);
        const params = {
          tenant_code: tenantCode,
          access_token: userAccessToken,
          token_type: 'bearer',
          issueId: ticketId,
          projectId: openFieldMappingId,
          yq_cloud_gateway: window._env_.API_HOST,
        };
        const queryParams = qs.stringify(params);

        let link;
        if (c7nTriggerType === 'CREATE_REQUIREMENT') { // 创建需求单
          link = `${domainUrl}/#/agile/outward/create/backlog?${queryParams}`;
        } else if (c7nTriggerType === 'CREATE_ISSUE') { // 创建工作项
          link = `${domainUrl}/#/agile/outward/create/agile?${queryParams}`;
        } else if (c7nTriggerType === 'ASSOCIATE_REQUIREMENT') { // 关联需求单
          link = `${domainUrl}/#/agile/outward/link/backlog?${queryParams}`;
        } else if (c7nTriggerType === 'ASSOCIATE_ISSUE') { // 关联工作项
          link = `${domainUrl}/#/agile/outward/link/issue?${queryParams}`;
        }
        Modal.open({
          className: styles.frame,
          children:
            (<C7nIframe link={link} message={message} resolve={resolve} />),
          footer: null,
          onCancel: () => {
            resolve(false);
          },
        });
      }
    };
    handleOpenAppModal();
  });
};

const C7nIframe = (props) => {
  const { link, message, modal, resolve } = props;
  useEffect(() => {
    window.addEventListener('message', messageListener, false);
    return () => {
      try {
        window.removeEventListener('message', messageListener, false);
      } catch { /**/ }
    };
  }, []);

  const messageListener = async (event) => {
    const frame = document.getElementById('third-party-application');
    if (event) {
      if (event.data === 'INITDONE' && message) {
        frame.contentWindow.postMessage({ choerodonMessage: message }, '*');
      }
      if (event?.data?.choerodonMessage?.action === 'close') {
        // 有猪齿鱼的消息, 类型是关闭
        resolve(false);
        modal?.close();
      }
      if (event?.data?.choerodonMessage?.type === 'success') {
        resolve(true);
        // 有猪齿鱼的消息, 创建成功了
        modal?.close();
      }
    }
  };

  return (
    <iframe
      id="third-party-application"
      className={styles.iframe}
      src={link}
      style={{ width: '100%', height: 'calc(100%)' }}
      title="editor"
      frameBorder="no"
      border={0}
    />
  );
};

export default useOpenAppModal;
