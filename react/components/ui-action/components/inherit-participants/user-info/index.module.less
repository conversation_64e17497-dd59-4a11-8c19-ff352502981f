@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.inherit-participants {
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-user {
    padding-top: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-first {
      padding-top: 0px;
    }
    .participants-card {
      justify-content: space-between;
      margin: 2px 0;
      padding: 0 16px;

      .flex-center;

      &:hover {
        background: #f2f3f5;
        .participants-card-right {
          display: flex;
        }
      }

      &-checkbox {
        margin-right: 12px;
      }
      &-left {
        .flex-center;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 400px;
      }
      &-avatar {
        user-select: none;
        .c7n-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        :global {
          .c7n-avatar-string {
            transform:scale(1) translateX(-50%) !important;
          }
        }
      }
      &-name {
        margin-right: 12px;
        margin-left: 8px;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #12274d;
        white-space: nowrap;
      }
      &-email {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(18, 39, 77, 0.65);
      }

      &-right {
        display: flex;
        align-items: center;
        justify-content: center;
        .action-close {
          font-size: 16px;
          cursor: pointer;
          color: #949EAF;
        }
      }
    }
  }
}