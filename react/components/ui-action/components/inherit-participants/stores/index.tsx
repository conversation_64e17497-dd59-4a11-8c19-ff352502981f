/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-10-19 11:02:29
 * @Description:
 */
import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
// @ts-ignore
import * as utils from '@/utils';
import { ExecutorDataSet } from './DataSet';

type ServiceAgreementProps = {
  intlPrefix: string,
  intl: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  children?: any,
  AppState: any,
  formDataSet: any,
  ticketId: string,
  viewRecord: any,
  businessObjectCode: string,
  executorDataset: any,
  modal?: any,
  selectedDataSet: any,
  onSuccessCallback?: any,
};

const Store = createContext<ServiceAgreementProps>({
  intlPrefix: '',
  intl: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  ticketId: '',
  viewRecord: '',
  businessObjectCode: '',
  executorDataset: '',
  selectedDataSet: '',
});

export default Store;

export const StoreProvider = inject('AppState')(
  observer((props: ServiceAgreementProps) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formDataSet,
      viewRecord,
      businessObjectCode,
      ticketId,
    } = props;
    const prefixCls = 'inherit-participants';
    const intlPrefix = 'lc.components.inheritParticipants';

    const selectedDataSet = useMemo(() => new DataSet({
      paging: false,
    }), []);

    const executorDataset = useMemo(
      () => new DataSet(
        ExecutorDataSet({
          tenantId,
          businessObjectCode,
          ticketId,
          selectedDataSet,
        })
      ),
      [ticketId]
    );

    const value = {
      ...props,
      formDataSet,
      prefixCls,
      tenantId,
      intlPrefix,
      ticketId,
      businessObjectCode,
      viewRecord,
      executorDataset,
      selectedDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
);
