// @ts-nocheck
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/interface';

const ExecutorDataSet = ({
  tenantId,
  businessObjectCode,
  ticketId,
  selectedDataSet,
}): DataSetProps => {
  return {
    autoQuery: true,
    paging: false,
    fields: [
      
    ],
    transport: {
      read: () => {
        if (!ticketId) return null;
        return {
          url: `/itsm/v1/${tenantId}/${businessObjectCode}/${ticketId}/participants`,
          method: 'get',
          transformResponse(response) {
            try {
              const res = JSON.parse(response);
              if (res?.failed) {
                return response;
              } else {
                const GROUP = res.group.map(item => {
                  item.name = item.participant;
                  item.type = 'GROUP';
                  item.primaryIdKey = item.id;
                  item.primaryKey = item.participantId;
                  item.id = item.participantId;
                  return item;
                });
                const USER = res.user.map(item => {
                  item.realName = item.participant;
                  item.real_name = item.participant;
                  item.name = item.participant;
                  item.type = 'USER';
                  item.primaryIdKey = item.id;
                  item.primaryKey = item.participantId;
                  item.id = item.participantId;
                  return item;
                });
  
                if (USER.length > 0 && GROUP.length > 0) {
                  selectedDataSet.loadData([...USER, { type: 'DIVIDER' }, ...GROUP]);
                  return [...USER, { type: 'DIVIDER' }, ...GROUP];
                }
                selectedDataSet.loadData([...USER, ...GROUP]);
                return [...USER, ...GROUP];
              }
            } catch (e) {
              return response;
            }
          },
        };
      },
    },
  };
};

export {
  ExecutorDataSet,
};
