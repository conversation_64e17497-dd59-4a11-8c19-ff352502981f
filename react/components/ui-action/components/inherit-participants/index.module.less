@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.inherit-participants {
  display: flex;
  &-header {
    display: flex;
    align-items: center;
    &-item {
      font-size: 14px;
      font-weight: 400;
      color: #12274D;
      line-height: 20px;
      margin-right: 24px;
    }
    border-bottom: 1px solid rgba(203,210,220,0.5);
    padding-bottom: 12px;
  }
  &-left {
    height: calc(100%);
    width: 50%;
    padding: 20px;
    border-right: rgba(203,210,220,0.5) 1px solid;
  }

  &-right {
    height: calc(100%);
    width: 50%;
    padding: 20px;
  }
  
  &-content {
    padding-top: 16px;
    .checked-all {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      &-text {
        font-size: 14px;
        font-weight: 400;
        color: #12274D;
        line-height: 22px;
        margin-left: 12px;
      }
    }
  }

  .participants-card-divider {
    margin: 14px 0px;
    border-bottom: 1px solid rgba(203, 210, 220, 0.5);
  }
}