/* eslint-disable react/no-danger */
import React, { useContext } from 'react';
// @ts-ignore
import { Empty, withErrorBoundary } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { CheckBox } from 'choerodon-ui/pro';
import _ from 'lodash';
import UserInfo from './user-info';
import GroupInfo from './group-info';
import Store from './stores';
import style from './index.module.less';

const MainView = withErrorBoundary(observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    intlPrefix,
    executorDataset,
    modal,
    selectedDataSet,
    onSuccessCallback = () => {},
  } = context;

  modal.handleOk(async () => {
    const data = selectedDataSet?.toData();
    onSuccessCallback(data);
  });

  const renderEmpty = (
    <div className={`${prefixCls}-content-empty`}>
      <Empty
        description={intl.formatMessage({ id: 'zknow.common.model.noData', defaultMessage: '暂无数据' })}
        style={{ padding: '20px 0 12px 0', paddingTop: '28px' }}
      />
    </div>
  );

  // 渲染每一项处理人处理组
  function renderCardItem(record, index, action) {
    if (!record) return null;
    const type = record.get('type');
    if (type === 'DIVIDER') {
      return <div className={style['participants-card-divider']} />;
    }
    if (type === 'USER') {
      return (
        <UserInfo
          record={record}
          isFirst={index === 0}
          selectedFlag={action === 'filter'}
          cancelFlag={action === 'cancel'}
          executorDataset={executorDataset}
          selectedDataSet={selectedDataSet}
        />
      );
    }
    if (type === 'GROUP') {
      return (
        <GroupInfo
          record={record}
          isFirst={index === 0}
          selectedFlag={action === 'filter'}
          cancelFlag={action === 'cancel'}
          executorDataset={executorDataset}
          selectedDataSet={selectedDataSet}
        />
      );
    }
  }

  function handleCheck(value) {
    if (value === false) {
      // 执行删除逻辑
      selectedDataSet.loadData([]);
      return true;
    }
    selectedDataSet.loadData(executorDataset?.toData());
    return true;
  }

  function calculationHasChecked() {
    const selectedIds = _.pullAll(selectedDataSet?.toData()?.map((i) => i.id), [undefined]);
    const executorIds = _.pullAll(executorDataset?.toData()?.map((i) => i.id), [undefined]);
    if (selectedIds?.length === 0) return false;
    if (selectedIds?.length === executorIds?.length) return true;
    return false;
  }

  function calculationAllIndeterminate() {
    const selectedIds = _.pullAll(selectedDataSet?.toData()?.map((i) => i.id), [undefined]);
    const executorIds = _.pullAll(executorDataset?.toData()?.map((i) => i.id), [undefined]);
    if (selectedIds?.length === executorIds?.length) {
      return false;
    } else if (selectedIds?.length > 0) {
      return true;
    }
    return false;
  }

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <div className={style[`${prefixCls}-left`]}>
          <div className={style[`${prefixCls}-header`]}>
            <div className={style[`${prefixCls}-header-item`]}>
              {intl.formatMessage({ id: 'lcr.components.desc.inheritParticipants.current.person', defaultMessage: '当前参与人：{count}' }, { count: executorDataset?.filter((i) => i.get('type') === 'USER')?.length || 0 })}
            </div>
            <div className={style[`${prefixCls}-header-item`]}>
              {intl.formatMessage({ id: 'lcr.components.desc.inheritParticipants.current.group', defaultMessage: '当前参与组：{count}' }, { count: executorDataset?.filter((i) => i.get('type') === 'GROUP')?.length || 0 })}
            </div>
          </div>
          <div className={style[`${prefixCls}-content`]}>
            {
              executorDataset.length > 0 && (
                <div className={style['checked-all']}>
                  {/* @ts-ignore */}
                  <CheckBox
                    checked={calculationHasChecked()}
                    onChange={handleCheck}
                    value="ALL"
                    indeterminate={calculationAllIndeterminate()}
                  />
                  <span className={style['checked-all-text']}>{intl.formatMessage({ id: 'zknow.common.button.selectAll', defaultMessage: '全选' })}</span>
                </div>
              )
            }
            {executorDataset.length > 0
              ? executorDataset?.map((record, index) => renderCardItem(record, index, 'filter'))
              : executorDataset.status === 'ready' && renderEmpty}
          </div>
        </div>
        <div className={style[`${prefixCls}-right`]}>
          <div className={style[`${prefixCls}-header`]}>
            <div className={style[`${prefixCls}-header-item`]}>
              {intl.formatMessage({ id: 'lcr.components.desc.inheritParticipants.inherit.person', defaultMessage: '继承参与人：{count}' }, { count: selectedDataSet?.filter((i) => i.get('type') === 'USER')?.length || 0 })}
            </div>
            <div className={style[`${prefixCls}-header-item`]}>
              {intl.formatMessage({ id: 'lcr.components.desc.inheritParticipants.inherit.group', defaultMessage: '继承参与组：{count}' }, { count: selectedDataSet?.filter((i) => i.get('type') === 'GROUP')?.length || 0 })}
            </div>
          </div>
          <div className={style[`${prefixCls}-content`]}>
            {selectedDataSet.length > 0
              ? selectedDataSet?.map((record, index) => renderCardItem(record, index, 'cancel'))
              : selectedDataSet.status === 'ready' && renderEmpty}
          </div>
        </div>
      </div>
    );
  };

  return renderMain();
}), { fallback: <span /> });

export default MainView;
