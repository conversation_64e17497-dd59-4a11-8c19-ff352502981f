/* eslint-disable react/no-danger */
import React, {
  useContext,
} from 'react';
// @ts-ignore
import { Icon } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { Popover } from 'choerodon-ui';
import { CheckBox } from 'choerodon-ui/pro';
import _ from 'lodash';
import PersonInGroup from './PersonInGroup';
import Store from '../stores';
import style from './index.module.less';

const GroupInfo = observer((props: any) => {
  const context = useContext(Store);
  const {
    prefixCls,
    intl,
    tenantId,
  } = context;

  const { 
    record,
    isFirst,
    selectedFlag = false,
    cancelFlag = false,
    selectedDataSet,
    executorDataset,
  } = props;
  
  // 渲染提示内容
  function renderCardTooltip() {
    if (!record) return null;
    return (
      <PersonInGroup 
        record={record}
        intl={intl}
        tenantId={tenantId}
      />
    );
  }

  function handleCheck(value, oldValue) {
    if (value === false) {
      // 执行删除逻辑
      calculationSelectedDataSet(oldValue, 'remove');
      return true;
    }
    calculationSelectedDataSet(value, 'add');
    return true;
  }

  function calculationSelectedDataSet(id, mode) {
    const checked = executorDataset?.toData()?.find((i) => i?.id === id);
    const currentData = selectedDataSet?.toData();
    if (mode === 'add') {
      currentData.push(checked);
    } else if (mode === 'remove') {
      _.remove(currentData, (i) => {
        return i.id === id;
      });
    }
    const USER: any = [];
    const GROUP: any = [];
    currentData.map((i) => {
      if (i.type === 'USER') {
        USER.push(i);
      } else if (i.type === 'GROUP') {
        GROUP.push(i);
      }
      return i;
    });
    if (USER.length > 0 && GROUP.length > 0) {
      selectedDataSet.loadData([...USER, { type: 'DIVIDER' }, ...GROUP]);
      return;
    }
    selectedDataSet.loadData([...USER, ...GROUP]);
  }

  function calculationHasChecked() {
    return selectedDataSet?.map(i => i.get('id'))?.includes(record?.get('id'));
  }

  const mainCls = classnames({
    [style[`${prefixCls}-group`]]: true,
    [style[`${prefixCls}-group-first`]]: isFirst,
  });

  const renderMain = () => {
    return (
      <div className={mainCls}>
        <div className={style['participants-card-left']}>
          {
            selectedFlag 
            && (
            // @ts-ignore
              <CheckBox 
                checked={calculationHasChecked()}
                className={style['participants-card-checkbox']}
                value={record?.get('id')}
                onChange={handleCheck}
              />
            )
          }
          <Popover
            // @ts-ignore 
            hidden={false}
            overlayClassName={classnames(style['participants-card-tooltip'], style['participants-card-tooltip-group'])}
            placement="bottomLeft"
            content={renderCardTooltip()}
            trigger="hover"
            mouseEnterDelay={0.5}
          >
            <span className={style['participants-card-avatar']}>
              <div className={style['participants-card-avatar-icon']}>
                <Icon type="PeoplesTwo" theme="outline" size="14" fill="#ffffff" />
              </div>
            </span>
            <span className={style['participants-card-name']}>{record.get('name')}</span>
          </Popover>
          <span className={style['participants-card-email']}>{record.get('email')}</span>
        </div>
        {
          cancelFlag && (
            <div className={style['participants-card-right']}>
              <Icon className={style['action-close']} type="close" onClick={() => calculationSelectedDataSet(record?.get('id'), 'remove')} />
            </div>
          )
        }
      </div>
    );
  };

  return renderMain();
});

export default GroupInfo;
