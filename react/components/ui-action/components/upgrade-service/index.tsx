import * as React from 'react';
import { withErrorBoundary, Icon } from '@zknow/components';
import { nomatch as Nomatch } from '@yqcloud/apps-master';
import { observer } from 'mobx-react-lite';
import { Spin, Tooltip, CheckBox, Form } from 'choerodon-ui/pro';
import axios from 'axios';
import qs from 'qs';
import styles from './UdmUpgrade.module.less';

function UpgradeService({ intl, tenantId, itemId, type, viewId, udmTenantId, udmItem, record, modalRef, dataSet, actionId, ticketId, udmUpgradeReplySyncDefaultFlag }) {
  const params = qs.stringify({
    tenantId,
    udmTenantId,
    udmViewType: 'modalView',
  });
  const [success, setSuccess] = React.useState(false);
  const [error, setError] = React.useState(false);

  React.useEffect(() => {
    (async () => {
      try {
        const res:any = await axios.post(`/itsm/v1/${tenantId}/shared/service/items/create/user?itemId=${itemId}`);
        if (res?.success) {
          setSuccess(true);
        }
      } catch (e) {
        setError(true);
      }
    })();
  }, [itemId, tenantId]);

  const handleMessage = async (event) => {
    if (event.origin !== window.location.origin) {
      return;
    }

    if (event?.data?.effect === 'YQ_UDM_COMMIT' && udmItem) {
      let mappingData = {};
      try {
        const formData = record.toData();
        const res: any = await axios.post(`/lc/v1/${tenantId}/lc_actions/upgrade/mapping?targetTenantId=${udmTenantId}&actionId=${actionId}&ticketId=${ticketId}`, formData);
        if (res && !res.failed) {
          mappingData = res;
        }
      } catch (e) {
        // 默认值未正确返回也没关系
      }
      // @ts-ignore
      window.frames.YQ_UDM_UPGRADE.postMessage({
        effect: 'YQ_UDM_UPGRADE',
        udmItem: { name: udmItem?.udmUpgradeItemName },
        mappingData,
        sourceTicketId: record.get('id'),
      }, window.location.origin);
    } else if (event?.data?.effect === 'YQ_UDM_UPGRADE_FINISH') {
      if (modalRef.current) {
        modalRef.current.close();
        // 刷新升级单列表
        const upgradeOrderDataSet = dataSet?.getState?.('subscribe')?.upgradeOrderDataSet;
        if (upgradeOrderDataSet) {
          const length = upgradeOrderDataSet?.length;
          setTimeout(async () => {
            await upgradeOrderDataSet.query();
            if (length === upgradeOrderDataSet?.length) {
              setTimeout(() => {
                upgradeOrderDataSet.query();
              }, 1000);
            }
          }, 1000);
        }
      } else {
        // cover 不住了
      }
    }
  };

  React.useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleChange = (value) => {
    dataSet.setState('UDM_SYNCHRONIZATION_REPLY', value);
  };

  const renderContent = () => {
    if (error) {
      return <Nomatch />;
    }

    return success ? (
      <div className={styles.content}>
        <iframe
          className={styles.iframe}
          title="udm-upgrade"
          name="YQ_UDM_UPGRADE"
          src={`${window.location.origin}/#/itsm/portal/service_catalog/udm/${type}/${viewId}/${itemId}?${params}`}
          frameBorder="0"
        />
        <div className={styles.bottom}>
          <div className={styles.bottomTitle}>
            <span className={styles.bottomTitleText}>{intl.formatMessage({ id: 'lcr.components.desc.upgrade.shared', defaultMessage: '选择同步信息' })}</span>
            <span className={styles.bottomTitleHelp}>
              <Tooltip title={intl.formatMessage({ id: 'lcr.components.desc.upgrade.shared.reply.help', defaultMessage: '选择当前单据的信息同步到升级服务单' })}>
                <Icon type="help" size="16" />
              </Tooltip>
            </span>
          </div>
          <div className={styles.bottomCheck}>
            <CheckBox
              name="synchronization"
              onChange={handleChange}
              defaultChecked={!!udmUpgradeReplySyncDefaultFlag}
            >
              {intl.formatMessage({ id: 'lcr.components.desc.upgrade.shared.reply', defaultMessage: '回复记录' })}
            </CheckBox>
          </div>
        </div>
      </div>
    ) : (
      <div className={styles.spin}>
        <Spin />
      </div>
    );
  };

  return (
    <div className={styles.wrapper}>
      {renderContent()}
    </div>
  );
}

export default withErrorBoundary(observer(UpgradeService));
