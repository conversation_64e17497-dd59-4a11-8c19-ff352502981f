/* eslint-disable no-prototype-builtins */
/* eslint-disable no-shadow */
import React, { useContext, useRef, useEffect, useState, lazy, Suspense } from 'react';
import { observer } from 'mobx-react-lite';
import { runInAction, toJS } from 'mobx';
import { Modal, Form, message, DataSet, Dropdown, Menu, TextField, Select, SelectBox, Lov, TextArea } from 'choerodon-ui/pro';
import { Result, Alert } from 'choerodon-ui';
import ValidationResult from 'choerodon-ui/pro/lib/validator/ValidationResult';
import { Button, ExternalComponent, Icon } from '@zknow/components';
import axios from 'axios';
import querystring from 'query-string';
import optionRender from '@/components/option-render';
import WorkHourForm from '@/renderer/work-hour/components/form/FormView';
import { getRichJson } from '@/renderer/utils/utils';
import GenerateKnowledge from '@/renderer/transform-knowledge';
import PhrasesText from '@/renderer/text-common-phrases';
import { durToDays, durToSec, getServiceItemDs, submitRequestContent } from '@/utils';
import InheritParticipants from './components/inherit-participants';
import WidgetField from '../widget-field';
import PageLoader from '../page-loader';
import { transformField } from '../page-loader/stores/DataSetManager';
import GenKnowledgeForm from './components/generate-knowledge';
import { getCascadeMap, validateMessage } from '../page-loader/utils';
import Store from './stores';
import ActionItem from './action-item';
import ActionButton from './action-button';
import SplitLine from './components/SplitLine';
import useModalBtnDisabled from './useModalBtnDisabled';
import openAppModal from './components/OpenApp';
import { updateFieldsConfig } from './utils';
import './index.less';

const modalKey = Modal.key();
const workHourModalKey = Modal.key();
const surveyModalKey = Modal.key();
const checkModalKey = Modal.key();
const viewModalKey = Modal.key();
const resultKey = Modal.key();
const inheritParticipantsModalKey = Modal.key();
const feedBackModalKey = Modal.key();
const udmModalKey = Modal.key();
const UDM_CANCEL_SIGN = 'CANCEL';

const UDMModalView = lazy(() => import('./components/upgrade-service'));

const { Item } = Menu;

const windowSizeMap = {
  LARGE: '1200px',
  MEDIUM: '800px',
  SMALL: '520px',
};

const APPROVE_ACTIONS = {
  AGREE: 'APPROVE',
  REJECT: 'REJECT',
  SIGN: 'ADD_SIGN,ADD_TASK_APPROVER',
  TRANSFER: 'TRANSFER',
  REBUT: 'RETURN',
  WITHDRAW: 'WITHDRAW',
};

function UIAction() {
  const {
    viewCode,
    actionDataSet,
    formDataSet,
    businessObjectFieldDataSet,
    dsFieldList,
    intl,
    mode,
    prefixCls,
    tenantId,
    formConfigRecord,
    surveyDataDataSet,
    listDataSet, // 列表的DS
    parentDataSet,
    pageRef,
    parentFormDataSet,
    approveDataSet,
    // 工时相关
    workLogsDataSet,
    replayListDataSet,
    actionListDataSet,
    viewDataSet,
    businessObjectCode,
    urlSuffix,
    instanceId,
    tabMenuDataSet,
    modal,
    onJumpNewPage,
    scPageRef,
    AppState,
    AppState: { userInfo },
    AppState: { currentMenuType: { code: tenantCode } },
    HeaderStore: {
      getTenantConfig: { udmEnableFlag }, // 上下游开关，控制服务单 【升级】动作
    },
    defaultData, // 工时配置
  } = useContext(Store);
  const surveyPageRef = useRef();
  const customViewRef = useRef();
  const createViewRef = useRef();
  const generateKnowledgeRef = useRef();
  const udmUpgradeModalRef = useRef(null);
  const workTimeModalRef = useRef(null);
  const [completeWorkHour, setCompleteWorkHour] = useState(false);
  const [completeInvestigation, setCompleteInvestigation] = useState(false);
  const [completeRelatedCreate, setCompleteRelatedCreate] = useState(false);
  const [completeGenerateKnowledge, setCompleteGenerateKnowledge] = useState(false);
  const [feedbackModal, setFeedbackModal] = useState(null);
  const [currentBtn, setCurrentBtn] = useState(null);
  const [currentSurveyObject, setCurrentSurveyObject] = useState(null);
  const [approveConfig, setApproveConfig] = useState({});

  // 监听低代码视图 富文本组件 附件上传状态
  const [setModal, setViewDs] = useModalBtnDisabled(null);

  const { viewId: urlViewId } = querystring.parse(window.location.href.split('?')?.[1] || '');

  useEffect(() => {
    if (formDataSet?.current) {
      actionDataSet.query();
      if (businessObjectFieldDataSet && businessObjectFieldDataSet.length === 0) {
        businessObjectFieldDataSet.query();
      }
    }
  }, [formDataSet?.current]);

  async function getConfig() {
    if (!instanceId) {
      return false;
    }
    const result = await axios.get(`/workflow/v1/${tenantId}/taskInstances/detail/${instanceId}`);
    if (result && !result.failed) {
      setApproveConfig(result);
    }
  }

  useEffect(() => {
    // 查询审批动作配置。【点动作在查询会影响体验，暂时先默认都查询】
    getConfig();
  }, []);

  async function getSurveyInstanceData(id) {
    surveyDataDataSet.setQueryParameter('id', id);
    await surveyDataDataSet.query();
    surveyDataDataSet.current = surveyDataDataSet.get(0);
    return surveyDataDataSet.current;
  }

  // 提交任务视图数据至请求项
  async function handleSubmitVariableData(data) {
    if (scPageRef?.current) {
      await submitRequestContent({
        tenantId,
        dataRef: scPageRef,
      });
      return;
    }
    const reqItemId = formDataSet?.current?.get('req_item_id');
    if (!reqItemId) {
      return;
    }
    const res = await axios.post(
      `/ticket/v1/${tenantId}/task/sc_req_item/${reqItemId}`,
      JSON.stringify({ jsonData: JSON.stringify(data) })
    );
    if (res?.failed) {
      message.error(res?.message);
    } else {
      return res;
    }
  }

  /**
   * 上下游服务单【升级】操作，打开跨租户提单
   * @param btn
   * @returns {Promise<boolean|string>}
   */
  function udmUpgrade(btn) {
    const udmItem = btn.get('consumerItem');
    if (!udmItem || !udmEnableFlag) {
      return Promise.resolve(true);
    }

    const udmViewId = udmItem?.viewId || udmItem?.variableViewId;
    const udmType = udmItem?.viewId ? 'NEW_RECORD' : 'RECORD';
    formDataSet?.setState?.('udmUpgradeDefault', false);
    return new Promise((resolve) => {
      udmUpgradeModalRef.current = Modal.open({
        key: udmModalKey,
        style: { width: 1200 },
        title: intl.formatMessage({ id: 'lcr.components.desc.service.upgrade', defaultMessage: '服务升级' }),
        children: (
          <Suspense fallback={<span />}>
            <UDMModalView
              intl={intl}
              tenantId={tenantId}
              itemId={udmItem?.providerItemId}
              type={udmType}
              viewId={udmViewId}
              udmTenantId={udmItem?.providerTenantId}
              udmItem={udmItem}
              record={formDataSet?.current}
              dataSet={formDataSet}
              modalRef={udmUpgradeModalRef}
              actionId={btn.get('id')}
              ticketId={formDataSet?.current?.get('id')}
              udmUpgradeReplySyncDefaultFlag={btn?.get('udmUpgradeReplySyncDefaultFlag')}
            />
          </Suspense>
        ),
        onOk: () => {
          window.frames.YQ_UDM_UPGRADE.postMessage({
            effect: 'YQ_UDM_UPGRADE_SUBMIT',
            syncReply: !!formDataSet.getState('UDM_SYNCHRONIZATION_REPLY'),
          }, window.location.origin);
          return false;
        },
        onCancel: () => {
          resolve(UDM_CANCEL_SIGN);
        },
        afterClose: () => {
          // 无论确认取消还是关闭，都不会影响后续操作
          formDataSet.setState('UDM_SYNCHRONIZATION_REPLY', false);
          resolve(true);
          udmUpgradeModalRef.current = null;
        },
      });
    });
  }

  function aiModalConfirm(aiPrompt) {
    const fieldList = [];
    Object.keys(aiPrompt).map((fieldCode) => {
      fieldList.push({ field: fieldCode });
    });
    if (fieldList.length) {
      const aiDataSet = getActionFormDataSet(fieldList);
      fieldList.map((fieldConfig) => {
        aiDataSet.current.set(fieldConfig.field, aiPrompt[fieldConfig.field]);
      });

      Modal.open({
        title: intl.formatMessage({ id: 'lcr.components.desc.ai.generate.confirm', defaultMessage: '确认智能生成结果' }),
        children: (
          <Form
            labelWidth={120}
            record={aiDataSet.current}
            dataSet={aiDataSet}
            className="lc-model-detail-form"
          >
            {fieldList.map((field, index) => renderField(field, aiDataSet, index))}
          </Form>
        ),
        key: modalKey,
        drawer: false,
        style: { width: '800px', maxHeight: '400px' },
        className: `${prefixCls}-modal`,
        destroyOnClose: true,
        onOk: async () => {
          fieldList.map((fieldConfig) => {
            formDataSet.current.set(fieldConfig.field, aiDataSet.current.get(fieldConfig.field));
          });
          await formDataSet.submit();
          await formDataSet.query();
        },
      });
    }
  }

  /**
   * 动作执行
   * @param id
   * @param actionFormDataSet
   * @param fieldList
   * @param btn 动作本身信息
   * @param modelFormDataSet
   * @param originSurveyObject
   * @returns {Promise<unknown>}
   */
  async function runAction(runProps) {
    const {
      id, actionFormDataSet, fieldList, btn, modelFormDataSet = {},
      surveyObject: originSurveyObject, approveRecord, aiPrompt,
    } = runProps;
    const record = formDataSet?.current;
    // 临时处理：将单据id存下来，
    const __ticketId = record?.get('id');
    let surveyObject = originSurveyObject;

    // 本来 new Promise 的执行函数不应该是 async function
    // 但是下面这个 promise 本身不期望也不会 reject
    // eslint-disable-next-line no-async-promise-executor
    const runPromise = new Promise(async (resolve) => {
      record?.set('_action_id', id);
      // 校验规则指定模式下展示字段生成的 dataSet
      if (actionFormDataSet?.current?.validate) {
        const result = await actionFormDataSet?.current?.validate(true);
        if (result) {
          const actionFormData = actionFormDataSet?.current?.toData();
          runInAction(() => {
            fieldList?.map((item) => {
              const fieldCode = item.field;
              if (fieldCode && actionFormData?.hasOwnProperty(fieldCode)) {
                const { widgetType, widgetConfig } = dsFieldList?.find(dsFieldItem => dsFieldItem.code === fieldCode) || {};
                const { durationUnit, durationMode } = widgetConfig || {};
                if (widgetType === 'Duration') {
                  record.set(fieldCode, durToDays(actionFormData[fieldCode], durationUnit, durationMode));
                } else {
                  record.set(fieldCode, actionFormData[fieldCode]);
                }
              }
              return item;
            });
          });
        } else {
          validateMessage(actionFormDataSet, intl);
          // message.error(intl.formatMessage({ id: 'zknow.common.validate.failed', defaultMessage: '字段校验未通过，请修改后重新提交' }));
          resolve(false);
          formDataSet.setState('prohibited', false);
          return false;
        }
      }
      const { transferDataSet, isReply } = customViewRef?.current || {};

      // 校验转交数据
      if (isReply && transferDataSet && transferDataSet?.validate) {
        if (!await transferDataSet?.validate()) {
          resolve(false);
          return false;
        }
      }

      // 合并弹窗内字段
      runInAction(() => {
        // 客户提出：
        // 1、清空表单后不保存
        // 2、点开动作弹窗保存（弹窗有1中清空字段，因为1中表单清空没保存，所以弹窗还有值
        // 3、提交动作，字段就被更新为空了
        const modalFields = customViewRef?.current?.pageRecord?.get('jsonData.datasets')?.find((item) => item.tag === 'Form')?.fields;
        const modalRecord = customViewRef?.current?.formDataSet?.current;
        if (modalRecord && modalFields && modalFields.length > 0) {
          modalFields.forEach((field) => {
            // 使用init 避免触发ds的update事件导致值丢失
            let value;
            if (modalRecord.get(field.code) === 0) {
              value = 0;
            } else if (modalRecord.get(field.code)) {
              value = modalRecord.get(field.code);
            } else {
              value = null;
            }
            record.init(field.code, value);
          });
        }
        // 更新弹窗中修改的字段
        if (Object.keys(modelFormDataSet).length > 0) {
          Object.keys(modelFormDataSet).forEach((fieldCode) => {
            // 使用init 避免触发ds的update事件导致值丢失
            let value;
            if (modelFormDataSet[fieldCode] === 0) {
              value = 0;
            } else if (modelFormDataSet[fieldCode]) {
              value = modelFormDataSet[fieldCode];
            } else {
              value = null;
            }
            record.init(fieldCode, value);
          });
        }
      });

      const assessmentFlag = btn.get('assessmentFlag');
      const assessmentExecuteType = btn.get('assessmentExecuteType');
      const workTimeFlag = btn.get('workTimeFlag');
      const relatedTicketWindowFlag = btn.get('relatedTicketWindowFlag');
      const workTimeExecuteType = btn.get('workTimeExecuteType');
      const relatedTicketExecuteType = btn.get('relatedTicketExecuteType');
      const popupWindowFlag = btn.get('popupWindowFlag');
      const windowType = btn.get('windowType');
      const generateKnowledgeFlag = btn.get('generateKnowledgeFlag');
      const generateKnowledgeExecuteType = btn.get('generateKnowledgeExecuteType');
      const assessmentId = btn.get('assessmentId');
      const isApprove = btn.get('actionType') === 'APPROVAL'; // 动作类型
      const approveType = btn.get('actionApprovalType'); // 审批类型
      const aiEnabledFlag = btn.get('aiEnabledFlag'); // 开启智能生成
      const openAppId = btn.get('openAppId'); // 三方创建应用
      const openFieldMappingId = btn.get('openFieldMappingId');

      const hasSurvey = assessmentFlag && surveyObject && surveyObject !== 'NO_PERMISSION';
      /**
       * 动作执行前调查
       */
      const isBeforeSurvey = hasSurvey && assessmentExecuteType === 'BEFORE';
      /**
       * 动作执行后调查
       */
      const isAfterSurvey = hasSurvey && assessmentExecuteType === 'AFTER';
      /**
       * 动作执行前添加工时
       */
      const isBeforeWorkHour = workTimeFlag && workTimeExecuteType === 'BEFORE' && defaultData?.workingTimeSettingFlag;
      /**
       *  动作执行后添加工时
       */
      const isAfterWorkHour = workTimeFlag && workTimeExecuteType === 'AFTER' && defaultData?.workingTimeSettingFlag;
      /**
       *  动作执行前关联创建
       */
      const isBeforeCreateRelatedTicket = relatedTicketWindowFlag && relatedTicketExecuteType === 'BEFORE';
      /**
       *  动作执行后关联创建
       */
      const isAfterCreateRelatedTicket = relatedTicketWindowFlag && relatedTicketExecuteType === 'AFTER';
      // 动作执行后关联知识
      const isAfterGenerateKnowledge = generateKnowledgeFlag && generateKnowledgeExecuteType === 'AFTER';
      // 动作执行前关联知识
      const isBeforeGenerateKnowledge = generateKnowledgeFlag && generateKnowledgeExecuteType === 'BEFORE';

      if (assessmentFlag && isAfterSurvey) {
        surveyObject = await generateSurvey({ id: assessmentId, actionId: id });
      }

      if (openAppId) { // 三方创建应用
        const res = await openAppModal({ intl, btn, tenantId, tenantCode, ticketId: __ticketId, openFieldMappingId, openAppId, businessObjectCode });
        formDataSet.setState('prohibited', false);
        if (!res) {
          message.error(intl.formatMessage({ id: 'lcr.components.desc.open.app.run.failed', defaultMessage: '三方应用创建未执行成功' }));
          resolve(false);
          return false;
        }
      }

      // 执行动作前校验👇
      if (isBeforeSurvey) {
        const surveyFormDataSet = surveyPageRef.current?.formDataSet;
        const validate = await surveyFormDataSet.current.validate(true);
        if (!validate) {
          formDataSet.setState('prohibited', false);
          resolve(false);
          return false;
        }
      }
      if (isBeforeWorkHour) {
        const workHourRecord = workTimeModalRef?.current?.dataSet?.current;
        const validate = await workHourRecord?.validate(true);
        const duration = workHourRecord?.get('duration');
        const durationMinutes = durToSec(duration, ['days', 'hours', 'minutes', 'seconds'], 'minutes');
        if (typeof durationMinutes === 'number' && durationMinutes > (2 ** 31 - 1)) {
          message.error(intl.formatMessage({ id: 'lcr.renderer.desc.duration.limit', defaultMessage: '工时超出最大值' }));
          formDataSet.setState('prohibited', false);
          resolve(false);
          return false;
        }

        if (!validate) {
          formDataSet.setState('prohibited', false);
          resolve(false);
          return false;
        }
      }
      if (isBeforeCreateRelatedTicket) {
        const res = await handleRelatedTicketWindow(btn, __ticketId, 'BEFORE');
        if (!res) {
          formDataSet.setState('prohibited', false);
          resolve(false);
          return false;
        }
      }

      if (isBeforeGenerateKnowledge) {
        const res = await handleOpenGenerateKnowlledgeModal({ beforeFlag: true, popFlag: popupWindowFlag || assessmentFlag || workTimeFlag, btn });
        if (!res) {
          formDataSet.setState('prohibited', false);
          resolve(false);
          return false;
        }
      }

      // 固定逻辑：审批动作，将审批意见字段也存到单据中
      if (isApprove && approveRecord && record) {
        record.set('remark', approveRecord.get('remark'));
      }

      /**
       * 如果配置了服务单升级，需要等待升级
       * 升级的操作其实就是跨租户提个单（udm）
       * 无论升级是否成功，都不会影响后续操作，不过这里需要异步等待一下
       */
      const res = await udmUpgrade(btn);
      if (res === UDM_CANCEL_SIGN) {
        formDataSet.setState('prohibited', false);
        resolve(false);
        return false;
      }
      try {
        const transferRecord = transferDataSet?.get?.(0);
        if (transferRecord) {
          /**
           * 把转交组件里的处理人处理组拿过来,覆盖到formDataSet上,不然如果不选处理人,就会用单据上的处理人传给后端
           */
          formDataSet.current.set({
            assignment_group_id: transferRecord?.get('assignment_group_id') || transferRecord.get('lov_assignment_group_id.id'),
            'assignment_group_id:name': transferRecord?.get('assignment_group_id:name') || transferRecord.get('lov_assignment_group_id.name'),
            assignee_person_id: transferRecord?.get('assignee_person_id') || transferRecord.get('lov_assignee_person_id.id'),
            'assignee_person_id:real_name': transferRecord?.get('assignee_person_id:real_name') || transferRecord.get('lov_assignee_person_id.real_name'),
          });
        }
        const res = await formDataSet.submit();
        if (res === false) {
          // 字段校验不通过时，需要抛出 message
          const errors = formDataSet.getValidationErrors();
          if (errors?.length) {
            message.error(intl.formatMessage({ id: 'zknow.common.validate.failed', defaultMessage: '字段校验未通过，请修改后重新提交' }));
            record.reset();
          }
        }
        formDataSet.setState('prohibited', false);
        resolve(true);

        if (res && !res?.failed) {
          if (isApprove && approveRecord) {
            const approveRes = await submitApprove({ approveType, approveRecord });
            if (!approveRes) {
              return false;
            }
            // 审批后重新查询配置
            getConfig();
          }
          // 是否需要添加回复
          if (isReply && record?.get('transfer_desc')) {
            const { businessObjectId, businessObjectCode } = formConfigRecord?.toData();
            const { id: taskId, transfer_desc: content, customer_hidden: visibleCallerFlag } = record?.toData() || {};
            const htmlObj = getRichJson(content) || {};
            const { attachments } = htmlObj || {};
            const data = {
              businessObjectId,
              ticketId: taskId,
              journalVO: {
                content,
                visibleCallerFlag,
                taskId,
                businessObjectCode,
                isPortal: false,
                attachments: attachments ? JSON.stringify(attachments) : '[]',
                sysCdcFlag: false, // 转交事件单时，填写的回复不需要单独发送站内信邮件等信息
                transferFlag: true, // 转交信息转为回复，添加标识
              },
              changeWorkingTimeDTO: {},
              journalCcDTOList: [],
            };
            const replyRes = await handleReplySubmit(data);
            if (!replyRes) {
              return false;
            }
          }
          // 执行动作后提交👇, 执行前储存数据，执行后提交数据
          if (isBeforeSurvey) {
            const surveyRes = await handleSurveySubmit({ surveyObject });
            if (!surveyRes) {
              return false;
            }
          }
          if (isBeforeWorkHour) {
            const workHourRes = await handleWorkHourSubmit({ actionRecord: btn, displayFeedMsg: false });
            if (!workHourRes) {
              return false;
            }
          }
          if (isBeforeCreateRelatedTicket) {
            const relationActionRelationship = btn?.get('relationActionRelationship');
            const _data = relationActionRelationship?.[0];
            const relationId = _data?.id || '';
            const view = {
              viewId: _data?.relatedTicketViewId,
              relationId,
            };
            const beforeInheritParticipantsRes = formDataSet?.getState('beforeInheritParticipantsRes');
            const createRelatedTicketRes = await handleSubmitView(() => { }, view, beforeInheritParticipantsRes);
            if (createRelatedTicketRes && !createRelatedTicketRes?.failed) {
              // 单据提交成功反馈
              await handleOpenResult(createRelatedTicketRes, _data?.feedbackViewId);
            } else {
              createRelatedTicketRes?.message && message.error(createRelatedTicketRes?.message);
            }
          }
          if (parentDataSet) {
            parentDataSet.query();
          }
          // 这里多次保存调用父级刷新，会导致内存巨量增大
          if (parentFormDataSet && parentFormDataSet?.current?.get('id')) {
            parentFormDataSet.query();
          }
          if (pageRef) {
            let requests = [];
            if (pageRef.current?.formTableDsList?.length) {
              requests = pageRef.current?.formTableDsList.map(ds => ds?.query());
            }
            await Promise.all(requests);
          }
          await formDataSet.query();
          // 如果弹窗为服务项，则将任务视图数据提交至请求项
          if (popupWindowFlag && windowType === 'SC_ITEM_VIEW') {
            await handleSubmitVariableData(customViewRef?.current?.formDataSet?.toData()[0] || {});
          }
          if (listDataSet) listDataSet.query();
          formDataSet?.current?.setState(
            'approvalHistoryDynamicRefreshCount',
            formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount') + 1 || new Date().getTime(),
          );
          // 动作执行完成后，没有后续的弹窗操作
          if (!isAfterSurvey && !isAfterWorkHour) {
            handleGotoListPage(btn);
          }
          // 执行后的弹框(工时、关联创建、生成知识、调查)需要整合
          const afterExecuteArr = [isAfterCreateRelatedTicket, isAfterSurvey, isAfterWorkHour, isAfterGenerateKnowledge];
          const afterExecuteNum = afterExecuteArr.reduce((p, c) => {
            if (c) { p += 1; }
            return p;
          }, 0);
          if (afterExecuteNum >= 2) {
            handleOpenAfterExecuteFeedback(btn, surveyObject);
          } else {
            // 动作关联创建弹窗处理
            if (isAfterCreateRelatedTicket) await handleRelatedTicketWindow(btn, __ticketId, 'AFTER');
            // 工时 、调查
            if (isAfterWorkHour && isAfterSurvey) {
              openWorkHourModal({ actionRecord: btn, next: () => openSurveyModal({ btn, surveyObject }) });
            } else if (isAfterSurvey) {
              openSurveyModal({ btn, surveyObject });
            } else if (isAfterWorkHour) {
              openWorkHourModal({ actionRecord: btn, isAfterSurvey });
            } else if (isAfterGenerateKnowledge) {
              handleOpenGenerateKnowlledgeModal(btn);
            }
          }
          // 如果配置了智能生成但未配置弹窗，需要在动作执行后弹窗让用户确认自动填充的字段是否保存
          if (aiEnabledFlag && aiPrompt) {
            aiModalConfirm(aiPrompt, btn);
          }
        }
      } catch {
        resolve(false);
        return false;
      }
    });
    runPromise.then((value) => {
      // 动作更新需要立即刷新 sla【特性 yq-9479】
      if (formDataSet?.getState?.('_slaDataSet')) {
        formDataSet.getState('_slaDataSet').query();
      }
      return value;
    });
    return runPromise;
  }

  /**
   * 初始化工时表单默认值
   * FIXME: 历史代码，有待优化，非我所写，搬运至此，实属无奈。
   */
  async function initDefaultData(_record) {
    const { workingTimeSettingFlag, workingTimeSettingVO } = defaultData;
    // 默认时间
    _record?.set('entryDate', new Date());
    const data = formDataSet?.current?.toData() || {};
    const getPersonInGroupWithCache = async (userId, groupId) => {
      if (!userId || !groupId) return false;
      const key = `personInGroup-${userId}-${groupId}`;
      const cache = AppState?.customConfig[key];
      if (cache) {
        return cache;
      }
      const res = axios.get(
        `/iam/yqc/${tenantId}/userGroups/query/whether/in/group?userId=${userId}&groupId=${groupId}`
      );
      AppState?.setCustomConfig(key, res);
      return res;
    };
    // 默认操作人
    _record?.set('operatorId', {
      id: userInfo?.id,
      name: userInfo?.realName,
      realName: userInfo?.realName,
      real_name: userInfo?.realName, // 值列表使用的下划线连接
    });
    // 默认操作组
    const inGroup = await getPersonInGroupWithCache(userInfo?.id, data?.assignment_group_id);
    if (inGroup) {
      _record?.set('operatorGroupId', {
        id: data.assignment_group_id,
        group_name: data['assignment_group_id:name'],
        groupName: data['assignment_group_id:name'],
        name: data['assignment_group_id:name'],
      });
    }

    if (workingTimeSettingFlag) {
      const { serviceMode, workHoursType, hours, minutes } = workingTimeSettingVO || {};
      // 工时类型
      workHoursType && _record?.set('type', workHoursType);
      // 服务方式
      serviceMode && _record?.set('serviceWay', serviceMode);
      // 时长默认值
      if (typeof hours === 'number') {
        _record.set('hours', hours);
      }
      if (typeof minutes === 'number') {
        _record.set('minutes', minutes);
      }
    }
  }

  const handleOpenGenerateKnowlledgeModal = ({ beforeFlag }) => {
    const businessObjectId = formConfigRecord?.get('businessObjectId');
    const record = formDataSet?.current;
    const __ticketId = record?.get('id');
    const name = record?.get('short_description')?.slice(0, 100);
    return new Promise(resolve => {
      Modal.open({
        children: (
          <GenKnowledgeForm
            intl={intl} // 必须传这个 intl 对象，否则弹框里会丢失多语言上下文
            instanceId={__ticketId}
            ticketId={__ticketId}
            viewDataSet={viewDataSet}
            formDataSet={formDataSet}
            originBusinessObjectId={businessObjectId}
            businessObjectId={businessObjectId}
            generateKnowledgeRef={generateKnowledgeRef}
            onCreateSuccessCallback={() => {
              resolve(true);
              setCompleteGenerateKnowledge(true);
            }}
            uiActionBeforeFlag={beforeFlag}
            newPageFlag
            name={name}
            onCancelCreateSuccess={() => {
              formDataSet.setState('prohibited', false);
              resolve(false);
            }}
          />
        ),
        footer: null,
        title: intl.formatMessage({ id: 'lcr.components.desc.generate.knowledge', defaultMessage: '生成知识' }),
        style: { width: 800 },
        onClose: () => {
          formDataSet.setState('prohibited', false);
        },
      });
    });
  };

  const AfterExecuteFeedbackView = ({ modal, btn, surveyObject }) => {
    const assessmentFlag = btn.get('assessmentFlag');
    const assessmentExecuteType = btn.get('assessmentExecuteType');
    const workTimeFlag = btn.get('workTimeFlag');
    const workTimeExecuteType = btn.get('workTimeExecuteType');
    const relatedTicketWindowFlag = btn.get('relatedTicketWindowFlag');
    const relatedTicketExecuteType = btn.get('relatedTicketExecuteType');
    const generateKnowledgeFlag = btn.get('generateKnowledgeFlag');
    const generateKnowledgeExecuteType = btn.get('generateKnowledgeExecuteType');
    const hasSurvey = assessmentFlag && surveyObject && surveyObject !== 'NO_PERMISSION';
    const isAfterSurvey = hasSurvey && assessmentExecuteType === 'AFTER';
    const isAfterCreateRelatedTicket = relatedTicketWindowFlag && relatedTicketExecuteType === 'AFTER';
    const isAfterWorkHour = workTimeFlag && workTimeExecuteType === 'AFTER';
    const isAfterGenerateKnowledge = generateKnowledgeFlag && generateKnowledgeExecuteType === 'AFTER';
    const record = formDataSet?.current;
    const __ticketId = record?.get('id');
    const businessObjectId = formConfigRecord?.get('businessObjectId');

    if (!feedbackModal) {
      return null;
    }

    return <div className="itsm-ui-action-afterExecute-feedback">
      <Icon type="check-one" theme="filled" fill="#2979FF" size={46} />
      <div className="itsm-ui-action-afterExecute-feedback-text">{intl.formatMessage({ id: 'lcr.components.desc.lc.action.flow.action.submit.success', defaultMessage: '已成功提交' })}</div>
      <div>
        {(isAfterCreateRelatedTicket && !completeRelatedCreate) && <Button color="primary" onClick={() => handleRelatedTicketWindow(btn, __ticketId, 'AFTER')}>{intl.formatMessage({ id: 'lcr.components.desc.lc.action.flow.action.related.create', defaultMessage: '关联创建' })}</Button>}
        {(isAfterSurvey && !completeInvestigation) && <Button color="primary" onClick={() => openSurveyModal({ btn, surveyObject })}>{intl.formatMessage({ id: 'lcr.components.desc.lc.action.flow.action.investigation', defaultMessage: '调查' })}</Button>}
        {(isAfterGenerateKnowledge && !completeGenerateKnowledge)
          && <GenerateKnowledge
            instanceId={__ticketId}
            ticketId={__ticketId}
            viewDataSet={viewDataSet}
            formDataSet={formDataSet}
            originBusinessObjectId={businessObjectId}
            generateKnowledgeRef={generateKnowledgeRef}
            onCreateSuccessCallback={() => {
              setCompleteGenerateKnowledge(true);
            }}
            newPageFlag
            config={
              { color: 'primary', noNeedIcon: true }
            }
          />}
        {(isAfterWorkHour && !completeWorkHour) && <Button color="primary" onClick={() => openWorkHourModal({ actionRecord: btn, isAfterSurvey })}>{intl.formatMessage({ id: 'lcr.components.desc.lc.action.flow.action.add.work.time', defaultMessage: '添加工时' })}</Button>}
        <Button onClick={() => { modal?.close(); setFeedbackModal(null); }}>{intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' })}</Button>
      </div>
    </div>;
  };

  useEffect(() => {
    if (feedbackModal && currentBtn) {
      feedbackModal.update({
        children: (
          <AfterExecuteFeedbackView btn={currentBtn} surveyObject={currentSurveyObject} />
        ),
      });
    }
  }, [feedbackModal, currentBtn, completeRelatedCreate, completeInvestigation, completeGenerateKnowledge, completeWorkHour, currentSurveyObject]);

  // 执行后的弹框(工时、关联创建、生成知识、调查)数量大于2时，需要整成提交反馈页面上的按钮
  async function handleOpenAfterExecuteFeedback(btn, surveyObject) {
    let currentSurveyObject = surveyObject;
    const assessmentId = btn?.get('assessmentId');
    const id = btn?.get('id');
    const assessmentFlag = btn.get('assessmentFlag');
    const assessmentExecuteType = btn.get('assessmentExecuteType');
    // 先判断有无调查权限，没有就不展示调查按钮
    if (assessmentFlag && assessmentExecuteType === 'AFTER' && !currentSurveyObject) {
      currentSurveyObject = await generateSurvey({ id: assessmentId, actionId: id });
    }
    const modalView = Modal.open({
      header: null,
      footer: null,
      key: feedBackModalKey,
      children: (
        <AfterExecuteFeedbackView
          btn={btn}
          surveyObject={currentSurveyObject}
        />
      ),
      destroyOnClose: true,
    });
    setFeedbackModal(modalView);
    setCurrentBtn(btn);
    setCurrentSurveyObject(surveyObject);
  }

  // 跳转到列表页面
  function handleGotoListPage(actionRecord) {
    const closeCurrentViewFlag = actionRecord?.get('closeCurrentViewFlag');
    if (closeCurrentViewFlag) {
      // 如果页面是嵌入在其他系统，关闭时需要给消息
      if (window?.parent?.postMessage) {
        window.parent.postMessage({
          event: 'YQ_TICKET_CLOSE',
          data: { id: instanceId },
        }, '*');
      }
      // 关闭单据页签
      if (tabMenuDataSet) {
        tabMenuDataSet?.setState('removeTabId', formDataSet?.current?.get('id') || instanceId);
        // 刷新列表
        tabMenuDataSet?.setState('refreshListPage', formDataSet?.current?.get('id') || instanceId);
        return;
      }
      // 打开视图的方式为弹窗
      if (modal) {
        modal?.close();
      }
    }
  }

  /**
   * 根据字段类型渲染字段
   * @param fieldItem
   * @param formDs
   * @returns {null|*}
   */
  function renderField(fieldItem, formDs, index) {
    const { field, required } = fieldItem;
    const record = formDataSet.current;
    // 用于渲染不在当前表单的字段
    let fieldRecord = null;
    const fieldConfig = record.getField(field);

    let widgetType;
    let name;
    let placeHolder;

    const dsFieldConfig = dsFieldList.find(dsFieldItem => dsFieldItem.code === field);
    const fieldDataSet = new DataSet();
    if (fieldConfig && dsFieldConfig) {
      // 当前单据已经有的字段，取当前单据字段的配置
      widgetType = dsFieldConfig.widgetType;
      name = dsFieldConfig.name;
      placeHolder = dsFieldConfig.placeholder;
      fieldRecord = fieldDataSet.create(dsFieldConfig);
    } else {
      // 当前单据没有的字段，取业务对象字段的配置
      const matchField = businessObjectFieldDataSet.find(r => r.get('code') === field)?.toData() || {};
      widgetType = matchField.widgetType;
      name = matchField.name;
      placeHolder = matchField.placeholder;
      fieldRecord = fieldDataSet.create(matchField);
    }
    if (!widgetType) {
      return null;
    }
    return (
      <WidgetField
        record={fieldRecord || record}
        viewCode={viewCode}
        businessObjectCode={businessObjectCode}
        widgetType={widgetType}
        label={name}
        key={field}
        name={field}
        code={field}
        intl={intl}
        formDs={formDs}
        disabled={false} // 动作集弹窗字段默认全部可以编辑
        readOnly={false}
        placeholder={placeHolder}
        required={required}
        autoFocus={index === 0}
        tenantId={tenantId}
      />
    );
  }

  /**
   * 由于Action需要单独控制必填，新建一个DataSet
   * @returns {DataSet}
   */
  function getActionFormDataSet(fieldList) {
    const record = formDataSet.current;
    const fields = [];
    const pageDsFieldMap = formConfigRecord?.getState('pageDsFieldMap') || {};
    const pageDsFields = [];
    const formFieldDefaultValue = {}; // 字段的默认值对象
    if (pageDsFieldMap && typeof pageDsFieldMap === 'object') {
      Object.keys(pageDsFieldMap).map(key => {
        if (pageDsFieldMap[key] && pageDsFieldMap[key].length) {
          pageDsFields.push(...pageDsFieldMap[key]);
        }
        return key;
      });
    }
    fieldList.forEach(fieldItem => {
      const { field, required } = fieldItem;
      // 单据已经有的字段
      const widgetType = dsFieldList?.find(dsFieldItem => dsFieldItem.code === field)?.widgetType;
      // const fieldConfig = formDataSet.getField(field);
      const fieldConfig = { props: formDataSet?.getField(field)?.props?.toJSON() };
      if (fieldConfig && fieldConfig.props && widgetType) {
        const { props } = fieldConfig;
        formFieldDefaultValue[props.name] = props.defaultValue;
        const newFieldConfig = {
          ...props,
          dynamicProps: {
            ...props.dynamicProps,
            required: () => required,
            disabled: () => false,
          },
          disabled: false,
          required,
        };
        if (widgetType === 'MasterDetail') {
          newFieldConfig.transformRequest = (value, record) => {
            if (value?.id) {
              return value.id;
            }
            return value;
          };
        }
        // 富文本这里不做处理，formDataSet中提交时统一处理成字符串
        if (widgetType === 'RichText') {
          newFieldConfig.transformRequest = undefined;
        }
        fields.push(newFieldConfig);
      } else {
        // 查询页面视图下的字段
        let fieldData = pageDsFields.find(r => r?.code === field);
        if (!fieldData) {
          // 单据没有的字段用业务对象的配置
          const bsFieldRecord = businessObjectFieldDataSet.find(r => r.get('code') === field);
          fieldData = bsFieldRecord?.toData() || {};
        }
        const fieldProps = transformField({
          fieldMap: { [field]: fieldData },
          field: fieldData,
          viewId: formConfigRecord?.get('pageId'),
          tenantId,
          intl,
        });
        const newFieldConfig = {
          ...fieldProps,
          dynamicProps: {
            ...fieldProps.dynamicProps,
            required: () => required,
            disabled: () => false,
          },
          disabled: false,
          required,
        };
        if (widgetType === 'RichText') {
          newFieldConfig.transformRequest = undefined;
        }
        fields.push(newFieldConfig);
      }
    });
    // NOTE: 级联数据
    const cascadeMap = getCascadeMap(dsFieldList);
    return new DataSet({
      fields,
      data: [Object.assign(formFieldDefaultValue, record.toData())],
      events: {
        update: ({ record: _record, name }) => {
          // 字段更新时，把关联子集字段设置为undefined
          if (cascadeMap.has(name)) {
            cascadeMap.get(name).forEach((item) => {
              _record.set(item, undefined);
            });
          }
        },
      },
    });
  }

  function getLabelWidth(labelWidth) {
    const result = labelWidth || 120;
    return result === 'auto'
      ? 'auto'
      : Number(result);
  }

  // 字段弹窗
  async function openPopFieldModal({ btn: current, aiPrompt = {} }) {
    const id = current.get('id');
    // 校验规则下的【展示字段】
    const windowShowField = current.get('windowShowField');
    // 当前动作的名称
    const name = current.get('name');
    // 校验规则下的【开启弹窗】
    const popupWindowFlag = current.get('popupWindowFlag');
    // 校验规则下的【关闭蒙层】
    const closeMaskFlag = current.get('closeMaskFlag');
    // 启用调查
    const assessmentFlag = current.get('assessmentFlag');
    // 弹窗的 title，来源于校验规则的【窗口标题】
    const windowTitle = popupWindowFlag ? current.get('windowTitle') : name;
    // 调查下的【触发节点】
    const assessmentExecuteType = current.get('assessmentExecuteType');
    // 调查下的【关联调查】
    const assessmentId = current.get('assessmentId');
    // 调查下的【窗口描述】，以一个固定高亮块展示
    const windowDescription = current.get('windowDescription');
    // 校验规则下的【执行方式】
    const windowType = current.get('windowType');
    // 校验规则下的【指定-标签长度】或者【普通视图/服务项视图-窗口大小】
    const windowSize = current.get('windowSize');
    // 校验规则下，如果 【执行方式】选择"普通视图"，就会显示【普通视图】字段，即下面字段
    const windowViewId = current.get('windowViewId');
    // 校验规则下，如果 【执行方式】选择"服务项视图"，后端会方会这个字段，而不是在动作中配置的
    const taskViewId = current.get('taskViewId');
    // 视图模式下，默认值处理方式
    const defaultValueMode = current.get('defaultValueMode') || 'USE_WHEN_EMPTY';
    // 启用添加工时
    const workTimeFlag = current.get('workTimeFlag');
    // 工时【触发节点】，执行前或后
    const workTimeExecuteType = current.get('workTimeExecuteType');
    // 开启智能生成
    const aiEnabledFlag = current.get('aiEnabledFlag');
    // 高亮块
    const windowDescriptionDom = (windowDescription && popupWindowFlag) ? [
      <Alert
        style={{ marginBottom: '8px' }}
        message={windowDescription}
        type="info"
        showIcon
      />,
    ] : '';
    const modelFormDataSet = {};
    let surveyObject = {};
    /**
     * 只在校验规则指定模式下有展示字段的时候使用
     * 校验规则下的【展示字段】，实际作用是在执行动作中改变表单的值
     * 但是每个字段的必填与否都是单独配置的，和主表单的配置不同，无法直接使用主表单 field 配置
     * 所以重新生成一个 dataSet 并传递给 runAction
     */
    let actionFormDataSet;
    let fieldList = [];

    const loadExpression = async () => {
      const fieldMap = {
        _parentId: formDataSet?.current?.get('id'), // 计算默认值将父级id传入
      };
      const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${windowViewId}/${windowViewId}/calculate`, JSON.stringify(fieldMap));
      if (result && !result.failed) {
        return result;
      } else {
        return {};
      }
    };

    let FieldFormView;
    if (windowType === 'SC_ITEM_VIEW') {
      // 校验规则的执行方式选择 服务项视图
      const ds = getServiceItemDs(formDataSet?.current?.get('req_item_id'), scPageRef);
      if (aiPrompt && aiEnabledFlag) {
        // 如果开启了智能生成, 仅给页面字段赋值，不继续动作的保存更新操作
        Object.keys(aiPrompt).map((fieldCode) => {
          ds.current.set(fieldCode, aiPrompt[fieldCode]);
        });
      }
      setViewDs(ds);
      FieldFormView = (
        <PageLoader
          viewId={taskViewId}
          formDataSet={ds}
          pageRef={customViewRef}
          mode="MODIFY"
          variableFlag
        />
      );
    } else if (windowType === 'VIEW') {
      const currentRecord = formDataSet?.current.toData();
      // 校验规则的执行方式选择 普通视图
      // 不使用默认值模式直接不请求默认值接口
      let defaultInstanceData = defaultValueMode === 'NOT_USE' ? {} : await loadExpression(); // 加载默认值
      if (aiPrompt && aiEnabledFlag) {
        // 如果开启了智能生成, 仅给页面字段赋值，不继续动作的保存更新操作
        defaultInstanceData = {
          ...defaultInstanceData,
          ...aiPrompt,
        };
      }
      let mergedData = { ...currentRecord };
      if (defaultValueMode === 'USE_WHEN_EMPTY') {
        // 原有值不为空时使用默认值
        Object.keys(defaultInstanceData).forEach((key) => {
          if (!mergedData.hasOwnProperty(key)) {
            mergedData[key] = defaultInstanceData[key];
          } else if (!currentRecord[key] && currentRecord[key] !== false && currentRecord[key] !== 0) {
            mergedData[key] = defaultInstanceData[key];
          }
        });
      } else {
        mergedData = {
          ...mergedData,
          ...defaultInstanceData,
        };
      }

      FieldFormView = (
        <PageLoader
          viewId={windowViewId}
          instanceId={formDataSet?.current?.get('id')}
          pageRef={customViewRef}
          mode="MODIFY"
          showHeaderFlag={false}
          formDataSetCreated={(_ds) => { setViewDs(_ds); }}
          defaultData={mergedData}
          events={{
            update: ({ dataSet, name: fieldName }) => {
              modelFormDataSet[fieldName] = dataSet?.current?.get(fieldName);
            },
            load: ({ dataSet }) => {
              // 转交相关，业务实现将一种转交的实现，杂糅进了校验规则里
              // 如果将执行方式选择普通视图，视图选择【转交专用视图】
              // 这个动作就有了转交的功能
              dataSet?.setState('transferInfo', {
                transferGroupLovId: current?.get('transferGroupLovId'),
                transferPersonLovId: current?.get('transferPersonLovId'),
                transferTypeFlag: current?.get('transferTypeFlag'),
              });
            },
          }}
        />
      );
    } else if (windowType === 'ASSIGN') {
      // 校验规则的执行方式选择 指定
      try {
        fieldList = JSON.parse(windowShowField) || [];
      } catch (e) {
        fieldList = [];
      }

      if (/* 开启弹窗才赋值 */ popupWindowFlag) {
        actionFormDataSet = getActionFormDataSet(fieldList);
        if (aiPrompt && aiEnabledFlag) {
          // 如果开启了智能生成, 仅给页面字段赋值，不继续动作的保存更新操作
          Object.keys(aiPrompt).map((fieldCode) => {
            actionFormDataSet.current.set(fieldCode, aiPrompt[fieldCode]);
          });
        }
        setViewDs(actionFormDataSet);
        // 动作字段的表单
        FieldFormView = (
          <Form
            labelWidth={getLabelWidth(fieldList?.[0]?.labelWidth)}
            record={actionFormDataSet.current}
            dataSet={actionFormDataSet}
            className="lc-model-detail-form"
          >
            {fieldList.map((field, index) => renderField(field, actionFormDataSet, index))}
          </Form>
        );
      }
    }

    // 审批表单 审批动作有一些固定的字段
    let approvalFormView;
    let approveRecord;
    const isApprove = current.get('actionType') === 'APPROVAL'; // 动作类型
    const approveType = current.get('actionApprovalType'); // 审批类型
    if (isApprove) {
      const {
        commentRequiredButtons = [], // 必填配置
        remarkRequiredButtons = [], // 加签说明
        reApproveOptionFlag,
        buttonPermissions = [],
        reApproveRunPathFlag,
        workflowType, // 当前执行的工作流类型
      } = approveConfig;
      const hasAddSign = buttonPermissions.includes('ADD_SIGN');
      const hasAddTaskApprover = buttonPermissions.includes('ADD_TASK_APPROVER');
      const userLovId = current.get('userLovId');
      updateFieldsConfig({
        approveDataSet,
        approveType,
        userLovId,
        tenantId,
        intl,
        instanceId,
      });
      approveRecord = await approveDataSet.create({
        id: instanceId,
        approveType,
        reApproveOptionFlag, // 是否复杂拒绝
        reApproveRunPathFlag, // 是否展示拒绝节点
        addSignPurpose: hasAddSign ? 'ADD_SIGN' : 'ADD_TASK_APPROVER',
        workflowType,
        commentRequiredButtons, // 必填配置
        remarkRequiredButtons, // 加签说明
      });
      const phrasesComponentId = `${id}_remark`;
      const remarkComponent = current?.get('historyInputFlag') ? <PhrasesText name="remark" labelWidth={0} phrasesComponentId={phrasesComponentId} changeFn={(text) => { approveRecord.set('remark', text); }} phrasesValue={approveRecord?.set('remark')} formComponentType="TextArea" displayOriginallyComponent /> : <TextArea name="remark" />;
      let fields;
      switch (approveType) {
        case 'REJECT':
          // 拒绝: 审批意见、【复杂-重新审批节点】、【复杂-当前节点重审规则】
          fields = [
            remarkComponent,
            reApproveOptionFlag && reApproveRunPathFlag && <Select name="rejectAppointNode" />,
            reApproveOptionFlag && <SelectBox name="reApproveFlag" optionRenderer={optionRender} />,
          ];
          break;
        case 'REBUT':
          // 驳回: 驳回节点、驳回意见
          fields = [
            <Select name="returnNode" />,
            remarkComponent,
          ];
          break;
        case 'AGREE':
          // 同意: 审批意见
          fields = [
            remarkComponent,
          ];
          break;
        case 'TRANSFER':
          // 转交: 转交人，转交意见
          fields = [
            <Lov name="transferPersonId" />,
            remarkComponent,
          ];
          break;
        case 'SIGN':
          // 加签:【加签目的】、加签类型、【加签顺序】、加签给、加签说明
          fields = [
            hasAddSign && hasAddTaskApprover && <SelectBox name="addSignPurpose" optionRenderer={optionRender} />,
            <SelectBox name="addSignType" optionRenderer={optionRender} />,
            workflowType === 'WITTY'
              ? <SelectBox name="signType" optionRenderer={optionRender} />
              : undefined,
            <Lov name="candidateUsers" />,
            remarkComponent,
          ];
          break;
        default:
          break;
      }
      approvalFormView = (
        <Form record={approveRecord} labelWidth={120}>
          {fields}
        </Form>
      );
    }

    // 调查表单
    let record;
    // NOTE: 执行前需要调查的话就生成一个实例，如果不想执行动作了，需要删掉，在onCancel中回调
    if (assessmentExecuteType === 'BEFORE' && assessmentFlag) {
      surveyObject = await generateSurvey({ id: assessmentId, actionId: id });
    }
    // 调查实例初始值
    if (surveyObject?.id) {
      record = await getSurveyInstanceData(surveyObject?.id);
    }
    const hasData = Object.keys(record?.toData() || {}).filter((key) => key !== '__dirty').length > 0;

    // 调查实例的视图
    const SurveyFormView = (
      <PageLoader
        viewId={surveyObject?.surveyId}
        pageRef={surveyPageRef}
        mode={['PENDING', 'READY'].includes(surveyObject?.state) ? 'MODIFY' : 'DISABLED'}
        surveyFlag
        // numberFlag={surveyObject?.numberEnabledFlag}
        numberFlag
        defaultInstanceData={hasData ? [record.toData()] : null}
        showHeaderFlag
        pageableFlag={surveyObject?.pageableFlag || false}
        isLastPage={(res) => {
          surveyDataDataSet.setState('isLastSurveyPage', res);
        }}
      />
    );
    /**
     * 执行前是在校验规则字段表单上展示组件，在提交时会运行 runAction 函数，里面会对执行前表单里的数据进行提交。
     * 执行后是在校验规则字段表单提交后，打开新表单，提交数据。
     */
    const hasSurvey = assessmentFlag && surveyObject?.id && surveyObject !== 'NO_PERMISSION';
    /**
     * 动作执行前调查
     */
    const isBeforeSurvey = hasSurvey && assessmentExecuteType === 'BEFORE';

    // 工时表单
    const _record = workLogsDataSet.create({ category: 'ACTUAL_WORK_TIME' });
    await initDefaultData(_record);
    const WorkHourFormProps = {
      record: _record,
      intl,
      intlPrefix: 'lc.components.workHour',
      tenantId,
      replayListDataSet,
      actionListDataSet,
      workHourTableDataSet: {
        query() {
        },
      }, // 需要刷新的数据源
      viewDataSet,
      businessObjectCode,
      urlSuffix,
      simpleMode: true,
      title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' }),
      businessObjectId: formConfigRecord?.get('businessObjectId'),
      ticketId: formDataSet?.current?.get('id'),
      workTimeRef: workTimeModalRef,
      hiddenRelatedContent: true,
    };
    const workHourFormView = <WorkHourForm {...WorkHourFormProps} />;
    /**
     * 动作执行前添加工时
     */
    const isBeforeWorkHour = workTimeFlag && workTimeExecuteType === 'BEFORE';

    formDataSet.setState('prohibited', false);

    // 如果没有弹窗
    if (!popupWindowFlag && !isBeforeSurvey && (!isBeforeWorkHour || !defaultData?.workingTimeSettingFlag)) {
      return runAction({ id, btn: current, surveyObject });
    }

    // 如果弹窗只包含提示信息，则展示为Modal.confirm
    if (
      (!windowType || (windowType === 'ASSIGN' && !fieldList?.length))
      && !isBeforeWorkHour
      && !isBeforeSurvey
      && (!isApprove || approveType === 'WITHDRAW') // 撤回类型无默认字段
    ) {
      return new Promise(resolve => {
        Modal.confirm({
          title: windowTitle || name,
          children: windowDescription,
          mask: !closeMaskFlag,
          onOk: async () => {
            const params = { actionFormDataSet, fieldList, surveyObject };
            const res = await runAction({
              id,
              btn: current,
              ...params,
              surveyObject,
              approveRecord,
            });
            resolve(res);
            return res;
          },
          onCancel: () => {
            // formDataSet.reset();
            customViewRef.current?.formDataSet?.reset();
            resolve({
              isBefore: true,
              cancel: true,
            });
            return true;
          },
        });
      });
    }

    /**
     * 动作执行前如果有弹窗，会整合在一个弹窗里面显示
     */
    const FormViewArr = [windowDescriptionDom];
    if (popupWindowFlag && isApprove) {
      // 审批动作
      FormViewArr.push([approvalFormView, FieldFormView]);
    } else if (popupWindowFlag) {
      // 非审批动作
      FormViewArr.push(FieldFormView);
    }
    isBeforeWorkHour && FormViewArr.push(workHourFormView);
    isBeforeSurvey && FormViewArr.push(SurveyFormView);
    const FormView = <>{FormViewArr.map((v, i) => (i > 1 ? <><SplitLine />{v}</> : v))}</>;
    const getWindowSize = () => {
      // 没有开启弹窗的时候，大小是800px
      if (!popupWindowFlag && isBeforeWorkHour) {
        return '1000px';
      } else if (!popupWindowFlag) {
        return '800px';
      }
      if (['VIEW', 'SC_ITEM_VIEW'].includes(windowType)) {
        return windowSizeMap[windowSize] || '1200px';
      } else if (assessmentFlag) {
        return '800px';
      } else if (isBeforeWorkHour) {
        return '1000px';
      } else {
        return '800px';
      }
    };
    return new Promise(resolve => {
      const __modal = Modal.open({
        title: windowTitle || name,
        children: FormView,
        key: modalKey,
        drawer: false,
        style: { width: getWindowSize(), maxHeight: '400px' },
        className: `${prefixCls}-modal ${windowType === 'VIEW' ? 'review-modal' : ''}`,
        destroyOnClose: true,
        mask: !closeMaskFlag,
        onOk: async () => {
          // 先启用弹窗，再判断是否为视图类型
          if (popupWindowFlag && windowType === 'VIEW') {
            const validate = await validateMessage(customViewRef.current?.formDataSet, intl);
            if (!validate) {
              return false;
            }
          }
          if (popupWindowFlag && windowType === 'SC_ITEM_VIEW') {
            const validate = await validateMessage(formDataSet, intl, scPageRef);
            if (!validate) {
              return false;
            }
          }
          if (popupWindowFlag && isApprove) {
            const validate = await approveRecord.validate(true);
            if (!validate) {
              message.error(intl.formatMessage({ id: 'lcr.components.model.required.tip', defaultMessage: '必填字段为空' }));
              return false;
            }
          }
          const params = windowType === 'VIEW' ? { modelFormDataSet } : { actionFormDataSet, fieldList };
          // resolve(true);
          const res = await runAction({
            id,
            btn: current,
            ...params,
            surveyObject,
            approveRecord,
          });
          resolve(res);
          return res;
        },
        onCancel: () => {
          // formDataSet.reset();
          // 只重置动作集弹窗中显示的字段，表单上其他字段不重置
          // fieldList.forEach((item) => {
          //   const { field } = item;
          //   formDataSet?.current?.init(field);
          // });
          customViewRef.current?.formDataSet?.reset();
          if (isBeforeSurvey) {
            deleteSurvey({ surveyObject });
          }
          resolve({
            isBefore: true,
            cancel: true,
          });
          return true;
        },
      });
      setModal(__modal);
    });
  }

  // 调查的弹框
  async function openSurveyModal({ btn: current, surveyObject: currentSurveyObject }) {
    const {
      id,
      windowTitle,
      name,
      assessmentId, // 关联的调查id
      windowDescription,
      popupWindowFlag,
    } = current.toData();
    let record;
    const surveyObject = currentSurveyObject || await generateSurvey({ id: assessmentId, actionId: id });
    // 调查实例初始值
    if (surveyObject?.id) record = await getSurveyInstanceData(surveyObject?.id);
    const hasData = Object.keys(record?.toData() || {}).filter(key => key !== '__dirty').length > 0;
    const windowDescriptionDom = (windowDescription && popupWindowFlag) ? [
      <Alert
        style={{ marginBottom: '8px' }}
        message={windowDescription}
        type="info"
        showIcon
      />,
    ] : [];

    if (surveyObject === 'NO_PERMISSION') {
      // 没有权限查看调查
      return true;
    }

    Modal.open({
      title: surveyObject?.surveyName || windowTitle || name,
      children: (
        <>
          {windowDescriptionDom}
          <ExternalComponent
            system={{
              scope: 'lcr',
              module: 'PageLoader',
            }}
            viewId={surveyObject?.surveyId}
            pageRef={surveyPageRef}
            mode={['PENDING', 'READY'].includes(surveyObject?.state) ? 'MODIFY' : 'DISABLED'}
            surveyFlag
            // numberFlag={surveyObject?.numberEnabledFlag}
            numberFlag
            defaultInstanceData={hasData ? [record.toData()] : null}
            showHeaderFlag
            pageableFlag={surveyObject?.pageableFlag || false}
            isLastPage={(res) => {
              surveyDataDataSet.setState('isLastSurveyPage', res);
            }}
          />
        </>
      ),
      key: surveyModalKey,
      drawer: false,
      style: { width: '800px', maxHeight: '400px' },
      className: `${prefixCls}-modal`,
      destroyOnClose: true,
      footer: (okBtn, cancelBtn) => {
        return (
          <div className="survey-list-detail-footer">
            <div className="button-group">
              <Button
                key="submit"
                funcType="raised"
                color="primary"
                onClick={async () => {
                  return handleSurveySubmit({ surveyObject }, (res) => {
                    if (res) {
                      message.success(intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
                      setCompleteInvestigation(true);
                      cancelBtn?.props?.onClick();
                      handleGotoListPage(current);
                    }
                  });
                }}
              >
                {intl.formatMessage({ id: 'zknow.common.button.submit', defaultMessage: '提交' })}
              </Button>
              <Button
                key="close"
                funcType="raised"
                onClick={() => {
                  formDataSet?.reset();
                  cancelBtn?.props?.onClick();
                  handleGotoListPage(current);
                  return true;
                }}
              >
                {intl.formatMessage({ id: 'zknow.common.button.close', defaultMessage: '关闭' })}
              </Button>
            </div>
          </div>
        );
      },
    });
  }

  // 调查表单的提交
  async function handleSurveySubmit({ surveyObject, type = 'submit' }, callback = () => { }) {
    const url = `/asmt/v1/${tenantId}/assessment_instances`;
    try {
      const surveyDetailRefresh = formDataSet.getState('surveyDetailRefresh');
      const surveyFormDataSet = surveyPageRef.current?.formDataSet;
      const result = await surveyFormDataSet.current.validate(true);
      if (result) {
        if (surveyFormDataSet.getState('__isSubmitted')) {
          return true;
        }
        const data = surveyFormDataSet?.toData();
        if (data && data.length) {
          const res = await axios.post(`${url}/${type}/${surveyObject.id}?surveyId=${surveyObject.surveyId}`, data[0]);
          if (res && !res?.failed) {
            callback(true);
            surveyFormDataSet.setState('__isSubmitted', true);
            if (typeof surveyDetailRefresh === 'function') {
              surveyDetailRefresh();
            }
            return true;
          } else {
            message.error(res?.message);
            callback(false);
            return false;
          }
        }
      }
    } catch (e) {
      return false;
    }
  }

  // 添加回复
  async function handleReplySubmit(data) {
    const { ticketId, businessObjectId, ...restData } = data || {};
    const res = await axios.post(`/itsm/v1/${tenantId}/journals/${businessObjectId}/${ticketId}/journals`, JSON.stringify(restData));
    if (res?.failed) {
      message.error(res?.message);
    } else {
      return res;
    }
  }

  // 生成调查实例
  async function generateSurvey({ id, actionId }) {
    const originalViewId = urlViewId || pageRef.current?.pageRecord?.get('id');

    const res = await axios.post(`/asmt/v1/${tenantId}/assessments/${id}/generate`, JSON.stringify({
      ticketId: formDataSet.current?.get('id'),
      actionId,
      businessObjectCode: formConfigRecord?.get('businessObjectCode'),
      viewId: originalViewId,
    }));
    if (res?.failed) {
      message.error(res?.message);
    } else {
      return res;
    }
  }

  // 删除调查实例
  async function deleteSurvey({ surveyObject }) {
    const id = surveyObject.id;
    const res = await axios.delete(`/asmt/v1/${tenantId}/assessment_instances/delete/temporary/${id}`);
    if (res?.failed) {
      message.error(res?.message);
    }
  }

  async function submitApprove({ approveType, approveRecord }) {
    try {
      // 按钮类型: 加签需要特殊处理
      let buttonType = APPROVE_ACTIONS[approveType];
      if (approveType === 'SIGN') {
        buttonType = approveRecord.get('addSignPurpose');
      }
      const approveObj = approveRecord?.toData();
      const res = await axios.post(`workflow/v1/${tenantId}/taskInstances/button_submit/${buttonType}`, JSON.stringify(approveObj));
      if (res?.failed) {
        message.error(res?.message);
        return false;
      } else {
        return res;
      }
    } catch (e) {
      return false;
    }
  }

  // 打开工时弹窗
  async function openWorkHourModal({ actionRecord, next = () => { }, isAfterSurvey }) {
    const _record = workLogsDataSet.create({ category: 'ACTUAL_WORK_TIME' });
    await initDefaultData(_record);
    const WorkHourFormProps = {
      record: _record,
      intl,
      intlPrefix: 'lc.components.workHour',
      tenantId,
      replayListDataSet,
      actionListDataSet,
      workHourTableDataSet: {
        query() { },
      }, // 需要刷新的数据源
      viewDataSet,
      businessObjectCode,
      urlSuffix,
      simpleMode: true,
      businessObjectId: formConfigRecord?.get('businessObjectId'),
      ticketId: formDataSet?.current?.get('id'),
      workTimeRef: workTimeModalRef,
      hiddenRelatedContent: true,
    };
    const workHourFormView = defaultData?.workingTimeSettingFlag ? <WorkHourForm {...WorkHourFormProps} /> : null;
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' }),
      children: <div>{workHourFormView}</div>,
      key: workHourModalKey,
      drawer: false,
      style: { width: 1000 },
      className: `${prefixCls}-modal`,
      destroyOnClose: true,
      onOk: async () => {
        const res = await handleWorkHourSubmit({
          actionRecord,
          callback: (v, feedback) => {
            if (v) {
              if (feedback) {
                message.success(intl.formatMessage({ id: 'zknow.common.success.submit', defaultMessage: '提交成功' }));
              }
              setCompleteWorkHour(true);
            }
          },
        });
        if (!isAfterSurvey) handleGotoListPage(actionRecord);
        return res;
      },
      onClose: next,
    });
  }

  // 工时表单的提交
  async function handleWorkHourSubmit({
    actionRecord,
    callback = () => { },
    displayFeedMsg = true,
  }) {
    const url = `/itsm/v1/${tenantId}/working/time?businessObjectCode=${businessObjectCode}`;
    try {
      const _record = workTimeModalRef?.current?.dataSet?.current;
      const durationFieldRequired = _record?.getField?.('duration')?.get?.('required');
      if (durationFieldRequired) {
        // 工时必填校验，如果都不填写
        //    工时值是 duration: { days: null, minutes: null, hours: null }，依然被判定为已填写值
        const duration = _record?.get('duration');
        if (typeof duration?.days !== 'number' && typeof duration?.minutes !== 'number' && typeof duration?.hours !== 'number') {
          message.error(intl.formatMessage({ id: 'lcr.renderer.desc.duration.required', defaultMessage: '请填写时长' }));
          return false;
        }
      }

      const validate = await _record?.validate(true);
      const actionId = actionRecord.get('id');
      if (validate) {
        const actionHisId = await axios.get(
          `itsm/v1/${tenantId}/journals/action/history/${instanceId}/${actionId}?businessObjectCode=${businessObjectCode}`,
        );
        const data = [
          {
            businessObjectCode,
            taskId: formDataSet?.current?.get('id'),
            ..._record.toData(),
          },
        ];
        if (actionHisId?.id && !actionHisId?.failed) {
          data[0].actionHisId = actionHisId.id;
        }
        if (instanceId) {
          data[0].task_id = instanceId;
        }
        // duration 后端使用的是 int 类型，最大能表示的正整数 2 ** 31 -1
        if (typeof data[0].duration === 'number' && data[0].duration > (2 ** 31 - 1)) {
          message.error(intl.formatMessage({ id: 'lcr.renderer.desc.duration.limit', defaultMessage: '工时超出最大值' }));
          return false;
        }

        const res = await axios.post(url, JSON.stringify(data));
        if (res?.failed) {
          // message.error(res?.message);
          callback(false);
          return false;
        } else {
          // 执行前的工时登记提交后不显示个性化提示
          if (displayFeedMsg && res?.[0]?._message) {
            message.success(res?.[0]?._message);
          }

          // 更新回复上的工时
          viewDataSet?.getState('_replyDataSet')?.query();
          viewDataSet?.getState('_workHourDataSet')?.query();
          callback(true, !res?.[0]?._message);
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // 校验动作
  async function checkAction(action) {
    const businessObjectId = formConfigRecord?.get('businessObjectId');
    const formData = formDataSet?.current.toData() || {};
    const { id: ticketId } = formData;
    const actionId = action?.get('id');
    const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/${businessObjectId}/v2/check`, JSON.stringify({
      ...formData,
      ticketId,
      actionId,
    }));
    if (!res?.success) {
      const { field, message } = res;
      // 校验字段
      const validationResults = [];
      const validationResult = new ValidationResult({
        validationMessage: message,
      });
      validationResults.push(validationResult);
      formDataSet?.current?.setValidationError(field, validationResults);
      Modal.open({
        title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.check.title', defaultMessage: '校验结果' }),
        children: (
          <div>{intl.formatMessage({ id: 'lcr.components.desc.field.validate.check.error', defaultMessage: '字段校验未通过，请检查以下字段：{message}' }, { message })}</div>
        ),
        key: checkModalKey,
        drawer: false,
        destroyOnClose: true,
        footer: null,
      });
      return false;
    } else {
      return true;
    }
  }

  /**
   * 执行动作前序校验
   * @param action
   * @returns {Promise<unknown>}
   */
  async function preorderClick(action) {
    return new Promise(resolve => {
      checkAction(action).then(async result => {
        if (result) {
          // FIXME: validate 校验有问题，校验不通过依然返回了 true
          const validate = await validateMessage(formDataSet, intl, scPageRef);
          if (validate === true) {
            resolve(true);
          } else {
            resolve('field.validate');
          }
        } else {
          resolve(false);
        }
      });
    });
  }

  // 确认配置项修改后执行原来的逻辑
  async function handleAfterConfirm(action) {
    // 表单数据校验，例如必填项
    //    另外，这个方法发起一个 check 请求，后端会计算动作配置中的【校验规则-字段校验】
    //    如果不满足字段校验，接口会抛出异常，通过 message 字段返回原因，例如 "字段校验未通过，请检查以下字段：状态"
    const checkResult = await preorderClick(action);
    // 如果主表单有字段校验不通过，那么
    if (checkResult === false || checkResult === 'field.validate') {
      if (checkResult) {
        message.error(intl.formatMessage({ id: 'zknow.common.validate.failed', defaultMessage: '字段校验未通过，请修改后重新提交' }));
      }
      // 释放动作按钮禁用控制
      formDataSet.setState('prohibited', false);
      return false;
    }
    const id = action.get('id');
    // 校验规则下的启用弹窗
    const popupWindowFlag = action.get('popupWindowFlag');
    // 启用调查
    const assessmentFlag = action.get('assessmentFlag');
    // 启用添加工时
    const workTimeFlag = action.get('workTimeFlag');
    // 开启智能生成
    const aiEnabledFlag = action.get('aiEnabledFlag');
    // 智能生成模板
    const aiPromptTemplateId = action.get('aiPromptTemplateId');

    // 智能生成: 通过AI理解出字段值，赋给页面对应字段
    let aiPrompt;
    if (aiEnabledFlag) {
      try {
        const result = await axios.post(`/ai/v1/${tenantId}/callPromptTemplate/run/${aiPromptTemplateId}?businessObjectCode=${businessObjectCode}&ticketId=${instanceId}`);
        if (result && !result.failed) {
          aiPrompt = result?.changedParams || {};
        }
      } catch (e) {
        aiPrompt = {};
      }
    }

    // 只是动作【执行前】的弹窗，在 runAction 方法里处理【执行后】的弹窗
    if (popupWindowFlag || assessmentFlag || workTimeFlag) {
      await openPopFieldModal({ btn: action, aiPrompt });
    } else {
      runAction({ id, btn: action, aiPrompt });
    }
    return true;
  }

  /**
   * 关联配置项字段变更提醒
   *  FIXME：取消的话，后续动作也不再执行，是否符合业务逻辑？
   * @param btn
   * @param ciRelationshipItem
   * @returns {Promise<unknown>}
   */
  async function openConfirmModelChange(btn, ciRelationshipItem) {
    const { itemRelated: { name } } = formDataSet?.get(0)?.get('item_id:variable_view_id:_variable') || formDataSet?.get(0)?.get('request_item_id:item_id:variable_view_id:_variable') || formDataSet?.get(0)?.get('req_item_id:item_id:variable_view_id:_variable');
    const { fieldName, fieldValue } = ciRelationshipItem;
    return new Promise(resolve => {
      Modal.open({
        title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.check.model', defaultMessage: '更新配置项确认' }),
        children: (
          <>
            <Form
              columns={2}
              labelWidth="auto"
              className="itsm-ui-action-confirmModal-modelName"
            >
              <TextField
                disabled
                colSpan={1}
                renderer={() => name}
                label={intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.modelname', defaultMessage: '配置项' })}
              />
              <TextField disabled renderer={() => fieldName} newLine />
              <TextField
                disabled
                renderer={() => fieldValue}
                label={intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.change', defaultMessage: '更新为' })}
              />
            </Form>
          </>
        ),
        key: checkModalKey,
        drawer: false,
        destroyOnClose: true,
        onOk: async () => {
          await handleAfterConfirm(btn);
          resolve(true);
        },
      });
    });
  }

  async function handleSubmitView(_resolve, view, inheritParticipantsRes = []) {
    const datasetId = createViewRef?.current?.pageData?.id;
    const { viewId, relationId } = view;
    const originalId = formDataSet?.current?.get('id');
    const originalViewId = urlViewId || pageRef.current?.pageRecord?.get('id');
    let participantList;
    if (Array.isArray(toJS(inheritParticipantsRes))) {
      //
      participantList = [];
      inheritParticipantsRes.forEach((i) => {
        if (['USER', 'GROUP'].includes(i.type)) {
          participantList.push({
            participantId: i.participantId,
            type: i.type,
          });
        }
      });
    }
    try {
      const originalViewParam = originalViewId ? `&originalViewId=${originalViewId}` : '';
      const res = await axios.post(
        `/lc/v1/${tenantId}/lc_action_relationships/${viewId}/${datasetId}/submit?relationId=${relationId}&originalId=${originalId}${originalViewParam}`,
        [{
          ...createViewRef.current.formDataSet.current.toData(),
          _status: 'create',
          participantList,
        }],
      );
      _resolve(res);
      return res;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log('error', error);
    }
  }

  // 弹窗打开低代码新建视图，创建关联单据
  async function openCreateView(view = {}) {
    const {
      viewName,
      viewId,
      relationId,
      defaultData,
      type,
      inheritParticipantsFlag,
      inheritParticipantsWindowFlag,
    } = view;

    return new Promise(resolve => {
      const __modal = Modal.open({
        title: viewName,
        children: (
          <PageLoader
            viewId={viewId}
            pageRef={createViewRef}
            mode="CREATE"
            openType="MIDDLE"
            parentDataSet={formDataSet}
            defaultData={defaultData}
            modalButtonFlag={false}
            formDataSetCreated={(_ds) => { setViewDs(_ds); }}
          />
        ),
        key: `${viewModalKey}-${viewId}`,
        drawer: false,
        style: { width: 1200 },
        destroyOnClose: true,
        closeOnLocationChange: true,
        okButton: true,
        onOk: async () => {
          const res = await validateMessage(createViewRef.current.formDataSet, intl, scPageRef);

          if (!res) {
            return false;
          } else if (type === 'AFTER') {
            let inheritParticipantsRes;
            if (inheritParticipantsFlag && inheritParticipantsWindowFlag) {
              inheritParticipantsRes = await openInheritParticipantsModal();
            }
            await handleSubmitView(resolve, view, inheritParticipantsRes);
          } else if (type === 'BEFORE') {
            let inheritParticipantsRes;
            if (inheritParticipantsFlag && inheritParticipantsWindowFlag) {
              inheritParticipantsRes = await openInheritParticipantsModal();
            }
            if (!inheritParticipantsRes) {
              resolve(true);
              return true;
            }
            formDataSet?.setState('beforeInheritParticipantsRes', inheritParticipantsRes);
            resolve(true);
            return true;
          } else {
            resolve(false);
            return false;
          }
        },
        onClose: () => {
          resolve(false);
        },
      });
      setModal(__modal);
    });
  }

  // 动作关联创建弹窗处理
  async function handleRelatedTicketWindow(_btn, _ticketId, type) {
    formDataSet?.setState('beforeInheritParticipantsRes', undefined);
    const relatedTicketWindowFlag = _btn.get('relatedTicketWindowFlag');
    const inheritParticipantsFlag = _btn?.get('inheritParticipantsFlag');
    const inheritParticipantsWindowFlag = _btn?.get('inheritParticipantsWindowFlag');
    if (relatedTicketWindowFlag) {
      const relationActionRelationship = _btn?.get('relationActionRelationship');
      const _data = relationActionRelationship?.[0];
      if (_data && _data?.relatedTicketViewId) {
        const relationId = _data?.id || '';
        let defaultData = {};
        try {
          const _postData = {
            ...formDataSet.current.toData(),
          };
          if (_ticketId) {
            _postData.id = _ticketId;
          }
          const res = await axios.post(`/lc/v1/${tenantId}/lc_action_relationships/${relationId}/calculate`, _postData);
          if (!res?.failed) {
            defaultData = res;
          }
        } catch (e) {
          return false;
        }
        const response = await openCreateView({
          viewId: _data?.relatedTicketViewId,
          viewName: _data?.relatedTicketViewName,
          relationId,
          defaultData,
          type,
          inheritParticipantsFlag,
          inheritParticipantsWindowFlag,
        });
        if (response && !response?.failed && typeof response === 'object') {
          // 单据提交成功反馈
          const res = await handleOpenResult(response, _data?.feedbackViewId);
          setCompleteRelatedCreate(true);
          return res;
        } else if (response) {
          return true;
        } else {
          // response?.message && message.error(response?.message);
          return false;
        }
      }
      return true;
    }
  }

  async function handleOpenResult(_data, _viewId) {
    let _modal;
    function handleCopy(e) {
      const content = e.target.parentElement.children['1'].innerText;
      const input = document.createElement('input');
      input.setAttribute('value', content);
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      message.success(intl.formatMessage({ id: 'lcr.components.desc.copy.success', defaultMessage: '已复制到剪切板' }));
    }
    function handleOpenDetail(e) {
      if (!_viewId) return;
      e.stopPropagation();
      _modal?.close?.();
      const _ds = new DataSet();
      _ds.create(_data[0]);
      onJumpNewPage({ record: _ds.current, viewId: _viewId });
    }
    const Content = () => (
      <div className="result-copy-info">
        <div><span>{intl.formatMessage({ id: 'zknow.common.model.title', defaultMessage: '标题' })}: </span>{_data[0].short_description}</div>
        <div onClick={handleCopy} style={{ cursor: 'pointer' }}><span>{intl.formatMessage({ id: 'lcr.components.desc.lc.component.proxy.scope.detail.number', defaultMessage: '单据编号' })}: </span><div onClick={handleOpenDetail}>{_data[0].number}<Icon type="copy" style={{ pointerEvents: 'none', marginLeft: 8 }} /></div></div>
      </div>
    );
    return new Promise(resolve => {
      setTimeout(() => {
        _modal = Modal.open({
          children: (
            <Result
              status="success"
              className="result-copy-info-cmp"
              title={intl.formatMessage({ id: 'zknow.common.success.create', defaultMessage: '创建成功' })}
              extra={[<Content />]}
            />
          ),
          key: resultKey,
          footer: null,
          style: { width: 800, height: 367 },
          closable: true,
          className: 'result-modal-class',
          onClose: () => {
            resolve(true);
          },
        });
      }, 1000);
    });
  }

  function openInheritParticipantsModal() {
    return new Promise(resolve => {
      Modal.open({
        title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.inherit.participants', defaultMessage: '继承参与人/参与组' }),
        children: (
          <InheritParticipants
            intl={intl}
            businessObjectCode={businessObjectCode}
            ticketId={instanceId}
            onSuccessCallback={(res) => {
              resolve(res);
              return res;
            }}
          />
        ),
        key: inheritParticipantsModalKey,
        style: { width: 960 },
        bodyStyle: { padding: 0 },
        closable: true,
        onClose: () => {
          resolve(false);
        },
      });
    });
  }

  function handleResetCustomerRef() {
    if (customViewRef?.current) {
      customViewRef.current = {};
    }
  }

  /**
   * 点击动作，开始执行动作
   * 所有页面上可以看到的动作均是已经通过【前提条件】的动作
   * 即动作配置中每个动作节点的第一个配置项是【前提条件】，后端会计算是否满足条件，如果不满足，前端查询不到这个动作
   * @param btn
   * @returns {Promise<boolean>}
   */
  async function handleClick(btn) {
    handleResetCustomerRef();
    // 动作流程执行中，阻止其它动作执行，直到当前动作流程结束
    formDataSet.setState('prohibited', true);
    // 动作节点中的【关联配置项】下的字段设置，可以有多组配置项分类，所以该值为数组或 undefined
    // 但是这里不会使用该值，只是用于判断是否需要进行弹窗提醒
    const ciRelationship = btn.get('ciRelationship');
    // 需要在视图中拖入【关联配置项】组件
    // FIXME：itemRelated【关联配置项】可能已经无效了
    const { itemRelated } = formDataSet?.get(0)?.get('item_id:variable_view_id:_variable') || formDataSet?.get(0)?.get('request_item_id:item_id:variable_view_id:_variable') || formDataSet?.get(0)?.get('req_item_id:item_id:variable_view_id:_variable') || {};
    if (!ciRelationship || ciRelationship?.length === 0 || !itemRelated) {
      await handleAfterConfirm(btn);
    } else if (ciRelationship) {
      const modelId = itemRelated.modelId;
      const belongFlag = ciRelationship.findIndex(i => i.modalId === modelId);
      if (belongFlag >= 0) {
        await openConfirmModelChange(btn, ciRelationship[belongFlag]);
      } else {
        await handleAfterConfirm(btn);
      }
    }
    return true;
  }

  /**
   * 根据审批配置，过滤审批动作
   * @param actionDataSet
   * @returns {*}
   */
  function filterApproveActions(actionDataSet) {
    return actionDataSet?.filter(action => {
      const { buttonPermissions } = approveConfig;
      if (action.get('actionType') === 'APPROVAL') {
        if (buttonPermissions) {
          const actionApprovalType = action.get('actionApprovalType');
          // 加签对应两种情况，需要特殊处理
          if (actionApprovalType === 'SIGN') {
            return buttonPermissions.includes('ADD_SIGN') || buttonPermissions.includes('ADD_TASK_APPROVER');
          } else {
            return buttonPermissions.includes(APPROVE_ACTIONS[actionApprovalType]);
          }
        }
        return false;
      }
      return true;
    });
  }

  /**
   * 动作集下拉菜单项
   * @returns {JSX.Element}
   */
  function actionContent(index = 0) {
    const filterActions = filterApproveActions(actionDataSet);
    return (
      <Menu selectable={false} style={{ maxHeight: '420px', overflow: 'auto' }}>
        {filterActions?.slice(index, filterActions?.length)?.map(record => (
          <Item id={record.get('code')} key={record.get('code')} disabled={formDataSet.getState('prohibited')}>
            <ActionItem
              key={record.get('code')}
              record={record}
              prefixCls={prefixCls}
              onClick={handleClick}
            />
          </Item>
        ))}
      </Menu>
    );
  }

  // 渲染平铺动作集
  function renderTiledActions() {
    const filterActions = filterApproveActions(actionDataSet);
    if (filterActions?.length <= 5) {
      return filterActions.map((action, index) => (
        <ActionButton
          key={action.get('code')}
          disabled={formDataSet.getState('prohibited') || formDataSet.status === 'loading' || actionDataSet.status === 'loading'}
          loading={formDataSet.getState('prohibited')}
          record={action}
          onClick={handleClick}
          prefixCls={prefixCls}
          isFirst={index === 0}
        />
      ));
    } else {
      const outRecords = filterActions?.slice(0, 4);
      const outElements = outRecords.map((action, index) => (
        <ActionButton
          key={action.get('code')}
          disabled={formDataSet.getState('prohibited') || formDataSet.status === 'loading' || actionDataSet.status === 'loading'}
          loading={formDataSet.getState('prohibited')}
          record={action}
          onClick={handleClick}
          prefixCls={prefixCls}
          isFirst={index === 0}
        />
      ));
      const firstButton = {
        name: intl.formatMessage({ id: 'zknow.common.button.more', defaultMessage: '更多' }),
        code: 'select a action',
        get: (key) => {
          return firstButton[key];
        },
      };
      return (
        <>
          {outElements}
          <Dropdown overlay={actionContent(4)} trigger={['hover']}>
            <div style={{ marginLeft: '8px', display: 'inline-block' }}>
              <ActionButton
                isFirst
                disabled={formDataSet.getState('prohibited') || formDataSet.status === 'loading' || actionDataSet.status === 'loading'}
                loading={formDataSet.getState('prohibited')}
                record={firstButton}
                prefixCls={prefixCls}
                onClick={() => { }}
                showIcon
              />
            </div>
          </Dropdown>
        </>
      );
    }
  }

  function renderContent() {
    if (mode !== 'menu') {
      return renderTiledActions();
    } else if (!actionDataSet?.current) {
      return null;
    }

    // 当没有可操作的动作时，隐藏下拉框
    if (filterApproveActions(actionDataSet)?.length === 0) {
      return null;
    }

    const firstButton = {
      name: intl.formatMessage({ id: 'lcr.components.desc.lc.components.ui.action.select.action', defaultMessage: '请选择动作' }),
      code: 'select a action',
      get: (key) => {
        return firstButton[key];
      },
    };
    return (
      <Dropdown overlay={actionContent()} trigger={['hover']}>
        <div>
          <ActionButton
            isFirst
            disabled={formDataSet.getState('prohibited') || formDataSet.status === 'loading' || actionDataSet.status === 'loading'}
            loading={formDataSet.getState('prohibited')}
            record={firstButton}
            prefixCls={prefixCls}
            onClick={() => { }}
            showIcon
          />
        </div>
      </Dropdown>
    );
  }

  return (
    <div className={prefixCls}>
      {renderContent()}
    </div>
  );
}

export default observer(UIAction);
