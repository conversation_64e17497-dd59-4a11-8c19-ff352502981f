import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, intl, instanceId }) => {
  const reApprove = intl.formatMessage({ id: 'lcr.components.model.re.approve', defaultMessage: '重审规则' });
  const reApproveHelp = intl.formatMessage({ id: 'lcr.components.model.re.approve.help', defaultMessage: '当拒绝之后，发起人重新发起，当前所在节点的审批人的审批规则' });
  const rejectAppointNode = intl.formatMessage({ id: 'lcr.components.model.reject.appoint.node', defaultMessage: '重新审批节点' });
  const rejectAppointNodeHelp = intl.formatMessage({ id: 'lcr.components.model.reject.appoint.node.help', defaultMessage: '当拒绝之后，发起人重新发起，需要重新审批的节点' });
  const allUserReApprove = intl.formatMessage({ id: 'lcr.components.model.all.user.re.approve', defaultMessage: '当前节点所有审批人重新审批' });
  const allUserReApproveHelp = intl.formatMessage({ id: 'lcr.components.model.all.user.re.approve.help', defaultMessage: '当前节点的所有人需要重新审批' });
  const onlyMeReApprove = intl.formatMessage({ id: 'lcr.components.model.only.me.re.approve', defaultMessage: '当前节点审批人中仅我重新审批' });
  const onlyMeReApproveHelp = intl.formatMessage({ id: 'lcr.components.model.only.me.re.approve.help', defaultMessage: '只有操作拒绝的我需要重新审批' });
  const returnNode = intl.formatMessage({ id: 'lcr.components.model.return.node', defaultMessage: '驳回节点' });
  const addSignPurpose = intl.formatMessage({ id: 'lcr.components.model.add.sign.purpose', defaultMessage: '加签目的' });
  const addSignType = intl.formatMessage({ id: 'lcr.components.model.add.sign.type', defaultMessage: '加签类型' });
  const signType = intl.formatMessage({ id: 'lcr.components.model.sign.type', defaultMessage: '加签顺序' });
  const remark = intl.formatMessage({ id: 'lcr.components.model.approve.remark', defaultMessage: '审批意见' });
  const approveReason = intl.formatMessage({ id: 'lcr.components.model.approve.reason', defaultMessage: '审批理由' });
  const returnRemark = intl.formatMessage({ id: 'lcr.components.model.return.remark', defaultMessage: '驳回意见' });
  const transferRemark = intl.formatMessage({ id: 'lcr.components.model.transfer.remark', defaultMessage: '转交意见' });
  const addSignRemark = intl.formatMessage({ id: 'lcr.components.model.add.sign.remark', defaultMessage: '加签说明' });
  const addApproveRemark = intl.formatMessage({ id: 'lcr.components.model.add.approve.remark', defaultMessage: '审批意见' });
  const addTaskApprover = intl.formatMessage({ id: 'lcr.components.model.add.task.approver', defaultMessage: '加入他人一起审批' });
  const addTaskApproverHelp = intl.formatMessage({ id: 'lcr.components.model.add.task.approver.help', defaultMessage: '当自己一人无法决定审批结果时， 可以添加其他人一起参与审批，他人的审批意见按照审批方式一同决定审批结果' });
  const addSign = intl.formatMessage({ id: 'lcr.components.model.add.sign', defaultMessage: '听取他人建议' });
  const addSignHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.help', defaultMessage: '当不确定审批结果时，可以添加他人一起参与审批，但是他人的审批意见不影响审批结果' });
  const addSignBefore = intl.formatMessage({ id: 'lcr.components.model.add.sign.before', defaultMessage: '审批前加签' });
  const addSignBeforeHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.before.help', defaultMessage: 'A前加签给D,E一起审批，只有DE都审批完任务，才会流转到A继续审批。D,E的审批结果不影响节点最终结果' });
  const addSignBeforeApproveHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.before.approve.help', defaultMessage: 'A前加签给D,E一起审批。只有D,E全部审批通过，才会流转到A继续审批；若D,E其中一个拒绝则表示A任务拒绝，再根据节点审批方式决定节点结果' });
  const addSignAfter = intl.formatMessage({ id: 'lcr.components.model.add.sign.after', defaultMessage: '审批后加签' });
  const addSignAfterHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.after.help', defaultMessage: 'A必须审批同意当前任务，然后才可以后加签给D,E。D,E审批完成不再回到A。D,E的审批结果不影响节点最终结果' });
  const addSignAfterApproveHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.after.approve.help', defaultMessage: 'A必须审批同意当前任务，然后才可以后加签给D,E。只有D,E全部审批通过，才表示A任务审批同意。若D,E其中一个拒绝则表示A任务审批拒绝。再根据节点审批方式决定节点结果' });
  const addSignAnd = intl.formatMessage({ id: 'lcr.components.model.add.sign.and', defaultMessage: '审批时加签' });
  const addSignAndHelp = intl.formatMessage({ id: 'lcr.components.model.add.sign.and.help', defaultMessage: 'A并签给DE同时审批，无论DE有没有审批，A都可以审批。A审批完成后，会把DE的任务自动跳过' });
  const counterSign = intl.formatMessage({ id: 'lcr.components.model.counter.sign', defaultMessage: '同时审批' });
  const counterSignHelp = intl.formatMessage({ id: 'lcr.components.model.counter.sign.help', defaultMessage: '加签人是否同时审批' });
  const orSign = intl.formatMessage({ id: 'lcr.components.model.or.sign', defaultMessage: '指定顺序审批' });
  const orSignHelp = intl.formatMessage({ id: 'lcr.components.model.or.sign.help', defaultMessage: 'A加签给DE，D审批完成后，E才接收到待办进行审批' });
  const systemAddSignBeforeHelp = intl.formatMessage({ id: 'lcr.components.model.system.add.sign.before.help', defaultMessage: 'A前加签给D,E一起审批。只有D,E全部审批通过，才会流转到A继续审批；若D,E其中一个拒绝则表示A任务拒绝' });
  const systemAddSignAfterHelp = intl.formatMessage({ id: 'lcr.components.model.system.add.sign.after.help', defaultMessage: 'A必须审批同意当前任务，然后才可以后加签给D,E。只有D,E全部审批通过，才表示A任务审批同意。若D,E其中一个拒绝则表示A任务审批拒绝' });

  const remarkLabels = {
    REJECT: remark,
    REBUT: returnRemark,
    AGREE: remark,
    TRANSFER: transferRemark,
    ADD_TASK_APPROVER: addApproveRemark, // 加审叫审批意见
    ADD_SIGN: addSignRemark,
  };

  const reApproveOptions = new DataSet({
    data: [
      { value: 1, meaning: allUserReApprove, help: allUserReApproveHelp },
      { value: 2, meaning: onlyMeReApprove, help: onlyMeReApproveHelp },
    ],
  });

  // 目的
  const addSignPurposeOptions = new DataSet({
    data: [
      { value: 'ADD_TASK_APPROVER', meaning: addTaskApprover, help: addTaskApproverHelp },
      { value: 'ADD_SIGN', meaning: addSign, help: addSignHelp },
    ],
  });

  // 类型
  function getSignTypeOptions(purpose, workflowType) {
    const data = [
      {
        value: 'BEFORE',
        meaning: addSignBefore,
        // eslint-disable-next-line no-nested-ternary
        help: purpose === 'ADD_SIGN'
          ? (workflowType === 'SYSTEM' ? systemAddSignBeforeHelp : addSignBeforeHelp)
          : addSignBeforeApproveHelp,
      },
      {
        value: 'AFTER',
        meaning: addSignAfter,
        // eslint-disable-next-line no-nested-ternary
        help: purpose === 'ADD_SIGN'
          ? (workflowType === 'SYSTEM' ? systemAddSignAfterHelp : addSignAfterHelp)
          : addSignAfterApproveHelp,
      },
    ];

    if (workflowType === 'WITTY') {
      data.splice(1, 0, {
        value: 'AND',
        meaning: addSignAnd,
        help: addSignAndHelp,
      });
    }
    return new DataSet({ data });
  }

  // 顺序
  const signTypeOptions = new DataSet({
    data: [
      { value: 'COUNTER_SIGN', meaning: counterSign, help: counterSignHelp },
      { value: 'OR_SIGN', meaning: orSign, help: orSignHelp },
    ],
  });

  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    paging: false,
    fields: [
      { name: 'approveType', type: 'string' },
      { name: 'reApproveRunPathFlag', type: 'boolean' },
      { name: 'reApproveOptionFlag', type: 'boolean' },
      { name: 'remarkRequiredButtons', type: 'object' }, // 前中加签必填数组（因为加签和加审不同）
      { name: 'commentRequiredButtons', type: 'object' }, // 必填数组
      // 拒绝 REJECT
      {
        name: 'rejectAppointNode',
        type: 'string',
        label: rejectAppointNode,
        multiple: ',',
        lookupUrl: `/workflow/v1/${tenantId}/taskInstances/reject_appoint/${instanceId}`,
        // dynamicProps: {
        //   required: ({ record }) => record.get('approveType') === 'REJECT' && record.get('reApproveOptionFlag') && record.get('reApproveRunPathFlag'),
        // },
        help: rejectAppointNodeHelp,
        valueField: 'code',
        textField: 'name',
      },
      {
        name: 'reApproveFlag',
        type: 'number',
        label: reApprove,
        options: reApproveOptions,
        dynamicProps: {
          required: ({ record }) => record.get('approveType') === 'REJECT' && record.get('reApproveOptionFlag'),
        },
        help: reApproveHelp,
      },
      // 驳回 REBUT
      {
        name: 'returnNode',
        type: 'string',
        label: returnNode,
        lookupUrl: `/workflow/v1/${tenantId}/taskInstances/return_nodes/${instanceId}`,
        valueField: 'code',
        textField: 'name',
        dynamicProps: {
          required: ({ record }) => record.get('approveType') === 'REBUT',
        },
      },
      // 加签 SIGN
      {
        name: 'addSignPurpose',
        type: 'string',
        label: addSignPurpose,
        options: addSignPurposeOptions,
      },
      {
        name: 'addSignType',
        type: 'string',
        label: addSignType,
        dynamicProps: {
          required: ({ record }) => record.get('approveType') === 'SIGN',
          options: ({ record }) => getSignTypeOptions(record.get('addSignPurpose'), record.get('workflowType')),
        },
      },
      {
        name: 'signType',
        type: 'string',
        label: signType,
        options: signTypeOptions,
        dynamicProps: {
          required: ({ record }) => record.get('approveType') === 'SIGN' && record.get('workflowType') === 'WITTY',
        },
      },
      // 意见
      {
        name: 'remark',
        type: 'string',
        dynamicProps: {
          label: ({ record }) => {
            let approveType = record.get('approveType');
            // 加签需要根据是否"后加签/加审"判断叫审批意见
            if (approveType === 'SIGN') {
              approveType = record.get('addSignType') === 'AFTER' ? 'ADD_TASK_APPROVER' : 'ADD_SIGN';
            }
            // 如果是班翎工作流，审批意见 改成 审批理由(班翎和flowable工作流，英文下都改成，Approve Reason)
            if (record.get('workflowType') === 'WITTY' && ['REJECT', 'AGREE'].includes(approveType)) {
              return approveReason;
            }
            return remarkLabels[approveType] || remark;
          },
          required: ({ record }) => {
            let approveType = record.get('approveType'); // 审批类型
            let requiredButtons = record.get('commentRequiredButtons'); // 审批必填配置
            if (approveType === 'AGREE') {
              approveType = 'APPROVE';
            }
            // 加签还包含加审，需要特殊处理
            if (approveType === 'SIGN') {
              // 加签必填根据加签目的取
              approveType = record.get('addSignPurpose');
              if (record.get('addSignType') !== 'AFTER') {
                // 前/中加签从remarkRequiredButtons获取
                requiredButtons = record.get('remarkRequiredButtons'); // 加签说明
              }
            }
            return !!requiredButtons.includes(approveType);
          },
        },
      },
    ],
  };
};
