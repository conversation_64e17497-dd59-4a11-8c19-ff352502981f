export default ({ tenantId, instanceId, viewCode, getActionDataSet, queryUrl }) => {
  const url = `/lc/v1/${tenantId}/lc_actions/apply_list/${viewCode}${instanceId ? `?id=${instanceId}` : ''}`;

  return {
    autoQuery: false,
    paging: false,
    selection: false,
    primaryKey: 'id',
    transport: {
      read: {
        url: queryUrl || url,
        method: 'get',
      },
    },
    events: {
      load: ({ dataSet }) => {
        getActionDataSet && getActionDataSet(dataSet);
      },
    },
  };
};
