import React, { createContext, useMemo, useState, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import pick from 'lodash/pick';
import ActionDataSet from './ActionDataSet';
import ApproveDataSet from './ApproveDataSet';
import SurveyDataDataSet from './SurveyDataDataSet';
import BusinessObjectFieldDataSet from './BusinessObjectFieldDataSet';
import { WorkLogsDataSet, ReplyListDataSet, ActionListDataSet } from './DurationDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState', 'HeaderStore')((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      AppState,
      viewDataSet,
      viewCode,
      instanceId,
      formConfigRecord,
      formDataSet,
      actionRef,
      mode = 'menu',
      getActionDataSet = () => { },
      queryUrl,
    } = props;
    const prefixCls = 'itsm-ui-action';
    const viewId = formConfigRecord?.get('id');
    const jsonData = formConfigRecord?.get('jsonData');
    const businessObjectId = formConfigRecord?.get('businessObjectId');
    const businessObjectCode = formConfigRecord?.get('businessObjectCode');
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];

    const actionDataSet = useMemo(
      () => new DataSet(ActionDataSet({ tenantId, instanceId, viewCode, getActionDataSet, queryUrl })),
      [viewCode]
    );
    const surveyDataDataSet = useMemo(() => new DataSet(SurveyDataDataSet({ tenantId })), []);

    const businessObjectFieldDataSet = useMemo(
      () => new DataSet(BusinessObjectFieldDataSet({ tenantId, businessObjectId })),
      [tenantId, businessObjectId]
    );

    if (actionRef) {
      actionRef.current = {
        actionDataSet,
      };
    }

    // 动作添加工时相关👇
    const [defaultData, setDefaultData] = useState({});

    const getDataWithCache = async () => {
      const key = businessObjectId;
      const cache = AppState?.customConfig[key];
      if (cache) {
        return cache;
      }
      if (!instanceId || !tenantId) return;
      const res = axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${instanceId}`);
      AppState?.setCustomConfig(key, res);
      AppState?.setCustomConfig(`${businessObjectId}-${instanceId}`, res);
      return res;
    };

    const initConfig = async () => {
      const res = await getDataWithCache();
      setDefaultData(pick(res, ['workingTimeSettingFlag', 'workingTimeSettingVO', 'deleteWorkTimeFlag', 'addWorkTimeFlag', 'editWorkTimeFlag']));
    };

    useEffect(() => {
      if (instanceId && businessObjectId) {
        initConfig();
      }
    }, [instanceId, businessObjectId, tenantId]);

    const urlSuffix = `${businessObjectCode?.toLowerCase()}_working_time`;

    // 审批弹窗
    const approveDataSet = useMemo(
      () => new DataSet(ApproveDataSet({ intl, tenantId, instanceId })),
      [tenantId, instanceId]
    );

    // 工时弹窗
    const workLogsDataSet = useMemo(
      () => new DataSet(WorkLogsDataSet({ intl, tenantId, defaultData, instanceId })),
      [defaultData, instanceId, tenantId]
    );

    // 工时关联回复列表
    const replayListDataSet = useMemo(
      () => new DataSet(ReplyListDataSet({ intl, tenantId, ticketId: instanceId, businessObjectCode })),
      [instanceId, businessObjectCode]
    );

    // 工时关联动作记录列表
    const actionListDataSet = useMemo(
      () => new DataSet(ActionListDataSet({ intl, tenantId, ticketId: instanceId, businessObjectCode })),
      [instanceId, businessObjectCode]
    );

    // 工时表单必填应用逻辑
    useEffect(() => {
      // FIXME：此处应该根据 workingTimeSettingFlag 进行判断
      const requiredFieldList = defaultData?.workingTimeSettingVO?.required || [];
      if (requiredFieldList?.length > 0) {
        workLogsDataSet.setState('requiredFieldList', requiredFieldList);
      }
    }, [defaultData, workLogsDataSet]);

    const value = {
      ...props,
      actionDataSet,
      formDataSet,
      businessObjectFieldDataSet,
      dsFieldList,
      mode,
      prefixCls,
      formConfigRecord,
      tenantId,
      surveyDataDataSet,
      // 工时相关
      urlSuffix,
      defaultData,
      approveDataSet,
      workLogsDataSet,
      replayListDataSet,
      actionListDataSet,
      businessObjectCode,
      viewDataSet,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
