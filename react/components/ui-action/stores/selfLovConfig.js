/**
 * lov 定制化太严重，复用复杂
 */
import { getQueryParams } from '@zknow/utils';
import { transformResponse, transformField } from '@/utils/lovConfig';
import { filterUserByGroupIdCondition, filterGroupByUserIdCondition } from '../../ticket-transfer/store/conditions';

function deepClone(obj) {
  const copy = JSON.stringify(obj);
  return JSON.parse(copy);
}

const defaultCondition = (ticketId) => ({
  condition: 'AND',
  filters: [
    {
      condition: 'AND',
      filter: 'is not',
      widgetType: 'Lov',
      componentType: 'Lov',
      field: 'id',
      fieldValue: ticketId,
    },
  ],
});

const lovTransformResponse = (originData) => {
  try {
    const jsonData = JSON.parse(originData);

    return {
      ...jsonData,
      content: jsonData?.content?.map(item => {
        return {
          ...item,
          primaryKey: item.id,
        };
      }) || [],
    };
  } catch (error) {
    return [];
  }
};

const getLovQueryAxiosConfig = (props) => {
  const { id, type, tenantId, ticketId } = props || {};
  const extraConditions = [];
  if (type === 'USER') {
    const user = deepClone(filterUserByGroupIdCondition);
    // eslint-disable-next-line no-chinese/no-chinese
    user.filters[0].expression = user.filters[0].expression?.replace('《group_id》', id?.id || id);
    extraConditions.push(user);
  } else if (type === 'GROUP') {
    const group = deepClone(filterGroupByUserIdCondition);
    // eslint-disable-next-line no-chinese/no-chinese
    group.filters[0].expression = group.filters[0].expression?.replace('《user_id》', id?.id || id);
    extraConditions.push(group);
  }
  return (lovCode, lovConfig = {}, { data, params }) => {
    lovConfig.method = 'POST';
    return {
      url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
      method: 'POST',
      data: {
        conditions: [...extraConditions, defaultCondition(ticketId)],
        params: {
          ...getQueryParams(data),
          __page_params: data?.__page_params,
        },
      },
      params,
      transformResponse: lovTransformResponse,
    };
  };
};

const getLovConfig = (tenantId, ticketId, intl) => {
  return {
    lovDefineAxiosConfig: lovCode => {
      return ({
        url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
        method: 'GET',
        transformResponse: data => {
          const tr = transformResponse(data, data?.name, (map, f) => transformField(map, f), intl, tenantId);
          // 为了保持交互的统一，强制开启输入，即使值列表不支持查询
          return { ...tr, editableFlag: 'Y' };
        },
      });
    },
    lovQueryAxiosConfig: getLovQueryAxiosConfig({ ticketId, tenantId }),
  };
};

export {
  getLovConfig,
  getLovQueryAxiosConfig,
};
