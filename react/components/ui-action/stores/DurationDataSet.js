import { DataSet } from 'choerodon-ui/pro';
import { getLovConfig, getLovQueryAxiosConfig } from './selfLovConfig';

export const WorkLogsDataSet = ({ intl, intlPrefix = 'lc.components.workHour', tenantId, defaultData, instanceId }) => {
  const entryDate = intl.formatMessage({ id: `${intlPrefix}.enterDate` });
  const category = intl.formatMessage({ id: `${intlPrefix}.category` });
  const type = intl.formatMessage({ id: `${intlPrefix}.type` });
  const timeSpent = intl.formatMessage({ id: `${intlPrefix}.timeSpent` });
  const note = intl.formatMessage({ id: `${intlPrefix}.note` });
  const assignmentPersonName = intl.formatMessage({ id: `${intlPrefix}.assignmentPersonName` });
  const assignmentGroupName = intl.formatMessage({ id: `${intlPrefix}.assignmentGroupName` });
  const serviceWay = intl.formatMessage({ id: `${intlPrefix}.serviceWay` });
  const relate = intl.formatMessage({ id: `${intlPrefix}.relate` });
  const reply = intl.formatMessage({ id: `${intlPrefix}.relate.reply` });
  const action = intl.formatMessage({ id: `${intlPrefix}.relate.action` });
  const requiredList = defaultData?.workingTimeSettingVO?.required || [];

  let operatorField = {
    lovCode: 'USER',
    dynamicProps: {
      lovPara: ({ record }) => {
        const id = record?.get?.('operatorGroupId')?.id;
        if (id) {
          return { search_groupId: record.get('operatorGroupId')?.id };
        } else {
          return null;
        }
      },
    },
  };

  let operatorGroupField = {
    lovCode: 'GROUP',
    dynamicProps: {
      lovPara: ({ record }) => {
        const id = record?.get?.('operatorId')?.id;
        if (id) {
          return { search_userId: record.get('operatorId')?.id };
        } else {
          return null;
        }
      },
    },
  };

  // NOTE: 【服务配置-工时设置】中会修改工时的处理人/组的值列表，这边需要使用指定的值列表查询人和组
  //    以满足可以给外部人员填写工时的需求
  if (defaultData?.workingTimeSettingFlag) {
    const lovConfig = getLovConfig(tenantId, instanceId, intl);
    const userLovId = defaultData?.workingTimeSettingVO?.userLovId;
    const groupLovId = defaultData?.workingTimeSettingVO?.groupLovId;
    if (userLovId) {
      operatorField = {
        lovCode: userLovId,
        ...lovConfig,
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const groupId = record?.get('operatorGroupId');
            return groupId ? getLovQueryAxiosConfig({ id: groupId, type: 'USER', tenantId, ticketId: instanceId }) : getLovQueryAxiosConfig({ tenantId, ticketId: instanceId });
          },
        },
      };
    }
    if (groupLovId) {
      operatorGroupField = {
        lovCode: groupLovId,
        ...lovConfig,
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const userId = record?.get('operatorId');
            return userId ? getLovQueryAxiosConfig({ id: userId, type: 'GROUP', tenantId, ticketId: instanceId }) : getLovQueryAxiosConfig({ tenantId, ticketId: instanceId });
          },
        },
      };
    }
  }

  const RelateOptions = new DataSet({
    data: [{ value: 'REPLY', text: reply }, { value: 'ACTION', text: action }],
  });
  return {
    autoCreate: true,
    autoQuery: false,
    autoLocateFirst: true,
    paging: false,
    fields: [
      {
        name: 'operatorId',
        label: assignmentPersonName,
        required: true,
        type: 'object',
        valueField: 'id',
        transformRequest: (value, record) => {
          return value?.id;
        },
        ...operatorField,
      },
      {
        name: 'operatorGroupId',
        label: assignmentGroupName,
        type: 'object',
        required: requiredList.includes('operatorGroupId'),
        transformRequest: (value, record) => {
          return value?.id;
        },
        ...operatorGroupField,
      },
      // 时长
      {
        name: 'duration',
        label: timeSpent,
        required: true,
        defaultValue: 0,
        transformRequest: (value, record) => {
          const hours = record.get('hours') || 0;
          const minutes = record.get('minutes') || 0;
          return hours * 60 + minutes;
        },
      },
      // 发生时间
      {
        name: 'entryDate',
        label: entryDate,
        dynamicProps: {
          required: ({ record }) => {
            return requiredList.includes('entryDate') && record.get('category') === 'ACTUAL_WORK_TIME';
          },
        },
      },
      // 工时类别
      {
        name: 'category',
        label: category,
        lookupCode: 'WORK_TIME_CATEGORY',
        defaultValue: 'SCHEDULED_WORK_TIME',
      },
      // 工时类型
      {
        name: 'type',
        label: type,
        lookupCode: 'WORKLOG_TYPE',
        required: requiredList.includes('type'),
      },
      // 服务方式
      {
        name: 'serviceWay',
        label: serviceWay,
        lookupCode: 'SERVICE_WAY',
        required: requiredList.includes('serviceWay'),
      },
      // 备注
      { name: 'note', label: note },
      // 时
      {
        name: 'hours',
        required: true,
        validator: async (value, fieldName, record) => {
          if (value > 9999) {
            return intl.formatMessage({ id: `${intlPrefix}.minutes.limit` });
          }
          if (!value && value !== 0 && !record?.get('minutes')) {
            return intl.formatMessage({ id: `${intlPrefix}.minutes.required` });
          }
        },
        transformResponse: (value, data) => {
          if (typeof data?.duration !== 'number') {
            return undefined;
          }
          return Math.floor((data?.duration || 0) / 60);
        },
        dynamicProps: {
          required: ({ record }) => record.get('minutes'),
        },
      },
      // 分
      {
        name: 'minutes',
        required: true,
        validator: async (value, fieldName, record) => {
          if (value > 9999) {
            return intl.formatMessage({ id: `${intlPrefix}.minutes.limit` });
          }
          if (!value && value !== 0 && !record?.get('hours')) {
            return intl.formatMessage({ id: `${intlPrefix}.minutes.required` });
          }
        },
        transformResponse: (value, data) => {
          // 防止给工时 0 的默认值
          if (typeof data?.duration !== 'number') {
            return undefined;
          }
          return (data?.duration || 0) % 60;
        },
        dynamicProps: {
          required: ({ record }) => record.get('hours'),
        },
      },
      {
        name: 'relate',
        type: 'string',
        defaultValue: 'REPLY',
        label: relate,
        textField: 'text',
        valueField: 'value',
        options: RelateOptions,
      },
    ],
  };
};

export const ReplyListDataSet = ({ intl, intlPrefix = 'lc.components.workHour', tenantId, businessObjectCode, ticketId }) => {
  const urlPrefix = `itsm/v1/${tenantId}/journals/journals/workTime`;

  const content = intl.formatMessage({ id: `${intlPrefix}.content` });
  const replyTime = intl.formatMessage({ id: `${intlPrefix}.replyTime` });

  return {
    autoQuery: false,
    cacheSelection: true,
    primaryKey: 'id',
    transport: {
      read: {
        url: `${urlPrefix}?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}&content=`,
        method: 'get',
      },
    },
    queryFields: [{ name: 'content', label: content }],
    fields: [
      { name: 'id' },
      { name: 'content', label: content },
      { name: 'replyTime', label: replyTime },
    ],
  };
};

export const ActionListDataSet = ({ intl, intlPrefix = 'lc.components.workHour', tenantId, businessObjectCode, ticketId }) => {
  const urlPrefix = `itsm/v1/${tenantId}/journals/action/${ticketId}`;

  const name = intl.formatMessage({ id: `${intlPrefix}.relate.action.name` });
  const creationDate = intl.formatMessage({ id: `${intlPrefix}.relate.action.createTime` });

  return {
    autoQuery: false,
    cacheSelection: true,
    primaryKey: 'id',
    transport: {
      read: {
        url: `${urlPrefix}?businessObjectCode=${businessObjectCode}`,
        method: 'get',
      },
    },
    queryFields: [{ name: 'name', label: name }],
    fields: [
      { name: 'id' },
      { name: 'name', label: name },
      { name: 'creationDate', label: creationDate },
    ],
  };
};
