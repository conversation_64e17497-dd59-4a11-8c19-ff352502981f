@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.c7n-pro-btn + .itsm-ui-action {
  margin-left: 0.08rem;
}

.itsm-ui-action + .c7n-pro-btn {
  margin-left: 0.08rem;
}

.itsm-ui-action {
  display: inline-block;

  .c7n-pro-btn-default {
    background-color: @primary-x;
  }

  &-modal {
    .quill {
      // height: 0.82rem;
      max-height: 80px;
    }

    .@{c7n-pro-prefix}-modal-body {
      .lc-form-page-loader-survey {
        td {
          padding-bottom: 16px;
          .@{c7n-pro-prefix}-field-label {
            height: 30px;
          }
          .@{c7n-pro-prefix}-field-wrapper {
            line-height: 0;
            padding: 0 0.16rem 0 0.16rem;
          }
        }
      }
    }
  }

  &-dropdown-icon {
    height: 0.32rem;
    width: 0.32rem;
    align-items: center;
    justify-content: center;
    border-left: solid 1px @primary-2;
    margin-left: 8px;
    padding: 0;
  }

  &-button-hasIcon {
    padding-right: 0 !important;
  }

  &-item-hasIcon {
    padding-right: 0;

    &:hover {
      .itsm-ui-action-dropdown-icon {
        border-color: @primary-color;
        border-left-color: rgba(255, 255, 255, 0.54) !important;
      }
    }
  }
  &-afterExecute-feedback {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px;
    &-text {
      margin: 16px 0;
    }
  }
}

span.itsm-ui-action-button-hasIcon {
  button {
    display: contents;
  }
}

.review-modal {
  .c7n-pro-modal-body {
    padding-top: 0;
    padding-bottom: 0;
  }
}

.result-modal-class {
  .c7n-pro-modal-header-button {
    margin-top: 17px;
  }
}

.result-copy-info {
  border-top: 1px solid rgba(203, 210, 220, 0.5);
  padding-top: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  &-cmp {
    padding-top: 28px;
    .c7n-result-icon {
      margin-bottom: 16px;
    }
    .c7n-result-title {
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
    }
    & .c7n-result-extra {
      margin-top: 20px;
    }
  }
  & > div {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 14px;
    & > span {
      color: rgba(18, 39, 77, 0.65);
      line-height: 22px;
      font-size: 14px;
      margin-right: 8px;
    }

    & > div {
      display: flex;
      align-items: center;
      color: #2979ff;
    }
  }
}
