import { getQueryParams } from '@zknow/utils';
import { transformResponse } from '@/components/page-loader/lovConfig';
import { transformField, FILTER_FIELD_CODES } from '@/components/page-loader/stores/DataSetManager';

const fieldMap = {
  SIGN: {
    name: 'candidateUsers',
    multiple: true,
    labelCode: 'lcr.components.model.candidate.users',
  },
  TRANSFER: {
    name: 'transferPersonId',
    multiple: false,
    labelCode: 'lcr.components.model.transfer.person',
  },
};

export function updateFieldsConfig({ approveDataSet, approveType, userLovId, tenantId, intl, instanceId }) {
  if (!Object.keys(fieldMap).includes(approveType)) return;
  const fixedParams = { id: instanceId, button: approveType, userLovId };
  approveDataSet.addField(fieldMap[approveType].name, {
    type: 'object',
    lovPara: userLovId ? { fixedParams } : fixedParams,
    label: intl.formatMessage({ id: fieldMap[approveType].labelCode }),
    multiple: fieldMap[approveType].multiple,
    lovCode: userLovId || 'TASK_PERSON_RANGE',
    textField: 'real_name',
    lovDefineAxiosConfig: userLovId ? lovCode => ({
      url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
      method: 'GET',
      transformResponse: data => transformResponse(
        data,
        data?.name,
        (map, f) => transformField(
          {
            fieldMap: map,
            field: f,
            tenantId,
            intl,
          },
        ),
        intl,
        tenantId,
      ),
    }) : undefined,
    lovQueryAxiosConfig: userLovId ? (lovCode, lovConfig = {}, { data, params }) => {
      const { parentIdField, treeFlag, idField } = lovConfig;
      let searchFlag = false;
      const { fixedParams, ...rest } = data;
      const queryParams = getQueryParams(rest, ['fixedParams', parentIdField]);

      Object.keys(queryParams).forEach((v) => {
        if (v.indexOf('search_') !== -1) {
          searchFlag = true;
        }
      });

      if (treeFlag === 'Y' && parentIdField) {
        if (data[parentIdField]) {
          params.size = 999;
        } else if (!searchFlag) {
          queryParams[parentIdField] = '0';
        }
      }

      const dataParams = {
        ...queryParams,
        ...(params || {}),
      };
      delete dataParams.fixedParams;
      return {
        url: `/workflow/v1/${tenantId}/taskInstances/person_range`,
        method: 'POST',
        data: {
          params: dataParams,
        },
        params: {
          ...params,
          ...(fixedParams || {}),
        },
        transformResponse: (originData) => {
          try {
            const jsonData = JSON.parse(originData);
            return {
              ...jsonData,
              content: jsonData?.content?.map(item => {
                // 由于富文本字段展示在值列表中需要转换，导致值列表赋值时，会导致样式丢失
                // 这里缓存一下原始字段值
                const backup = {};
                Object.keys(item).map(key => {
                  if (
                    !FILTER_FIELD_CODES.includes(key)
                    && item[key]
                    && typeof item[key] === 'string'
                    && (item[key].includes('<p>') || item[key].includes('<table>'))
                  ) {
                    backup[`${key}-backup`] = item[key];
                  }
                  return key;
                });
                if (searchFlag) {
                  // 搜索时，树形结构打平显示
                  return {
                    ...backup,
                    ...item,
                    isLeaf: true,
                    [parentIdField]: null,
                    id: item[idField] || item.id,
                    primaryKey: item.id,
                  };
                }
                return {
                  ...backup,
                  ...item,
                  id: item[idField] || item.id,
                  primaryKey: item.id,
                };
              }) || [],
            };
          } catch (error) {
            return [];
          }
        },
      };
    } : undefined,
    transformRequest: (value) => {
      if (approveType === 'SIGN') {
        return Array.isArray(value) ? value.map(v => v.id) : value;
      }
      return value?.id || value;
    },
    dynamicProps: {
      required: ({ record }) => record.get('approveType') === approveType,
    },
  });
}
