import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, message, Modal, CodeArea } from 'choerodon-ui/pro';
import axios from 'axios';
import { color as colorUtils } from '@zknow/utils';
import { StatusTag, TableHoverAction } from '@zknow/components';
import Store from './stores';
import './index.less';

const { Column } = Table;
const PrintHistory = () => {
  const context = useContext(Store);
  const { intl, historyDataSet, tenantId } = context;
  const renderStatus = ({ value = '', text }) => {
    const colorMap = {
      COMPLETED: colorUtils?.getColorDefaultValue('green'),
      RUNNING: colorUtils?.getColorDefaultValue('orange'),
      FAILED: colorUtils?.getColorDefaultValue('red'),
    };
    return <StatusTag mode="icon" color={colorMap[value]}>{text}</StatusTag>;
  };

  const renderTableAction = ({ dataSet, record: current }) => {
    let actions = [];
    if (current?.get('status') === 'COMPLETED') {
      actions = [
        {
          name: intl.formatMessage({ id: 'lcr.components.desc.print', defaultMessage: '打印' }),
          icon: 'printer',
          key: 'print',
          onClick: async () => {
            const res = await axios.get(`/report/v1/${tenantId}/report-print/history/download/${current.get('id')}`, {
              responseType: 'blob',
            });
            if (!res?.failed) {
              const blob = new Blob([res], { type: 'application/pdf' });
              const link = window.URL.createObjectURL(blob);
              // 创建一个隐藏的iframe用于加载PDF
              const iframe = document.createElement('iframe');
              iframe.style.display = 'none'; // 确保iframe不会显示在页面上
              iframe.src = link;
              document.body.appendChild(iframe);

              // 加载完成后自动打印
              iframe.onload = function () {
                iframe.contentWindow.print();
              };
            } else {
              message.error(`${intl.formatMessage({ id: 'lcr.components.desc.print.template.api.error', defaultMessage: '接口错误数据: {message}' }, { message: res?.message })}`);
            }
          },
        },
      ];
    } else if (current?.get('status') === 'FAILED') {
      actions = [
        {
          name: intl.formatMessage({ id: 'lcr.components.desc.print.template.error.detail.watch', defaultMessage: '查看日志' }),
          icon: 'preview-open',
          key: 'dailry',
          onClick: () => {
            Modal.open({
              title: intl.formatMessage({ id: 'lcr.components.desc.print.template.error.detail', defaultMessage: '错误日志' }),
              children: (
                <CodeArea value={current.get('failureMsg')} disabled />
              ),
              drawer: true,
              footer: null,
            });
          },
        },
      ];
    }
    return <TableHoverAction key="action" record={current} actions={actions} intlBtnIndex={-1} />;
  };
  return (
    <div className="import-history-form">
      <Table
        pristine
        autoHeight
        dataSet={historyDataSet}
        queryBarProps={{
          title: intl.formatMessage({ id: 'lcr.components.desc.import.history', defaultMessage: '导入历史' }),
        }}
      >
        <Column name="name" />
        <Column name="size" />
        <Column name="createdByName" />
        <Column name="status" renderer={renderStatus} />
        <Column name="creationDate" />
        <Column
          width={0}
          renderer={renderTableAction}
          lock="right"
          tooltip="none"
        />
      </Table>
    </div>
  );
};

export default observer(PrintHistory);
