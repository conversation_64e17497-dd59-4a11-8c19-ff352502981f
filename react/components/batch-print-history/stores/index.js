import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import HistoryDataSet from './historyDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId },
        currentLanguage: language,
      },
    } = props;

    const intlPrefix = 'batch.print';
    const prefixCls = 'batch-print';

    const historyDataSet = useMemo(() => new DataSet(HistoryDataSet({
      intl, tenantId,
    })), [tenantId]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      historyDataSet,
      language,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
