import moment from 'moment';
import { getQueryParams } from '@zknow/utils';
import { DataSet } from 'choerodon-ui/pro';
import filesize from 'filesize';

export default ({ intl, tenantId }) => {
  const url = `/report/v1/${tenantId}/report-print/history/page`;
  const name = intl.formatMessage({ id: 'lcr.components.model.download.history.file', defaultMessage: '文件' });
  const size = intl.formatMessage({ id: 'lcr.components.model.download.history.size', defaultMessage: '文件大小' });
  const createdBy = intl.formatMessage({ id: 'zknow.common.model.createdBy', defaultMessage: '创建人' });
  const createdDate = intl.formatMessage({ id: 'zknow.common.model.creationDate', defaultMessage: '创建时间' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status', defaultMessage: '状态' });
  const momentFormatStart = 'YYYY-MM-DD 00:00:00';
  const momentFormatEnd = 'YYYY-MM-DD 23:59:59';
  const statusOptionDs = new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'lcr.components.model.completed', defaultMessage: '已完成' }), value: 'COMPLETED' },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.failure', defaultMessage: '失败' }), value: 'FAILED' },
      { meaning: intl.formatMessage({ id: 'lcr.components.model.part.failed', defaultMessage: '部分失败' }), value: 'PART_FAILED' },
      { meaning: intl.formatMessage({ id: 'lcr.components.model.printing', defaultMessage: '打印中' }), value: 'RUNNING' },
    ],
  });
  return {
    autoQuery: true,
    selection: false,
    transport: {
      read: ({ data }) => {
        return {
          url,
          method: 'get',
          data: getQueryParams(data),
        };
      },
    },
    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
      },
      {
        name: 'size',
        type: 'string', 
        label: size, 
        transformResponse: (value, object) => filesize(+(value || 0)),
      },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        options: statusOptionDs,
      },
      {
        name: 'creationDate',
        type: 'string',
        label: createdDate,
      },
    ],
    queryFields: [
      {
        name: 'name',
        type: 'string',
        label: name,
      },
      {
        name: 'createdByName',
        type: 'string',
        label: createdBy,
      },
      {
        name: 'status',
        type: 'string',
        label: status,
        options: statusOptionDs,
      },
      {
        name: 'creationDate',
        type: 'date',
        range: ['start', 'end'],
        format: 'YYYY-MM-DD',
        label: createdDate,
      },
    ],
    events: {
      query: ({ params, data }) => {
        if (params.search_creationDate) {
          if (params.search_creationDate.start) {
            params.search_startCreationDate = moment(params.search_creationDate.start).format(momentFormatStart);
          }
          params.search_endCreationDate = moment(params.search_creationDate.end).format(momentFormatEnd);
          delete params.search_creationDate;
        }
      },
    },
  };
};
