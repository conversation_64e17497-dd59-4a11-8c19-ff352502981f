import { useEffect, useRef } from 'react';

export const usePollingFn = (fn, openFlag, item) => {
  const tp = useRef();
  const closePolling = () => {
    if (tp.current) clearInterval(tp.current);
  };
  const openPolling = () => {
    closePolling();
    if (!item) return;
    tp.current = setInterval(() => {
      fn();
    }, item);
  };

  useEffect(() => {
    if (openFlag) openPolling();
    if (!openFlag) closePolling();
    return () => {
      closePolling();
    };
  }, [openFlag, item]);
};
