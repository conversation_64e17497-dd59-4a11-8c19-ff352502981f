export const getInitClewToneData = () => {
  let initData = sessionStorage.getItem('yqcloud_lowcode_clew_tone');
  try {
    initData = JSON.parse(initData) || [];
  } catch (e) {
    initData = [];
  }
  return initData;
};

export default ({ intl }) => {
  const clewTone = intl.formatMessage({ id: 'lcr.renderer.model.clew.tone.button', defaultMessage: '提示音' });
  const promptFrequency = intl.formatMessage({ id: 'lcr.renderer.model.prompt.frequency', defaultMessage: '提示频率(s)' });

  return {
    autoCreate: true,
    primaryKey: 'id',
    data: getInitClewToneData(),
    fields: [
      { name: 'clewToneFlag', type: 'boolean', label: clewTone },
      { name: 'promptFrequency', type: 'number', required: true, label: promptFrequency },
    ],
    // events: {
    //   update: ({ dataSet, record, name, value, oldValue }) => {
    //     console.log(dataSet, record, name, value, oldValue);
    //   },
    // },
  };
};
