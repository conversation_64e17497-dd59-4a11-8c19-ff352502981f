import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { Button, Form, DataSet, message, Modal, Switch, NumberField } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import ModalDataSet, { getInitClewToneData } from './ModalDataSet';
import { usePollingFn } from './pollingFn';
import clewToneMp3 from './clewTone.mp3';

const modalKey = Modal.key();
const audio = new Audio(clewToneMp3);

const DetailButton = injectIntl(
  observer((props) => {
    const {
      intl,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config = {},
      formDataSet,
      viewDataSet,
      pageContext,
    } = props;
    const {
      icon,
      name,
      id,
      color = 'default',
      widgetConfig,
      frequency,
    } = config;
    const [clewToneFlagSubmit, setClewToneFlagSubmit] = useState();
    const [promptFrequencySubmit, setPromptFrequencySubmit] = useState();
    const modalDataSet = useMemo(() => (new DataSet(ModalDataSet({ intl }))), []);

    usePollingFn(async () => {
      const [view] = viewDataSet.toData();
      const businessObjectId = view?.businessObjectId;
      const res = await axios({
        url: `/report/v1/${tenantId}/beep/test-condition?businessObjectId=${businessObjectId}`,
        method: 'POST',
        data: widgetConfig?.condition || [],
      });
      if (res.failed) {
        setClewToneFlagSubmit(false);
        message.error(res.message);
        return false;
      }
      if (res.result) {
        await audio.play().catch(err => {
        });
      }
    }, clewToneFlagSubmit, 1000 * (promptFrequencySubmit || 0));

    useEffect(() => {
      if (frequency && (!getInitClewToneData()?.[0]?.promptFrequency || getInitClewToneData()?.[0]?.clewToneFlag)) {
        setClewToneFlagSubmit(true);
        setPromptFrequencySubmit(frequency);
        modalDataSet.current.set('promptFrequency', frequency);
        modalDataSet.current.set('clewToneFlag', true);
        sessionStorage.setItem('yqcloud_lowcode_clew_tone', JSON.stringify(modalDataSet.toData() || []));
      }
    }, []);

    const handleClick = useCallback(async () => {
      Modal.open({
        title: intl.formatMessage({ id: 'lcr.renderer.desc.create.service.order', defaultMessage: '创建服务单' }),
        key: modalKey,
        autoCenter: true,
        closable: true,
        children: (
          <Form
            labelLayout="horizontal"
            dataSet={modalDataSet}
          >
            <Switch name="clewToneFlag" />
            <NumberField name="promptFrequency" />
          </Form>
        ),
        onOk: async () => {
          if (modalDataSet.validate()) {
            setClewToneFlagSubmit(modalDataSet.current.get('clewToneFlag'));
            setPromptFrequencySubmit(modalDataSet.current.get('promptFrequency'));
            sessionStorage.setItem('yqcloud_lowcode_clew_tone', JSON.stringify(modalDataSet.toData() || []));
          }
        },
        onCancel: () => {
          modalDataSet.loadData(getInitClewToneData());
        },
        onClose: () => {
          modalDataSet.loadData(getInitClewToneData());
        },
      });
    }, [formDataSet, viewDataSet]);

    return (
      <Button
        funcType="flat"
        key={id}
        hidden={pageContext && !pageContext?.callRecordsDataSet?.current}
        onClick={handleClick}
        color={color}
        icon={icon}
        waitType="debounce"
        wait={200}
      >
        {name}
      </Button>
    );
  }),
);
export default inject('AppState')(observer((props) => (
  <DetailButton {...props} />
)));

/* externalize: ClewToneButton */
