/*
 * @Author: x<PERSON><PERSON><PERSON> <<EMAIL>>
 * @Date: 2022-10-25 10:46:20
 * @Description:
 */
import sanitizeHtml from 'sanitize-html';

/* defautl options
allowedTags: [ 'h3', 'h4', 'h5', 'h6', 'blockquote', 'p', 'a', 'ul', 'ol',
  'nl', 'li', 'b', 'i', 'strong', 'em', 'strike', 'code', 'hr', 'br', 'div',
  'table', 'thead', 'caption', 'tbody', 'tr', 'th', 'td', 'pre' ],

allowedAttributes: {
  a: [ 'href', 'name', 'target' ],
  // We don't currently allow img itself by default, but this
  // would make sense if we did
  img: [ 'src' ]
},

  // Lots of these won't come up by default because we don't allow them
selfClosing: [ 'img', 'br', 'hr', 'area', 'base', 'basefont', 'input', 'link', 'meta' ],
  // URL schemes we permit
allowedSchemes: [ 'http', 'https', 'ftp', 'mailto' ],
allowedSchemesByTag: {},
allowProtocolRelative: true
*/

const externalTags = [
  'img',
  'h1',
  'h2',
  'h7',
  'font',
  'span',
  'del',
  'u',
  'sub',
  'sup',
  'figure',
  'a',
  'figcaption',
];

const excludeTags = ['iframe'];
const allowedTags = (sanitizeHtml.defaults.allowedTags || []).filter(item => !excludeTags.includes(item)).concat(externalTags);
const allowedAttr = [
  'src',
  'style',
  'class', 'id', 'role',
  'data-id', 'data-type', 'data-card-editable', 'data-syntax', 'data-value',
  'target',
  'checked',
  'colspan', 'rowspan',
  'data-yqmention', 'yqmention-type', 'tenantid', 'fromtenantid',
  'data-json',
  'href',
  'color', 'size', 'align',
];

const allowedAttributes = {};

allowedTags.forEach(tag => {
  allowedAttributes[tag] = allowedAttr;
});

const sanitizeOptions = {
  allowedTags,
  allowedAttributes,
  allowedSchemes: ['data', 'http', 'https'], // 防止 base64 格式的图片被吞，增加 data 类型 scheme
};

export default sanitizeOptions;
