@import '~choerodon-ui/lib/style/themes/default';

.yq-components-rich-text-preview {
  .yqmention {
    color: @primary-color !important;
    cursor: pointer;
  }

  .ck-editor-preview {
    .reply-audio {
      display: none;
    }

    .dynamic-box-fileList {
      display: none;
    }
  }

  .ck-read-only {
    min-height: unset !important;
    padding: 0;
    // 不知道ckeditor最后为什么有个P标签包裹了换行。倒数第二个元素的margin去掉
    p:nth-last-child(2) {
      margin-bottom: 0;
    }

    p:last-child {
      display: none;
    }

    p:first-child {
      margin-top: 0;
      margin-bottom: 14px;
    }
  }

  img {
    max-width: 80%;
  }

  table, tr, td, th {
    border: 1px #bfbfbf solid;
    border-collapse: collapse;
  }

  table td {
    min-width: 2em;
    padding: .4em;
  }

  figcaption {
    caption-side: bottom;
    word-break: break-word;
    color: var(--ck-color-image-caption-text);
    background-color: var(--ck-color-image-caption-background);
    padding: 0.6em;
    font-size: .75em;
    outline-offset: -1px;
    max-width: 80%;
    text-align: center;
  }
}

.solution-renderer-main {
  .yq-components-rich-text-preview {
    .ck-read-only {
      background: #f2f7ff !important;
    }
  }
}

// 单据头
.ticket-header-renderer {
  .ck-editor-preview {
    .dynamic-box-fileList {
      display: none;
    }
  }

  &-description {
    img {
      width: 100%;
      max-width: 500px;
      max-height: 450px;
    }
  }
}

.yq-components-wysiwyg-preview {
  .yqmention {
    color: @primary-color !important;
    cursor: pointer;
  }

  // 字体颜色统一为 #12274d
  p {
    color: #12274d;

    &:last-child {
      margin-bottom: 0;
    }
  }

  img {
    max-width: 80%;
  }

  table, tr, td, th {
    border: 1px #bfbfbf solid;
    border-collapse: collapse;
  }

  table td {
    min-width: 2em;
    padding: .4em;
  }

  figcaption {
    caption-side: bottom;
    word-break: break-word;
    color: var(--ck-color-image-caption-text);
    background-color: var(--ck-color-image-caption-background);
    padding: 0.6em;
    font-size: .75em;
    outline-offset: -1px;
    max-width: 80%;
    text-align: center;
  }

  .yqmention-company {
    font-size: 12px;
    font-weight: 400;
    color: #fd7d23;
    line-height: 20px;
    background: #fff7e9;
    margin-left: 4px;
    border-radius: 2px;
    height: 20px;
    text-align: center;
    padding: 0 6px;
    display: none;
  }
}
