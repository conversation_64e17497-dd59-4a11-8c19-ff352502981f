/*
 * @Author: xia<PERSON>ya
 * @Date: 2022-05-05 11:35:59
 * @Description:
 */
import React, { useEffect, useMemo, Fragment } from 'react';
import { ExternalComponent } from '@zknow/components';
import sanitizeHtml from 'sanitize-html';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { getCookieToken } from '@zknow/utils';
import { getRichJson } from '../utils/utils.js';
import FileItem from '../ticket-header/components/file-item';
import sanitizeOptions from './sanitizeOptions';
import './index.less';
import { deltaToHtmlStr } from '../../utils';

const WysiwygPreview = injectIntl((props) => {
  const {
    className,
    showExtraElement = false, // 展示附件，音频等信息
    intl,
    tenantId,
    udmTenantId,
    ticketId,
    AppState,
  } = props;

  useEffect(() => {
    setAtCompanyStyle();
    setAtKnowledgeStyle();
    setTimeout(() => {
      setAtCompanyStyle();
      setAtKnowledgeStyle();
    }, 500);
  }, [props?.data]);

  function setAtCompanyStyle() {
    const yqMentionHTMLCollection = document.getElementsByClassName('yqmention-company');
    [].forEach.call(yqMentionHTMLCollection, dom => {
      // 本租户的不显示公司名称
      if (dom.getAttribute('fromtenantid') === tenantId) {
        dom.setAttribute('style', 'display: none');
      } else if (dom.getAttribute('fromtenantid') && tenantId) {
        dom.setAttribute('style', 'display: inline-block');
      }
    });
  }

  function setAtKnowledgeStyle() {
    const yqMentionHTMLCollection = document.getElementsByClassName('yqmention');
    [].forEach.call(yqMentionHTMLCollection, dom => {
      // 不是本租户的知识无法点击，显示黑色
      if (dom.getAttribute('yqmention-type') === 'knowledge' && dom.getAttribute('tenantid') !== tenantId) {
        dom.setAttribute('style', 'color:#12274D');
      }
    });
  }

  // 获取附加信息
  function getExtraInfo() {
    try {
      const content = props?.dataAttachment || props?.data;
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  }

  // 渲染音频文件
  function renderAudioArea() {
    try {
      const descriptionExtraInfo = getExtraInfo();
      const { audios = [] } = descriptionExtraInfo || {};
      if (audios?.length === 0) return null;
      return <div className="reply-audio">{audios.map((i) => <ExternalComponent system={{ scope: 'itsm', module: 'YqAudio' }} fileInfo={i} />)}</div>;
    } catch {
      //
    }
  }

  // 渲染附件
  function renderAttachmentsArea() {
    const descriptionExtraInfo = getExtraInfo();
    const { attachments = [] } = descriptionExtraInfo || {};
    if (attachments?.length === 0) return null;
    return attachments?.map((i, index) => {
      return (
        <FileItem
          data={i}
          isLast={attachments.length - 1 === index}
          tenantId={tenantId}
          intl={intl}
          prefixCls="ticket-header-renderer-file"
        />
      );
    });
  }

  const htmlString = useMemo(() => {
    let htmlContent = '';
    let data = props?.data;
    if (typeof props?.data === 'number') {
      data = props?.data.toString();
    }
    if (data?.constructor === Object) {
      data = deltaToHtmlStr(data);
    }
    if (data?.indexOf('<p data-json=') > -1) {
      htmlContent = data.substr(0, data.indexOf('<p data-json='));
    } else {
      htmlContent = data;
    }
    return htmlContent?.replace(/(access_token=)[^\s&"]+/, `$1${getCookieToken() || AppState?.getAccessToken}`);
  }, [props.data]);

  const extraElement = useMemo(() => {
    return (
      <Fragment>
        {renderAudioArea()}
        {renderAttachmentsArea()}
      </Fragment>
    );
  }, [props.data, props.dataAttachment]);

  if (!htmlString) {
    return (showExtraElement && <div>{extraElement}</div>) || null;
  }

  const handleRichTextClick = (e) => {
    if (udmTenantId) {
      return;
    }
    const { target } = e;
    // 设置at的知识标签点击事件，增加逻辑:不是本租户的知识无法点击，上下游场景
    if (target.className === 'yqmention' && target.getAttribute('yqmention-type') === 'knowledge' && target.getAttribute('tenantid') === tenantId) {
      const knowledgeId = target.getAttribute('data-yqmention');
      const url = `${window.location.origin}/#/itsm/portal/knowledge?tenantId=${tenantId}&menu=knowledge&knowledgeId=${knowledgeId}&sourceModule=TICKETS&sourceFunction=QUOTING_KNOWLEDGE&sourceId=${ticketId}`;
      window.open(url);
    }
  };

  return (
    <ExternalComponent
      system={{
        scope: 'itsm',
        module: 'YqImgView',
      }}
      fallback={<span />} /* 添加 fallback，防止页面出现全白 */
    >
      <span
        className={`yq-components-wysiwyg-preview ${className || ''}`}
        onClick={handleRichTextClick}
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{
          __html: `${sanitizeHtml(htmlString, { ...sanitizeOptions }) || ''}`,
        }}
        style={{ display: 'inline-block' }}
      />
      {showExtraElement && extraElement}
    </ExternalComponent>
  );
});

export default inject('AppState')(observer((props) => (
  <WysiwygPreview {...props} />
)));

/* externalize: WysiwygPreview */
