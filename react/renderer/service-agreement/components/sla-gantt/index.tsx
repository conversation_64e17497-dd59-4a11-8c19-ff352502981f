/*
 * @Author: xia<PERSON><PERSON> <<EMAIL>>
 * @Date: 2022-10-19 14:59:30
 * @Description: sla的图形展示
 */
import React, { useEffect, useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import dayjs from 'dayjs';
import { Tooltip } from 'choerodon-ui/pro';
import classnames from 'classnames';
import minMax from 'dayjs/plugin/minMax';
import { getHourList, getAllData } from './utils';
import style from './index.module.less';

dayjs.extend(minMax);

export default observer((props) => {
  const {
    goalRecord,
    intl,
    renderMinutesToDays = () => {},
  } = props;
  const [breakDownDateRange, setBreakDownDateRange] = useState([]);
  const [tooltipTransformX, setTooltipTransformX] = useState(0);
  const [timeRangeList, setTimeRangeList] = useState(getHourList(0, 24));
  const [timeAccuracy, setTimeAccuracy] = useState(60);
  // const hourTimeList = getHourList(0, 24);
  const GANTT_ITEM_WIDTH = 96; // 一个小时格子的宽度
  const GANTT_ITEM_HEIGHT = 182;
  const ONE_MINTE_WIDTH = useMemo(() => {
    return GANTT_ITEM_WIDTH / timeAccuracy;
  }, [timeAccuracy]); // 一分钟的宽度

  useEffect(() => {
    if (goalRecord) {
      const startList = goalRecord.getCascadeRecords('goalItems').map(i => i.get('startTime')) || [];
      const stopList = goalRecord.getCascadeRecords('goalItems').map(i => {
        if (i.get('stopTime')) return i.get('stopTime');
        return dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
      }) || [];
      const mimData = calculateMinData(startList);
      const maxData = calculateMaxData(stopList);
      const dataRange = getAllData(mimData, maxData);
      const timeDifference = Math.abs(dayjs(mimData).diff(dayjs(maxData), 'minute'));
      // 如果时间维度只有一天的时候，精度可以缩小一些
      if (dataRange.length < 2) {
        let areaMin = 20;
        if (timeDifference <= 10) {
          areaMin = 1;
        } else if (timeDifference <= 20) {
          areaMin = 2;
        } else if (timeDifference <= 60) {
          areaMin = 5;
        } else if (timeDifference <= 120) {
          areaMin = 10;
        }
        setTimeAccuracy(areaMin);
        const list = getHourList(0, 24, areaMin);
        setTimeRangeList(list);
      }
      // @ts-ignore
      const startRange = dayjs(dataRange[0]);
      const currentTime = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
      goalRecord.getCascadeRecords('goalItems').forEach((j) => {
        const startTime = j.get('startTime');
        const stopTime = j.get('stopTime');
        const breachTime = j.get('breachTime');
        // 判断是否超时
        const isTimeOut = dayjs(stopTime || currentTime).isAfter(dayjs(breachTime));
        const startX = Math.abs(dayjs(startRange).diff(dayjs(startTime), 'minute'));
        // 起点坐标
        j.setState('startX', startX * ONE_MINTE_WIDTH);
        autoGanttMainScroll(startX * ONE_MINTE_WIDTH);
        // 条条的总长度
        const totleWidth = Math.abs(dayjs(startTime).diff(dayjs(stopTime || currentTime), 'minute')) * ONE_MINTE_WIDTH;
        // 期限条条的总长度
        const branchWidth = Math.abs(dayjs(startTime).diff(dayjs(breachTime), 'minute')) * ONE_MINTE_WIDTH;
        if (!isTimeOut /* 如果未超时, 开始时间到期限时间的75%是正常绿的，剩下的25%是橙色的 */) {
          if (totleWidth <= branchWidth * 0.75) {
            j.setState('normalWidth', totleWidth);
          } else {
            j.setState('normalWidth', branchWidth * 0.75);
            j.setState('warningWidth', totleWidth - branchWidth * 0.75);
          }
        } else {
          // 如果超时, 期限时间的75%是正常绿的，剩下的25%是橙色的, 超时的都是红色的
          j.setState('normalWidth', branchWidth * 0.75);
          j.setState('warningWidth', branchWidth * 0.25);
          j.setState('overtimeWidth', totleWidth - branchWidth);
        }
      });
      setBreakDownDateRange(dataRange);
    }
  }, [goalRecord && goalRecord.getCascadeRecords('goalItems').length, timeAccuracy, timeRangeList.length]);

  useEffect(() => {
    const ganttDom = document.getElementById('sla-gantt');
    if (ganttDom) {
      ganttDom.addEventListener('scroll', scrollListener, false);
      return () => {
        try {
          ganttDom.removeEventListener('scroll', scrollListener, false);
        } catch { /**/ }
      };
    }
  }, []);

  //
  function autoGanttMainScroll(distance) {
    const ganttDom = document.getElementById('sla-gantt');
    if (ganttDom) {
      ganttDom.scrollLeft = distance - 96;
    }
  }

  const scrollListener = async (event) => {
    if (event) {
      // 事件
      const left = event.target.scrollLeft || 0;
      setTooltipTransformX(left);
    }
  };

  // 计算最大日期
  function calculateMaxData(dataList = []) {
    if (dataList.length === 1) return dataList[0];
    const compareList = dataList.map((i) => dayjs(i));
    return dayjs.max(compareList).format('YYYY-MM-DD');
  }

  // 计算最小日期
  function calculateMinData(dataList = []) {
    if (dataList.length === 1) return dataList[0];
    const compareList = dataList.map((i) => dayjs(i));
    return dayjs.min(compareList).format('YYYY-MM-DD');
  }

  // 渲染提示
  const renderLineDetailInfo = (data) => {
    return (
      <div className={style['line-detail-conent']}>
        <div className={style['line-detail-item']}>
          {intl.formatMessage({ id: 'zknow.common.model.startTime' })}{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.colon' })}{data.get('startTime') || '-'}
        </div>
        <div className={style['line-detail-item']}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breachTime.one' })}{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.colon' })}{data.get('stopTime') || '-'}
        </div>
        <div className={style['line-detail-item']}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.overtime' })}{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.colon' })}{data.get('breachTime') || '-'}
        </div>
        <div className={style['line-detail-item']}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breachedFlag' })}{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.colon' })}{(data.get('breachedFlag') ? intl.formatMessage({ id: 'zknow.common.status.yes' }) : intl.formatMessage({ id: 'zknow.common.status.no' })) || '-'}
        </div>
        <div className={`${style['line-detail-item']} ${style['line-detail-item-last']}`}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.phase.duration' })}{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.colon' })}
          {renderMinutesToDays(parseInt(data.get('businessElapsedDuration'), 10)) || '-'}</div>
      </div>
    );
  };

  function renderOneDay(day, isLast) {
    return (
      <div className={classnames({
        [style['gantt-time-axis-major']]: true,
        [style['gantt-time-axis-major-last']]: isLast,
      })}
      >
        <Tooltip title={day}>
          <div className={style['major-top']}>
            {day}
          </div>
        </Tooltip>
        <div className={style['major-bottom']} style={{ height: GANTT_ITEM_HEIGHT }}>
          {timeRangeList.map((i, index) => {
            return (
              <div
                className={classnames({
                  [style['major-bottom-item']]: true,
                  [style['major-bottom-item-last']]: index === timeRangeList.length - 1,
                })}
                style={{
                  width: GANTT_ITEM_WIDTH,
                }}
              >
                <div className={style.hour}>{i}</div>
                <div className={style.content} />
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  function renderLineBox() {
    const goalItems = goalRecord.getCascadeRecords('goalItems') || [];
    if (goalItems.length === 0) return null;
    if (goalItems.length < 2) {
      return renderLine({
        style: { top: '100px' },
        data: goalItems[0],
      });
    } else {
      return (
        <React.Fragment>
          {renderLine({
            style: { top: '70px' },
            data: goalItems[0],
          })}
          {renderLine({
            style: { top: '135px' },
            data: goalItems[1],
          })}
        </React.Fragment>
      );
    }
  }

  function getTranslateX(startX) {
    if (tooltipTransformX < startX) {
      return 0;
    }
    return tooltipTransformX - startX;
  }

  // 渲染响应
  function renderLine({
    style: _style,
    data,
  }) {
    const type = data.get('target');
    // @ts-ignore
    const target = data.getField('target')
    && (data.getField('target').getText() || data.getField('target').getLookupData().meaning);
    return (
      <Tooltip
        popupClassName={style['sla-gantt-line-tooltip']}
        title={renderLineDetailInfo(data)}
        popupStyle={{
          transform: `translateX(${getTranslateX(data.getState('startX') || 0)}px)`,
        }}
      >
        <div
          className={style['gantt-line']}
          style={{ ..._style, left: data.getState('startX') }}
        >
          <div style={{ width: '200px' }}>
            <div className={classnames({
              [style['gantt-line-type']]: true,
              [style[`gantt-line-type-${type}`]]: true,
            })}
            >
              {target}
            </div>
          </div>
          <div className={style['gantt-line-content']}>
            <div className={style['gantt-line-content-normal']} style={{ width: data.getState('normalWidth') || '0px' }} />
            <div className={style['gantt-line-content-warning']} style={{ width: data.getState('warningWidth') || '0px' }} />
            <div className={style['gantt-line-content-overtime']} style={{ width: data.getState('overtimeWidth') || '0px' }} />
          </div>
        </div>
      </Tooltip>
    );
  }

  const renderMain = () => {
    return (
      <div className={style['sla-gantt']} id="sla-gantt">
        {renderLineBox()}
        {breakDownDateRange.map((i, index) => renderOneDay(i, index === breakDownDateRange.length - 1))}
      </div>
    );
  };

  return renderMain();
});
