/*
 * @Author: x<PERSON><PERSON><PERSON> <<EMAIL>>
 * @Date: 2022-10-19 16:25:55
 * @Description: 
 */
// @ts-nocheck
import dayjs from 'dayjs';

const hourList = [
  '0:00',
  '1:00',
  '2:00',
  '3:00',
  '4:00',
  '5:00',
  '6:00',
  '7:00',

  '8:00',
  '9:00',
  '10:00',
  '11:00',
  '12:00',
  '13:00',
  '14:00',
  '15:00',

  '16:00',
  '17:00',
  '18:00',
  '19:00',
  '20:00',
  '21:00',
  '22:00',
  '23:00',
];

/**
 * @param {*} startTime 开始的点
 * @param {*} count 数量
 * @areaMin {*} areaMin 为区域分钟数, 能被60整除的数。
 * @description: 
 * @return {*} 小时的list
 */
function getHourList(startTime = 9, count = 10, areaMin = 60) {
  const list = [];
  let _count = count;
  count > 24 && (_count = 24);
  for (let i = 0; i < _count; i++) {
    const time = parseInt(startTime, 10) + i;
    if (time < 24) {
      list.push(`${time}: 00`);
      pushMinuteList(list, areaMin, time);
    } else {
      list.push(`${time - 24}: 00`);
      pushMinuteList(list, areaMin, time);
    }
  }
  return list;
}

function pushMinuteList(list, areaMin, time) {
  let startMin = '00';
  while ((parseInt(startMin, 10) + areaMin) < 60) {
    const minute = (parseInt(startMin, 10) + areaMin) < 10 ? `0${parseInt(startMin, 10) + areaMin}` : (parseInt(startMin, 10) + areaMin);
    startMin = parseInt(startMin, 10) + areaMin;
    list.push(`${time}: ${minute}`);
  }
}

/**
   * @param {*} startTime // 开始时间
   * @param {*} endTime // 结束时间
   * @description: 获取两个日期之间的天数间隔
   * @return {*}
   */  
function getAllData(startTime, endTime) {
  if (!startTime && !endTime) return [];
  // 初始化日期列表，数组
  const allDate = new Array([]);
  let i = 0;
  // 开始日期小于等于结束日期,并循环
  while (startTime <= endTime) {
    const beginTime = dayjs(startTime).startOf('day').format('YYYY-MM-DD');
    // @ts-ignore
    allDate[i] = beginTime;
    // 获取开始日期时间戳
    const startTimeTs = new Date(beginTime).getTime();
    // 增加一天时间戳后的日期
    const nextDate = startTimeTs + 24 * 60 * 60 * 1000;
    // 拼接年月日，这里的月份会返回（0-11），所以要+1
    const nextDatesY = `${new Date(nextDate).getFullYear() }-`;
    const nextDatesM = new Date(nextDate).getMonth() + 1 < 10
      ? `0${ new Date(nextDate).getMonth() + 1 }-`
      : `${new Date(nextDate).getMonth() + 1 }-`;
    const nextDatesD = new Date(nextDate).getDate() < 10
      ? `0${ new Date(nextDate).getDate()}`
      : new Date(nextDate).getDate();
    startTime = nextDatesY + nextDatesM + nextDatesD;
    // 增加数组key
    i += 1;
  }
  return allDate;
}

export {
  hourList,
  getHourList,
  getAllData,
};
