/*
 * @Author: xiaoreya
 * @Date: 2022年10月25日
 * @Description: sla break down
 */
import React from 'react';
import { Icon, Empty, YqAvatar } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import style from './index.module.less';

const SLABreakdown = observer((props: any) => {
  const {
    record,
    intl,
    renderMinutesToDays,
  } = props;

  // 渲染提示内容
  function renderMain() {
    if (!record) return null;
    const assignTimeDisplay = record.get('assignTimeDisplay') || [];
    const titleCLassName = classnames({
      [style['tooltip-content-cell']]: true,
      [style['tooltip-content-cell-title']]: true,
    });
    const valueCLassName = classnames({
      [style['tooltip-content-cell']]: true,
      [style['tooltip-content-cell-value']]: true,
      [style['tooltip-content-cell-name']]: true,
    });

    const timeCLassName = classnames({
      [style['tooltip-content-cell']]: true,
      [style['tooltip-content-cell-value']]: true,
      [style['tooltip-content-cell-time']]: true,
    });

    return (
      <div className={style['sla-card-tooltip-main']}>
        <div className={style['tooltip-top']}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breakdown.title' })}
        </div>
        <div className={style['tooltip-content']}>
          {
            assignTimeDisplay && assignTimeDisplay.length === 0
              ? (
                <Empty
                  style={{ padding: '8px 50px' }}
                  innerStyle={{ width: '80px', height: '80px' }}
                  type="empty"
                />
              )
              : assignTimeDisplay.map((i) => {
                return (
                  <div className={style['tooltip-content-row']}>
                    <div className={titleCLassName}>{i.assignmentGroupName || '-'}</div>
                    <div className={valueCLassName}>
                      <span className={style['tooltip-content-cell-flex']}>
                        {i.assigneePersonName && (
                          <>
                            <YqAvatar src={i.imageUrl} size={22} style={{ marginRight: '4px' }}>
                              {i.assigneePersonName}
                            </YqAvatar>
                          </>
                        )}
                        {i.assigneePersonName || '-'}
                      </span>
                    </div>
                    <div className={timeCLassName}>
                      <span className={style['tooltip-content-cell-flex']}>
                        {
                          parseInt(i.totalTime || 0, 10) !== 0 && (
                            <>
                              <Icon
                                type="time"
                                size={16}
                                style={{
                                  marginRight: '4px',
                                }}
                              />
                              {renderMinutesToDays(parseInt(i.totalTime || 0, 10)) || '-'}
                            </>
                          )
                        }
                      </span>
                    </div>
                  </div>
                );
              })
          }
        </div>
      </div>
    );
  }

  return renderMain();
});

export default SLABreakdown;
