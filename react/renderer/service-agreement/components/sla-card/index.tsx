/* eslint-disable jsx-a11y/alt-text */
/*
 * @Author: xiaoreya
 * @Date: 2022年10月25日 15:02:46
 * @Description: 每一项服务目标
 */
import React, { Fragment } from 'react';
import { Icon } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import { Modal, Tooltip, Row, Col } from 'choerodon-ui/pro';
import DetailView from './DetailView';
import SLABreakdown from '../break-down';
// @ts-ignore
import BAEACHED_FILL from '../../images/breached-fill.gif';
// @ts-ignore
import IN_PROCESS_FILL from '../../images/inprocess-fill.gif';
import style from './index.module.less';

const modalKey = Modal.key();

const SLACard = observer((props: any) => {
  const {
    intl,
    prefixCls,
    renderMinutesToDays,
    breakDownDataSet,
    tenantId,
    record,
    currentShowType,
    slaPerformanceEvaluation,
  } = props;

  async function handleOpenDetail() {
    if (!record) return null;
    Modal.open({
      key: modalKey,
      title: record.get('goalName'),
      className: style[`${prefixCls}-modal`],
      okFirst: true,
      style: { width: '1200px' },
      children: (
        <DetailView
          breakDownDataSet={breakDownDataSet}
          intl={intl}
          prefixCls={prefixCls}
          tenantId={tenantId}
          renderMinutesToDays={renderMinutesToDays}
          goalRecord={record}
          slaPerformanceEvaluation={slaPerformanceEvaluation}
        />
      ),
      destroyOnClose: true,
      footer: (okBtn, cancelBtn) => cancelBtn,
      afterClose: () => {
        breakDownDataSet.queryDataSet && breakDownDataSet.queryDataSet.reset();
        breakDownDataSet.reset();
      },
    });
  }

  const renderDuration = (goalItem) => {
    const durationJSON = goalItem.get('duration');
    const res = durationJSON && JSON.parse(durationJSON);
    let value = '-';
    if (!res) return value;
    if (res.days && res.days !== 0) {
      value = `${res.days}${intl.formatMessage({ id: 'zknow.common.model.day' })}${res.hours || 0}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res.minutes || 0}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else if (res.hours && res.hours !== 0) {
      value = `${res.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res.minutes || 0}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else if (res.minutes && res.minutes !== 0) {
      value = `${res.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else {
      value = '-';
    }
    return value;
  };

  const renderTimeout = (goalItem) => {
    if (/* 进行中 */ goalItem.get('state') === 'IN_PROGRESS') {
      if (goalItem.get('breachedFlag')) {
        const time = renderMinutesToDays(parseInt(goalItem.get('businessElapsedDuration') || 0, 10) - parseInt(goalItem.get('durationCount') || 0, 10));
        return (
          <>
            <span className={style['card-bottom-right-timeout']}>
              {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution.timeout' }, { time })}
            </span>
          </>
        );
      }
      return (
        <>
          <span className={style['card-bottom-right-rightOff']}>
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution.surplus' }, { time: renderMinutesToDays(parseInt(goalItem.get('businessLeftDuration') || 0, 10)) })}
          </span>
        </>
      );
    }
  };

  const renderBreachedFlag = (goalItem) => {
    const iconStyle = {
      style: { marginRight: '4px' },
      theme: 'multi-color',
      size: '14px',
      strokeLinecap: 'square',
    };
    if (/* 完成 */ goalItem.get('state') === 'COMPLETED') {
      // 超时
      if (goalItem.get('breachedFlag')) {
        return (
          <span className={style['card-top-status-breached']}>
            <Icon {...iconStyle} type="attention" fill={['#FFFFFF', '#FFFFFF', '#F8353F', '#FFFFFF']} />
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' })}
            {renderMinutesToDays(parseInt(goalItem.get('businessElapsedDuration') || 0, 10) - parseInt(goalItem.get('durationCount') || 0, 10))}
          </span>
        );
      }
      // 达标
      return (
        <span className={style['card-top-status-done']}>
          <Icon {...iconStyle} type="check-one" fill={['#FFFFFF', '#FFFFFF', '#7BC95A', '#FFFFFF']} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.done' })}
        </span>
      );
    }
    if (/* 进行中 */ goalItem.get('state') === 'IN_PROGRESS') {
      // 进行中超时
      if (goalItem.get('breachedFlag')) {
        return (
          <span className={style['card-top-status-breached']}>
            <img className={style['status-image']} src={BAEACHED_FILL} />
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.inProgress' })}
          </span>
        );
      }
      return (
        <span className={classnames({
          [style['card-top-status-breached']]: true,
          [style['card-top-status-breached-inProgress']]: true,
        })}
        >
          <img className={style['status-image']} src={IN_PROCESS_FILL} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.inProgress' })}
        </span>
      );
    }
    if (/* 暂挂 */ goalItem.get('state') === 'PAUSED') {
      return (
        <span className={classnames({
          [style['card-top-status-breached']]: true,
          [style['card-top-status-breached-paused']]: true,
        })}
        >
          <Icon {...iconStyle} type="ReduceOne" fill={['#FFFFFF', '#FFFFFF', '#6454F4', '#FFFFFF']} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.paused' })}
        </span>
      );
    }
    if (/* 取消 */ goalItem.get('state') === 'CANCEL') {
      return (
        <span className={classnames({
          [style['card-top-status-breached']]: true,
          [style['card-top-status-breached-cancel']]: true,
        })}
        >
          <Icon {...iconStyle} type="CloseOne" fill={['#FFFFFF', '#FFFFFF', '#8C8C8C', '#FFFFFF']} />
          {intl.formatMessage({ id: 'zknow.common.button.cancel' })}
        </span>
      );
    }
    return '-';
  };

  // 渲染提示内容
  function renderCardTooltip(goalItem) {
    if (!goalItem) return null;
    return <SLABreakdown {...props} record={goalItem} />;
  }

  const renderCardHeader = () => {
    return (
      <div className={style['card-header']}>
        <div className={style['card-header-title']}>{record.get('goalName')}</div>
        <div className={style['card-header-action']} onClick={() => handleOpenDetail()}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.see.detail' })}
          <Icon type="double-right" size="14" style={{ marginLeft: '2px' }} />
        </div>
      </div>
    );
  };

  function renderGoalItem(goalItem, isFirst) {
    // @ts-ignore
    const target = goalItem.getField('target')
    && (goalItem.getField('target').getText() || goalItem.getField('target').getLookupData().meaning);
    return (
      <Fragment>
        {!isFirst && <div className={style['card-split-line']}><div className={style['card-split-line-inner']} /></div>}
        <Tooltip placement="bottomLeft" title={renderCardTooltip(goalItem)} popupClassName={style['sla-card-tooltip']}>
          <div className={style['card-content']}>
            <div className={style['card-top']}>
              <span className={style['card-top-duration']}>
                <div className={`${style['target-status']} ${style[`${goalItem.get('target')}`]}`}>{target}</div>
                {intl.formatMessage(
                  { id: 'lcr.renderer.serviceAgreement.businessElapsedDuration' },
                  { businessElapsedDuration: renderMinutesToDays(parseInt(goalItem.get('businessElapsedDuration') || 0, 10)) }
                )}
                /
                {intl.formatMessage(
                  { id: 'lcr.renderer.serviceAgreement.duration' },
                  { duration: renderDuration(goalItem) }
                )}
              </span>
              <div className={style['card-top-status']}>{renderBreachedFlag(goalItem)}</div>
            </div>
            <div className={style['card-bottom']}>
              <div className={style['card-bottom-left']}>
                <span className={style['breach-time']}>
                  {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breachTime' }, { breachTime: goalItem.get('breachTime') })}
                </span>
              </div>
              <div className={style['card-bottom-right']}>
                <span className={style['card-bottom-right-content']}>
                  {renderTimeout(goalItem)}
                </span>
              </div>
            </div>
          </div>
        </Tooltip>
      </Fragment>
    );
  }

  function renderPersonCardHeader() {
    const title = record?.get('goalName');
    return <div className={style['card-header']}><span className={style['card-header-line']} /><span className={style['card-header-text']}>{title}</span></div>;
  }

  function renderPersonCardItem(item) {
    const breakDownList = item.get('breakDownList');
    const targetCode = item.get('target');

    const textMapping = {
      RESOLUTION: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.RESOLUTION' }),
      RESPONSE: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.RESPONSE' }),
    };

    return (
      <div className={style['card-item']}>
        <div className={style['card-item-header']}>
          <div className={style['card-top']}>
            <div className={style['card-top-status']}>{renderBreachedFlag(item)}</div>
            <div className={style['card-top-title']}>{textMapping[targetCode]}</div>
          </div>
          <div className={style['card-item-action']} onClick={() => handleOpenDetail()}>
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.see.detail' })}
            <Icon type="double-right" size="14" style={{ marginLeft: '2px' }} />
          </div>
        </div>
        <div className={style['card-item-content']}>
          <Row className={style['row-header']} align="middle" justify="center">
            <Col span={10} className={style['row-cell']}><span>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assignmentGroupName' })}</span></Col>
            <Col span={4} className={style['row-cell']}><span>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assigneePersonName' })}</span></Col>
            <Col span={6} className={style['row-cell']}><span>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.delayDuty' })}</span></Col>
            <Col span={4} className={style['row-cell']}><span>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.consumingTime' })}</span></Col>
          </Row>
          {breakDownList?.map(i => renderassignTimeRow(i))}
        </div>
      </div>
    );
  }

  function renderassignTimeRow(i) {
    let delayDuty = '';
    const activeFlag = i.maxActiveElapsedFlag && (i.assignmentGroupName || i.assigneePersonName);
    if (!i.assignmentGroupName && !i.assigneePersonName) {
      delayDuty = '-';
    } else {
      delayDuty = i.maxActiveElapsedFlag ? intl.formatMessage({ id: 'zknow.common.status.yes' }) : intl.formatMessage({ id: 'zknow.common.status.no' });
    }
    return (
      <Row className={style['row-item']} align="middle" justify="center">
        <Col span={10} className={style['row-cell']}><span>{i.assignmentGroupName || '-'}</span></Col>
        <Col span={4} className={style['row-cell']}><span>{i.assigneePersonName || '-'}</span></Col>
        <Col span={6} className={classnames(style['row-cell'], { [style['row-active']]: activeFlag })}><span>{delayDuty}</span></Col>
        <Col span={4} className={classnames(style['row-cell'], { [style['row-active']]: activeFlag })}>{renderMinutesToDays(parseInt(i.activeElapsedDuration || 0, 10)) || '-'}</Col>
      </Row>
    );
  }

  const renderCardItem = () => {
    const goalItems = record.getCascadeRecords('goalItems') || [];
    if (currentShowType === 'person') {
      return (
        <div className={style[`${prefixCls}-area-personCard`]}>
          {renderPersonCardHeader()}
          {goalItems.map((item) => renderPersonCardItem(item))}
        </div>
      );
    } else {
      return (
        <div className={style[`${prefixCls}-area-card`]}>
          {renderCardHeader()}
          {goalItems.map((item, index) => renderGoalItem(item, index === 0))}
        </div>
      );
    }
  };

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}-area`]}>
        {renderCardItem()}
      </div>
    );
  };
  return renderMain();
});

export default SLACard;
