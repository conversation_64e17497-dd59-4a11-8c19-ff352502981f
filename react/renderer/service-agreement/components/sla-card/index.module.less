@import '~choerodon-ui/lib/style/themes/default';

.sla-renderer {
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-area {
    margin-bottom: 24px;
    &-title {
      font-size: 14px;
      font-weight: 500;
      color: #12274d;
      line-height: 22px;
      padding-left: 6px;
      border-left: solid 4px @primary-color;
      margin-bottom: 12px;
    }

    &-card {
      background: #FFFFFF;
      border-radius: 6px;
      border: 1px solid #E5E6EB;
      margin-bottom: 12px;
      .card-top {
        .flex-center;
        justify-content: space-between;
        margin-bottom: 8px;
        &-duration {
          font-size: 14px;
          font-weight: 400;
          color: #12274D;
          line-height: 22px;
        }
        &-title {
          font-size: 14px;
          font-weight: 500;
          color: #12274d;
          line-height: 22px;
          white-space: nowrap;
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-right: 6px;
        }
        &-status {
          font-size: 12px;
          font-weight: 500;
          color: #FFFFFF;
          line-height: 22px;
          white-space: nowrap;
          &-done {
            padding: 0px 8px 0px 4px;
            border-radius: 11px;
            background-color: #7BC95A;
            .flex-center;
          }
          &-breached {
            padding: 0px 8px 0px 4px;
            border-radius: 11px;
            background-color: #F8353F;
            .flex-center;
            &-inProgress {
              background-color: #FF9500;
            }
            &-paused {
              background-color: #6454F4;
            }
            &-cancel {
              background-color: #8C8C8C;
            }
          }
          .status-image {
            width: 14px;
            height: 14px;
            margin-right: 4px;
          }
        }
      }
      .card-bottom {
        .flex-center;
        justify-content: space-between;
        &-left {
          .flex-center;
          &-target {
            background: @minor-color;
            border-radius: 2px;
            padding: 1px 8px;
            font-weight: 500;
            color: @primary-color;
            line-height: 20px;
            margin-right: 16px;
            white-space: nowrap;
          }
          &-time {
            font-size: 12px;
            font-weight: 400;
            color: #595959;
            line-height: 22px;
            margin-right: 4px;
          }
        }

        &-right {
          font-size: 12px;
          font-weight: 400;
          color: #484D5E;
          line-height: 22px;
          &-timeout {
            color: #F8353F;
          }
          &-rightOff {
            color: #12274d;
          }
          &-extra {
            font-size: 14px;
            font-weight: 400;
            color: @primary-color;
            line-height: 22px;
            display: none;
          }
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        background: #EDF2FF;
        border-radius: 6px 6px 0px 0px;
        padding: 6px 12px;

        font-size: 14px;
        font-weight: 500;
        color: rgba(18, 39, 77, 0.85);
        line-height: 22px;
        &-action {
          font-size: 13px;
          font-weight: 400;
          color: rgba(18, 39, 77, 0.65);
          line-height: 17px;
          display: flex;
          align-items: center;
          cursor: pointer;
          .yqcloud-icon-park-wrapper {
            position: relative;
            top: 1px;
          }
        }
      }

      .card-content {
        padding: 12px;
        .target-status {
          // background: @minor-color;
          // color: @primary-color;
          color: #2979FF ;
          background: #F2F7FF;
          border-radius: 2px;
          padding: 1px 8px;
          font-weight: 500;
          line-height: 20px;
          margin-right: 10px;
          white-space: nowrap;
          font-size: 12px;
          display: inline-block;
        }
        .RESPONSE {
          color: #FF9100 !important;
          background: #FFF1DF !important;
        }
        .RESOLUTION {
          color: #2979FF !important;
          background: #F2F7FF !important;
        }
        .breach-time {
          font-size: 12px;
          font-weight: 400;
          color: rgba(18, 39, 77, 0.65);
          line-height: 22px;
        }
      }

      .card-split-line {
        padding: 0px 12px;
        &-inner {
          width: 100%;
          border-top: 1px solid rgba(203,210,220,0.5); 
        }
      }
    }

    &-personCard {
      font-family: PingFangSC-Medium, PingFang SC;
      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        &-line {
          width: 4px;
          height: 16px;
          background: #2979ff;
          border-radius: 2px;
          display: inline-block;
          margin-right: 16px;
        }
        &-text {
          color: #12274d;
          font-style: 14px;
          font-weight: 500;
        }
      }
      .card-item {
        padding: 16px;
        background: #f7f9fc;
        border-radius: 8px;
        border: 1px solid #eef4fc;
        margin-bottom: 12px;
        .card-top {
          display: flex;
          &-status {
            font-size: 12px;
            font-weight: 500;
            color: #FFFFFF;
            line-height: 22px;
            white-space: nowrap;
            margin-right: 12px;
            &-done {
              padding: 0px 8px 0px 4px;
              border-radius: 11px;
              background-color: #7BC95A;
              .flex-center;
            }
            &-breached {
              padding: 0px 8px 0px 4px;
              border-radius: 11px;
              background-color: #F8353F;
              .flex-center;
              &-inProgress {
                background-color: #FF9500;
              }
              &-paused {
                background-color: #6454F4;
              }
              &-cancel {
                background-color: #8C8C8C;
              }
            }
            .status-image {
              width: 14px;
              height: 14px;
              margin-right: 4px;
            }
          }
          &-title {
            font-weight: 500;
            font-size: 14px;
          }
        }
        &-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }
        &-action {
          font-size: 13px;
          font-weight: 400;
          line-height: 17px;
          display: flex;
          align-items: center;
          cursor: pointer;
          color: @primary-color;
          .yqcloud-icon-park-wrapper {
            position: relative;
            top: 1px;
          }
        }
        &-content {
          padding: 16px;
          background: #fff;
          .row-header {
            width: 100%;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(203, 210, 220, 0.65);
            font-weight: 500;
            font-size: 14px;
          }
          .row-item {
            width: 100%;
            margin-top: 16px;
          }
          .row-cell {
            text-align: center;
          }
          .row-active {
            color: #f83552;
          }
        }
      }
    }
  }
}

.lc-ticket-area-title {
  margin-left: 0px !important;
}

.sla-renderer-modal {
  .@{c7n-pro-prefix}-modal-body {
    padding: 0px;
    // padding-top: 8px;
  }
  .@{c7n-pro-prefix}-modal-footer {
    border-top: unset;
  }
}

.sla-card-tooltip {
  background-color: #ffffff;
  min-width: 200px;
  :global {
    .@{c7n-pro-prefix}-tooltip-popup-inner {
      background: #FFFFFF !important;
      box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.12);
      border-radius: 4px;
      padding: 0px;
    }
    .@{c7n-pro-prefix}-tooltip-popup-arrow {
      display: none;
      &-dark {
        display: none;
      }
    }
    .@{c7n-prefix}-avatar-string {
      transform: scale(0.875) translateX(-50%) !important;
      font-size: 14px !important;
    }
  }
}

.sla-card-detail {
  &-basic {
    margin-bottom: 20px;
    .basic-info {
      display: flex;
      align-items: center;
      margin-top: 12px;
      margin-bottom: 20px;
      &-item {
        font-size: 14px;
        font-weight: 400;
        color: rgba(18,39,77,0.65);
        line-height: 22px;
        display: flex;
        &-serviceWindow {
          margin-left: 40px;
        }
      }
    }
  }

  &-table {
    .detail-records-header {
      margin-bottom: 12px;
    }
    .active-text {
      color: #f8353f;
      display: inline-flex;
      align-items: center;
    }
    .primary-text {
      color: @primary-color;
      display: inline-flex;
      align-items: center;
      > span {
        margin-right: 2px;
      }
    }
  }
}