/*
 * @Author: xia<PERSON>ya
 * @Date: 2022年10月25日
 * @Description: SLA详情。
 */
import { Empty, Icon } from '@zknow/components';
import {
  Table,
  Tabs,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import React, { Fragment, useState, useEffect } from 'react';
import classnames from 'classnames';
import SectionHeader from '../section-header';
import SlaGantt from '../sla-gantt';
import style from './index.module.less';

const { Column } = Table;
const TabPane = Tabs.TabPane;

function DetailView(props) {
  const {
    intl,
    breakDownDataSet,
    renderMinutesToDays,
    goalRecord,
    slaPerformanceEvaluation,
  } = props;

  const [tabKey, setTabKey] = useState('response');

  useEffect(() => {
    // 响应的sla
    const goalItems = goalRecord.get('goalItems');
    const data = goalItems.find(j => j.target === 'RESPONSE');
    const breakDownList = (data && data.breakDownList) || [];
    breakDownDataSet.loadData(breakDownList);
  }, [goalRecord]);

  const renderNullValue = ({ text }) => {
    if (text) {
      return text;
    }
    return '-';
  };

  const renderBreachedFlag = ({ record }) => {
    if (record.get('breachedFlag')) {
      return (
        <span style={{ color: '#F8353F' }}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' })}
        </span>
      );
    }
    if (record.get('state') === 'COMPLETED' && !record.get('breachedFlag')) {
      return (
        <span style={{ color: '#75C940' }}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.done' })}
        </span>
      );
    }
    return '-';
  };

  function handleTabsChange(key) {
    setTabKey(key);
    const goalItems = goalRecord.get('goalItems');
    if (key === 'response') {
      // 响应的sla
      const data = goalItems.find(j => j.target === 'RESPONSE');
      const breakDownList = (data && data.breakDownList) || [];
      breakDownDataSet.loadData(breakDownList);
    } else if (key === 'resolve') {
      // 解决的sla
      const data = goalItems.find(j => j.target === 'RESOLUTION');
      const breakDownList = (data && data.breakDownList) || [];
      breakDownDataSet.loadData(breakDownList);
    }
  }

  // 基本信息
  function renderBasicInfo() {
    return (
      <div className={style['sla-card-detail-basic']}>
        <SectionHeader title={intl.formatMessage({ id: 'zknow.common.desc.basic.information' })} />
        <div className={style['basic-info']}>
          <div className={style['basic-info-item']}>
            <div className={style.label}>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.goal.name' })}</div>
            <div className={style.value}>{goalRecord.get('goalName')}</div>
          </div>
          <div className={`${style['basic-info-item']} ${style['basic-info-item-serviceWindow']}`}>
            <div className={style.label}>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.service.window' })}</div>
            <div className={style.value}>{goalRecord.getCascadeRecords('goalItems')[0] && goalRecord.getCascadeRecords('goalItems')[0].get('scheduleWindowName')}</div>
          </div>
        </div>
        <SlaGantt
          goalRecord={goalRecord}
          intl={intl}
          renderMinutesToDays={renderMinutesToDays}
        />
      </div>
    );
  }

  function renderDelayDutyFlag({ record }) {
    // sla耗时最长是逾期责任人
    const maxActiveElapsedFlag = record.get('maxActiveElapsedFlag');
    return <span className={classnames({ [style['active-text']]: maxActiveElapsedFlag })}>{maxActiveElapsedFlag ? intl.formatMessage({ id: 'zknow.common.status.yes' }) : intl.formatMessage({ id: 'zknow.common.status.no' })}</span>;
  }

  function activeDuration({ text, field, record, totalFlag }) {
    const flag = record.get(field);
    return <span className={classnames({ [style['active-text']]: flag, [style['primary-text']]: totalFlag && !flag })}>{totalFlag && <Icon type="StopwatchStart" />}{renderMinutesToDays(parseInt(text || 0, 10)) || '-'}</span>;
  }

  function renderTable() {
    return (
      <Table
        dataSet={breakDownDataSet}
        condition="param"
        empty={<Empty type="empty" />}
        queryBar="none"
      >
        <Column name="assignmentGroupName" renderer={({ text }) => (text || '-')} />
        <Column name="assigneePersonName" renderer={({ text }) => (text || '-')} />
        <Column name="startTime" width={180} renderer={({ record, text }) => renderNullValue({ record, text })} />
        <Column name="endTime" width={180} renderer={({ record, text }) => renderNullValue({ record, text })} />
        <Column name="breachedFlag" width={80} renderer={({ record }) => renderBreachedFlag({ record })} />
        <Column name="ticketStatusName" renderer={({ text }) => (text || '-')} />
        <Column name="slaStatus" />
        {slaPerformanceEvaluation && <Column name="delayDutyFlag" width={80} renderer={({ record }) => renderDelayDutyFlag({ record })} />}
        <Column name="activeElapsedDuration" renderer={({ record, text }) => activeDuration({ record, text, field: 'maxActiveElapsedFlag' })} />
        <Column name="businessElapsedDuration" renderer={({ record, text }) => activeDuration({ record, text, field: 'breachedFlag', totalFlag: true })} />
      </Table>
    );
  }

  // SLA明细记录
  function renderSlaTable() {
    return (
      <div className={style['sla-card-detail-table']}>
        <SectionHeader title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.detail.records' })} className={style['detail-records-header']} />
        <Tabs activeKey={tabKey} onChange={handleTabsChange}>
          <TabPane tab={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.response' })} key="response">
            {renderTable()}
          </TabPane>
          <TabPane tab={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolve' })} key="resolve">
            {renderTable()}
          </TabPane>
        </Tabs>
      </div>
    );
  }

  return (
    <Fragment>
      {renderBasicInfo()}
      {renderSlaTable()}
    </Fragment>
  );
}

export default observer(DetailView);
