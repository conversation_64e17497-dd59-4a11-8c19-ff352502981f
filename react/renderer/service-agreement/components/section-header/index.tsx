/*
 * @Author: xiaoreya <<EMAIL>>
 * @Date: 2022-10-12 13:50:39
 * @Description: section头部
 */

import React from 'react';
import { observer } from 'mobx-react-lite';
import { SectionHeaderLine } from '@zknow/components';
import style from './index.module.less';

type Props = {
  title?: string,
  className?: string,
};

export default observer((props: Props) => {
  const {
    title,
    className,
  } = props;

  return (
    <div className={`${style['section-header']} ${className}`}>
      <SectionHeaderLine />
      <div className={style['top-title']}>{title}</div>
    </div>
  );
});
