/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-10-19 11:02:29
 * @Description:
 */
import React, { createContext, useEffect, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
// @ts-ignore
import * as utils from '@/utils';
import { GoalsDataSet, BreakDownDataSet, GoalItemDataSet } from './DataSet';

type ServiceAgreementProps = {
  intl: any,
  history: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  path?: string,
  match?: any,
  formConfig?: any,
  children?: any,
  AppState: any,
  formDataSet: any,
  viewDataSet: any,
  goalsDataSet: any,
  ticketId: string,
  viewRecord: any,
  ticketType: string,
  slaPerformanceEvaluation: boolean,
  displayDimension: any,
};

const Store = createContext<ServiceAgreementProps>({
  intl: undefined,
  history: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  viewDataSet: '',
  goalsDataSet: '',
  ticketId: '',
  viewRecord: '',
  ticketType: '',
  slaPerformanceEvaluation: false,
  displayDimension: [],
});

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props: ServiceAgreementProps) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
      ticketId,
      ticketType,
      viewRecord,
    } = props;
    const prefixCls = 'sla-renderer';
    const businessObjectCode = ticketType;
    const defaultTicketSLADisplayMode = (viewRecord && viewRecord.get('widgetConfig.ticketSLADisplayMode')) || 'active';
    const slaPerformanceEvaluation = (viewRecord && viewRecord.get('widgetConfig.slaPerformanceEvaluation'));
    const displayDimension = (viewRecord && viewRecord.get('widgetConfig.displayDimension')) || [];

    const breakDownDataSet = useMemo(() => new DataSet(BreakDownDataSet({ intl })), []);

    const goalItemDataSet = useMemo(() => new DataSet(GoalItemDataSet({
      tenantId,
      intl,
      autoLocateFirst: false,
    })), [businessObjectCode, ticketId]);

    const goalsDataSet = useMemo(() => new DataSet(GoalsDataSet({
      tenantId,
      businessObjectCode,
      ticketId,
      autoLocateFirst: false,
      goalItemDataSet,
    })), [businessObjectCode, ticketId]);

    useEffect(() => {
      // 动作修改需要及时刷新 sla，将sla的查询dataset引用传给动作
      if (formDataSet) {
        formDataSet.setState('_slaDataSet', goalsDataSet);
      }
    }, [formDataSet, goalsDataSet]);

    const renderMinutesToDays = (duration) => {
      let value = '-';
      const res = utils.durationToDays(duration || 0);
      if (!res) return value;
      if (res.days !== 0) {
        value = `${res.days}${intl.formatMessage({ id: 'zknow.common.model.day' })}${res.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else if (res.hours !== 0) {
        value = `${res.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else if (res.minutes !== 0) {
        value = `${res.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else {
        value = `${res.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      }
      return value;
    };

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      goalsDataSet,
      ticketId,
      breakDownDataSet,
      renderMinutesToDays,
      businessObjectCode,
      defaultTicketSLADisplayMode,
      viewRecord,
      slaPerformanceEvaluation,
      displayDimension,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
