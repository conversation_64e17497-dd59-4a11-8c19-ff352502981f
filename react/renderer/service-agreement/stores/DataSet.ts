/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-10-19 11:02:29
 * @Description:
 */
// @ts-nocheck
import { DataSet } from 'choerodon-ui/pro';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/interface';

const GoalItemDataSet = ({
  intl,
  tenantId,
  autoLocateFirst,
}): DataSetProps => {
  const name = intl.formatMessage({ id: 'zknow.common.model.name' });
  const type = intl.formatMessage({ id: 'zknow.common.model.type' });
  const use = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.use' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status' });
  const duration = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.duration' });
  const remainingWorkingDate = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.remainingWorkingDate' });
  const consumedWorkingDate = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.consumedWorkingDate' });
  const startDate = intl.formatMessage({ id: 'zknow.common.model.startTime' });
  const stopDate = intl.formatMessage({ id: 'zknow.common.model.endTime' });
  const timeOut = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' });

  return {
    autoQuery: false,
    autoLocateFirst,
    paging: false,
    primaryKey: 'id',
    selection: false,
    fields: [
      // 名称
      { name: 'slaDefinitionName', label: name },
      // 类型
      { name: 'type', label: type, required: true, lookupCode: 'SLA_TYPE' },
      // 用途
      { name: 'target', label: use, required: true, lookupCode: 'SLA_TARGET' },
      // 状态
      { name: 'state', label: status, lookupUrl: `/hpfm/v1/${tenantId}/lookup/queryByCode?lookupTypeCode=TASK_SLA_STATE` },
      // 超时
      { name: 'breachedFlag', label: timeOut },
      // 期限
      { name: 'duration', label: duration },
      // 剩余工作时间
      { name: 'businessLeftDuration', label: remainingWorkingDate },
      // 已消耗工作时间
      { name: 'businessElapsedDuration', label: consumedWorkingDate },
      // 开始时间
      { name: 'startTime', label: startDate },
      // 停止时间
      { name: 'stopTime', label: stopDate },
      // 版本号
      { name: 'objectVersionNumber' },
    ],
    events: {

    },
    children: {
      goalItems: new DataSet(),
    },
  };
};

const GoalsDataSet = ({
  tenantId,
  autoLocateFirst,
  ticketId,
  businessObjectCode,
  goalItemDataSet,
}): DataSetProps => {
  return {
    autoQuery: false,
    autoLocateFirst,
    paging: false,
    primaryKey: 'id',
    selection: false,
    transport: {
      read: () => {
        if (!ticketId || !businessObjectCode) return null;
        return {
          url: `/itsm/v1/${tenantId}/tasks/${ticketId}/slaGoals?businessObjectCode=${businessObjectCode}`,
          method: 'GET',
        };
      },
    },
    children: {
      goalItems: goalItemDataSet,
    },
  };
};

const BreakDownDataSet = ({ intl }): DataSetProps => {
  // 处理组、处理人、单据状态、开始时间、结束时间、总时长、是否超时、sla记录状态
  const assignmentGroupName = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assignmentGroupName' });
  const assignmentPersonName = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assignmentPersonName' });
  const startTime = intl.formatMessage({ id: 'zknow.common.model.startTime' });
  const stopTime = intl.formatMessage({ id: 'zknow.common.model.endTime' });
  const businessElapsedDuration = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.totalTime' });
  const ticketStatus = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.ticketStatus' });
  const breachedFlag = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breachedFlag' });
  const slaStatus = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.slaStatus' });
  const delayDutyFlag = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.delayDutyFlag' });
  const activeElapsedDuration = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.activeElapsedDuration' });

  return {
    autoQuery: false,
    paging: false,
    selection: false,
    fields: [
      // 处理组
      { name: 'assignmentGroupName', label: assignmentGroupName },
      // 处理人
      { name: 'assigneePersonName', label: assignmentPersonName },
      // 开始时间
      { name: 'startTime', label: startTime },
      // 停止时间
      { name: 'endTime', label: stopTime },
      // 总时长
      { name: 'businessElapsedDuration', label: businessElapsedDuration },
      // 单据状态
      { name: 'ticketStatusName', label: ticketStatus },
      // 是否超时
      { name: 'breachedFlag', label: breachedFlag },
      // sla记录状态
      { name: 'slaStatus', label: slaStatus, lookupCode: 'TASK_SLA_STATE' },
      // 是否逾期责任人
      { name: 'delayDutyFlag', label: delayDutyFlag },
      // sla时效内时长
      { name: 'activeElapsedDuration', label: activeElapsedDuration },
    ],
  };
};

export {
  GoalsDataSet,
  BreakDownDataSet,
  GoalItemDataSet,
};
