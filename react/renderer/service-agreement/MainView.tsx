/* eslint-disable react/no-danger */
import React, {
  useState,
  useEffect,
  useContext,
  useRef,
} from 'react';
import { Icon, Empty, Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { TextField, Modal, Spin, Tooltip, Dropdown, Menu } from 'choerodon-ui/pro';
import Store from './stores';
import SLACard from './components/sla-card';
import Fixsla from './components/fix-sla';
import style from './index.module.less';

const modalKey = Modal.key();
const { Item: Mitem } = Menu;

const MainView = observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    goalsDataSet,
    ticketId,
    viewRecord,
    defaultTicketSLADisplayMode,
    slaPerformanceEvaluation,
    displayDimension,
  } = context;
  const [initCount, setInitCount] = useState(0);
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();
  const [currentType, setCurrentType] = useState(intl.formatMessage({ id: 'zknow.common.status.valid' }));
  const [currentShowType, setCurrentShowType] = useState('node');
  const [currentShowTypeText, setCurrentShowTypeText] = useState(intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.node' }));
  const repairModalRef = useRef();
  const showTypeRecord = [
    { key: 'node', text: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.node' }), value: 'node' },
    { key: 'person', text: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.person' }), value: 'person' },
  ];

  useEffect(() => {
    return () => {
      // @ts-ignore
      if (window.slaInterval) {
        // @ts-ignore
        clearInterval(window.slaInterval);
      }
    };
  }, []);

  useEffect(() => {
    if (defaultTicketSLADisplayMode === 'active') {
      goalsDataSet.setQueryParameter('activeFlag', true);
    }
    goalsDataSet.query();
  }, [ticketId]);

  useEffect(() => {
    if (goalsDataSet.status === 'ready' && !!ticketId && initCount === 0) {
      setInitCount(1);
      // @ts-ignore 一分钟刷一次
      window.slaInterval = setInterval(() => {
        goalsDataSet.query();
      }, 1000 * 60);
    }
  }, [goalsDataSet.status, initCount]);

  // 修复SLA
  function fixSLA() {
    Modal.confirm({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.warning.title' }),
      okFirst: true,
      children: <Fixsla repairModalRef={repairModalRef} {...context} />,
      destroyOnClose: true,
      onOk: () => {
        // @ts-ignore
        repairModalRef.current && repairModalRef.current.handleOk();
      },
      onClose: async () => {
        // @ts-ignore
        repairModalRef.current && repairModalRef.current.handleCancel();
      },
    });
  }

  async function handleChangeType(record) {
    const { value, text } = record || {};
    setCurrentType(text);
    if (value === 'active') {
      goalsDataSet.setQueryParameter('activeFlag', true);
    } else {
      goalsDataSet.setQueryParameter('activeFlag', null);
    }
    goalsDataSet.query();
  }

  function handleChangeShowType(r) {
    const { text, value } = r || {};
    setCurrentShowType(value);
    setCurrentShowTypeText(text);
  }

  const typeContent = () => {
    const showRecordList = [
      {
        key: 'all',
        text: intl.formatMessage({ id: 'zknow.common.desc.all' }),
        value: 'all',
      },
      {
        key: 'active',
        text: intl.formatMessage({ id: 'zknow.common.status.valid' }),
        value: 'active',
      },
    ];
    return (
      <Menu defaultSelectedKeys={[(viewRecord && viewRecord.get('widgetConfig.ticketSLADisplayMode')) || 'active']} className={style['yq-sla-dropdown-menu']}>
        {
          showRecordList.map(record => {
            return (
              <Mitem key={record.key} onClick={() => { handleChangeType(record); }}>
                <div className={style.temp}>
                  {record.text}
                </div>
              </Mitem>
            );
          })
        }
      </Menu>
    );
  };

  const showTypeContent = () => {
    return (
      <Menu defaultSelectedKeys={['node']} className={style['yq-sla-dropdown-menu']}>
        {
          showTypeRecord.map(record => {
            return (
              <Mitem key={record.key} onClick={() => { handleChangeShowType(record); }}>
                <div className={style.temp}>
                  {record.text}
                </div>
              </Mitem>
            );
          })
        }
      </Menu>
    );
  };

  const renderSLAHeader = () => {
    if (searching) {
      return (
        // @ts-ignore
        <TextField
          style={{ width: '100%', marginBottom: '20px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              goalsDataSet.setQueryParameter('param', e);
              goalsDataSet.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              goalsDataSet.setQueryParameter('param', e.target.value);
              goalsDataSet.query();
            }
          }}
          onEnterDown={(e) => {
            // @ts-ignore
            const value = e.target.value;
            if (!value) return;
            goalsDataSet.setQueryParameter('param', value);
            goalsDataSet.query();
          }}
        />
      );
    }
    return (
      <div className={style['header-top']}>
        <span className={style['header-top-title']}>{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.title' })}</span>
        <div className={style['header-top-icon']}>
          {slaPerformanceEvaluation && displayDimension?.includes('personnelPerformanceDetails') && <Dropdown overlay={showTypeContent()}>
            <Button className={`${style.action} ${style['action-active']}`} style={{ width: 'unset' }}>
              {currentShowTypeText}
              <Icon type="DownOne" theme="filled" size={14} style={{ marginLeft: '6px' }} />
            </Button>
          </Dropdown>}
          {/* @ts-ignore */}
          <Dropdown overlay={typeContent()}>
            <Button className={`${style.action} ${style['action-active']}`} style={{ width: 'unset' }}>
              {currentType}
              <Icon type="DownOne" theme="filled" size={14} style={{ marginLeft: '6px' }} />
            </Button>
          </Dropdown>
          <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.repair' })}>
            <Icon
              className={style['filter-icon']}
              style={{ marginLeft: '8px' }}
              type="change"
              size={16}
              onClick={() => fixSLA()}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className={style['filter-icon']}
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.refresh' })}>
            <Icon
              className={style['filter-icon']}
              style={{ marginLeft: '8px' }}
              type="refresh"
              size={16}
              onClick={() => {
                goalsDataSet.query();
              }}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const renderSLACard = () => {
    if (goalsDataSet && goalsDataSet.length === 0) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '20px' }}
          innerStyle={{ width: '80px', height: '80px' }}
          type="empty"
        />
      );
    }
    return (
      <div className={style[`${prefixCls}-content`]}>
        {goalsDataSet && goalsDataSet.map((i) => {
          return (
            <SLACard
              record={i}
              title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution' })}
              currentShowType={currentShowType}
              {...context}
            />
          );
        })}
      </div>
    );
  };

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <div className={style[`${prefixCls}-header`]}>
          {renderSLAHeader()}
        </div>
        {/* @ts-ignore */}
        <Spin dataSet={goalsDataSet}>
          {renderSLACard()}
        </Spin>
      </div>
    );
  };

  return renderMain();
});

export default MainView;
