@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.sla-renderer {
  padding: 16px;
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-header {
    .header-top {
      .flex-center;
      justify-content: space-between;
      margin-bottom: 16px;
      &-title {
        font-size: 16px;
        font-weight: 500;
        color: #12274d;
        line-height: 22px;
      }
      &-icon {
        .flex-center;
        .filter-icon {
          cursor: pointer;
          background: @minor-color;
          border-radius: 4px;
          padding: 8px;
          line-height: 1;
          height: 32px;
          color: @primary-color;
          &:hover {
            background-color: @primary-6;
            color: #fff;
          }
        }
      }
    }
    .header-bottom {
      .flex-center;
      margin-bottom: 21px;
      &-item {
        margin-right: 24px;
      }
    }

    .action {
      margin-left: 8px;
      padding: 6px 8px;
      white-space: nowrap;
      background: @minor-color !important;
      color: @primary-color !important;
      // border: solid 1px @minor-color;
      border: unset;
      border-radius: 0.04rem;
      cursor: pointer;

      font-size: 14px;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.85);
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:hover {
        background-color: @primary-color !important;
        color: #fff !important;
      }
      & > span {
        display: flex;
        align-items: center;
      }
    }
  }
}

.yq-sla-dropdown-menu {
  .temp {
    display: flex;
    align-items: center;
  }

  :global {
    .@{c7n-prefix}-menu-item {
      color: #12274d !important;
    }
  
    .@{c7n-prefix}-menu-item-selected {
      color: #12274d !important;
    }
  
    .@{c7n-prefix}-menu:not(.@{c7n-prefix}-menu-horizontal) .@{c7n-prefix}-menu-item-selected {
      color: #12274d !important;
    }
  }
}
