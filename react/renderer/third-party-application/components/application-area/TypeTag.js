/* eslint-disable no-mixed-operators */
import React from 'react';
import { Icon, Tooltip } from 'choerodon-ui/pro';
// import './TypeTag.less';
import classNames from 'classnames';

const TypeTag = ({ data, showName, style, featureType, iconSize = 24, className, tooltip = true, instanceType, intl }) => {
  const { name, colour } = data || {};
  let { icon } = data || {};
  // if (featureType === 'business') {
  //   colour = '#3D5AFE';
  //   name = '特性';
  //   icon = 'characteristic';
  // } else if (featureType === 'enabler') {
  //   colour = '#FFCA28';
  //   name = '使能';
  //   // icon = 'characteristic';
  // }
  if (icon === 'agile-backlog') {
    icon = 'highlight';
  }
  const reverse = ['agile_activity', 'agile_milestone', 'agile_view_timeline', 'agile_epic', 'agile_story', 'agile_fault', 'agile_task', 'agile_subtask', 'test-case', 'test-automation', 'agile-feature', 'agile_risk'].includes(icon);
  const tooltipTitle = typeof tooltip === 'boolean' && tooltip ? name : tooltip;
  if (instanceType === 'backlog') {
    const iconStyle = {
      width: '20px',
      height: '20px',
      background: 'rgba(246, 127, 90, 1)',
      borderRadius: '3px',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: 'white',
    };
    return (
      <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.thirdParty.backlog' })}>
        <Icon type="highlight" style={{ fontSize: 14, ...iconStyle }} />
      </Tooltip>
    );
  }
  return (
    <Tooltip title={tooltipTitle}>
      <div className={classNames('c7n-typeTag', className)} style={style}>
        {!reverse ? (<Icon
          className="c7n-typeTag-icon-normal"
          style={{
            transition: 'none',
            fontSize: iconSize * 15 / 24 || '15px',
            width: ((iconSize + 5) * 15 / 24) || '20px',
            height: ((iconSize + 5) * 15 / 24) || '20px',
            // fontSize: iconSize || '24px',
            lineHeight: `${((iconSize + 5) * 15 / 24)}px` || '20px',
            background: colour || '#fab614',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '3px',
          }}
          type={icon}
        />) : (<Icon
          style={{
            transition: 'none',
            fontSize: iconSize || '24px',
            color: colour || '#fab614',
          }}
          type={icon || 'help'}
        />)}
        {showName && (<span className="name">{name}</span>)}
      </div>
    </Tooltip>
  );
};
export default TypeTag;
