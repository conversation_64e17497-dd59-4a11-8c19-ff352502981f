@import '~choerodon-ui/lib/style/themes/default';

.third-party-application-renderer {
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-area {
    margin-bottom: 24px;
    .lc-title.lc-ticket-area-title {
      margin-bottom: 0.12rem;
      padding-left: 0;
      border-left: none;
      flex-shrink: 0;
      font-size: 0.14rem;
      font-weight: 500;
      line-height: 0.16rem;
      height: 0.16rem;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      &::before {
        content: ' ';
        display: inline-block;
        background-color: @primary-color;
        height: 100%;
        width: 4px;
        border-radius: 4px;
        margin-right: 8px;
      }
    }
    &-title {
      font-size: 14px;
      font-weight: 500;
      color: #12274d;
      line-height: 22px;
      padding-left: 6px;
      border-left: solid 4px @primary-color;
      margin-bottom: 12px;
    }

    &-card {
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid #e5e6eb;
      padding: 14px 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: background-color, border-color 0.2s ease;
      position: relative;
      &-cancel {
        position: absolute;
        right: 8px;
        bottom: 12px;
        background: white;
        display: none;
      }
      &:hover {
        transition: background-color, border-color 0.2s ease;
        background-color: #f7f8fa;
        // border-color: @primary-color;
      }
      &:hover &-cancel {
        display: block;
        .@{c7n-pro-prefix}-btn-wrapper {
          background-color: #f7f8fa;
        }
      }
      .header-top {
        .flex-center;
        justify-content: space-between;
        margin-bottom: 8px;
        &-left {
          .flex-center;
          &-icon {
            margin-right: 4px;
          }
          &-number {
            font-size: 0.14rem;
            font-weight: 400;
            color: rgba(18, 39, 77, 0.65);
            line-height: 0.22rem;
          }
        }
        &-right {
          .yq-cmp-status-tag {
            border-radius: 2px;
            color: #fff !important; // 猪齿鱼的文字颜色是白色
          }
        }
      }
      .header-title {
        font-size: 0.14rem;
        font-weight: 500;
        color: #12274d;
        line-height: 0.22rem;
        margin-bottom: 0.11rem;
      }
      .header-content {
        &-row {
          margin-bottom: 0.1rem;
          display: flex;
          align-items: center;
          &-last {
            margin-bottom: 0;
          }
          &-user {
            width: 1.42rem;
            display: inline-flex;
            align-items: center;
            .header-content-row-value-user-stack {
              // 单头像名字样式覆盖
              .yq-avatar-stack-avatar-single {
                .yq-avatar-stack-username {
                  font-size: 0.13rem;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  max-width: 50px;
                }
              }
            }
            .@{c7n-prefix}-avatar-string {
              transform: scale(1) translateX(-50%) !important;
            }
          }
          &-time {
            width: 100%;
          }
          &-label {
            font-size: 0.13rem;
            font-weight: 400;
            color: rgba(18, 39, 77, 0.65);
            line-height: 0.17rem;
            min-width: 0.52rem;
          }
          &-value {
            display: inline-flex;
            font-size: 0.13rem;
            font-weight: 400;
            color: #12274d;
            line-height: 0.17rem;
            // 单头像名字样式覆盖
            .yq-avatar-stack-avatar-single {
              .yq-avatar-stack-username {
                font-size: 0.13rem;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 50px;
              }
            }
            &-user-stack {
              display: flex;
              width: 100%;
              height: 0.24rem;
            }
            .@{c7n-prefix}-avatar-string {
              transform: scale(1) translateX(-50%) !important;
            }
          }
        }
      }
    }
  }
}

.lc-ticket-area-title {
  margin-left: 0 !important;
}
