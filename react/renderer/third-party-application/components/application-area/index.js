import React, { useMemo, useCallback } from 'react';
import { StatusTag, YqAvatarStack, Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { Tooltip } from 'choerodon-ui/pro';
import handleCommonRequest from '@/service';
import TypeTag from './TypeTag';
import FormTitle from '@/components/title';
import './index.less';

const ApplicationArea = observer((props) => {
  const {
    record = [], // record集合
    title,
    intl,
    prefixCls,
    tenantId,
    handleClickItem = () => {},
    thirdPartyApplicationId,
    ticketId,
  } = props;

  const statusObject = useMemo(() => {
    return {
      todo: '#ffb100',
      doing: '#4d90fe',
      done: '#00bfa5',
      prepare: '#F67F5A',
    };
  }, []);

  const data = useMemo(() => {
    return record.get('groupList') || [];
  }, [record.get('groupList')]);

  // 添加任务数量展示
  const areaTitle = useMemo(() => {
    const count = data?.length ? <span>&nbsp;({data?.length || ''})</span> : null;
    const renderTitle = <span>{title}{count}</span>;
    return <FormTitle className="lc-ticket-area-title" title={renderTitle} />;
  }, [title, data?.length]);

  // 经办人信息
  function getAssignees({ assignees = [], type = 'ellipsis' }) {
    if (type === 'ellipsis') {
      if (assignees?.length > 1) {
        const firstUser = assignees[0];
        return `${firstUser?.realName},+${assignees?.length - 1}...`;
      }
      return assignees?.[0]?.realName;
    } else {
      if (assignees?.length > 1) {
        let fullName;
        (assignees || [])?.map((i, index) => {
          if (index === 0) {
            fullName = `${i?.realName}`;
          } else {
            fullName = `${fullName}，${i?.realName}`;
          }
          return i;
        });
        return fullName;
      }
      return assignees?.[0]?.realName;
    }
  }
  // 渲染用户头像
  const renderUsers = (useArr = []) => {
    const avatarArr = (useArr || [])?.map(user => {
      const { imageUrl: src = '', realName: alternate } = user;
      return { src, alternate };
    });
    return <YqAvatarStack avatars={avatarArr} limit={3} size={20} bordered />;
  };

  // 展示的人员信息
  const getUserInfo = useCallback((i) => {
    let label;
    let renderer;
    if (i.instanceType === 'backlog') {
      // 需求展示处理人
      label = intl.formatMessage({ id: 'lcr.renderer.thirdParty.handler' });
      renderer = (
        <Tooltip title={getAssignees({ assignees: i?.assignees, type: 'full' })}>
          {renderUsers(i?.assignees)}
        </Tooltip>
      );
    } else if (i.instanceType === 'issue' && i?.issueType?.typeCode === 'feature') {
      // 特性，展示更新人， 使能是特性的一种类型
      label = intl.formatMessage({ id: 'lcr.renderer.thirdParty.updater' });
      renderer = (
        <Tooltip title={getAssignees({ assignees: [i?.updater], type: 'full' })}>
          {renderUsers([i?.updater])}
        </Tooltip>
      );
    } else {
      // 其他工作项展示经办人
      label = intl.formatMessage({ id: 'lcr.renderer.thirdParty.assignees' });
      renderer = (
        <Tooltip title={getAssignees({ assignees: i?.assignees, type: 'full' })}>
          {renderUsers(i?.assignees)}
        </Tooltip>
      );
    }
    return {
      label,
      renderer,
    };
  }, [data]);

  async function handleCancelLink(e, _data) {
    e.stopPropagation();
    await handleCommonRequest('handleC7NDelink', {
      dataSet: record?.dataSet,
      successTip: intl.formatMessage({ id: 'lcr.renderer.thirdParty.relation.delete.success' }),
      params: {
        ..._data,
        tenantId,
        openAppId: thirdPartyApplicationId,
        ticketId,
        openInstanceId: ticketId,
      },
    });
  }

  const renderCardItem = () => {
    return (data || [])?.map((i) => {
      const creatorInfo = i.instanceType === 'backlog' ? i?.creator ?? {} : i?.reporter ?? {};
      const creatorAvatar = [{
        src: creatorInfo?.imageUrl || '',
        alternate: creatorInfo?.realName,
      }];
      return (
        <div className={`${prefixCls}-area-card`} onClick={() => handleClickItem({ itemInfo: i })}>
          <div className="header-top">
            <div className="header-top-left">
              <div className="header-top-left-icon">
                <TypeTag
                  data={i?.issueType}
                  instanceType={i?.instanceType}
                  intl={intl}
                />
              </div>
              <div className="header-top-left-number">{i?.instanceNum}</div>
            </div>
            <div className="header-top-right">
              <StatusTag color={statusObject[i?.status?.type] || '#ffb100'}>{i?.status?.name}</StatusTag>
            </div>
          </div>
          <div className="header-title">{i?.summary || '-'}</div>
          <div className="header-content">
            <div className="header-content-row">
              <div>
                <div className="header-content-row-user">
                  <span className="header-content-row-label">
                    {/* 工作项是报告人，需求是创建人 */}
                    {i.instanceType === 'backlog' ? intl.formatMessage({ id: 'lcr.renderer.thirdParty.creator' }) : intl.formatMessage({ id: 'lcr.renderer.thirdParty.reporter' })}
                  </span>
                  <Tooltip title={creatorInfo?.realName}>
                    <span className="header-content-row-value">
                      <YqAvatarStack avatars={creatorAvatar} size={20} />
                    </span>
                  </Tooltip>
                </div>
              </div>
              <div className="header-content-row-time">
                <span className="header-content-row-label">{intl.formatMessage({ id: 'zknow.common.model.creationDate' })}</span>
                <span className="header-content-row-value">{i?.creationDate}</span>
              </div>
            </div>
            <div className="header-content-row header-content-row-last">
              <div>
                <div className="header-content-row-user">
                  <span className="header-content-row-label">{getUserInfo(i)?.label}</span>
                  <span className="header-content-row-value-user-stack">
                    {getUserInfo(i)?.renderer}
                  </span>
                </div>
              </div>
              <div className="header-content-row-time">
                <span className="header-content-row-label">{intl.formatMessage({ id: 'lcr.renderer.thirdParty.lastUpdateDate' })}</span>
                <span className="header-content-row-value">{i?.lastUpdateDate}</span>
              </div>
            </div>
          </div>
          <div className={`${prefixCls}-area-card-cancel`}>
            <Button
              icon="LinkBreak"
              onClick={async (e) => {
                await handleCancelLink(e, i);
              }}
              funcType="flat"
              style={{ color: '#F83552' }}
            >{intl.formatMessage({ id: 'lcr.renderer.thirdParty.delink' })}</Button>
          </div>
        </div>
      );
    });
  };

  const renderMain = () => {
    if (data?.length === 0) return null;
    return (
      <div className={`${prefixCls}-area`}>
        {areaTitle}
        {renderCardItem()}
      </div>
    );
  };
  return renderMain();
});

export default ApplicationArea;
