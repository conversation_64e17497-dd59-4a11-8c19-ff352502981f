/* eslint-disable jsx-a11y/alt-text */
/*
 * @Author: xiaoreya
 * @Description: 每一项猪齿鱼
 */
import React, { useMemo, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import qs from 'qs';
import './index.less';

const ApplicationInfo = observer((props) => {
  const {
    itemInfo, // 卡片信息
    prefixCls,
    type,
    openAppsDetailDataSet,
    thirdPartyApplicationDataSet,
    thirdTokenDataSet,
    modal,
    tenantCode,
    mappingJson,
  } = props;

  useEffect(() => {
    window.addEventListener('message', messageListener, false);
    return () => {
      try {
        window.removeEventListener('message', messageListener, false);
      } catch { /**/ }
    };
  }, []);

  const messageListener = async (event) => {
    const frame = document.getElementById('third-party-application');
    if (event) {
      if (event.data === 'INITDONE') {
        frame.contentWindow.postMessage({ choerodonMessage: mappingJson }, '*');
      }
      if (event?.data?.choerodonMessage?.action === 'close') {
        // 有猪齿鱼的消息, 类型是关闭
        thirdPartyApplicationDataSet.query();
        modal?.close();
      }
      if (event?.data?.choerodonMessage?.type === 'success') {
        // 有猪齿鱼的消息, 创建成功了
        thirdPartyApplicationDataSet.query();
        modal?.close();
      }
    }
  };

  const url = useMemo(() => {
    // let prefixUrl = 'https://apps.dev.devops.hand-china.com';
    let prefixUrl;
    try {
      const info = JSON.parse(openAppsDetailDataSet?.current?.get('jsonConfig'));
      prefixUrl = info?.domainUrl;
    } catch (e) {
      //
    }
    const params = {
      tenant_code: tenantCode,
      access_token: thirdTokenDataSet?.current?.get('userAccessToken'),
      token_type: 'bearer',
      issueId: itemInfo?.instanceId,
      projectId: itemInfo?.projectId,
      yq_cloud_gateway: window._env_.API_HOST,
    };
    const queryParams = qs.stringify(params);
    let link; 
    if (/* 详情 */ itemInfo?.instanceId) {
      if (/* 需求 */ itemInfo?.instanceType === 'backlog') {
        link = `${prefixUrl}/#/agile/outward/detail/backlog?${queryParams}`;
      } else if (/* 工作项 */ itemInfo?.instanceType === 'issue') {
        link = `${prefixUrl}/#/agile/outward/detail/agile?${queryParams}`;
      }
    } else if (/* 新建工作项 */ type === 'WorkItem') {
      link = `${prefixUrl}/#/agile/outward/create/agile?${queryParams}`;
    } else if (/* 新建需求单 */ type === 'DemandList') {
      link = `${prefixUrl}/#/agile/outward/create/backlog?${queryParams}`;
    } else if (type === 'relatedWorkItem') {
      link = `${prefixUrl}/#/agile/outward/link/issue?${queryParams}`;
    } else if (type === 'relatedDemand') {
      link = `${prefixUrl}/#/agile/outward/link/backlog?${queryParams}`;
    } 
    return link;
  }, [type, itemInfo]);

  const renderMain = () => {
    return (
      <div className={`${prefixCls}-info`}>
        <iframe
          id="third-party-application" 
          className={`${prefixCls}-info-iframe`}
          src={url}
          style={{ width: '100%', height: 'calc(100%)' }}
          title="editor"
          frameBorder="no"
          border={0}
        />
      </div>
    );
  };
  return renderMain();
});

export default ApplicationInfo;
