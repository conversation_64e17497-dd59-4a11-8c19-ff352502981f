import React, { createContext, useMemo, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import ThirdPartyApplicationDataSet from './ThirdPartyApplicationDataSet';
import ThirdTokenDataSet from './ThirdTokenDataSet';
import OpenAppsDetailDataSet from './OpenAppsDetailDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId, code: tenantCode } },
      viewCode,
      formDataSet,
      viewDataSet,
      viewRecord,
    } = props;
    const prefixCls = 'third-party-application-renderer';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];
    const ticketId = formDataSet?.current?.get('_id') || formDataSet?.current?.get('id');
    const thirdPartyApplicationId = useMemo(() => viewRecord?.get('widgetConfig.thirdPartyApplication'), [viewRecord?.get('widgetConfig.thirdPartyApplication')]);
    const [mappingJson, setMappingJson] = useState('');

    const thirdPartyApplicationDataSet = useMemo(() => new DataSet(ThirdPartyApplicationDataSet({
      tenantId,
      businessObjectCode,
      ticketId,
      thirdPartyApplicationId,
    })), [businessObjectCode, ticketId, thirdPartyApplicationId]);

    const thirdTokenDataSet = useMemo(() => new DataSet(ThirdTokenDataSet({
      tenantId,
      ticketId,
      thirdPartyApplicationId,
    })), [ticketId, thirdPartyApplicationId]);

    const openAppsDetailDataSet = useMemo(() => new DataSet(OpenAppsDetailDataSet({
      tenantId,
      thirdPartyApplicationId,
    })), [thirdPartyApplicationId]);

    const value = {
      ...props,
      formDataSet,
      dsFieldList,
      prefixCls,
      formConfig,
      tenantId,
      tenantCode,
      ticketId,
      thirdPartyApplicationDataSet,
      thirdPartyApplicationId,
      thirdTokenDataSet,
      openAppsDetailDataSet,
      mappingJson,
      businessObjectCode,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
