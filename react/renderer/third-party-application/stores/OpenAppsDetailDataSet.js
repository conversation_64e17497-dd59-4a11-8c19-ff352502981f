export default ({ tenantId, thirdPartyApplicationId }) => {
  return {
    autoQuery: true,
    paging: false,
    selection: false,
    transport: {
      read: () => {
        if (!thirdPartyApplicationId) return null;
        return {
          url: `/iam/yqc/${tenantId}/open_apps/${thirdPartyApplicationId}`,
          method: 'get',
        };
      },
    },
    fields: [],
    events: {
    
    },
  };
};
