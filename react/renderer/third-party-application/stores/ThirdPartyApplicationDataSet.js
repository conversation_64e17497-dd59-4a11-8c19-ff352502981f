/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-10-25 10:46:20
 * @Description:
 */
/* eslint-disable guard-for-in */
/* eslint-disable no-restricted-syntax */

export default ({ tenantId, ticketId, businessObjectCode, thirdPartyApplicationId }) => {
  function groupBy(list, key, ...others) {
    const obj = list.reduce((rv, current) => {
      (rv[current[key]] = rv[current[key]] || []).push(current);
      return rv;
    }, {});
    const result = [];
    for (const i in obj) {
      const data = {};
      others.forEach((j) => {
        data[j] = obj[i]?.[0]?.[j];
      });
      data[key] = i;
      data.groupList = obj[i];
      result.push(data);
    }
    return result;
  }

  return {
    autoQuery: true, // 待办
    paging: false,
    selection: false,
    transport: {
      read: () => {
        if (!ticketId) return null;
        if (!thirdPartyApplicationId) return null;
        if (!businessObjectCode) return null;
        return {
          url: `/itsm/v1/${tenantId}/c7n_ticket/list/${thirdPartyApplicationId}/${ticketId}`,
          method: 'get',
          transformResponse(response) {
            try {
              const res = JSON.parse(response);
              if (res?.failed) {
                // 现在异常会有全局捕获，所以不需要额外开发提示信息
                return response;
              } else {
                // 最终返回的数组
                const list = groupBy(res, 'projectId', 'projectName');
                return list;
              }
            } catch (e) {
              return response;
            }
          },
        };
      },
    },
    fields: [],
    events: {

    },
  };
};
