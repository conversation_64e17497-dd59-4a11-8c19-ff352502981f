export default ({ tenantId, ticketId, thirdPartyApplicationId }) => {
  return {
    autoQuery: true,
    paging: false,
    selection: false,
    transport: {
      read: () => {
        if (!ticketId) return null;
        if (!thirdPartyApplicationId) return null;
        return {
          url: `/iam/v1/${tenantId}/choerodon_user/token/${thirdPartyApplicationId}`,
          method: 'get',
        };
      },
    },
    fields: [],
    events: {
    
    },
  };
};
