/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022-01-24 19:41:53
 * @Description:
 */
import React, { useState, useContext, useEffect, useRef } from 'react';
import { Icon } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Empty } from '@zknow/components';
import { Dropdown, Menu, Tooltip, TextField, Spin, Modal } from 'choerodon-ui/pro';
import { queryAppFieldMappingConfig } from '@/service';
import ApplicationArea from './components/application-area';
import ApplicationInfo from './components/application-info';
import Store from './stores';
import './index.less';

const { Item: Mitem } = Menu;
const modalKey = Modal.key();

const ThirdPartyApplication = observer(() => {
  const context = useContext(Store);
  const {
    formDataSet,
    intl,
    viewRecord,
    prefixCls,
    intlPrefix,
    ticketId,
    tenantId,
    thirdPartyApplicationDataSet,
    thirdTokenDataSet,
    openAppsDetailDataSet,
    thirdPartyApplicationId,
    tenantCode,
    businessObjectCode,
  } = context;
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();
  const textMapping = {
    workItem: intl.formatMessage({ id: 'lcr.renderer.thirdParty.workItem' }),
    demandList: intl.formatMessage({ id: 'lcr.renderer.thirdParty.demandList' }),
    relatedWorkItem: intl.formatMessage({ id: 'lcr.renderer.thirdParty.relatedWorkItem' }),
    relatedDemand: intl.formatMessage({ id: 'lcr.renderer.thirdParty.relatedDemand' }),
  };
  const thirdPartyAppLength = useRef(); // 记录三方创建单据的数量
  useEffect(() => {
      // 回复组件重新校验同步猪齿鱼权限
      formDataSet?.setState('hasThirdPartyApplication', thirdPartyApplicationDataSet?.length);
  }, [thirdPartyApplicationDataSet?.length]);

  function refresh() {
    thirdPartyApplicationDataSet.query();
  }

  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: '20px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              thirdPartyApplicationDataSet.setQueryParameter('param', e);
              thirdPartyApplicationDataSet.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              thirdPartyApplicationDataSet.setQueryParameter('param', e.target.value);
              thirdPartyApplicationDataSet.query();
            }
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            thirdPartyApplicationDataSet.setQueryParameter('param', value);
            thirdPartyApplicationDataSet.query();
          }}
        />
      );
    }
    return (
      <div className="header-top">
        <span className="header-top-title">{viewRecord?.get('name') || intl.formatMessage({ id: 'lcr.renderer.thirdParty.title' })}</span>
        <div className="header-top-icon">
          <Dropdown overlay={typeContent()}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="add-one"
              size={16}
            />
          </Dropdown>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.refresh' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="refresh"
              size={16}
              onClick={() => refresh()}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const typeContent = () => {
    const actions = [
      { lang: 'workItem', onClick: () => { handleOpenModal({ type: 'WorkItem', lang: 'workItem' }); } },
      { lang: 'demandList', onClick: () => { handleOpenModal({ type: 'DemandList', lang: 'demandList' }); } },
    ];
    const version = JSON.parse(openAppsDetailDataSet?.current?.get('jsonConfig') || '{}')?.version;
    if (Number(version) >= 2) {
      actions.push(
        { lang: 'relatedWorkItem', onClick: () => { handleOpenModal({ type: 'relatedWorkItem', lang: 'relatedWorkItem' }); } },
        { lang: 'relatedDemand', onClick: () => { handleOpenModal({ type: 'relatedDemand', lang: 'relatedDemand' }); } },
      );
    }

    return (
      <Menu className="yq-dynamic-main-dropdown-menu" selectable={false}>
        {actions.map(item => <Mitem>
          <div className="temp" onClick={item.onClick}>
            {textMapping[item.lang]}
          </div>
        </Mitem>)}
      </Menu>
    );
  };

  // 建单
  async function handleOpenModal({ type, lang, itemInfo, drawer = false }) {
    let title;
    let mappingJson = null;
    if (lang) {
      title = textMapping[lang];
    } else {
      title = intl.formatMessage({ id: 'lcr.renderer.thirdParty.ticketDetail' });
    }
    const res = await queryAppFieldMappingConfig({ tenantId, ticketId, thirdPartyApplicationId, businessObjectCode });
    if (res && !res.failed) {
      mappingJson = res;
    }
    const extraProps = {
      key: modalKey,
      title,
      drawer,
      style: { width: '800px' },
      destroyOnClose: true,
      footer: null,
    };
    await thirdTokenDataSet.query();
    Modal.open({
      className: `${prefixCls}-modal`,
      children: (
        <ApplicationInfo
          itemInfo={itemInfo}
          type={type}
          tenantId={tenantId}
          prefixCls={prefixCls}
          thirdTokenDataSet={thirdTokenDataSet}
          formDataSet={formDataSet}
          openAppsDetailDataSet={openAppsDetailDataSet}
          thirdPartyApplicationDataSet={thirdPartyApplicationDataSet}
          tenantCode={tenantCode}
          mappingJson={mappingJson}
        />
      ),
      afterClose: () => {
        // 编辑后，关闭弹窗刷新列表。
        if (!type) thirdPartyApplicationDataSet.query();
      },
      ...extraProps,
    });
  }

  useEffect(() => {
    const length = thirdPartyApplicationDataSet?.current?.get('groupList')?.length; // 三方创建单据的实时数量
    if (length !== thirdPartyAppLength.current) { // 只要关联三方单据数量变化，就会触发
      thirdPartyAppLength.current = length;
      const setJournalSvncFlag = formDataSet.getState('setJournalSvncFlag'); // 从itsm传来的回复上的方法，调用接口查询同步猪齿鱼的可用性
      if (setJournalSvncFlag && typeof setJournalSvncFlag === 'function') {
        setJournalSvncFlag();
      }
    }
  }, [thirdPartyApplicationDataSet?.current?.get('groupList')?.length]);

  // 渲染正文
  function renderThirdPartyApplicationList() {
    if (thirdPartyApplicationDataSet?.length === 0) {
      return (
        <Empty
          style={{ padding: '8px 50px', paddingTop: '16px' }}
        />
      );
    }
    return thirdPartyApplicationDataSet?.map((i) => {
      return (
        <ApplicationArea
          record={i}
          title={i?.get('projectName')}
          intl={intl}
          tenantId={tenantId}
          intlPrefix={intlPrefix}
          prefixCls={prefixCls}
          ticketId={ticketId}
          thirdPartyApplicationId={thirdPartyApplicationId}
          handleClickItem={({ itemInfo }) => handleOpenModal({ itemInfo, drawer: true })}
        />
      );
    });
  }

  const renderMain = () => {
    if (!formDataSet?.current) return null;
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-header`}>
          {renderHeader()}
        </div>
        <Spin dataSet={thirdPartyApplicationDataSet}>
          {renderThirdPartyApplicationList()}
        </Spin>
      </div>
    );
  };

  return renderMain();
});

const ThirdPartyApplicationRenderer = injectIntl((props) => {
  return <ThirdPartyApplication {...props} />;
});

export default ThirdPartyApplicationRenderer;
