import React, { useContext, useEffect, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, SelectBox, Table } from 'choerodon-ui/pro';
import { Empty } from '@zknow/components';
import Store from './stores';
import { deltaToHtml } from '../work-hour/util';

function MainView() {
  const {
    intl,
    formDataSet,
    replayListDataSet,
    actionListDataSet,
  } = useContext(Store);

  const record = formDataSet?.current;
  const category = (formDataSet?.getField?.('category')?.options?.all || []).filter(r => r.get('enabledFlag')).map(r => r.get('code'))[0];
  const isActualWorkTime = record?.get('category') === 'ACTUAL_WORK_TIME';
  const isRelateReply = record?.get('relate') === 'REPLY';
  const defaultType = formDataSet?.current?.getField('category')?.options?.get(0)?.get('code');
  const replyText = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' });
  const actionText = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.action', defaultMessage: '动作' });

  const tableDataSet = useMemo(() => (isRelateReply ? replayListDataSet : actionListDataSet), [isRelateReply]);
  const relateQuery = () => {
    tableDataSet?.selected?.forEach((r) => replayListDataSet.unSelect(r));
    tableDataSet.query();
  };
  useEffect(() => {
    relateQuery();
  }, [isRelateReply]);

  useEffect(() => {
    let relate;
    if (category === 'ACTUAL_WORK_TIME') {
      relate = 'REPLY';
    }
    formDataSet?.current?.set({
      category,
      relate,
    });
  }, [category]);

  useEffect(() => {
    if (tableDataSet?.selected?.length > 0) {
      const idList = tableDataSet?.selected?.map(_record => {
        return _record?.get('id');
      });
      formDataSet?.setState('tableList', idList);
    }
  }, [tableDataSet?.selected?.length]);

  const handleChangeCategory = (value) => {
    if (value === 'ACTUAL_WORK_TIME') {
      record?.set('relate', 'REPLY');
    }
  };

  return (
    <div>
      <Form dataSet={formDataSet} labelWidth="100" style={{ marginBottom: '16px' }}>
        <SelectBox name="category" onChange={handleChangeCategory} />
        {isActualWorkTime && <SelectBox name="relate" />}
      </Form>
      {isActualWorkTime && record?.get('relate') && <div>
        {isRelateReply ? (
          <Table
            key="reply"
            dataSet={replayListDataSet}
            placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
            condition="param"
            title={replyText}
            empty={<Empty type="serviceorder" />}
            autoHeight
            queryBarProps={{
              title: replyText,
              queryFieldsStyle: {
                content: { width: 200 },
              },
            }}
          >
            <Table.Column
              name="content"
              tooltip="overflow"
              renderer={({ record: _record, value }) => deltaToHtml(value, _record)}
              tooltipProps={{
                popupInnerStyle: {
                  maxHeight: '240px',
                  maxWidth: '380px',
                  padding: '16px',
                  overflow: 'auto',
                  paddingBottom: '0',
                },
                theme: 'light',
              }}
            />
            <Table.Column name="replyTime" />
          </Table>
        ) : (
          <Table
            key="action"
            dataSet={actionListDataSet}
            placeholder={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}
            condition="param"
            title={actionText}
            empty={<Empty type="serviceorder" />}
            autoHeight
            queryBarProps={{
              title: actionText,
              queryFieldsStyle: {
                name: { width: 140 },
              },
            }}
          >
            <Table.Column name="name" tooltip="overflow" />
            <Table.Column name="creationDate" />
          </Table>
        )}
      </div>}
    </div>
  );
}

export default observer(MainView);
