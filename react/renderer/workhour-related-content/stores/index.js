import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { ActionListDataSet, ReplyListDataSet } from '../../work-hour/createButton/ButtonStores/DurationDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      businessObjectCode,
    } = props;
    const intlPrefix = 'lc.components.workHour';

    const replayListDataSet = useMemo(
      () => new DataSet(ReplyListDataSet({ intl, tenantId, intlPrefix, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );
    const actionListDataSet = useMemo(
      () => new DataSet(ActionListDataSet({ intl, tenantId, intlPrefix, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );
    const value = {
      ...props,
      intl,
      tenantId,
      ticketId,
      businessObjectCode,
      replayListDataSet,
      actionListDataSet,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
