import React, { useContext } from 'react';
import { Icon } from '@zknow/components';
import { Timeline } from 'choerodon-ui';
import Store from './stores';
import styles from './styles/main.module.less';
import Content from './components/Content';

const { Item } = Timeline;

export default () => {
  const { processData, ACTION_MAP } = useContext(Store);
  if (!processData || processData?.failed || !Array.isArray(processData)) {
    return null;
  }
  const RenderDot = ({ type }) => {
    return (
      <Icon
        size={18}
        type={ACTION_MAP[type]?.icon}
        fill={ACTION_MAP[type]?.color}
        theme={ACTION_MAP[type]?.theme ?? 'filled'}
      />
    );
  };
  return (
    <Timeline className={styles.timeline}>
      {processData.map((item) => {
        return (
          <Item dot={<RenderDot type={item.stateType} />}>
            <Content data={item} />
          </Item>
        );
      })}
    </Timeline>
  );
};
