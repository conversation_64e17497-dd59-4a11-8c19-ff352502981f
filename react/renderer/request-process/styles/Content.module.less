.actionText {
  margin: 0 8px;
}
.collapse {
  cursor: pointer;
  position: relative;
  top: 3px;
  left: 4px;
  transition: transform 0.5s ease-in-out;
  &:hover {
    background: rgba(41, 121, 255, 0.2);
    border-radius: 2px;
  }
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}
.records {
  margin-top: 12px;
}
.record {
  margin-bottom: 6px;
}
.result {
  margin: 0 8px;
}
.agree {
  color: #7bc95a;
}
.refuse {
  color: #ff2728;
}
.time {
  font-size: 12px;
  line-height: 22px;
  color: rgba(18, 39, 77, 0.45);
}
.recordHeader {
  height: 22px;
  line-height: 22px;
  margin-bottom: 4px;
}
.recordContent {
  background: #f6f7f9;
  border-radius: 4px;
  padding: 8px;
  & > label {
    color: rgba(18, 39, 77, 0.65);
    margin-right: 4px;
  }
  & > span {
    color: rgba(18, 39, 77, 0.85);
  }
}
.main {
  overflow: hidden;
  max-height: 500px;
  transition: max-height 0.2s ease-in-out;
}
.collapsed {
  max-height: 0px;
}
.rotate {
  transform: rotate3d(1, 0, 0, 180deg);
  transition: transform 0.5s ease-in-out;
}
.sla {
  font-weight: 400;
}
.remain {
  color: #FF9100;
}
.timeout {
  color: #F34C4B;
}
.onTime {
  color: #1AB335;
}
.remain,
.timeout,
.onTime {
  display: flex;
  align-items: center;
  & > span {
    margin-right: 4px;
  }
}