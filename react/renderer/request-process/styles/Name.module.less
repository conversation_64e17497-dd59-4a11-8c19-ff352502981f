.more {
  color: @primary-color !important;
}
.name {
  height: 22px;
  color: #12274d;
  cursor: pointer;
  &:hover {
    background-color: rgb(242, 243, 245);
  }
}
.popList {
  width: 388px;
  max-height: 356px;
  overflow: scroll;
  padding: 16px 14px 2px 16px !important;
  box-shadow: 0px 2px 12px 0px rgba(39, 61, 97, 0.12);
  background: #fff;
  :global {
    .c7n-popover-inner-content {
      padding: 0 !important;
    }
    .c7n-popover-inner {
      box-shadow: none;
    }
    .c7n-popover-arrow {
      display: none !important;
    }
  }
}
.popItem {
  display: grid;
  grid-template-columns: auto 1fr auto;
  grid-column-gap: 8px;
  grid-row-gap: 12px;
  align-items: center;
  justify-content: space-between;
  align-items: center;
  line-height: 24px;
  color: rgba(18, 39, 77, 0.65);
}
.popItemName {
  display: flex;
  align-items: center;
  color: #12274d;
  grid-column: 1 / 2;
}
.popItemNameText {
  margin-left: 4px;
}
.email {
  grid-column: 2 / 3;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.department {
  grid-column: 3 / 4;
}
