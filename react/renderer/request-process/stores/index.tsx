import React, { createContext, useMemo, useEffect } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { useRequest } from 'ahooks';
import { formatterCollections } from '@zknow/utils';
import { getRequestProcess } from '@/service';
import { transformResponse, getActionMap } from '../utils';

const Store = createContext<any>({});

export default Store;

export const StoreProvider = formatterCollections({
  code: ['zknow.common', 'lcr.renderer'],
})(injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      instanceId,
      intl,
      formDataSet,
    } = props;

    const { data, refresh: refreshData } = useRequest(() => getRequestProcess({ tenantId, id: instanceId }));
    const processData = useMemo(() => data && transformResponse(data), [data]);
    const ACTION_MAP = useMemo(() => getActionMap(intl), []);

    useEffect(() => {
      if (formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount') !== undefined) {
        setTimeout(refreshData, 3000);
      }
    }, [formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount')]);

    const value = {
      ...props,
      processData,
      refreshData,
      ACTION_MAP,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
)));
