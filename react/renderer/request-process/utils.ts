import moment from 'moment';

export const getActionMap = (intl) => {
  return {
    Start: {
      icon: 'CheckOne',
      color: '#23C343',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.launch', defaultMessage: '发起' }),
    },
    Unexecuted: {
      icon: 'Round',
      color: '#CBD2DC',
      theme: '',
      action: '',
    },
    Open: {
      icon: 'ArrowCircleRight',
      color: '#2979FF',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.pending', defaultMessage: '待处理' }),
    },
    Processing: {
      icon: 'LoadingThree',
      color: '#FFC013',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.processing', defaultMessage: '处理中' }),
    },
    Approved: {
      icon: 'CheckOne',
      color: '#23C343',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.approved', defaultMessage: '审批同意' }),
      textCls: 'agree',
    },
    APPROVED: {
      icon: 'CheckOne',
      color: '#23C343',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.approved', defaultMessage: '审批同意' }),
      textCls: 'agree',
    },
    Rejected: {
      icon: 'CloseOne',
      color: '#F34C4B',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.rejected', defaultMessage: '审批拒绝' }),
      textCls: 'refuse',
    },
    REJECTED: {
      icon: 'CloseOne',
      color: '#F34C4B',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.rejected', defaultMessage: '审批拒绝' }),
      textCls: 'refuse',
    },
    RETURN: {
      icon: 'CloseOne',
      color: '#F34C4B',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.dismissed', defaultMessage: '驳回' }),
      textCls: 'refuse',
    },
    Return: {
      icon: 'CloseOne',
      color: '#F34C4B',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.approval.dismissed', defaultMessage: '审批驳回' }),
      textCls: 'refuse',
    },
    Completed: {
      icon: 'CheckOne',
      color: '#23C343',
      action: intl.formatMessage({ id: 'lcr.components.model.completed', defaultMessage: '已完成' }),
    },
    Canceled: {
      icon: 'CloseOne',
      color: '#F34C4B',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.canceled', defaultMessage: '已取消' }),
    },
    SKIPPED: {
      icon: 'MoreTwo',
      color: '#C9CDD4',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.skipped', defaultMessage: '已跳过' }),
    },
    Skipped: {
      icon: 'MoreTwo',
      color: '#C9CDD4',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.skipped', defaultMessage: '已跳过' }),
    },
    Requested: {
      icon: 'ArrowCircleRight',
      color: '#2979FF',
      action: intl.formatMessage({ id: 'lcr.renderer.desc.requested', defaultMessage: '待审批' }),
    },
  };
};

export const END_ACTIONS = [
  'Skipped', 'Completed', 'Rejected', 'Approved',
];

export const transformResponse = (data) => {
  if (!data || !Array.isArray(data)) {
    return null;
  }
  return data
    .reduce((pre, cur) => {
      // 取消后的节点不展示
      if (!(pre?.[pre.length - 1]?.stateType === 'Canceled') || cur.stateType === 'Canceled') {
        pre.push(cur);
      }
      return pre;
    }, [])
    .map((item, index) => {
      if (index === 0) {
        return {
          ...item,
          stateType: 'Start',
        };
      }
      const { counterSignInfos, assignmentInfos, ...rest } = item;
      let list: Array<any> = [];
      list = counterSignInfos || assignmentInfos || [item];
      // 或签 审批过的节点处理
      if (item.approvalType === 'JUSTONESIGN' && item.approvalState) {
        const appRecord = list.find(i => i.userId === item.userId);
        if (!appRecord) list = [item];
        else list = [{ ...appRecord, date: appRecord?.date || item.date }];
      }
      // 已结束的任务节点 只展示执行的人员
      if (item.type !== 'APPROVAL' && END_ACTIONS.includes(item.stateType)) {
        list = [item];
      }
      if (item.stateType === 'Canceled') {
        list = [];
      }
      return {
        ...rest,
        list,
      };
    });
};
