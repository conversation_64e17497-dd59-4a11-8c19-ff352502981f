import React, { useState, useContext, useMemo } from 'react';
import { Tooltip } from 'choerodon-ui/pro';
import classnames from 'classnames';
import { Icon } from '@zknow/components';
import { sec2string, getEstimatedTime } from '@/utils';
import { END_ACTIONS } from '../utils';
import Store from '../stores';
import { Name, NameList } from './Name';
import styles from '../styles/Content.module.less';

export default ({ data }) => {
  const { intl, ACTION_MAP, collapseFlag } = useContext(Store);
  const [collapse, setCollapse] = useState(!!collapseFlag);

  const getAction = () => {
    if (data.list && data.approvalType) {
      return data.approvalType === 'COUNTERSIGN'
        ? intl.formatMessage({ id: 'lcr.renderer.desc.flow.parallelApproval', defaultMessage: '会签' })
        : intl.formatMessage({ id: 'lcr.renderer.desc.flow.anyoneApproval', defaultMessage: '或签' });
    }
    return ACTION_MAP[data.stateType]?.action;
  };

  const Records = ({ records = [] }: { records: Array<any>}) => {
    const list = records.filter(record => record?.approvalState);
    if (!list.length) return null;
    return (
      <div className={styles.records}>
        {list.map(record => {
          return (
            <div className={styles.record}>
              <div className={styles.recordHeader}>
                <Name name={record.userName || record.username} id={record.userId} />
                <span
                  className={classnames(styles.result, styles[ACTION_MAP[record?.approvalState]?.textCls])}
                >
                  {ACTION_MAP[record?.approvalState]?.action}
                </span>
                <span className={styles.time}>{record?.date}</span>
              </div>
              {record?.remark && <div className={styles.recordContent}>
                <label htmlFor="content">{intl.formatMessage({ id: 'lcr.renderer.desc.approval.opinions', defaultMessage: '审批意见为:' })}</label>
                <span>{record?.remark || '-'}</span>
              </div>}
            </div>
          );
        })}
      </div>
    );
  };

  const content = useMemo(() => (
    <>
      {data?.list?.length > 1 ? <NameList
        intl={intl}
        data={data?.list && data?.list.map(item => {
          return {
            email: item.userEmail,
            imageUrl: item.userImage,
            department: item.departmentName,
            name: item.userName || item.username,
            id: item.userId,
          };
        })}
      /> : null}
      <Records records={data?.list || []} />
    </>
  ), [data?.list]);

  if (data.type === 'START') {
    return (
      <header className={styles.header}>
        <div>
          <Name name={data.userName || '-'} id={data.userId} />
          <span className={styles.actionText}>
            {intl.formatMessage({ id: 'lcr.renderer.desc.launch', defaultMessage: '发起' })}
          </span>
          <span>{data?.itemName}</span>
        </div>
      </header>
    );
  }

  const displayName = useMemo(() => {
    if (data.stateType === 'Canceled') return false;
    if (data.type === 'APPROVAL') return data?.list?.length === 1 && (data.userName || data.username || data.userId);
    if (data?.list?.length === 1 || END_ACTIONS.includes(data.stateType)) return true;
    return false;
  }, [data]);
  const displayExpand = useMemo(() => {
    if (data.type === 'APPROVAL') {
      const pLen = data.list.filter(item => (item.userName || item.username || item.userId)).length;
      const aLen = data.list.filter(item => item?.approvalState).length;
      // 人数>1 或者 审批记录>0
      if (pLen > 1 || aLen > 0) return true;
    }
    return data?.list?.length > 1;
  }, [data]);
  const displayAction = useMemo(() => {
    if (data.type === 'APPROVAL') return data.list.length > 1;
    return false;
  }, [data]);
  const userData = !END_ACTIONS.includes(data.stateType) ? data.list[0] : data;

  const renderSLA = () => {
    const sla = data?.sla;
    if (sla) {
      if (sla.breachedFlag) { // 超时的任务
        const timeout = (sla.businessElapsedDuration - sla.durationCount) * 60;
        return (
          <span className={styles.timeout}>
            <Icon type="attention" />
            {intl.formatMessage({ id: 'lcr.renderer.desc.sla.timeout.tip', defaultMessage: '处理超时{dr}' }, { dr: sec2string(timeout, intl) })}
          </span>
        );
      }
      if (['IN_PROGRESS', 'PAUSED'].includes(sla.state)) { // 处理中 还未超时
        return (
          <span className={styles.remain}>
            <Icon type="time" />
            {intl.formatMessage({ id: 'lcr.renderer.desc.sla.remaining.time', defaultMessage: '预计 {time} 处理完成' }, { time: getEstimatedTime(sla?.breachTime, intl) })}
          </span>
        );
      }
      return ( // 未超时
        <span className={styles.onTime}>
          <Icon type="check-one" />
          {intl.formatMessage({ id: 'lcr.renderer.desc.treatment.up.to.standard', defaultMessage: '处理达标' })}
        </span>
      );
    }
  };

  return (
    <>
      <header className={styles.header}>
        <div>
          <span>{`${data?.name}`}</span>
          {displayName
            && <span>
            &nbsp;(
              <Name name={userData?.userName || userData?.username || '-'} id={userData.userId} />
              )
            </span>}
          {displayAction && <span className={styles.actionText}>
            {getAction()}
          </span>}
          <span>&nbsp;{ACTION_MAP[data.stateType]?.action || data?.stateName}</span>
          {displayExpand
            ? (
              <Tooltip
                title={!collapse
                  ? intl.formatMessage({ id: 'zknow.common.button.expand', defaultMessage: '展开' })
                  : intl.formatMessage({ id: 'zknow.common.button.up', defaultMessage: '收起' })}
              >
                <Icon
                  type="UpOne"
                  className={classnames(styles.collapse, { [styles.rotate]: !collapse })}
                  theme="filled"
                  onClick={() => setCollapse(!collapse)}
                />
              </Tooltip>
            ) : null}
        </div>
        <div className={styles.sla}>
          {renderSLA()}
        </div>
      </header>
      {displayExpand && <main className={classnames(styles.main, { [styles.collapsed]: !collapse })}>
        {content}
      </main>}
    </>
  );
};
