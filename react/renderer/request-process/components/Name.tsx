import React from 'react';
import { Popover, Tooltip } from 'choerodon-ui';
import { YqAvatar } from '@zknow/components';
import classnames from 'classnames';
import AvatarTooltip from '@/components/avatar-tooltip';

import styles from '../styles/Name.module.less';

type NameProps = {
  name: string,
  id: string,
  email?: string,
  department?: string,
  imageUrl?: string,
};

type NameListProps = {
  data: Array<NameProps>,
  className?: string,
  intl?: any,
};

export const Name = (props: NameProps) => {
  const { id, name, email, department, imageUrl } = props;
  return (
    <AvatarTooltip
      id={id}
      trigger="click"
    >
      <span className={styles.name}>
        {name}
      </span>
    </AvatarTooltip>
  );
};

const PopContent = ({ data = [] }: NameListProps) => {
  if (!data.length) return null;
  
  return (
    <div className={styles.popItem}>
      {data.map(item => {
        return (
          <>
            <div className={styles.popItemName}>
              <YqAvatar src={item.imageUrl} size={24}>{item.name}</YqAvatar>
              <span className={styles.popItemNameText}><Name name={item.name} id={item.id} /></span>
            </div>
            <Tooltip title={item.email}>
              <div className={styles.email}>{item.email}</div>
            </Tooltip>
            <div className={styles.department}>{item.department}</div>
          </>
        );
      })}
    </div>
  );
};

export const NameList = ({ data = [], intl }: NameListProps) => {
  if (!data.length) return null;
  return (
    <div>
      {'('}
      {data.map((item, index) => {
        return index < 5 
          ? <>
            <Name name={item.name || '-'} id={item.id} />
            {index === data.length - 1 && data.length <= 5 ? '' : '，'}
          </>
          : null;
      })}
      {data.length > 5 && <Popover
        trigger="click"
        placement="bottomLeft"
        content={<PopContent data={data} />}
        overlayClassName={styles.popList}
      >
        <span className={classnames(styles.more, styles.name)}>
          {intl.formatMessage({ id: 'zknow.common.button.more', defaultMessage: '更多' })}
        </span>
      </Popover>}
      {')'}
    </div>
  );
};
