import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Tooltip, Modal, message } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { getEnv } from '@zknow/utils';
import Store from './stores';

const key = Modal.key();

const testUrl = 'https://adoffice-te.yfdyf.com/repair-front/qywx/#/BusinessWeChat';
const prodUrl = 'https://adoffice.yfdyf.com/repair-front/qywx/#/BusinessWeChat';
const url = getEnv('COOKIE_PREFIX') === 'prodhwsh' ? prodUrl : testUrl;

// 益丰租户专用
const YFMainView = () => {
  const {
    intl,
    personId,
    loginName,
  } = useContext(Store);

  const [chatUserCode, setChatUserCode] = useState('');

  const handleGoToChat = async () => {
    if (!chatUserCode) {
      const userInfo = await axios.get(`/iam/choerodon/v1/users/${personId}/info`);
      if (userInfo && userInfo.loginName) {
        setChatUserCode(userInfo.loginName);
        Modal.open({
          key,
          children: <iframe src={`${url}?chatUserCode=${userInfo.loginName}&operatorUserCode=${loginName}`} title="wechat open" style={{ width: '100%', height: '100%', border: 0 }} />,
          closable: true,
          footer: null,
          bodyStyle: { height: '500px' },
        });
      } else {
        message.warn(intl.formatMessage({ id: 'lcr.renderer.desc.open.wxwork.chat.failed', defaultMessage: '打开企业微信聊天失败' }));
      }
    } else {
      Modal.open({
        key,
        children: <iframe src={`${url}?chatUserCode=${chatUserCode}&operatorUserCode=${loginName}`} title="wechat open" style={{ width: '100%', height: '100%', border: 0 }} />,
        closable: true,
        footer: null,
        bodyStyle: { height: '500px' },
      });
    }
  };

  return navigator.userAgent.toLowerCase().includes('wxwork') && personId && (
    <Tooltip placement="top" title={intl.formatMessage({ id: 'lcr.renderer.desc.open.wxwork.chat', defaultMessage: '打开企业微信聊天' })}>
      <Icon type="icon-mobile-qywx-logo" style={{ marginLeft: 8, marginRight: 8, cursor: 'pointer' }} onClick={handleGoToChat} />
    </Tooltip>
  );
};

export default observer(YFMainView);
