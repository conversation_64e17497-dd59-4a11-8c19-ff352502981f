import React, { createContext } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props) => {
    const {
      intl,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { person: { loginName } } },
      HeaderStore: { getTenantConfig: { wechatAgentId, wechatAppId } },
      children,
    } = props;

    const value = {
      ...props,
      intl,
      tenantId,
      loginName,
      wechatAgentId,
      wechatAppId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
