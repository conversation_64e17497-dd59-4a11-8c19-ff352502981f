import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon } from '@zknow/components';
import { Tooltip } from 'choerodon-ui/pro';
import axios from 'axios';
import Store from './stores';

import './index.less';
import wxWorkSign from './wx-work-sign';

if (!window.__openAccountProvider) {
  window.__openAccountProvider = {
    infoMap: {},
    fetcher: async (ids) => {
      
    },
    trigger: (id, callback) => {

    },
  };
}
export default observer(() => {
  const context = useContext(Store);
  const {
    tenantId,
    personId,
    wechatAgentId,
    wechatAppId,
    intl,
  } = context;
  const [openAccount, setOpenAccount] = useState(window.__openAccountProvider.infoMap[personId]);
  // const []
  useEffect(() => {
    /**
     * 不是企业微信的 跳过查询和加载jssdk
     */
    if (!navigator.userAgent.toLowerCase().includes('wxwork')) {
      return;
    }
    if (!window.__wxWorkJSSDKLoaded) {
      wxWorkSign(wechatAppId, wechatAgentId, tenantId);
    }
    fetchOpenAccount({ ids: [personId] }).then(() => {
      setInterval(() => {
        if (typeof openAccount !== 'object') {
          setOpenAccount(window.__openAccountProvider.infoMap[personId]);
        }
      }, 1000);
    });
  }, []);

  function originalOpenChat() {
    return new Promise((resolve, reject) => {
      // @ts-ignore
      wx.openEnterpriseChat({
        groupName: '',
        userIds: openAccount?.openId,
        fail(res) {
          // eslint-disable-next-line no-console
          console.error('Failed to open chat', res);
          // if (appStore.loginInfo.openId === openAccount?.openId) {
          //   // eslint-disable-next-line no-chinese/no-chinese
          //   // Taro.atMessage({ message: '暂不支持跳转自自身会话', type: 'warning' });
          // } else {
          //   // eslint-disable-next-line no-chinese/no-chinese
          //   // Taro.atMessage({ message: '此用户不支持跳转', type: 'warning' });
          // }
          reject(new Error('Failed to open chat'));
        },
        success() {
          resolve(true);
        },
      });
    });
  }

  async function retryOpenChat(fn, maxRetries = 8, delay = 500, retries = 0) {
    try {
      const result = await fn();
      return result;
    } catch (error) {
      if (retries < maxRetries) {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(retryOpenChat(fn, maxRetries, delay, retries + 1));
          }, delay);
        });
      } else {
        // eslint-disable-next-line no-console
        console.error('达到最大重试次数');
        throw new Error('Max retries reached');
      }
    }
  }
  async function openChat() {
    await retryOpenChat(originalOpenChat);
  }
  async function fetchOpenAccount({ ids }) {
    const fetcher = (_ids) => {
      return axios.post(`ecos/v1/${tenantId}/userOpenAccount/contact/information${wechatAppId ? `?appId=${wechatAppId}` : ''}`, _ids);
    };

    const noCacheIds = [];
    ids.forEach((id) => {
      if (!window.__openAccountProvider.infoMap[id]) {
        noCacheIds.push(id);
      }
    });
    if (noCacheIds.length > 0) {
      const res = await fetcher(noCacheIds);
      res.forEach((item) => {
        window.__openAccountProvider.infoMap[item.userId] = item;
      });
    }
  }

  async function handleGoToChat() {
    openChat();
  }

  return openAccount?.openId ? (
    <Tooltip placement="top" title={intl.formatMessage({ id: 'lcr.renderer.desc.open.wxwork.chat', defaultMessage: '打开企业微信聊天' })}>
      <Icon type="icon-mobile-qywx-logo" style={{ marginLeft: 8, marginRight: 8, cursor: 'pointer' }} onClick={handleGoToChat} />
    </Tooltip>
  ) : null;
});
