import React from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { StoreProvider } from './stores';
import MainView from './MainView';
import YFMainView from './YFMainView';

const YFTenantIds = ['367991266203660288', '423852212098428928'];

export default inject('AppState')(observer((props) => {
  const { AppState: { currentMenuType: { organizationId: tenantId } } } = props;
  return (
    <StoreProvider {...props}>
      {YFTenantIds.includes(tenantId) ? <YFMainView /> : <MainView />}
    </StoreProvider>
  );
}));

/* externalize: WechatWorkChatIcon */
