import React from 'react';
import { observer } from 'mobx-react-lite';
import { TextArea, TextField, Tooltip } from 'choerodon-ui/pro';
import { formatterCollections } from '@zknow/utils';
import { Icon } from '@zknow/components';
import axios from 'axios';
import { injectIntl } from 'react-intl';
import { inject } from 'mobx-react';
import Styles from './Index.module.less';

const PhrasesTextArea = observer((props) => {
  const { 
    intl,
    phrasesComponentId, 
    AppState: {
      currentMenuType: { organizationId: tenantId },
    },
    changeFn = () => {},
    phrasesValue,
    formComponentType = 'TextField',
    displayOriginallyComponent,
    updateListCount = 0,
  } = props;

  const [phrasesList, setPhrasesList] = React.useState([]);
  const [open, setOpen] = React.useState(true);
  const handleChange = async (value) => {
    const res = await axios.post(`/lc/v1/${tenantId}/common_phrases/use`, {
      [phrasesComponentId]: value,
    });
    if (res && !res?.failed) {
      await getPhrasesList();
    }
  };
  let FormComponent = <TextField {...props} onChange={handleChange} />;
  if (formComponentType === 'TextArea') {
    FormComponent = <TextArea {...props} onChange={handleChange} />;
  } else if (formComponentType === 'RichText') {
    //
    FormComponent = null;
  }

  const getPhrasesList = async () => {
    const res = await axios.get(`/lc/v1/${tenantId}/common_phrases/list?components=${`${phrasesComponentId}`}&size=3`);
    setPhrasesList(res);
  };
  React.useEffect(() => {
    getPhrasesList();
  }, [updateListCount]);

  const deletePhrasesList = async (i) => {
    const res = await axios.delete(`/lc/v1/${tenantId}/common_phrases/delete`, { data: { [i?.component]: i?.phrase } });
    if (res && !res?.failed) {
      await getPhrasesList();
    }
  };

  const handlefillinPhrases = (text) => {
    handleChange(text);
    changeFn(text);
  };

  function extractTextFromHTMLDOM(htmlString) {
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    return doc.body.textContent || doc.body.innerText;
  }

  return <div>
    {displayOriginallyComponent && <div>
      {FormComponent}
    </div>}
    <div className={Styles.phrase}>
      <div className={Styles.left} onClick={() => { setOpen(!open); }}>
        <Icon type={open ? 'up-one' : 'down-one'} size={16} theme="filled" />
        <div>{intl.formatMessage({ id: 'lcr.renderer.renderer.commonPhrases', defaultMessage: '常用回复' })}</div>
      </div>
      <div
        className={Styles.right}
        style={
          { display: open ? 'flex' : 'none' }
        }
      >
        {phrasesList && phrasesList?.length > 0 && phrasesList?.map((i) => {
          const phraseText = extractTextFromHTMLDOM(i?.phrase);
          return <div className={Styles.item}>
            <Tooltip text={phraseText}>
              <div className={Styles.text} onClick={() => handlefillinPhrases(i?.phrase)}>{phraseText}</div>
            </Tooltip>
            <Icon type="close" size={10} onClick={() => deletePhrasesList(i)} />
          </div>;
        })}
      </div>
    </div>
  </div>;
});

export default injectIntl(inject('AppState')(formatterCollections({ code: ['lcr.renderer'] })(PhrasesTextArea)));
