import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { Table, Modal, DataSet } from 'choerodon-ui/pro';
import { toJS } from 'mobx';
import { TableHoverAction, ClickText } from '@zknow/components';
import _ from 'lodash';
import handleCommonRequest from '@/service';
import Header from '../ticket-relation-header';
import Store from './stores';

import './index.less';

function MainView() {
  const {
    intl,
    prefixCls,
    tenantId,
    listDataSet,
    fields,
    formDataSet,
    config,
    ticketId,
    businessObjectId,
    onJumpNewPage,
    viewId: approvalViewId,
    relatedTicketLovId,
  } = useContext(Store);
  const [expand, setExpand] = useState(true);
  const { widgetConfig: {
    ticketRelationListType,
  } } = config;

  const isRelation = ticketRelationListType !== 'APPROVAL';

  useEffect(() => {
    if (formDataSet?.status === 'ready' && ticketId) {
      listDataSet.query();
    }
  }, [formDataSet?.status]);

  function handleOpenDetail(_record) {
    const viewId = isRelation ? _record?.get('_view_id') : approvalViewId;
    const id = _record?.get('id');
    if (viewId && id) {
      const _ds = new DataSet();
      _ds.create({ ..._.pick(_record.toData(), ['id', 'number', 'short_description']) });
      onJumpNewPage({ record: _ds.current, viewId });
    }
  }

  function renderName({ record, name, text }) {
    if (!(isRelation ? record?.get('_view_id') : approvalViewId)) {
      return text;
    }
    return (
      <ClickText
        record={record}
        onClick={() => handleOpenDetail(record)}
        valueField={name}
      />
    );
  }

  const handleReplaceRichText = (text) => {
    let res = '';
    res = text.replace(new RegExp('<[^>]*>', 'g'), ' ');
    res = res.replace(new RegExp('\\s+', 'g'), ' ');
    res = res.replace(new RegExp('&nbsp;', 'g'), '');
    return res;
  };

  function renderText({ text }) {
    let res = '';
    if (typeof text === 'string') {
      res = handleReplaceRichText(text);
      // 富文本加上变粗之类的操作格式会变，需要特殊取一下文字去显示
      try {
        const richText = JSON.parse(res);
        const richTextArr = richText.map(i => {
          let itemText = '';
          if (typeof i.insert === 'string') {
            itemText = handleReplaceRichText(i.insert);
            return itemText;
          } else {
            return '';
          }
        });
        res = richTextArr.join('');
      } catch (error) {
        //
      }
    } else {
      const deltaValue = text?.ops || text;
      if (deltaValue && Array.isArray(toJS(deltaValue))) {
        deltaValue.forEach(v => {
          res += v?.insert?.image || v?.insert;
        });
      }
      res = res.replace('\n', '');
    }
    return res;
  }

  function handleCancelLink(_data) {
    Modal.confirm({
      title: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.action.tip' }),
    }).then(async btn => {
      if (btn === 'ok') {
        await handleCommonRequest('handleDeleteTicketLink', {
          params: {
            tenantId,
            linkId: _data._link_id,
          },
          dataSet: listDataSet,
          successTip: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete.success' }),
        });
      }
    }).catch(e => {
      throw Error(e);
    });
  }

  function renderAction({ record }) {
    const actions = [
      {
        icon: 'LinkBreak',
        name: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete' }),
        onClick: () => handleCancelLink(record?.toData()),
      },
    ];
    return (
      <TableHoverAction
        record={record}
        actions={actions}
        intlBtnIndex={2}
      />
    );
  }

  return (
    <div className={prefixCls}>
      <Header
        dataSet={listDataSet}
        ticketId={ticketId}
        businessObjectId={businessObjectId}
        onExpand={setExpand}
        title={isRelation ? intl.formatMessage({ id: 'lcr.renderer.ticketRelation.ticket.relation' }) : intl.formatMessage({ id: 'lcr.renderer.ticketRelation.ticket.approval' })}
        mode="table"
        isRelation={isRelation}
        lovId={relatedTicketLovId}
      />
      {expand && (
        <Table
          dataSet={listDataSet}
          pristine
          filter={false}
          customizable={false}
          queryBar="none"
          selectionMode="none"
          style={{ padding: '0 16px 16px' }}
          pagination={{
            showTotal: true,
            showPager: true,
            showQuickJumper: false,
            showSizeChanger: false,
          }}
        >
          {isRelation && <Table.Column name="linkName" lock="left" />}
          {
            fields.map((v, index) => {
              const _config = {
                name: v.code,
                tooltip: 'overflow',
              };
              if (index === 0) {
                _config.renderer = renderName;
              } else {
                _config.renderer = renderText;
              }
              return <Table.Column {..._config} lock={!isRelation && index === 0 ? 'left' : false} />;
            })
          }
          {isRelation && <Table.Column renderer={renderAction} tooltip="none" />}
        </Table>
      )}
    </div>
  );
}

export default observer(MainView);
