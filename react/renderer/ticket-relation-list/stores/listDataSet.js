export default ({
  intl,
  tenantId,
  ticketId,
  fields,
  ticketRelationListType,
  ticketRelationListPageSize,
}) => {
  const url = `lc/v1/${tenantId}/svs_ticket_links/${ticketId}/query?type=table&relationType=${ticketRelationListType}`;
  function getQueryData() {
    return fields.map(v => v.code);
  }

  const tableFields = [
    ...fields?.map(v => ({ name: v?.code, label: v[intl.locale] || v?.name, type: 'string' })),
    {
      name: 'linkName',
      label: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.relation' }),
      type: 'string',
    },
  ];

  function getResponse(_data = []) {
    return _data.map(item => {
      if (item.ticket) {
        return {
          linkName: item.linkName,
          ...item.ticket,
        };
      } else {
        return null;
      }
    }).filter(v => v);
  }

  return {
    autoQuery: !!ticketId,
    paging: true,
    pageSize: ticketRelationListPageSize || 10,
    dataKey: 'content',
    totalKey: 'totalElements',
    transport: {
      read: ({ dataSet }) => {
        const param = dataSet.queryParameter?.param;
        return {
          url: `${url}${param ? `&param=${param}` : ''}`,
          method: 'post',
          data: getQueryData(),
          transformResponse(response) {
            try {
              const res = JSON.parse(response);
              if (res?.failed) {
                return response;
              } else {
                return {
                  content: getResponse(res?.content),
                  number: res?.number,
                  numberOfElements: res?.numberOfElements,
                  size: res?.size,
                  totalElements: res?.totalElements,
                  totalPages: res?.totalPages,
                };
              }
            } catch (e) {
              throw Error(e);
            }
          },
        };
      },
    },
    fields: tableFields,
  };
};
