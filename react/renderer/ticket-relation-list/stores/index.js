import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import ListDataSet from './listDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config,
      viewDataSet,
    } = props;

    const prefixCls = 'lc-components-ticket-relation-list';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { businessObjectId } = formConfig;
    const { name, widgetConfig: {
      ticketRelationListFields,
      ticketRelationListType,
      ticketRelationListPageSize,
      relatedTicketLovId,
    } } = config;
    const getFields = () => {
      try {
        return JSON.parse(ticketRelationListFields) || [];
      } catch (e) {
        return [];
      }
    };
    const fields = getFields();

    const listDataSet = useMemo(() => new DataSet(ListDataSet({
      intl, ticketId, tenantId, fields, ticketRelationListType, ticketRelationListPageSize,
    })), [ticketId, tenantId, fields]);

    const value = {
      ...props,
      prefixCls,
      intl,
      tenantId,
      ticketId,
      listDataSet,
      name,
      fields,
      businessObjectId,
      ticketRelationListType,
      relatedTicketLovId,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
