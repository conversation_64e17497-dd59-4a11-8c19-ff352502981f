const FieldDataSet = ({ intl }) => {
  return {
    autoQuery: false,
    paging: false,
    fields: [
      {
        name: 'incidentNo',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.incidentNo',
          defaultMessage: '事件NO',
        }),
      },
      {
        name: 'time',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.time',
          defaultMessage: '操作时间',
        }),
      },
      {
        name: 'menuInfo',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.menuInfo',
          defaultMessage: '调用源画面信息',
        }),
      },
      {
        name: 'changeBefore',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.changeBefore',
          defaultMessage: '变更内容（变更前）',
        }),
      },
      {
        name: 'changeAfter',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.changeAfter',
          defaultMessage: '变更内容（变更后）',
        }),
      },
      {
        name: 'submitBy',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.submitBy',
          defaultMessage: '提出人',
        }),
      },
      {
        name: 'currentPerson',
        label: intl.formatMessage({
          id: 'lcr.renderer.frontMonitoringRecords.model.currentPerson',
          defaultMessage: '对应人',
        }),
      },
    ],
  };
};

export { FieldDataSet };
