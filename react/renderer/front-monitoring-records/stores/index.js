import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldDataSet } from './DataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject(
    'AppState',
    'HeaderStore',
    'MenuStore'
  )((props) => {
    const {
      intl,
      children,
      formDataSet,
      model,
      config,
      record,
      dataSet,
      MenuStore: { activeMenu },
      callback = () => {},
      buttonConfig,
      orgDataSet,
      AppState: {
        currentMenuType: { tenantId },
      },
      viewDataSet,
    } = props;
    const fieldDataSet = useMemo(() => new DataSet(FieldDataSet({ intl })), []);

    const value = {
      ...props,
      intl,
      formDataSet,
      fieldDataSet,
      model,
      config,
      record,
      activeMenu,
      dataSet,
      callback,
      buttonConfig,
      tenantId,
      viewDataSet,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
