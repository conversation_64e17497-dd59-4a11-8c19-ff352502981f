import React, { useContext, useRef, useEffect, useState, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import PageLoader from '@/components/page-loader';
import Store from './stores';
import Style from './Index.module.less';

function MainView() {
  const {
    modal,
    config,
    activeMenu,
    dataSet,
    callback,
    buttonConfig,
    orgDataSet,
    customDefaultData,
    tenantId,
    viewDataSet,
    orgformDataSet,
  } = useContext(Store);
  const createRef = useRef();
  const [modalDefaultData, setModalDefaultData] = useState(null);
  const record = dataSet?.current;
  function toObject(observableArray) {
    try {
      const result = {};
      observableArray?.forEach((item, index) => {
        result[item?.key] = item?.value;
      });
      return result;
    } catch (error) {
      return observableArray || {};
    }
  }

  useEffect(() => {
    queryViewDefaultData();
  }, [record, customDefaultData]);

  function handleSetDefaultData(data) {
    if (modalDefaultData) {
      setModalDefaultData({ ...modalDefaultData, ...data });
    } else {
      setModalDefaultData(data);
    }
  }

  async function queryViewDefaultData() {
    // 操作数据
    let optionalData = {};
    if (customDefaultData) {
      if (activeMenu && activeMenu?.name) {
        customDefaultData.t_menu = customDefaultData.t_operational_menu || `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
      }
      optionalData = customDefaultData;
    } else {
      optionalData = getDefaultValue() || {};
    }
    let viewId;
    if (customDefaultData && config && config.viewId) {
      viewId = config.viewId;
    } else if (config) {
      const configObj = toObject(config);
      viewId = configObj && configObj.viewId;
    }
    const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/calculate`, {});
    if (res && !res?.failed) {
      handleSetDefaultData({ ...optionalData, ...res });
    }
  }

  function handleSaveType() {
    const defaultData = {};
    let beforeVal = '';
    let afterVal = '';
    const data = record?.toData();
    // clone暂存当前record，reset原始的record获取修改之前的值
    const clone = record.clone();
    clone?.set('id', data?.id);
    record.save();
    record.reset();
    const beforeData = record.toData();
    const currentData = clone.toData();
    Object.keys(data).forEach((i) => {
      const label = dataSet?.getField(i)?.get('label');
      const lovCode = dataSet?.getField(i)?.get('lovCode');
      const lovTextField = dataSet?.getField(i)?.get('textField');
      if (currentData[i] !== beforeData[i] && (currentData[i] || beforeData[i]) && label) {
        if (i.includes('multiplechoice')) {
          // 多选组件不支持
        } else if (lovCode) {
          beforeVal += `${label}：${extractTextFromHTML(beforeData[i] ? record?.get(i)?.[lovTextField] || beforeData[`${i}:${lovTextField}`] || beforeData[i] : '')}\n`;
          afterVal += `${label}：${extractTextFromHTML(currentData[i] ? clone?.get(i)?.[lovTextField] || currentData[`${i}:${lovTextField}`] || currentData[i] : '')}\n`;
        } else {
          beforeVal += `${label}：${extractTextFromHTML(beforeData[i] ? dataSet?.getField(i)?.getText(beforeData[i], beforeData[i], record) || beforeData[i] : '')}\n`;
          afterVal += `${label}：${extractTextFromHTML(currentData[i] ? dataSet?.getField(i)?.getText(currentData[i], currentData[i], clone) || currentData[i] : '') }\n`;
        }
      }
      if (beforeData[i] && typeof (beforeData[i]) === 'string' && !currentData[i]) {
        // 还原record
        record.set(i, '');
      } else {
        // 还原record
        record.set(i, clone?.get(i));
      }
    });
    if (activeMenu && activeMenu?.name) {
      defaultData.t_menu = `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
    }
    if (beforeVal) {
      defaultData.t_before_json_data = beforeVal;
    }
    if (afterVal) {
      defaultData.t_after_json_data = afterVal;
    }
    if (config) {
      const configObj = toObject(config);
      // 记录操作行为：保存、更新
      if (configObj && configObj.t_operate) {
        defaultData.t_operate = configObj.t_operate;
      }
      if (configObj && configObj.t_operational_data) {
        let operationalData = '';
        // eslint-disable-next-line camelcase
        const operationalDataArr = configObj.t_operational_data.split(',');
        const currentDataSet = configObj.t_operational_headLine ? orgformDataSet : dataSet;
        const currentFatherData = configObj.t_operational_headLine ? orgformDataSet?.current?.toData() : data;
        operationalDataArr.forEach((i, index) => {
          const label = currentDataSet?.getField(i)?.get('label');
          const lovCode = currentDataSet?.getField(i)?.get('lovCode');
          const lovTextField = currentDataSet?.getField(i)?.get('textField');
          if (lovCode) {
            operationalData += `${currentFatherData[i] ? clone?.get(i)?.[lovTextField] || currentFatherData[`${i}:${lovTextField}`] || currentFatherData[i] : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else {
            operationalData += `${currentFatherData[i] ? (currentDataSet?.getField(i)?.getText(currentFatherData[i], currentFatherData[i], clone) || currentFatherData[i]) : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          }
          defaultData.t_operational_data = operationalData;
        });
      }
      if (configObj && configObj.t_operational_menu) {
        defaultData.t_menu = configObj.t_operational_menu;
      }
    }
    // handleSetDefaultData(defaultData);
    return defaultData;
  }
  function handleDeleteType() {
    const configButtonUpdateField = toObject(config)?.customUpdateField;
    const defaultData = {};
    let beforeVal = '';
    const afterVal = '';
    const data = record?.toData();
    const firstField = configButtonUpdateField || Array.from(orgDataSet?.fields)[0][0];
    const list = orgDataSet?.selected;
    list.forEach((i) => {
      beforeVal += `${i[firstField] || i?.get(firstField)}\n`;
    });
    defaultData.t_before_json_data = beforeVal;
    if (activeMenu && activeMenu?.name) {
      defaultData.t_menu = `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
    }
    if (config) {
      const configObj = toObject(config);
      // 记录操作行为：保存、更新
      if (configObj && configObj.t_operate) {
        defaultData.t_operate = configObj.t_operate;
      }
      if (configObj && configObj.t_operational_data) {
        let operationalData = '';
        // eslint-disable-next-line camelcase
        const operationalDataArr = configObj.t_operational_data.split(',');
        const currentDataSet = configObj.t_operational_headLine ? orgformDataSet : orgDataSet;
        const currentData = configObj.t_operational_headLine ? orgformDataSet?.current?.toData() : data;
        operationalDataArr.forEach((i, index) => {
          const label = currentDataSet?.getField(i)?.get('label');
          const lovCode = currentDataSet?.getField(i)?.get('lovCode');
          const lovTextField = currentDataSet?.getField(i)?.get('textField');
          if (lovCode) {
            operationalData += `${currentData[i] ? currentData?.get(i)?.[lovTextField] || currentData[`${i}:${lovTextField}`] || currentData[i] : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else {
            operationalData += `${currentData[i] ? currentDataSet?.getField(i)?.getText(currentData[i], currentData[i], currentData) || currentData[i] : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          }
          defaultData.t_operational_data = operationalData;
        });
      }
      if (configObj && configObj.t_operational_menu) {
        defaultData.t_menu = configObj.t_operational_menu;
      }
    }
    // handleSetDefaultData(defaultData);
    return defaultData;
  }
  function handleRemoveType() {
    const configButtonUpdateField = toObject(config)?.customUpdateField;
    const defaultData = {};
    let beforeVal = '';
    const afterVal = '';
    const data = record?.toData();
    const firstField = configButtonUpdateField || Array.from(orgDataSet?.fields)[0][0];
    const list = orgDataSet?.selected;
    beforeVal = `${data[firstField] || data?.get(firstField)}\n`;
    defaultData.t_before_json_data = beforeVal;
    if (activeMenu && activeMenu?.name) {
      defaultData.t_menu = `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
    }
    if (config) {
      const configObj = toObject(config);
      // 记录操作行为：保存、更新
      if (configObj && configObj.t_operate) {
        defaultData.t_operate = configObj.t_operate;
      }
      if (configObj && configObj.t_operational_data) {
        let operationalData = '';
        // eslint-disable-next-line camelcase
        const operationalDataArr = configObj.t_operational_data.split(',');
        const currentDataSet = configObj.t_operational_headLine ? orgformDataSet : orgDataSet;
        const currentData = configObj.t_operational_headLine ? orgformDataSet?.current?.toData() : data;
        operationalDataArr.forEach((i, index) => {
          const label = currentDataSet?.getField(i)?.get('label');
          const lovCode = currentDataSet?.getField(i)?.get('lovCode');
          const lovTextField = currentDataSet?.getField(i)?.get('textField');
          if (lovCode) {
            operationalData += `${currentData[i] ? currentData?.get(i)?.[lovTextField] || currentData[`${i}:${lovTextField}`] || currentData[i] : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else {
            operationalData += `${currentData[i] ? currentData[i] : ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          }
          defaultData.t_operational_data = operationalData;
        });
      }
      if (configObj && configObj.t_operational_menu) {
        defaultData.t_menu = configObj.t_operational_menu;
      }
    }

    // handleSetDefaultData(defaultData);
    return defaultData;
  }
  function handleAddType() {
    const configButtonUpdateField = toObject(config)?.customUpdateField;
    const defaultData = {};
    const beforeVal = '';
    let afterVal = '';
    const data = record?.toData();
    const firstField = configButtonUpdateField || Array.from(orgDataSet?.fields)[0][0];
    const list = orgDataSet?.selected;
    afterVal = `${data[firstField] || data?.get(firstField)}\n`;
    defaultData.t_before_json_data = afterVal;
    if (activeMenu && activeMenu?.name) {
      defaultData.t_menu = `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
    }
    if (config) {
      const configObj = toObject(config);
      // 记录操作行为：保存、更新
      if (configObj && configObj.t_operate) {
        defaultData.t_operate = configObj.t_operate;
      }
      if (configObj && configObj.t_operational_data) {
        let operationalData = '';
        // eslint-disable-next-line camelcase
        const operationalDataArr = configObj.t_operational_data.split(',');
        operationalDataArr.forEach((i, index) => {
          const label = dataSet?.getField(i)?.get('label');
          const lovCode = dataSet?.getField(i)?.get('lovCode');
          const lovTextField = dataSet?.getField(i)?.get('textField');
          if (lovCode) {
            operationalData += `${data?.get(i)?.[lovTextField] || data[`${i}:${lovTextField}`] || data[i]}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else {
            operationalData += `${data[i] || ''}${index === operationalDataArr.length - 1 ? '' : ','}`;
          }
          defaultData.t_operational_data = operationalData;
        });
      }
      if (configObj && configObj.t_operational_menu) {
        defaultData.t_menu = configObj.t_operational_menu;
      }
    }
    // handleSetDefaultData(defaultData);
    return defaultData;
  }
  function handleUpdateType(cField) {
    const defaultData = {};
    let beforeVal = '';
    let afterVal = '';
    const data = record?.toData();
    const field = cField || buttonConfig?.updateFieldCode;
    const label = dataSet?.getField(field)?.get('label');
    beforeVal += `${label}：${!!data?.[field]}\n`;
    afterVal += `${label}：${!data?.[field]}\n`;
    defaultData.t_before_json_data = beforeVal;
    defaultData.t_after_json_data = afterVal;
    if (activeMenu && activeMenu?.name) {
      defaultData.t_menu = `${activeMenu?.name}${viewDataSet?.current?.get('viewType') === 'UPDATE' && viewDataSet?.current?.get('name') ? `/${viewDataSet?.current?.get('name')}` : ''}`;
    }
    if (config) {
      const configObj = toObject(config);
      // 记录操作行为：保存、更新
      if (configObj && configObj.t_operate) {
        defaultData.t_operate = configObj.t_operate;
      }
      if (configObj && configObj.t_operational_data) {
        let operationalData = '';
        // eslint-disable-next-line camelcase
        const operationalDataArr = configObj.t_operational_data?.split(',');
        operationalDataArr.forEach((i, index) => {
          const lovCode = dataSet?.getField(i)?.get('lovCode');
          const lovTextField = dataSet?.getField(i)?.get('textField');
          const options = dataSet?.getField(i)?.get('options');
          const lookup = dataSet?.getField(i)?.get('lookup');
          if (lovCode) {
            operationalData += `${data?.get(i)?.[lovTextField] || data[`${i}:${lovTextField}`] || data[i]}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else if (lookup || options) {
            operationalData += `${dataSet?.getField(i)?.getText(data[i], data[i], data) || data[i]}${index === operationalDataArr.length - 1 ? '' : ','}`;
          } else {
            operationalData += `${data[i]}${index === operationalDataArr.length - 1 ? '' : ','}`;
          }
          defaultData.t_operational_data = operationalData;
        });
      }
      if (configObj && configObj.t_operational_menu) {
        defaultData.t_menu = configObj.t_operational_menu;
      }
    }
    // handleSetDefaultData(defaultData);
    return defaultData;
  }

  function extractTextFromHTML(htmlString) {
    if (!htmlString) return '';
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');
    return doc.body.textContent || '';
  }

  // 获取操作前和操作后的数据
  function getDefaultValue() {
    try {
      const data = record?.toData();
      if (data && ['SAVE', 'CREATE', 'SUBMIT', 'lineSave'].includes(buttonConfig?.type)) {
        return handleSaveType();
      } else if (
        ['DELETE', 'REMOVE'].includes(buttonConfig?.type)
        && orgDataSet?.fields
        && orgDataSet?.selected
        && orgDataSet?.selected?.length
      ) {
        return handleDeleteType();
      } else if (['UPDATE'].includes(buttonConfig?.type)) {
        return handleUpdateType();
      } else if (['UPDATE'].includes(buttonConfig?.type)) {
        return handleUpdateType();
      } else if (['ADD'].includes(buttonConfig?.type)) {
        return handleAddType();
      } else if (['EXPRESSION'].includes(buttonConfig?.type)) {
        const configObj = toObject(config);
        const configButtonType = configObj.buttonType;
        const configButtonUpdateField = configObj.customUpdateField;

        if (configButtonType === 'SAVE') {
          return handleSaveType();
        } else if (configButtonType === 'DELETE') {
          return handleDeleteType();
        } else if (configButtonType === 'REMOVE') {
          return handleRemoveType();
        } else if (configButtonType === 'UPDATE') {
          return handleUpdateType(configButtonUpdateField);
        }
      }
    } catch (error) {
      //
    }
  }

  modal.handleOk(async () => {
    const validate = await createRef?.current?.formDataSet?.validate();
    const validateRecord = await record?.validate();
    if (validate && (validateRecord || !record)) {
      await createRef?.current?.formDataSet.submit();
      callback();
    } else {
      return false;
    }
  });

  if (customDefaultData && config && config.viewId && modalDefaultData) {
    return (
      <div className={Style.modal}>
        <PageLoader
          viewId={config.viewId}
          mode="CREATE"
          pageRef={createRef}
          defaultData={modalDefaultData}
        />
      </div>
    );
  }

  if (config && modalDefaultData) {
    const configObj = toObject(config);
    if (configObj && configObj.viewId) {
      return (
        <div className={Style.modal}>
          <PageLoader
            viewId={configObj.viewId}
            mode="CREATE"
            pageRef={createRef}
            defaultData={modalDefaultData}
          />
        </div>
      );
    }
  }
}

export default observer(MainView);
