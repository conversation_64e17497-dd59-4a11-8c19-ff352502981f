.wrapper {
  border: solid 1px #FF9500;
  border-radius: 2px;
  width: auto;
  height: 42px;
  text-align: center;
}

.nodeWrapper {
  display: flex;
}

.nodeIcon {
  display: inline-block;
  width: 42px;
  height: 40px;
  padding: 12px;
  border-radius: 2px 0 0 2px;
  border-right: 1px solid #FF9500;
}

.nodeText {
  padding: 10px 14px;
  width: 100%;
  overflow: hidden;
  word-break: keep-all;
  text-overflow: ellipsis;
}

.popoverWrapper {
  padding: 4px 0;
}

.popoverHeader {
  margin-bottom: 12px;
}

.popoverTitle {
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
}

.popoverTable {
  width: 766px;

  :global(.c7n-pro-table-bordered) {
    border-radius: 3px;
  }

  :global(.c7n-pro-table-thead) {
    background: #F7F8FA;
  }
}
