import React, { useState } from 'react';
import { Handle } from 'react-flow-renderer';
import { observer } from 'mobx-react-lite';
import { Icon, StatusTag } from '@zknow/components';
import { color as colorUtils } from '@zknow/utils';
import classnames from 'classnames';

import styles from './index.module.less';

const COLOR_MAP = {
  HISTORY: '#00C48C',
  SETTING: '#354767',
  CURRENT: '#FFB100',
  PREDICTION: '#2EA2FF',
};

const CustomNodeComponent = (e) => {
  const { data, current, onNodeClick } = e;
  const color = COLOR_MAP[data.stage] || '#354767';
  const [isHover, setHover] = useState(false);

  function getTop({ allCount, index }) {
    const step = 100 / (allCount + 1);
    if (allCount === 1) {
      return 50;
    }
    return step * (index + 1);
  }

  // 获取类型
  function getType({ allCount, sourceCount, targetCount, direction, index }) {
    if (allCount === 1) {
      return sourceCount === 1 ? 'source' : 'target';
    }
    if (sourceCount === 0) return 'target';
    if (targetCount === 0) return 'source';

    if ((index + 1 <= targetCount)) {
      return 'target';
    }
    return 'source';
  }

  function renderHandle({ nodeInfo }) {
    // 出发点
    const sourceList = [];
    const directionList = ['right', 'bottom', 'left', 'top'];
    const directionEdgesMap = {
      right: 'top',
      left: 'top',
      bottom: 'left',
      top: 'left',
    };
    directionList.map((j) => {
      // 如果边上有两个点以上
      const allEdgePointCount = nodeInfo?.[`${j}SourceHandle`] + nodeInfo?.[`${j}TargetHandle`];
      if (allEdgePointCount > 1) {
        const arr = [];
        for (let i = 0; i < allEdgePointCount; i++) {
          arr.push(i);
        }
        return arr.map((i, index) => {
          const type = getType({
            allCount: allEdgePointCount,
            sourceCount: nodeInfo?.[`${j}SourceHandle`],
            targetCount: nodeInfo?.[`${j}TargetHandle`],
            direction: j,
            index,
          });
          sourceList.push(
            <Handle
              type={type}
              position={j}
              id={`${j}-${type}-${data.id}-${index}`}
              style={{
                [`${directionEdgesMap[j]}`]: `${
                  getTop({ 
                    allCount: allEdgePointCount,
                    sourceCount: nodeInfo?.[`${j}SourceHandle`],
                    targetCount: nodeInfo?.[`${j}TargetHandle`],
                    direction: j,
                    type,
                    index,
                  })
                }%`,
                borderRadius: 0,
              }}
            />
          );
          return i;
        });
      } else if (allEdgePointCount === 1) {
        const type = getType({
          allCount: allEdgePointCount,
          sourceCount: nodeInfo?.[`${j}SourceHandle`],
          targetCount: nodeInfo?.[`${j}TargetHandle`],
          direction: j,
          index: 0,
        });
        sourceList.push(
          <Handle
            type={type}
            position={j}
            id={`${j}-${type}-${data.id}-${0}`}
            style={{
              [`${directionEdgesMap[j]}`]: `${
                getTop({ 
                  allCount: allEdgePointCount,
                  sourceCount: nodeInfo?.[`${j}SourceHandle`],
                  targetCount: nodeInfo?.[`${j}TargetHandle`],
                  direction: j,
                  type,
                  index: 0,
                })
              }%`,
              borderRadius: 0,
            }}
          />
        );
      }
      return j;
    });
    return sourceList;
  }

  if (data.shape) {
    return (
      <div>
        {renderHandle({ nodeInfo: data, type: 'target' })}
        <div
          className={classnames({
            [data.shape]: !!data.shape,
          })}
        >
          <span>{data.name}</span>
        </div>
      </div>
    );
  }

  function handleNodeClick(event) {
    event.stopPropagation();
    if (onNodeClick) {
      if (!data.activities || data.activities.length === 0) {
        onNodeClick(null);
      } else {
        onNodeClick(data);
      }
    }
  }

  function renderNode() {
    return (
      <div
        className={styles.nodeWrapper}
        onClick={handleNodeClick}
      >
        <span
          className={styles.nodeIcon}
          style={{
            borderColor: color,
            backgroundColor: colorUtils?.colorOpacityRGB(color, 0.05),
          }}
        >
          <Icon type={data.icon} fill={color} />
        </span>
        <span className={styles.nodeText}>
          {data.name}
        </span>
      </div>
    );
  }

  return (
    <div
      className={styles.wrapper}
      style={{
        borderColor: color,
        boxShadow: current?.id === data.id || isHover ? `0px 0px 12px 0px ${colorUtils?.colorOpacityRGB(color, 0.5)}` : '',
      }}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {renderHandle({ nodeInfo: data, type: 'target' })}
      {renderNode()}
    </div>
  );
};

export default observer(CustomNodeComponent);
