import React, { FC } from 'react';
import {
  EdgeProps,
  EdgeText,
  // getBezierPath,
  // getBezierEdgeCenter,
} from 'react-flow-renderer';

const DEVIATION = 70;

const CustomEdge: FC<EdgeProps> = ({
   id,
   sourceX,
   sourceY,
   targetX,
   targetY,
   sourcePosition,
   // targetPosition,
   data,
   markerEnd,
}) => {
  // const edgePath = getBezierPath({ sourceX, sourceY, sourcePosition, targetX, targetY, targetPosition });
  // const [centerX, centerY] = getBezierEdgeCenter({
  //   sourceX,
  //   sourceY,
  //   targetX,
  //   targetY,
  // });
  let centerX = 0;
  let centerY = 0;
  let path;
  if (sourcePosition === 'left') {
    path = `M ${sourceX} ${sourceY} L ${sourceX - DEVIATION} ${sourceY} L ${sourceX - DEVIATION} ${targetY} L ${targetX} ${targetY}`;
    centerX = sourceX - DEVIATION;
    centerY = (sourceY + targetY) / 2;
  } else {
    path = `M ${sourceX} ${sourceY} L ${sourceX + DEVIATION} ${sourceY} L ${sourceX + DEVIATION} ${targetY} L ${targetX} ${targetY}`;
    centerX = sourceX + DEVIATION;
    centerY = (sourceY + targetY) / 2;
  }

  return (
    <>
      <path
        id={id}
        className="react-flow__edge-path"
        d={path}
        markerEnd={markerEnd}
        style={data.style}
      />
      <EdgeText
        x={centerX}
        y={centerY}
        label={data.label}
        labelStyle={data.labelStyle}
        labelShowBg
        labelBgStyle={data.labelBgStyle}
        labelBgPadding={data.labelBgPadding}
        labelBgBorderRadius={data.labelBgBorderRadius}
        onClick={() => console.log(data)}
      />
    </>
  );
};

export default CustomEdge;
