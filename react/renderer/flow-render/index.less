@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.flow-render-wrapper,
.flow-render-full-wrapper {
  height: 100%;
  .react-flow {
    min-height: unset !important;
    .react-flow__node {
      background-color: #fff;
    }
    .react-flow__attribution {
      display: none !important;
    }
  }
}

.flow-render-wrapper {
  height: calc(100% - 36px);

  .actions-legend {
    padding: 24px 0 0 28px;
  }

  &.only-fullscreen {
    .actions-wrapper {
      top: 0 !important;
    }

    .actions-legend {
      padding-top: 7px !important;
    }
  }
}

.actions-wrapper {
  position: relative;
  float: right;
  top: -32px;
  right: 16px;
  z-index: 999;

  .actions-button {
    padding: 4px 7px !important;
  }
}

.actions-legend {
  display: flex;
  flex-wrap: wrap;

  &-item {
    display: flex;
    align-items: center;
    margin-right: 24px;
    font-size: 12px;
  }
  &-block {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    border: 2px solid;
    margin-right: 4px;
  }
}

.popover-wrapper {
  position: fixed;
  bottom: 0;
  height: 320px;
  width: 100%;
  z-index: 999;
  background: #fff;
  box-shadow: 12px 0 12px 0 rgba(0, 0, 0, 0.12);

  &.full-screen {
    position: relative;
    width: 420px;
    float: right;
    right: -19px;
    height: calc(100vh - 55px);
    bottom: calc(100vh - 55px);
  }

  &-header {
    width: 100%;
    height: 48px;
    border-bottom: 1px solid @yq-border-4;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-close {
    cursor: pointer;
  }

  &-body {
    overflow-y: scroll;
    height: calc(100% - 50px);
  }

  &-activity {
    margin-left: 16px;
    padding: 12px 16px 12px 0;
    border-bottom: 1px solid @yq-border-4;

    &:last-child {
      border-bottom: none;
    }
  }

  &-approval {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
  }

  &-user {
    display: flex;
    align-items: center;
  }

  &-userName {
    margin-left: 4px;
    font-weight: 500;
  }

  &-action {
    margin-left: 12px;
    font-weight: 400;

    &.approve_approved {
      color: #1ab335;
    }

    &.approve_complete {
      color: #1ab335;
    }

    &.approve_rejected {
      color: #f34c4b;
    }

    &.approve_return {
      color: #f34c4b;
    }
  }

  &-remark {
    margin-left: 4px;
    font-weight: 400;
  }

  &-time {
    margin-left: 26px;
    font-weight: 400;
    color: @yq-text-6;
    line-height: 20px;
  }

  &-title {
    font-size: 16px;
    font-weight: 500;
    margin-right: 8px;
  }
}
