import React, { useContext, useMemo, useState, useRef } from 'react';
import ReactFlow, { ReactFlowProvider } from 'react-flow-renderer';
import { observer } from 'mobx-react-lite';
import { Modal } from 'choerodon-ui/pro';
import { Button, Icon, StatusTag, YqAvatar } from '@zknow/components';
import { color as colorUtils } from '@zknow/utils';
import useClickOutside from '@/hooks/useOnClickOutside';
import AvatarTooltip from '@/components/avatar-tooltip';
import CustomNodeComponent from './components/status-node';
import CustomEdgeComponent from './components/custom-edge';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();

const LEGENDS = [
  {
    key: 'HISTORY',
    color: '#00C48C',
  }, {
    key: 'CURRENT',
    color: '#FFB100',
  }, {
    key: 'PREDICTION',
    color: '#2EA2FF',
  }, {
    key: 'SETTING',
    color: '#354767',
  },
];

const COLOR_MAP = {
  HISTORY: '#00C48C',
  SETTING: '#354767',
  CURRENT: '#FFB100',
  PREDICTION: '#2EA2FF',
};

const LayoutFlow = () => {
  const context = useContext(Store);
  const {
    intl,
    defaultData = [],
    fitView = true,
    BoundingClientRect,
    predictionFlag,
    onlyShowPredictionFlag,
    multipleFlag,
  } = context;
  let modal;
  const [node, setNode] = useState(null);
  const modalStyle = useMemo(() => ({ width: '100%', height: '100%' }), []);
  const modalBodyStyle = useMemo(() => ({ height: '100%' }), []);
  const flowStyle = useMemo(() => ({ width: '100%', height: '100%' }), []);
  const domRef = useRef(null);
  const flowIntlMap = useMemo(() => ({
    HISTORY: intl.formatMessage({ id: 'lcr.renderer.desc.flow.history', defaultMessage: '已经过节点' }),
    CURRENT: intl.formatMessage({ id: 'lcr.renderer.desc.flow.current', defaultMessage: '当前节点' }),
    SETTING: intl.formatMessage({ id: 'lcr.renderer.desc.flow.setting', defaultMessage: '未经过节点' }),
    PREDICTION: intl.formatMessage({ id: 'lcr.renderer.desc.flow.prediction', defaultMessage: '预计经过节点' }),
  }), []);

  const approvalIntlMap = useMemo(() => ({
    parallelApproval: intl.formatMessage({ id: 'lcr.renderer.desc.flow.parallelApproval', defaultMessage: '会签' }),
    anyoneApproval: intl.formatMessage({ id: 'lcr.renderer.desc.flow.anyoneApproval', defaultMessage: '或签' }),
  }), []);

  const statusIntlMap = useMemo(() => ({
    approve_complete: intl.formatMessage({ id: 'lcr.renderer.desc.approve.complete', defaultMessage: '处理完成' }),
    approve_approved: intl.formatMessage({ id: 'lcr.renderer.desc.approve.approved', defaultMessage: '审批通过' }),
    approve_rejected: intl.formatMessage({ id: 'lcr.renderer.desc.approve.rejected', defaultMessage: '审批拒绝' }),
    approve_return: intl.formatMessage({ id: 'lcr.renderer.desc.approve.return', defaultMessage: '审批驳回' }),
    approve_skipped: intl.formatMessage({ id: 'lcr.renderer.desc.approve.skipped', defaultMessage: '自动跳过' }),
    approve_withdraw: intl.formatMessage({ id: 'lcr.renderer.desc.approve.withdraw', defaultMessage: '撤回' }),
    approve_interrupt: intl.formatMessage({ id: 'lcr.renderer.desc.approve.interrupt', defaultMessage: '中断' }),
    approve_pending: intl.formatMessage({ id: 'lcr.renderer.desc.approve.pending', defaultMessage: '将要处理' }),
    approve_processing: intl.formatMessage({ id: 'lcr.renderer.desc.approve.processing', defaultMessage: '审批中' }),
    approve_unprocessed: intl.formatMessage({ id: 'lcr.renderer.desc.approve.unprocessed', defaultMessage: '未处理' }),
    approve_automatically: intl.formatMessage({ id: 'lcr.renderer.desc.approve.automatically', defaultMessage: '已自动处理' }),
  }), []);

  useClickOutside(domRef, () => {
    handleModalClose();
  });

  function handleModalClose() {
    setNode(null);
    if (modal) {
      modal.update({
        children: (
          <div className="flow-render-full-wrapper">
            {renderLegend()}
            <ReactFlowProvider>
              <ReactFlow
                style={flowStyle}
                nodes={defaultData.nodes}
                edges={defaultData.lines}
                connectionLineType="smoothstep" // 这个没用，用node的type控制
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                defaultZoom={0.6}
                defaultPosition={getDefaultPosition()}
                fitView={fitView}
              />
            </ReactFlowProvider>
          </div>
        ),
      });
    }
  }

  function getDefaultPosition() {
    if (BoundingClientRect) {
      const StepClientRectWidth = document.getElementsByClassName('step-renderer-main')[0]?.getBoundingClientRect()?.width;
      const x = (BoundingClientRect?.width - StepClientRectWidth) / 2;
      return [x, 70];
    }
    return [100, 70];
  }

  function handleNodeClick(data) {
    setNode(data);
    if (modal) {
      modal.update({
        children: (
          <div className="flow-render-full-wrapper">
            {renderLegend()}
            <ReactFlowProvider>
              <ReactFlow
                style={flowStyle}
                nodes={defaultData.nodes}
                edges={defaultData.lines}
                connectionLineType="smoothstep" // 这个没用，用node的type控制
                nodeTypes={{
                  status: (e) => (
                    <CustomNodeComponent
                      {...e}
                      intl={intl}
                      current={data}
                      onNodeClick={handleNodeClick}
                    />
                  ),
                }}
                edgeTypes={edgeTypes}
                defaultZoom={0.6}
                defaultPosition={getDefaultPosition()}
                fitView={fitView}
              />
            </ReactFlowProvider>
            {data && renderDetail(data)}
          </div>
        ),
      });
    }
  }

  const nodeTypes = {
    status: (e) => (
      <CustomNodeComponent
        {...e}
        intl={intl}
        current={node}
        onNodeClick={handleNodeClick}
      />
    ),
  };

  const edgeTypes = {
    custom: CustomEdgeComponent,
  };

  function renderLegendItem(legend) {
    return (
      <div className="actions-legend-item" key={legend.key}>
        <div
          className="actions-legend-block"
          style={{
            borderColor: legend.color,
            background: colorUtils?.colorOpacityRGB(legend.color, 0.05),
          }}
        />
        {flowIntlMap[legend.key]}
      </div>
    );
  }

  function renderLegend() {
    let legends = [];
    if (!predictionFlag) {
      // 未开启预测: 不显示预测图例
      legends = LEGENDS.filter(l => l.key !== 'PREDICTION');
    } else if (onlyShowPredictionFlag) {
      // 开启预测，并且只显示预测节点: 不显示未经过图例
      legends = LEGENDS.filter(l => l.key !== 'SETTING');
    } else {
      // 开启预测，显示全部节点
      legends = LEGENDS;
    }
    return (
      <div
        className="actions-legend"
      >
        {legends.map(renderLegendItem)}
      </div>
    );
  }

  function openModal() {
    modal = Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.desc.stage.expand', defaultMessage: '审批流程' }),
      children: (
        <div className="flow-render-full-wrapper">
          {renderLegend()}
          <ReactFlowProvider>
            <ReactFlow
              style={flowStyle}
              nodes={defaultData.nodes}
              edges={defaultData.lines}
              connectionLineType="smoothstep" // 这个没用，用node的type控制
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              defaultZoom={0.6}
              defaultPosition={getDefaultPosition()}
              fitView={fitView}
            />
          </ReactFlowProvider>
        </div>
      ),
      key: modalKey,
      style: modalStyle,
      bodyStyle: modalBodyStyle,
      destroyOnClose: true,
      transitionAppear: false,
      footer: null,
      fullScreen: true,
    });
  }

  function renderActions() {
    return (
      <div className="actions-wrapper">
        <Button
          key="fullscreen"
          icon="FullScreen"
          funcType="raised"
          color="secondary"
          onClick={openModal}
          className="actions-button"
        />
      </div>
    );
  }

  function renderDetail(data = node) {
    if (!data) {
      return null;
    }
    // fixed定位，用于计算宽度
    const flowEle = document.getElementById('lcr-flow-render');
    const flowEleWidth = flowEle ? (flowEle.clientWidth || flowEle.offsetWidth || flowEle.getBoundingClientRect().width) : false;
    const color = COLOR_MAP[data.stage] || '#354767';
    return (
      <div
        className={`popover-wrapper ${modal ? 'full-screen' : ''}`}
        ref={domRef}
        style={{ width: !modal && flowEleWidth ? (flowEleWidth + 12) : undefined }}
      >
        <div className="popover-wrapper-header">
          <div className="popover-wrapper-left">
            <span className="popover-wrapper-title">{data.activityTitle}</span>
            {data.activityMethod
              ? (
                <StatusTag
                  color={colorUtils?.colorOpacityRGB(color, 0.05)}
                >
                  {`${approvalIntlMap[data.activityMethod]} · ${data.activities.length}`}
                </StatusTag>
              ) : null}
          </div>
          <Icon
            className="popover-wrapper-close"
            type="Close"
            onClick={handleModalClose}
          />
        </div>
        <div className="popover-wrapper-body">
          {data.activities.map((activity) => {
            const {
              userId,
              user,
              userImageUrl,
              action,
              remark,
              startAt,
              endAt,
              task,
            } = activity;
            return (
              <div className="popover-wrapper-activity">
                <div className="popover-wrapper-approval">
                  <span className="popover-wrapper-user">
                    <AvatarTooltip id={userId} placement="leftTop">
                      <YqAvatar src={userImageUrl} size={22}>
                        {user}
                      </YqAvatar>
                    </AvatarTooltip>
                    <span className="popover-wrapper-userName">{user}</span>
                  </span>
                  <span className={`popover-wrapper-action ${action}`}>
                    {action ? statusIntlMap[action] : ''}
                  </span>
                  <span className="popover-wrapper-remark">
                    {remark ? `（${remark}）` : ''}
                  </span>
                </div>
                <div className="popover-wrapper-time">
                  {`${intl.formatMessage({ id: 'lcr.renderer.desc.task.start.at', defaultMessage: '任务开始' })}: ${startAt || '-'}`}
                </div>
                <div className="popover-wrapper-time">
                  {`${task
                    ? intl.formatMessage({ id: 'lcr.renderer.desc.flow.completeTime', defaultMessage: '完成时间' })
                    : intl.formatMessage({ id: 'lcr.renderer.desc.flow.approvedTime', defaultMessage: '审批时间' })
                  }: ${endAt || '-'}`}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div
      id="lcr-flow-render"
      className={`flow-render-wrapper ${!multipleFlag && !predictionFlag ? 'only-fullscreen' : ''}`}
      onClick={() => node && setNode(null)}
    >
      {renderActions()}
      {renderLegend()}
      <ReactFlowProvider>
        <ReactFlow
          style={flowStyle}
          nodes={defaultData.nodes}
          edges={defaultData.lines}
          connectionLineType="smoothstep" // 这个没用，用node的type控制
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          defaultZoom={0.6}
          defaultPosition={getDefaultPosition()}
          fitView={fitView}
        />
      </ReactFlowProvider>
      {!modal && node && renderDetail()}
    </div>
  );
};

export default observer(LayoutFlow);
