import React, { createContext } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';

const Store = createContext();

export default Store;

export const StoreProvider = formatterCollections({ code: ['lcr.renderer'] })(injectIntl(inject('AppState')(observer((props) => {
  const {
    children,
    AppState: { currentMenuType: { organizationId: tenantId } },
  } = props;

  const value = {
    ...props,
    tenantId,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
}))));
