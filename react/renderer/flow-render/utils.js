/*
 * @Author: xia<PERSON>ya
 * @Date: 2021-12-22 15:48:44
 * @Description: 工具包
 */

// 计算各个节点的位置
const setPositionData = (data, stagesData, verticalFlag = false) => {
  const floatWidth = 20; // 这个值是为了后期方便调整宽度设定，默认为0
  const xStep = 250 - floatWidth;
  const yStep = 150;

  // NOTE  算法一： 算出来不太好看，换一种。
  const NODE_DATA = data.NODE.map((i, iIndex) => {
    // 坐标
    const position = { x: 0, y: 0 };
    // 纵向数据
    const colNodeData = data.NODE.filter((j) => j.stageId === i.stageId) || [];

    let stageIndex = 0;
    stagesData?.forEach((o, oIndex) => {
      if (o.id === i.stageId) {
        stageIndex = oIndex;
        if (verticalFlag) {
          position.y = oIndex * yStep;
        } else {
          position.x = oIndex * xStep;
        }
      }
    });
    // 计算中位数
    const length = colNodeData.length;
    const middle = Math.floor(length / 2);
    colNodeData?.forEach((o, oIndex) => {
      if (o.id === i.id) {
        if (verticalFlag) {
          // position.x = oIndex * xStep;
          position.x = (length === 1 && stageIndex !== 0) ? -((stageIndex % 2 ? 0 : -1) * xStep) : (oIndex - middle) * xStep;
        } else {
          position.y = (length === 1 && stageIndex !== 0) ? -((stageIndex % 2 ? 0 : -1) * yStep) : (oIndex - middle) * yStep;
        }
      }
    });
    i.position = position;
    return i;
  });

  data.NODE = NODE_DATA;
  return data;
};

// 修复线的排序，按照状态
const fixEdgesSort = (data) => {
  const NODES = data.NODE;
  const EDGES = data.EDGES.map((i) => {
    const target = NODES.find((j) => j.id === i.target);
    const source = NODES.find((j) => j.id === i.source);
    i.targetPosition = target.position;
    i.sourcePosition = source.position;
    return i;
  });
  const fixedEdges = [];
  const obj = {};
  NODES.forEach((i) => {
    obj[i.id] = EDGES.filter((j) => j.source === i.id) || [];
    // 根据x轴大小升序
    fixedEdges.push(...obj[i.id].sort((a, b) => a.targetPosition.x - b.targetPosition.x));
  });
  data.EDGES = fixedEdges;
};

// 计算连接线的起始点与结束点
const setEdgesHandle = ({ type, currentEdge, direction, count, data }) => {
  if (!data) return;
  data.EDGES.forEach((edge) => {
    if (edge?.id === currentEdge?.id) {
      if (type === 'source') {
        edge.sourceHandle = `${direction}-${type}-${currentEdge.source}-${count}`;
      }
      if (type === 'target') {
        edge.targetHandle = `${direction}-${type}-${currentEdge.target}-${count}`;
      }
    }
  });
};

// 修正计算连接线的起始点与结束点
const fixSetEdgesHandle = ({
  data,
}) => {
  if (!data) return;
  data.EDGES.forEach((edge) => {
    const node = data.NODE.find((i) => i.id === edge.source);
    if (!node) return;
    const {
      leftTargetHandle = 0,
      rightSourceHandle = 0,
      topTargetHandle = 0,
      bottomSourceHandle = 0,
    } = node.data || {};
    if (edge.sourceHandle?.indexOf('left-source') > -1) {
      const souceHandleArr = edge.sourceHandle.split('-');
      souceHandleArr[3] = parseInt(leftTargetHandle, 10) + parseInt(souceHandleArr[3] || 0, 10);
      edge.sourceHandle = souceHandleArr.join('-');
    }
    if (edge.sourceHandle?.indexOf('top-source') > -1) {
      const souceHandleArr = edge.sourceHandle.split('-');
      souceHandleArr[3] = parseInt(topTargetHandle, 10) + parseInt(souceHandleArr[3] || 0, 10);
      edge.sourceHandle = souceHandleArr.join('-');
    }
    edge.type = 'step';
    edge.arrowHeadType = 'arrow';
    edge.style = { stroke: '#DEDEDE' };
    edge.labelStyle = { fill: '#DEDEDE' };
    // NOTE:: edge.node 的值如下：
    // DONE  已经经过
    // ABOUT_TO_PASS  即将经过
    // FUTURE_COURSE  未来可能会经过
    if (edge.node === 'FUTURE_COURSE') {
      edge.style = { animation: 'unset', stroke: '#DEDEDE' };
      edge.labelStyle = { fill: '#DEDEDE' };
    }
  });
};

// 计算连接点的位置以及构造数据
const changeInitData = (data = { NODE: [], EDGES: [] }, stageList = [], verticalFlag = false) => {
  // 计算坐标
  const PositionData = setPositionData(data, stageList, verticalFlag);
  // 排序
  fixEdgesSort(PositionData);

  // 计算每个节点的出入口（连线端点）
  PositionData.NODE.map((i, iIndex) => {
    // 目标
    let topTargetHandle = 0;
    let rightTargetHandle = 0;
    let bottomTargetHandle = 0;
    let leftTargetHandle = 0;
    // 来源
    let topSourceHandle = 0;
    let rightSourceHandle = 0;
    let bottomSourceHandle = 0;
    let leftSourceHandle = 0;

    // 从当前节点出发的线集合
    const sourceList = data.EDGES.filter((j) => j.source === i.id) || [];
    // 回归到当前节点先的集合
    const targetList = data.EDGES.filter((j) => j.target === i.id) || [];

    // 当前节点同一行的数据
    const currentRowsData = data.NODE.filter((o) => o.position.y === i.position.y);
    // 当前节点同一列的数据
    const currentColData = data.NODE.filter((o) => o.position.x === i.position.x);

    // 来源点， 出口
    sourceList.map((edge, eIndex) => {
      const edgeIds = edge.id.split('-');
      const currentRowsIds = currentRowsData.map((j) => j.id);
      const currentColIds = currentColData.map((j) => j.id);
      const targetNodeInfo = PositionData.NODE.find((j) => j.id === edge.target) || {};
      if (/* 判断连线的节点是否是同一行 */ currentRowsIds.includes(edgeIds[0]) && currentRowsIds.includes(edgeIds[1])) {
        // 同一行的操作
        if (i.nodeSort < currentRowsData.find((t) => t.id === edge.target).nodeSort) {
          setEdgesHandle({
            type: 'source',
            currentEdge: edge,
            direction: 'right',
            count: rightSourceHandle,
            data: PositionData,
          });
          rightSourceHandle += 1;
        } else {
          setEdgesHandle({
            type: 'source',
            currentEdge: edge,
            direction: 'left',
            count: leftSourceHandle,
            data: PositionData,
          });
          leftSourceHandle += 1;
        }
      } else if (/* 判断连线的节点是否是同一列 */ currentColIds.includes(edgeIds[0]) && currentColIds.includes(edgeIds[1])) {
        // 同一列的操作
        if (i.nodeSort < currentColData.find((t) => t.id === edge.target).nodeSort) {
          setEdgesHandle({
            type: 'source',
            currentEdge: edge,
            direction: 'bottom',
            count: bottomSourceHandle,
            data: PositionData,
          });
          bottomSourceHandle += 1;
        } else {
          setEdgesHandle({
            type: 'source',
            currentEdge: edge,
            direction: 'top',
            count: topSourceHandle,
            data: PositionData,
          });
          topSourceHandle += 1;
        }
      } else if (/* 来源在目标左边 */ i.position.x < targetNodeInfo.position.x) {
        setEdgesHandle({
          type: 'source',
          currentEdge: edge,
          direction: 'right',
          count: rightSourceHandle,
          data: PositionData,
        });
        rightSourceHandle += 1;
      } else if (/* 来源在目标右边 */ i.position.x > targetNodeInfo.position.x) {
        setEdgesHandle({
          type: 'source',
          currentEdge: edge,
          direction: 'left',
          count: leftSourceHandle,
          data: PositionData,
        });
        leftSourceHandle += 1;
      }
      return edge;
    });

    // 进入点， 入口
    targetList.map((edge, eIndex) => {
      const edgeIds = edge.id.split('-');
      const currentRowsIds = currentRowsData.map((j) => j.id);
      const currentColIds = currentColData.map((j) => j.id);
      const sourceNodeInfo = PositionData.NODE.find((j) => j.id === edge.source) || {};
      if (/* 判断连线的节点是否是同一行 */ currentRowsIds.includes(edgeIds[0]) && currentRowsIds.includes(edgeIds[1])) {
        // 同一行的操作
        if (i.nodeSort < currentRowsData.find((t) => t.id === edge.source).nodeSort) {
          setEdgesHandle({
            type: 'target',
            currentEdge: edge,
            direction: 'right',
            count: rightTargetHandle + rightSourceHandle,
            data: PositionData,
          });
          rightTargetHandle += 1;
        } else {
          setEdgesHandle({
            type: 'target',
            currentEdge: edge,
            direction: 'left',
            count: leftTargetHandle + leftSourceHandle,
            data: PositionData,
          });
          leftTargetHandle += 1;
        }
      } else if (/* 判断连线的节点是否是同一列 */ currentColIds.includes(edgeIds[0]) && currentColIds.includes(edgeIds[1])) {
        // 同一列的操作
        if (i.nodeSort < currentColData.find((t) => t.id === edge.source).nodeSort) {
          setEdgesHandle({
            type: 'target',
            currentEdge: edge,
            direction: 'bottom',
            count: bottomTargetHandle + bottomSourceHandle,
            data: PositionData,
          });
          bottomTargetHandle += 1;
        } else {
          setEdgesHandle({
            type: 'target',
            currentEdge: edge,
            direction: 'top',
            count: topTargetHandle + topSourceHandle,
            data: PositionData,
          });
          topTargetHandle += 1;
        }
      } else if (/* 目标在来源左边 */ i.position.x < sourceNodeInfo.position.x) {
        setEdgesHandle({
          type: 'target',
          currentEdge: edge,
          direction: 'right',
          count: rightTargetHandle + rightSourceHandle,
          data: PositionData,
        });
        rightTargetHandle += 1;
      } else if (/* 来源在目标右边 */ i.position.x > sourceNodeInfo.position.x) {
        setEdgesHandle({
          type: 'target',
          currentEdge: edge,
          direction: 'left',
          // count: leftTargetHandle + leftSourceHandle,
          count: leftTargetHandle,
          data: PositionData,
        });
        leftTargetHandle += 1;
      }
      return edge;
    });

    // 节点数据都提取出来放到data中，
    const NODE_INFO = {
      topTargetHandle,
      rightTargetHandle,
      bottomTargetHandle,
      leftTargetHandle,
      topSourceHandle,
      rightSourceHandle,
      bottomSourceHandle,
      leftSourceHandle,
      name: i.name, // 状态名字
      color: i.color, // 颜色
      stageId: i.stageId, // 关联的阶段
      nodeSort: i.nodeSort,
      id: i.id,
      type: i.type,
      code: i.code,
    };
    i.data = NODE_INFO;
    i.targetHandle = sourceList.length; // 有多少个入口
    i.sourceHandle = targetList.length; // 有多少个出口
    i.type = 'status';
    return i;
  });

  fixSetEdgesHandle({
    data: PositionData,
  });
  return [...PositionData.NODE, ...PositionData.EDGES];
};

// 更新节点及连线状态
const updateInitData = (statusMindData = { NODE: [], EDGES: [] }, data = []) => {
  return data.map((d) => {
    if (d.type === 'status') {
      const nodeData = statusMindData.NODE.find(n => (n.code === d.code || d.code?.split(',')?.includes(n.code)));
      return nodeData ? {
        ...d,
        data: {
          ...d.data,
          type: nodeData.type,
        },
      } : d;
    }
    const edgeData = statusMindData.EDGES.find(e => e.source === d.source && e.target === d.target);
    return edgeData ? {
      ...d,
      node: edgeData.node,
    } : d;
  });
};

export {
  changeInitData,
  updateInitData,
};
