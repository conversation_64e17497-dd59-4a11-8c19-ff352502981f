import React, { useState, useEffect, useContext } from 'react';
import { Icon, Empty } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { TextField, Tooltip, Icon as UiIcon, Pagination, Spin } from 'choerodon-ui/pro';
import SurveyCard from './components/survey-card';
import Store from './stores';
import './index.less';

const SurveyList = observer(() => {
  const context = useContext(Store);
  const {
    formDataSet,
    intl,
    viewRecord,
    prefixCls,
    surveyListDataSet,
    ticketId,
  } = context;
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      surveyListDataSet.query();
    }
  }, [formDataSet?.status, ticketId]);

  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: '20px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              surveyListDataSet.setQueryParameter('param', e);
              surveyListDataSet.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              surveyListDataSet.setQueryParameter('param', e.target.value);
              surveyListDataSet.query();
            }
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            surveyListDataSet.setQueryParameter('param', value);
            surveyListDataSet.query();
          }}
        />
      );
    }
    return (
      <div className="header-top">
        <span className="header-top-title">{viewRecord?.get('name') || intl.formatMessage({ id: 'lcr.renderer.survey.title' })}</span>
        <div className="header-top-icon">
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  function pagerRenderer(page, type) {
    switch (type) {
      case 'first':
        return <UiIcon type="first_page" />;
      case 'last':
        return <UiIcon type="last_page" />;
      case 'prev':
        return <UiIcon type="navigate_before" />;
      case 'next':
        return <UiIcon type="navigate_next" />;
      case 'jump-prev':
      case 'jump-next':
        return '•••';
      default:
        return page;
    }
  }

  function sizeChangerRenderer({ text }) {
    return `${text} 条/页`;
  }

  const renderList = () => {
    if (!surveyListDataSet?.length) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '15px' }}
          innerStyle={{ width: '80px', height: '80px' }}
        />
      );
    }
    return surveyListDataSet.map((i) => {
      return (
        <div className={`${prefixCls}-content`}>
          <SurveyCard
            record={i}
            {...context}
          />
        </div>
      );
    });
  };

  const renderMain = () => {
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-header`}>
          {renderHeader()}
        </div>
        <Spin dataSet={surveyListDataSet}>
          {renderList()}
          {
            surveyListDataSet?.length > 0 && surveyListDataSet.paging && (
              <div className={`${prefixCls}-pagination`}>
                <Pagination
                  showSizeChangerLabel={false}
                  showTotal={false}
                  showPager
                  showQuickJumper
                  sizeChangerPosition="right"
                  sizeChangerOptionRenderer={sizeChangerRenderer}
                  itemRender={pagerRenderer}
                  dataSet={surveyListDataSet}
                />
              </div>
            )
          }
        </Spin>
      </div>
    );
  };

  return renderMain();
});

const SurveyListRenderer = injectIntl((props) => {
  return <SurveyList {...props} />;
});

export default SurveyListRenderer;
