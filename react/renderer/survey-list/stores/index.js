import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import SurveyListDataSet from './SurveyListDataSet';
import SurveyDetailDataSet from './SurveyDetailDataSet';
import SurveyConfigDataSet from './SurveyConfigDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      viewCode,
      instanceId,
      formDataSet,
      viewDataSet,
    } = props;
    const prefixCls = 'survey-list-renderer';
    const ticketId = formDataSet?.current?.get('id');
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];

    const statusMap = {
      PENDING: { name: intl.formatMessage({ id: 'lcr.renderer.survey.pending' }), color: '#6454F4' },
      OUT_OF_DATE: { name: intl.formatMessage({ id: 'lcr.renderer.survey.outOfDate' }), color: '#FF9500' },
      COMPLETED: { name: intl.formatMessage({ id: 'lcr.renderer.survey.finished' }), color: '#7BC95A' },
    };

    const surveyListDataSet = useMemo(() => new DataSet(SurveyListDataSet({
      tenantId,
      instanceId,
      viewCode,
      ticketId,
      businessObjectId,
    })), [ticketId, businessObjectId]);

    const surveyDetailDataSet = useMemo(() => new DataSet(SurveyDetailDataSet({ tenantId })), []);

    const configDataSet = useMemo(() => new DataSet(SurveyConfigDataSet({ tenantId })), []);

    const value = {
      ...props,
      surveyListDataSet,
      surveyDetailDataSet,
      formDataSet,
      dsFieldList,
      prefixCls,
      formConfig,
      tenantId,
      statusMap,
      configDataSet,
      ticketId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
