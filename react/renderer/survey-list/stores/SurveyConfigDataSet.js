export default ({ tenantId }) => {
  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    paging: false,
    transport: {
      read: ({ data: { id } }) => {
        const url = `/asmt/v1/${tenantId}/assessment_instances/self/${id}`;
        return {
          url,
          method: 'get',
        };
      },
    },
    fields: [
      { name: 'id', type: 'string', label: 'ID' },
    ],
  };
};
