@import '~choerodon-ui/lib/style/themes/default';

.survey-detail-renderer-modal {
  .survey-list-detail-undone {
    label.c7n-pro-field-label-vertical {
      width: 100%;
      padding-right: 0;

      > span.lc-form-page-loader-label {
        width: 100%;
      }
    }

    label.c7n-pro-field-label + .c7n-pro-field-wrapper {
      padding-left: 0.04rem !important;
      font-size: 0.14rem;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.85);
    }
  }
}

.survey-list-renderer {
  .flex-center {
    display: flex;
    align-items: center;
  }

  &-card {
    cursor: pointer;
    background: #fff;
    border-radius: 4px;
    border: 12px solid #f2f7ff;
    padding: 16px;
    margin-bottom: 12px;

    &:hover {
      box-shadow: 0 2px 8px 0 rgba(129, 137, 153, 0.32);
    }

    &-top {
      .flex-center;
      justify-content: space-between;

      .top-left {
        font-size: 14px;
        font-weight: 500;
        color: #12274d;
        line-height: 22px;
      }
    }

    &-bottom {
      .flex-center;
      font-size: 12px;
      font-weight: 400;
      color: #8c8c8c;
      line-height: 16px;
      margin-top: 8px;
    }
  }
}
