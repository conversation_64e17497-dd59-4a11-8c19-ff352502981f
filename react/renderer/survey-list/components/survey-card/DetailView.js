/*
 * @Author: xia<PERSON>ya
 * @Date: 2022-01-25 11:01:08
 * @Description: 详情
 */
import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import axios from 'axios';
import { ExternalComponent } from '@zknow/components';
import { Spin } from 'choerodon-ui';
import { message } from 'choerodon-ui/pro';

export default forwardRef((props, ref) => {
  const {
    intl,
    surveyDetailDataSet,
    configDataSet,
    id,
    tenantId,
    modal,
    dataSet,
    afterAction = () => { },
    record,
    resetFlag = true,
    readOnly = false,
    titleFlag = true,
    surveyHasAnchorComponentParentViewId,
  } = props;
  const pageRef = useRef();
  const [visible, setVisible] = useState(false);
  const url = `/asmt/v1/${tenantId}/assessment_instances`;

  async function refresh() {
    surveyDetailDataSet.setQueryParameter('id', id);
    await surveyDetailDataSet.query();
    setVisible(true);
  }

  useImperativeHandle(ref, () => ({
    submit: handleSubmit,
    save: handleSave,
    close: handleClose,
  }));

  useEffect(() => {
    if (id) refresh();
  }, [id]);

  async function handleSubmit() {
    try {
      const formDataSet = pageRef.current?.formDataSet;
      const result = await formDataSet.current.validate(true);
      if (result) {
        const data = formDataSet?.toData();
        if (data && data.length) {
          const res = await axios.post(`${url}/submit/${id}?surveyId=${configDataSet?.current?.get('surveyId')}`, data[0]);
          if (res?.failed) {
            message.error(res.message);
            return false;
          } else {
            message.success(intl.formatMessage({ id: 'lcr.renderer.survey.submitSuccess' }));
            await dataSet.query();
            handleReset();
            afterAction();
          }
        }
      } else {
        // 将必填校验未通过的消息传递给外部
        return 'validate';
      }
    } catch (e) {
      return false;
    }
  }

  async function handleSave() {
    try {
      const formDataSet = pageRef.current?.formDataSet;
      const result = await formDataSet.current.validate(true);

      if (result) {
        const data = formDataSet?.toData();
        if (data && data.length) {
          const res = await axios.post(`${url}/save/${id}?surveyId=${configDataSet?.current?.get('surveyId')}`, data[0]);
          if (res?.failed) {
            message.error(res.message);
            return false;
          } else {
            message.success(intl.formatMessage({ id: 'lcr.renderer.survey.save.success' }));
            await dataSet.query();
            handleReset();
            afterAction();
          }
        }
      } else {
        // 将必填校验未通过的消息传递给外部
        return 'validate';
      }
    } catch (e) {
      return false;
    }
  }

  function handleReset() {
    if (!resetFlag) return;
    const formDataSet = pageRef.current?.formDataSet;
    formDataSet?.reset();
    surveyDetailDataSet.loadData([]);
    configDataSet.loadData([]);
    setVisible(false);
    modal.close();
  }

  function handleClose() {
    handleReset();
  }

  function getDefaultSurveyData() {
    const configRecord = configDataSet.current;
    const hasData = Object.keys(surveyDetailDataSet?.current?.toData() || {}).filter(key => key !== '__dirty').length > 0;
    const editFlag = record?.get('editFlag');
    if (configRecord && configRecord?.get('state') === 'COMPLETED' && hasData) {
      return [surveyDetailDataSet?.current.toData()];
    } else if (configRecord && configRecord?.get('state') === 'PENDING' && editFlag) {
      return [surveyDetailDataSet?.current.toData()];
    } else {
      return null;
    }
  }

  function getMode() {
    const editFlag = record?.get('editFlag');
    if (readOnly) {
      return 'OUTPUT';
    }
    if (!editFlag) {
      return 'DISABLED';
    } else if (configDataSet?.current?.get('state') === 'PENDING') {
      return 'MODIFY';
    }
    return 'DISABLED';
  }

  if (!visible) return <div style={{ width: '100%', height: '150px', textAlign: 'center' }}><Spin /></div>;
  return (
    <div className="survey-list-detail-undone">
      {configDataSet?.current && surveyDetailDataSet?.current ? (
        <ExternalComponent
          system={{
            scope: 'lcr',
            module: 'PageLoader',
          }}
          viewId={configDataSet?.current?.get('surveyId')}
          pageRef={pageRef}
          mode={getMode()}
          surveyFlag
          surveyTitleFlag={titleFlag}
          numberFlag={configDataSet?.current?.get('numberEnabledFlag')}
          defaultInstanceData={getDefaultSurveyData()}
          showHeaderFlag
          pageableFlag={configDataSet?.current?.get('pageableFlag') || false}
          isLastPage={(res) => {
            dataSet?.setState('isLastSurveyPage', res);
          }}
          surveyHasAnchorComponentParentViewId={surveyHasAnchorComponentParentViewId}
          fixedSurveyHasAnchorRect={{
            top: 40,
            width: '100%',
            margin: '0px',
          }}
        />) : null}
    </div>
  );
});
