/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022-01-25 09:32:02
 * @Description: 调查卡片
 */
import React, { useRef } from 'react';
import { Icon, StatusTag, Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { Modal } from 'choerodon-ui/pro';
import { Divider } from 'choerodon-ui';
import DetailView from './DetailView';

import './index.less';

const modalKey = Modal.key();

const SurveyCard = observer((props) => {
  const {
    record, // record集合
    intl,
    prefixCls,
    statusMap,
    surveyListDataSet,
    configDataSet,
  } = props;

  const surveyRef = useRef(null);

  async function handleCardClick() {
    surveyListDataSet.setState('isLastSurveyPage', true);
    if (record.get('id')) {
      configDataSet.setQueryParameter('id', record.get('id'));
      await configDataSet.query();
    }
    Modal.open({
      key: modalKey,
      title: record?.get('surveyName'),
      className: `${prefixCls}-modal`,
      okFirst: true,
      style: { width: '800px' },
      children: (
        <DetailView
          id={record.get('id')}
          record={record}
          dataSet={surveyListDataSet}
          {...props}
          ref={surveyRef}
        />
      ),
      destroyOnClose: true,
      footer: (okBtn, cancelBtn) => {
        const configRecord = configDataSet.current;
        return configRecord && configRecord?.get('state') === 'PENDING' && record?.get('editFlag')
          ? (
            <div className="survey-list-detail-footer">
              <div className="button-group">
                <Button
                  key="submit"
                  funcType="raised"
                  color="primary"
                  onClick={async () => {
                    await surveyRef?.current.submit();
                  }}
                >
                  {intl.formatMessage({ id: 'zknow.common.button.submit' })}
                </Button>
                <Divider style={{ width: '2px', height: '.32rem', marginRight: '.24rem', marginLeft: '.24rem' }} type="vertical" />
                <Button
                  key="save"
                  funcType="raised"
                  color="primary"
                  onClick={async () => {
                    await surveyRef?.current.save();
                  }}
                >
                  {intl.formatMessage({ id: 'zknow.common.button.save' })}
                </Button>
                <Button
                  key="close"
                  funcType="raised"
                  onClick={async () => {
                    await surveyRef?.current.close();
                  }}
                >
                  {intl.formatMessage({ id: 'zknow.common.button.close' })}
                </Button>
              </div>
            </div>
          ) : cancelBtn;
      },
    });
  }

  const renderMain = () => {
    if (!record) return null;
    const stateField = record?.get('state');
    const { name, color } = statusMap[stateField] || {};
    const date = record?.get('dueDate') === '2999-01-01 23:59:59' ? intl.formatMessage({ id: 'lcr.renderer.survey.permanent' }) : record?.get('dueDate')?.split(' ')[0];
    return (
      <div className={`${prefixCls}-card`} onClick={() => handleCardClick()}>
        <div className={`${prefixCls}-card-top`}>
          <div className="top-left">
            {record?.get('surveyName')}
          </div>
          <div className="top-right">
            <StatusTag name={name} color={color}>{name}</StatusTag>
          </div>
        </div>
        {date && <div className={`${prefixCls}-card-bottom`}><Icon type="time" size={14} style={{ marginRight: '8px' }} />{date}</div>}
      </div>
    );
  };

  return renderMain();
});

export default SurveyCard;
