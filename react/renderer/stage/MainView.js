/*
 * @Author: xia<PERSON>ya
 * @Date: 2021-12-15 17:32:21
 * @Description: 阶段
 */
import React, { useState, useContext, useEffect, useMemo } from 'react';
import { Icon, Button } from '@zknow/components';
import { Modal } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import YqStep from '@/components/yq-step';
import StatusMind from '@/components/status-mind';
import './index.less';
import Store from './stores';

const modalKey = Modal.key();

const MainView = () => {
  const context = useContext(Store);
  const {
    setOverflowDisplay,
    stepsDataSet,
    formDataSet,
    intl,
    mode, // horizontal, vertical
    title,
    defaultData,
    fitView,
  } = context;
  const verticalFlag = mode === 'vertical';
  const prefixCls = 'stage-renderer';
  const [open, setOpen] = useState(false);
  const [openCount, setOpenCount] = useState(0);
  const [stepRenderer<PERSON><PERSON>, setStepRendererKey] = useState(Modal.key());
  const modalStyle = useMemo(() => ({ width: '100%', height: '100%' }), []);
  let modal;

  useEffect(() => {
    if (formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount') !== undefined) {
      stepsDataSet.query();
    }
  }, [formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount')]);

  function handleClose() {
    if (modal) {
      modal.close();
    }
  }

  function openModal() {
    modal = Modal.open({
      title: '',
      children: (
        <div className={`${prefixCls}-modal-wrapper`}>
          <div className={`${prefixCls}-modal-header`}>
            <div className={`${prefixCls}-modal-title`}>
              {title || intl.formatMessage({ id: 'lcr.renderer.desc.ticket.flow', defaultMessage: '单据流程图' })}
            </div>
            <div>
              <Button
                key="offscreen"
                icon="OffScreen"
                funcType="raised"
                color="secondary"
                className={`${prefixCls}-modal-offscreen`}
                onClick={handleClose}
              />
            </div>
          </div>
          {!verticalFlag ? <YqStep {...context} /> : null}
          <div className={`${prefixCls}-modal-body ${verticalFlag ? 'vertical' : ''}`}>
            <StatusMind
              {...context}
              vertical={verticalFlag}
              fullscreen
            />
          </div>
        </div>
      ),
      className: `${prefixCls}-modal`,
      key: modalKey,
      style: modalStyle,
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
      header: null,
    });
  }

  function renderMind() {
    if (!openCount) return null;
    const BoundingClientRect = document
      .getElementById(`step-renderer-${stepRendererKey}`)
      .getBoundingClientRect();
    const style = {
      width: BoundingClientRect.width,
      display: open ? 'block' : 'none',
    };
    return (
      <>
        <div className={`${prefixCls}-mind`} style={style}>
          <div className={`${prefixCls}-mind-line`}>
            <span
              className="down-icon-wrapper"
              onClick={() => {
                setOpen(false);
                if (setOverflowDisplay) {
                  setOverflowDisplay(false);
                }
                setOpenCount(openCount + 1);
              }}
            >
              <Icon
                className="down-icon"
                type="UpOne"
                theme="filled"
                size="12"
                fill="#12274D"
                strokeLinecap="square"
              />
            </span>
          </div>
          <StatusMind
            BoundingClientRect={BoundingClientRect}
            {...context}
            handleFullScreen={openModal}
            title={title}
            defaultData={defaultData}
            fitView={fitView}
          />
        </div>
      </>
    );
  }

  function renderLine() {
    if (open) return null;
    return (
      <div className={`${prefixCls}-line`}>
        <span
          className="down-icon-wrapper"
          onClick={() => {
            setOpen(true);
            if (setOverflowDisplay) {
              setOverflowDisplay(true);
            }
            setOpenCount(openCount + 1);
          }}
        >
          <Icon
            className="down-icon"
            type="down-one"
            theme="filled"
            size="12"
            fill="#12274D"
            strokeLinecap="square"
          />
        </span>
      </div>
    );
  }

  if (verticalFlag) {
    return (
      <div className={`${prefixCls} vertical`} id={`step-renderer-${stepRendererKey}`}>
        <StatusMind
          {...context}
          handleFullScreen={openModal}
          title={title}
          vertical
          defaultData={defaultData}
          fitView={fitView}
        />
      </div>
    );
  }

  return (
    <div className={prefixCls} id={`step-renderer-${stepRendererKey}`}>
      <YqStep {...context} />
      {renderLine()}
      {renderMind()}
    </div>
  );
};

export default observer(MainView);
