@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.stage-renderer {
  z-index: 20;
  padding-bottom: 12px; // 阶段组件上有一个展开的按钮
  &-line {
    height: 1px;

    text-align: center;

    border: 1px solid #e8e8e8;
    border-bottom: none;
    .down-icon-wrapper {
      position: relative;
      display: inline-block;
      width: 34px;
      height: 12px;
      background: rgba(203, 210, 220, 0.5);
      border: 1px solid #e8e8e8;
      border-radius: 0 0 4px 4px;
      cursor: pointer;
      .down-icon {
        position: relative;
        top: -5px;
      }
    }
  }

  &-mind {
    position: absolute;
    z-index: 10;
    padding: 16px;
    background: #fff;
    border-top: solid 1px #e8e8e8;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: -6px 3px 12px -6px rgba(0, 0, 0, 0.3);
    &-line {
      position: relative;
      top: -29px;

      height: 1px;

      text-align: center;

      .down-icon-wrapper {
        position: relative;

        display: inline-block;
        width: 34px;
        height: 12px;

        background: rgba(203, 210, 220, 0.5);
        border: 1px solid #e8e8e8;
        border-radius: 4px 4px 0 0;
        cursor: pointer;
        .down-icon {
          position: relative;
          top: -4px;
        }
      }
    }
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.26);

    transition: background-color 0.3s;
    &:hover {
      background-color: #aeaeae;
    }
  }

  &-modal {
    .c7n-pro-modal-body {
      padding: 0;
    }
    .react-flow {
      min-height: unset !important;
      .react-flow__node {
        background-color: #ffffff;
      }
      .react-flow__attribution {
        display: none !important;
      }
    }
    &-wrapper {
      width: 100%;
      height: 100%;
    }
    &-header {
      height: 57px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 20px;
      border-bottom: 1px solid @yq-border-4;
    }
    &-title {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }
    &-offscreen {
      padding: 4px 7px !important;
    }
    &-body {
      height: calc(100% - 118px);
      overflow: scroll;
      border-top: 1px solid @yq-border-2;

      &.vertical {
        height: calc(100% - 57px);
        border-top: none;
      }
    }
  }

  &.vertical {
    min-height: calc(100vh - 150px);
    height: 100%;
    padding: 16px;
  }
}
