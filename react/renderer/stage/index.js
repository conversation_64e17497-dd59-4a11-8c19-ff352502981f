import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import React from 'react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import MainView from './MainView';

export default inject('AppState', 'HeaderStore')(
  formatterCollections({
    code: ['lcr.renderer', 'lc.components'],
  })((props) => (
    <StoreProvider {...props}>
      <MainView />
    </StoreProvider>
  ))
);

/* externalize: StageRenderer */
