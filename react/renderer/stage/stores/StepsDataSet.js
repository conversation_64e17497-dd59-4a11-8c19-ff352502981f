export default ({ tenantId, businessObjectCode, ticketId, businessObjectId }) => {
  const url = `/itsm/v1/${tenantId}/stage`;

  return {
    autoQuery: true,
    paging: false,
    selection: false,
    primaryKey: 'id',
    transport: {
      read: () => {
        if (!businessObjectId) return;
        if (!businessObjectCode) return;
        if (!ticketId) return;
        return {
          url: `${url}?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}&businessObjectId=${businessObjectId}`,
          method: 'get',
        };
      },
    },
    events: {
 
    },
  };
};
