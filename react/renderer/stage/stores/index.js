import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { getQueryString } from '@/utils';
import StepsDataSet from './StepsDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
    } = props;
    const prefixCls = 'step-renderer';
    const intlPrefix = 'lc.components.step';
    const ticketId = formDataSet?.current?.get('id') || getQueryString('ticketId');
    const { businessObjectCode, businessObjectId } = viewDataSet?.current?.toData() || {};

    const stepsDataSet = useMemo(() => new DataSet(StepsDataSet({ 
      tenantId, 
      businessObjectCode, 
      ticketId, 
      intlPrefix, 
      intl,
      businessObjectId,
    })), [businessObjectCode, ticketId, businessObjectId]);

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      stepsDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },)
));
