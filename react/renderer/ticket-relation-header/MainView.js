import React, { useContext, useState, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Tooltip, Modal, Form, Select, Lov } from 'choerodon-ui/pro';
import { debounce } from 'lodash';
import { Icon, SectionHeaderLine } from '@zknow/components';
import Store from './stores';

import './index.less';

const modalKey = Modal.key();
export default observer(() => {
  const {
    intl,
    prefixCls,
    title,
    dataSet,
    addDataset,
    onExpand = () => {},
    mode = 'table', // 模式分为卡片与表格， 卡片是card, 表格是table
    isRelation = true,
    addBtnRef = null,
    lovId,
  } = useContext(Store);
  const [searching, setSearching] = useState(false);
  const [expand, setExpand] = useState(true);
  const [searchValue, setSearchValue] = useState();
  const inputRef = useRef();

  const handleSearch = debounce(async (value) => {
    dataSet.setQueryParameter('param', value);
    await dataSet.query();
  }, 500);

  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: expand ? '16px' : '0px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          ref={inputRef}
          onChange={(e) => {
            setSearchValue(e);
          }}
          onBlur={(e) => {
            const value = e.target.value;
            if (!value) {
              setSearching(false);
              handleSearch(null);
            }
          }}
          onClear={() => {
            handleSearch(null);
            inputRef?.current.element.focus();
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            handleSearch(value);
          }}
        />
      );
    }

    function renderTypeOption({ value }) {
      const _map = {
        related: 'source',
        beRelated: 'target',
      };
      const _config = addDataset?.current?.get('linkId');
      return _config?.[_map?.[value]];
    }

    const handleAdd = async () => {
      await addDataset.reset();
      const record = addDataset.create();
      Modal.open({
        key: modalKey,
        title: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.create' }),
        children: (
          <Form record={record} labelTooltip="overflow">
            <Lov name="linkId" />
            <Select
              name="type"
              optionRenderer={renderTypeOption}
              renderer={renderTypeOption}
              searchable={false}
            />
            <Lov
              searchMatcher={lovId ? 'fuzzy_params_' : 'search_fuzzy_params_'}
              name="ticketId"
              placeholder={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.selectTicket' })}
            />
          </Form>
        ),
        drawer: false,
        style: { width: 520 },
        bodyStyle: { padding: 16 },
        destroyOnClose: true,
        onOk: async () => {
          const validate = await record.validate();
          if (!validate) {
            return false;
          } else {
            await record.dataSet.submit();
            dataSet.query();
          }
        },
        onCancel: () => {
          record.dataSet.reset();
        },
      });
    };

    const addIcon = (
      <Tooltip
        title={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.create' })}
      >
        <span ref={addBtnRef} onClick={handleAdd}><Icon className="filter-icon" type="add-one" size={16} /></span>
      </Tooltip>
    );

    return (
      <div className={`${prefixCls}-header`}>
        <div className="header-top">
          <span className="header-top-title">
            {/* <span className="header-top-title-line" />  */}
            {mode === 'table' && <SectionHeaderLine style={{ height: 16 }} />}
            {title}
            {
              mode === 'table' && (
                <Icon
                  className="header-top-title-icon"
                  onClick={() => {
                    setExpand(!expand);
                    onExpand(!expand);
                  }}
                  type={expand ? 'DownOne' : 'RightOne'}
                  theme="filled"
                  size={14}
                />
              )
            }
          </span>
          <div className="header-top-icon">
            {isRelation && addIcon}
            <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
              <Icon
                className="filter-icon"
                style={{ marginLeft: '8px' }}
                type="icon-search"
                size={16}
                onClick={() => setSearching(true)}
              />
            </Tooltip>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={prefixCls}>
      {renderHeader()}
    </div>
  );
});
