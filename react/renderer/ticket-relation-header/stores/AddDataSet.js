import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import qs from 'qs';
import omit from 'lodash/omit';
import { transformResponse } from '@/components/page-loader/lovConfig';
import { transformField } from '@/components/page-loader/stores/DataSetManager';

export default ({
  intl,
  tenantId,
  ticketId,
  businessObjectId,
  lovId,
  viewId,
  defaultRelatedTicketType,
}) => {
  const optionDataset = new DataSet({
    data: [
      { value: 'related' },
      { value: 'beRelated' },
    ],
  });

  function getTicketIdConfig() {
    const presetProps = {};

    presetProps.lovDefineAxiosConfig = lovCode => ({
      url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
      method: 'GET',
      transformResponse: data => transformResponse(
        data,
        data?.name,
        (map, f) => transformField(
          {
            fieldMap: map,
            field: f,
            viewId,
            tenantId,
          }
        ),
        intl,
        tenantId,
      ),
    });
    presetProps.lovQueryAxiosConfig = (lovCode, lovConfig = {}, { data, params }) => {
      const queryParams = getQueryParams(data, ['current_params', '__page_params', 'linkId', 'ticketId']);
      const pathParams = qs.stringify({
        ticketId: queryParams?.ticketId,
        linkId: queryParams?.linkId,
      });
      return {
        url: `/lc/v1/${tenantId}/svs_ticket_links/${lovCode}/queryWithCondition?${pathParams}`,
        method: 'post',
        data: {
          params: omit(queryParams, ['ticketId', 'linkId']),
          conditions: [],
        },
      };
    };
    presetProps.lovCode = lovId;
    return lovId ? presetProps : {};
  }

  function getCreateData(_data) {
    return (_data?.ticketId || [])?.map(item => {
      let extra;
      if (_data.type === 'related') {
        extra = {
          sourceTicketId: ticketId,
          sourceTicketTypeId: businessObjectId,
          targetTicketId: item?.id,
          targetTicketTypeId: item?.business_id,
        };
      } else {
        extra = {
          sourceTicketId: item?.id,
          sourceTicketTypeId: item?.business_id,
          targetTicketId: ticketId,
          targetTicketTypeId: businessObjectId,
        };
      }
      return {
        linkId: _data?.linkId?.id,
        ...extra,
      };
    });
  }

  return {
    autoQuery: false,
    paging: false,
    transport: {
      create: ({ data: [data] }) => ({
        url: `lc/v1/${tenantId}/svs_ticket_links`,
        method: 'post',
        data: getCreateData(data),
      }),
    },
    fields: [
      {
        name: 'linkId',
        label: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.name' }),
        type: 'object',
        lovCode: 'TICKET_LINK_CONFIG',
        required: true,
        valueField: 'id',
        defaultValue: defaultRelatedTicketType,
      },
      {
        name: 'type',
        label: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.relation' }),
        type: 'string',
        options: optionDataset,
        dynamicProps: {
          disabled: ({ record }) => !record?.get('linkId')?.id,
          options: ({ record }) => {
            const _config = record?.get('linkId');
            const _map = {
              related: 'source',
              beRelated: 'target',
            };
            const relatedName = _config?.[_map?.related];
            const beRelatedName = _config?.[_map?.beRelated];
            if (relatedName === beRelatedName) {
              const dataset = new DataSet({
                data: [
                  { value: 'related' },
                ],
              });
              return dataset;
            }
            return optionDataset;
          },
        },
        required: true,
      },
      {
        name: 'ticketId',
        label: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.ticket' }),
        type: 'object',
        lovCode: lovId || 'TICKET_TASK_QUERY',
        dynamicProps: {
          lovPara: ({ record }) => {
            return {
              ticketId,
              linkId: record?.get('linkId')?.id,
            };
          },
        },
        required: true,
        valueField: 'id',
        multiple: true,
        textField: 'short_description',
        optionsProps: (dsProps) => {
          return {
            ...dsProps,
            primaryKey: 'id',
          };
        },
        ...getTicketIdConfig(),
      }],
  };
};
