import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import AddDataSet from './AddDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      businessObjectId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      lovId,
      defaultRelatedTicketType = null,
    } = props;

    const prefixCls = 'lc-components-ticket-relation-header-new';

    const addDataset = useMemo(() => new DataSet(
      AddDataSet({ intl, ticketId, tenantId, businessObjectId, lovId, defaultRelatedTicketType })
    ),
    [ticketId, tenantId, lovId]);

    const value = {
      ...props,
      prefixCls,
      intl,
      tenantId,
      ticketId,
      addDataset,
      businessObjectId,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
