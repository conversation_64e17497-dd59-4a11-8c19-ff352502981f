import React, { useEffect, useState, useCallback } from 'react';
import { Button, message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import { formatterCollections } from '@zknow/utils';

const DetailButton = injectIntl(
  observer((props) => {
    const {
      intl,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config = {},
      formDataSet,
      viewDataSet,
      HeaderStore: {
        getTenantConfig: { ticketDraftFlag },
      },
      disableACL,
      pageRef,
    } = props;
    const businessObjectCode = viewDataSet?.current?.get('businessObjectCode');
    const viewId = viewDataSet?.current?.get('id');
    const { icon, name, id, color = 'default', draftType = 'OPTIONAL' } = config;

    const [draftData, setDraftData] = useState();

    // 租户未启用草稿
    if (!ticketDraftFlag) {
      return (
        <div />
      );
    }

    const loadDraft = (data = {}) => {
      data.draft_flag = false;
      formDataSet?.setState('__isDraft', true);
      formDataSet?.loadData([data]);
    };

    const initData = async () => {
      if (formDataSet.getState('hasInitDraftData')) {
        return;
      }
      formDataSet.setState('hasInitDraftData', true);

      // 新版草稿不用查询
      if (disableACL) return;

      const res = await axios.get(`/itsm/v1/${tenantId}/ticket/draft/${businessObjectCode}/query`);

      if (!res?.failed && res?.id) {
        const data = await axios.post(
          `/lc/v1/engine/${tenantId}/dataset/${viewId}/${viewId}/draft/query`,
          { id: res?.id }
        );
        if (!data?.failed) {
          const _draftData = data?.content[0] || {};
          setDraftData(_draftData);

          // 2023.7.26 因为弹框逻辑移除，配置端未删除OPTIONAL配置，无法修数据，这里兼容成默认应用草稿
          if (draftType === 'AUTO_USE' || draftType === 'OPTIONAL') {
            loadDraft(_draftData);
          }
        } else {
          message.error(res.message);
        }
      }
    };

    useEffect(() => {
      initData();
    }, []);

    const handleClick = useCallback(async () => {
      // eslint-disable-next-line no-shadow
      const { formDataSet, formTableDsList, pageRecord } = pageRef.current || {};
      formDataSet.setState('saveToDraftLoading', true);
      try {
        const data = formDataSet?.current?.toData();
        if (draftData?.id) {
          data.id = draftData?.id;
          data.draft_flag = true;
          data.object_version_number = draftData?.object_version_number;
        }
        const _children = [];
        formTableDsList?.map((table) => {
          const tableConfig = pageRecord.get('jsonData.datasets')?.find(ds => ds.id === table.id);
          _children.push({
            businessObjectId: tableConfig?.businessObjectId,
            relatedFieldCode: tableConfig?.relatedFieldCode,
            data: table.toData(),
          });
          return table;
        });
        if (_children.length) data._children = _children;
        // 应该是特例
        if (data.parent_id) {
          data.parent_id = typeof data.parent_id === 'string' ? data.parent_id : data.parent_id?.id || '';
        }
        const res = await axios.post(`/itsm/v1/${tenantId}/ticket/draft/${businessObjectCode}/submit?viewId=${viewId}`, data);
        if (!res?.failed) {
          message.success(intl.formatMessage({ id: 'lcr.renderer.draftBox.save.success' }));
          setDraftData(res);
          formDataSet?.current?.set('id', res.id);
        } else {
          message.error(res.message);
        }
        formDataSet.setState('saveToDraftLoading', false);
      } catch (e) {
        formDataSet.setState('saveToDraftLoading', false);
      }
    }, [draftData]);

    return (
      <Button
        loading={formDataSet?.getState('saveToDraftLoading')}
        key={id}
        onClick={handleClick}
        funcType="raised"
        color={color}
        icon={icon}
        waitType="debounce"
        wait={200}
      >
        {name}
      </Button>
    );
  })
);

export default inject('AppState', 'HeaderStore')(
  formatterCollections({
    code: 'lcr.renderer',
  })(observer((props) => (<DetailButton {...props} />)))
);

/* externalize: SaveToDraftButton */
