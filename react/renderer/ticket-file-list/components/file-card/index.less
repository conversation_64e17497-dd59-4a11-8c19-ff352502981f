@import '~choerodon-ui/lib/style/themes/default';

.file-list-renderer {
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-content {
    border-radius: 0.04rem;
    padding-left: 0.08rem;
    transition: background-color 0.2s ease;
    &:hover {
      background-color: #f2f3f5;
      .file-list-renderer-card {
        padding-right: 0;
      }
      .card-bottom {
        display: none;
      }
      .file-preview {
        display: block;
        cursor: pointer;
        user-select: none;
      }
    }
  }
  &-selected-main {
    display: flex;
    align-items: center;
  }
  &-card {
    border-radius: 4px;
    padding: 0.08rem 0;
    min-width: calc(100% - 16px);
    padding-right: 0.2rem;
    flex-grow: 1;
    .flex-center;
    &:hover {
      .file-list-renderer-card-extra {
        display: inline-block;
      }
    }
    &-right {
      overflow: hidden;
      flex-grow: 1;
      .card-top {
        width: 100%;
        font-size: 0.14rem;
        font-weight: 400;
        color: #12274d;
        line-height: 0.22rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: pointer;
      }
      .card-bottom {
        font-size: 12px;
        font-weight: 400;
        color: rgba(18, 39, 77, 0.65);
        line-height: 20px;
        .card-bottom-item {
          margin-right: 4px;
        }
      }
      .file-preview {
        font-size: 12px;
        font-weight: 400;
        color: rgba(18, 39, 77, 0.65);
        line-height: 20px;
        display: none;
      }
    }
    &-extra {
      margin-left: 0.16rem;
      margin-right: 0.16rem;
      min-width: 30px;
      flex-grow: 0;
      display: none;
    }
  }
}
