import React, { useState, useEffect } from 'react';
import { Icon, ExternalComponent, YqPreview } from '@zknow/components';
import { getCookieToken } from '@zknow/utils';
import { observer } from 'mobx-react-lite';
import { Modal, CheckBox, Tooltip } from 'choerodon-ui/pro';
import copy from 'copy-to-clipboard';
import FileSaver from 'file-saver';
import { getEnv } from '@zknow/utils';

import './index.less';

const FileIcon = (props) => {
  const { name } = props;
  return (
    <ExternalComponent
      system={{
        scope: 'knowledge',
        module: 'knowledge-icon',
      }}
      fileType={name?.split('.')?.[name?.split('.')?.length - 1]}
      {...props}
    />
  );
};

const modalKey = Modal.key();

const FileCard = observer((props) => {
  const {
    record, // record集合
    prefixCls,
    tenantId,
    // setSelectedList = () => {},
    // selectedList,
    searching,
    fileListDataSet,
    intl,
  } = props;
  const [checked, setChecked] = useState(false);

  useEffect(() => {
    const selectedList = fileListDataSet.getState('selectedList') || [];
    if (selectedList?.map(i => i.get('fileId')).includes(record.get('fileId'))) {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }, [fileListDataSet.getState('selectedList')?.length]);

  const renderFileSize = (size) => {
    if (!size) {
      return size;
    }
    if (size / (1024 * 1024) > 1) {
      return `${Math.round((size / (1024 * 1024)) * 100) / 100} MB`;
    } else {
      return `${Math.round((size / 1024) * 100) / 100} KB`;
    }
  };

  // 下载附件
  function downloadFile() {
    const token = getCookieToken();
    const url = `${getEnv('API_HOST')}/hfle/yqc/v1/${tenantId}/files/download-by-key?fileKey=${encodeURIComponent(record?.get('fileKey'))}&access_token=${token}`;
    if (navigator?.userAgent?.includes('wxwork')) {
      copy(url);
      Modal.info({
        title: intl.formatMessage({ id: 'lcr.renderer.ticketFileList.wxworkTips' }),
        children: intl.formatMessage({ id: 'lcr.renderer.ticketFileList.wxworkTipsDetail' }),
      });
    } else {
      FileSaver.saveAs(url, record?.get('fileName'));
    }
  }

  // 预览附件
  const previewFile = () => {
    Modal.open({
      key: modalKey,
      title: record?.get('fileName'),
      children: (
        <YqPreview
          fileKey={record?.get('fileKey')}
          udmTenantId={record?.get('udmTenantId')}
        />
      ),
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
    });
  };

  const handleOnChange = () => {
    const selectedList = fileListDataSet.getState('selectedList') || [];
    if (selectedList?.map(i => i.get('fileId')).includes(record.get('fileId'))) {
      selectedList.splice(selectedList.findIndex(i => i.get('fileId') === record.get('fileId')), 1);
    } else {
      selectedList.push(record);
    }
    fileListDataSet.setState('selectedList', selectedList);
  };

  const renderMain = () => {
    if (!record) return null;
    return (
      <div className={`${prefixCls}-selected-main`} key={record.get('fileId')}>
        {
          !searching
          && <CheckBox
            style={{ marginRight: '12px' }}
            checked={checked}
            onInput={() => handleOnChange()}
          />
        }
        <div className={`${prefixCls}-card`}>
          <div className={`${prefixCls}-card-left`}>
            <FileIcon
              name={record.get('fileName')}
              style={{ marginRight: '8px', width: '32px', height: '32px' }}
              size={32}
            />
          </div>
          <div className={`${prefixCls}-card-right`}>
            <Tooltip title={record?.get('fileName') || ''} placement="topLeft">
              <div className="card-top" onClick={() => previewFile()}>{record?.get('fileName')}</div></Tooltip>
            <div className="card-bottom">
              <span className="card-bottom-item">{renderFileSize(record?.get('fileSize'))}</span>
              {record?.get('source') && <span className="card-bottom-item">{record?.get('source')}</span>}
              {record?.get('createdByName') && <span className="card-bottom-item">{record?.get('createdByName')}</span>}
              {record?.get('creationDate') && <span className="card-bottom-item">{record?.get('creationDate')}</span>}
            </div>
            <div className="file-preview" onClick={() => previewFile()}>{intl.formatMessage({ id: 'lcr.renderer.ticketFileList.button.preview' })}</div>
          </div>
          <div className={`${prefixCls}-card-extra`}>
            <Icon type="download" style={{ cursor: 'pointer', color: 'rgba(18,39,77,0.8500)' }} size={20} onClick={() => downloadFile()} />
          </div>
        </div>
      </div>
    );
  };

  return renderMain();
});

export default FileCard;
