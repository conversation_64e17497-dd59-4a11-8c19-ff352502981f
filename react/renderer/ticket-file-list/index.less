@import '~choerodon-ui/lib/style/themes/default';

.file-list-renderer {
  padding: 0.08rem;
  padding-top: 0.16rem;
  padding-right: 0.16rem;
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-header {
    .header-top {
      .flex-center;
      justify-content: space-between;
      &-title {
        padding-left: 0.08rem;
        font-size: 0.16rem;
        font-weight: 500;
        color: #12274d;
        line-height: 0.22rem;
      }
      &-icon {
        .flex-center;
        .filter-icon {
          cursor: pointer;
          background: @minor-color;
          border-radius: 4px;
          padding: 8px;
          line-height: 1;
          height: 32px;
          color: @primary-color;
          &:hover {
            background-color: @primary-6;
            color: #fff;
          }
        }
      }
    }
    .header-bottom {
      .flex-center;
      margin-bottom: 0.16rem;
      &-item {
        margin-right: 24px;
      }
    }
  }

  &-selected {
    margin-bottom: 12px;
    margin-left: 0.08rem;
    display: flex;
    align-items: center;
    color: #12274d;
  }

  &-pagination {
    text-align: right;
    margin-top: 24px;
    .c7n-pro-select-wrapper {
      width: 120px !important;
    }
  }
}
