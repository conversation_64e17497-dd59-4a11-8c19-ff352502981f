import React, { useState, useEffect, useContext } from 'react';
import { Icon, Empty } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { TextField, Tooltip, CheckBox, message, Spin } from 'choerodon-ui/pro';
import axios from 'axios';
import FileSaver from 'file-saver';
import FileCard from './components/file-card';
import Store from './stores';
import './index.less';

const FileList = observer(() => {
  const context = useContext(Store);
  const {
    formDataSet,
    intl,
    viewRecord,
    prefixCls,
    fileListDataSet,
    ticketId,
    tenantId,
  } = context;
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      fileListDataSet.query();
    }
  }, [formDataSet?.status, ticketId]);

  // 下载所选附件
  async function handleDownloadSelected() {
    const selectedList = fileListDataSet.getState('selectedList') || [];
    const fileKeyList = selectedList.map((i) => i.get('fileKey'));
    if (fileKeyList?.length === 0) return false;
    const res = await axios.post(`/hfle/yqc/v1/${tenantId}/files/batch-download/file-keys`, JSON.stringify(fileKeyList), { responseType: 'arraybuffer' });
    if (res?.failed) {
      message.error(res.message);
    } else {
      fileListDataSet.setState('selectedList', []);
      const blob = new Blob([res], { type: 'application/zip' });
      const fieldName = formDataSet?.current?.get?.('number');
      // 张总需求，批量下载的附件需要有单据号
      FileSaver.saveAs(blob, `${fieldName || 'Files'}_${Date.now()}.zip`);
    }
  }

  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: '20px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              fileListDataSet.setQueryParameter('param', e);
              fileListDataSet.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              fileListDataSet.setQueryParameter('param', e.target.value);
              fileListDataSet.query();
            }
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            fileListDataSet.setQueryParameter('param', value);
            fileListDataSet.query();
          }}
        />
      );
    }

    return (
      <div className="header-top">
        <span className="header-top-title">{viewRecord?.get('name') || intl.formatMessage({ id: 'lcr.renderer.ticketFileList.title' })}</span>
        <div className="header-top-icon">
          <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.ticketFileList.download.selectedAll' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="inbox-in"
              size={16}
              onClick={() => handleDownloadSelected()}
            />
          </Tooltip>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const renderSelectAll = () => {
    if (searching) return null;
    if (!fileListDataSet.length) return null;
    const selectedList = fileListDataSet.getState('selectedList') || [];
    let text = intl.formatMessage({ id: 'zknow.common.button.selectAll' });
    if (selectedList.length > 0) {
      // text = `已选择${selectedList.length}项`;
      text = intl.formatMessage({ id: 'lcr.renderer.ticketFileList.selecet' }, { count: selectedList.length });
    }
    return (
      <div className={`${prefixCls}-selected`}>
        <CheckBox
          checked={fileListDataSet.length > 0 && (selectedList.length === fileListDataSet.length)}
          style={{ marginRight: '12px' }}
          indeterminate={renderIndeterminate()}
          onInput={() => handleOnChange()}
        />
        {text}
      </div>
    );
  };

  const handleOnChange = () => {
    const selectedList = fileListDataSet.getState('selectedList') || [];
    if (selectedList.length === fileListDataSet.length) {
      fileListDataSet.setState('selectedList', []);
    } else {
      const list = [];
      fileListDataSet.forEach((i) => {
        list.push(i);
      });
      fileListDataSet.setState('selectedList', list);
    }
  };

  function renderIndeterminate() {
    const selectedList = fileListDataSet.getState('selectedList') || [];
    if (selectedList.length > 0 && selectedList.length < fileListDataSet.length) return true;
    return false;
  }

  const renderList = () => {
    if (!fileListDataSet?.length) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '20px' }}
          innerStyle={{ width: '80px', height: '80px' }}
          type="empty"
        />
      );
    }
    return fileListDataSet.map((i) => {
      return (
        <div className={`${prefixCls}-content`}>
          <FileCard
            record={i}
            searching={searching}
            {...context}
          />
        </div>
      );
    });
  };

  const renderMain = () => {
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-header`}>
          {renderHeader()}
        </div>
        <Spin dataSet={fileListDataSet}>
          {renderSelectAll()}
          {renderList()}
        </Spin>
      </div>
    );
  };
  return renderMain();
});

const TicketFileListRenderer = injectIntl(observer((props) => {
  return <FileList {...props} />;
}));

export default TicketFileListRenderer;
