export default ({ tenantId, ticketId, businessObjectCode, viewId, replyDisplay }) => {
  const urlPrefix = `/lc/v1/${tenantId}/views/queryAttachmentsByTicketId/${viewId}`;

  return {
    autoQuery: false,
    selection: false,
    autoCreate: false,
    paging: false,
    primaryKey: 'fileId',
    transport: {
      read: () => {
        if (!ticketId) return null;
        if (!businessObjectCode) return null;
        return {
          url: `${urlPrefix}?ticketId=${ticketId}&businessObjectCode=${businessObjectCode}&replyDisplay=${replyDisplay}`,
          method: 'get',
        };
      },
    },
    events: {
      
    },
  };
};
