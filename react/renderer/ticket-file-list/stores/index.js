import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import FileListDataSet from './FileListDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formDataSet,
      viewDataSet,
    } = props;
    const prefixCls = 'file-list-renderer';
    const ticketId = formDataSet?.current?.get('id');
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];
    const replyDisplay = jsonData && JSON.stringify(jsonData).includes('Comment');

    const fileListDataSet = useMemo(() => new DataSet(FileListDataSet({
      tenantId,
      ticketId,
      businessObjectId,
      businessObjectCode,
      viewId,
      replyDisplay,
    })), [ticketId, businessObjectCode, viewId, replyDisplay]);

    const allFileListDataSet = useMemo(() => new DataSet(FileListDataSet({
      tenantId,
      ticketId,
      businessObjectId,
      businessObjectCode,
      viewId,
      replyDisplay,
    })), [ticketId, businessObjectCode, viewId, replyDisplay]);

    const value = {
      ...props,
      fileListDataSet,
      formDataSet,
      dsFieldList,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      allFileListDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
