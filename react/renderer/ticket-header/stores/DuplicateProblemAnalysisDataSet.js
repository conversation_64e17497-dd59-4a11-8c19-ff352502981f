export default ({ tenantId, ticketId, shortDescription, duplicateProblemFlag }) => {
  return {
    autoQuery: !!ticketId && duplicateProblemFlag && shortDescription,
    selection: false,
    autoCreate: false,
    paging: false,
    transport: {
      read: () => {
        return {
          url: `/itsm/v1/${tenantId}/ticket/repeat/${ticketId}?maxValue=2&shortDescription=${shortDescription}`,
          method: 'get',
        };
      },
    },
    fields: [
      { name: 'name', type: 'string' },
    ],
  };
};
