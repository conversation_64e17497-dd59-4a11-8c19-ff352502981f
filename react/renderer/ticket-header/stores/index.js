import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { getQueryString } from '@/utils';
import { transformField } from '@/components/page-loader/stores/DataSetManager';
import useLcAutoQuery from '@/hooks/useLcAutoQuery';
import HeaderDataSet from './HeaderDataSet';
import BusinessObjectFieldDataSet from '../../../components/ui-action/stores/BusinessObjectFieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formDataSet,
      viewDataSet,
      viewRecord,
      tabMenuDataSet,
    } = props;
    const prefixCls = 'ticket-header-renderer';
    const extraFileList = []; // 额外要传的，比如头像等
    function getConfigData() {
      const ticketId = formDataSet?.current?.get('id') || getQueryString('ticketId');
      const viewData = viewDataSet?.current?.toData();
      return {
        ...viewData,
        viewId: viewData?.id || getQueryString('viewId'),
        ticketId,
      } || { ticketId };
    }
    const fieldsConfig = viewRecord?.get('widgetConfig.ticketHeaderFieldsConfig');
    const fieldsKey = viewRecord?.get('widgetConfig.ticketHeaderFields')?.split?.(',') || [];
    function getTitleFields(onlyPath) {
      const fields = [];
      if (fieldsConfig) {
        [...new Set([...fieldsKey, 'number', 'shortDescription'])].forEach(key => {
          const field = fieldsConfig?.slice?.().find(item => item?.key === key)?.field;
          fields.push({
            key,
            field,
          });
        });
      }
      if (onlyPath) return fields.map(i => i?.field?.path || i?.field?.code || i?.key);
      return fields;
    }

    function getField(key) {
      const field = getTitleFields().find(i => i.key === key)?.field;
      return field?.path || field?.code || (key === 'shortDescription' ? 'short_description' : key);
    }

    const { businessObjectCode, viewId, jsonData, businessObjectId, ticketId } = getConfigData();
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];

    const descriptionField = getField('description');
    const attachmentsField = getField('attachments');
    const numberField = getField('number');
    const shortDescriptionField = getField('shortDescription');

    const showFileList = getTitleFields(true) || [];
    const displayField = viewRecord.get('widgetConfig.ticketHeaderDisplayFields')?.map(item => {
      const { field } = item || {};
      if (field?.relationObjectCode === 'IAM_USER' && field?.widgetType === 'MasterDetail') {
        extraFileList.push(`${field?.path || field?.code}:image_url`);
      }
      return field?.path || field?.code;
    })?.filter((v) => !!v) || [];

    const queryFields = [...new Set([
      'state_id', 'priority_id', 'submitted_by', 'submitted_at',
      ...displayField,
      ...showFileList,
    ])];

    const showFavorite = fieldsKey?.includes('favorite');
    const showDescription = fieldsKey?.includes('description');
    const showAttachments = fieldsKey?.includes('attachments');

    const businessObjectFieldDataSet = useMemo(() => new DataSet(BusinessObjectFieldDataSet({
      tenantId,
      businessObjectId,
      autoQuery: false,
    })), [tenantId, businessObjectId]);

    function getFields() {
      return displayField.map(code => {
        const formField = formDataSet?.getField(code)?.props?.toJSON();
        if (formField) return formField;
        const display = viewRecord.get('widgetConfig.ticketHeaderDisplayFields') || [];
        const config = display.find(
          i => ((i?.field?.path || i?.field?.code) === code)
        )?.field;
        if (config) {
          if (config?.widgetConfig?.lookupCode) config.widgetConfig.dataSource = 'lookup';
          const data = transformField({
            fieldMap: { [code]: config },
            field: config,
            viewId,
            tenantId,
            intl,
          });
          return {
            ...data,
            name: code,
          };
        }
        return null;
      }).filter(i => i);
    }

    const displayDs = useMemo(() => new DataSet({
      fields: getFields(),
    }), [viewRecord.get('widgetConfig.ticketHeaderDisplayFields')]);

    const headerDataSet = useMemo(() => new DataSet(HeaderDataSet({
      tenantId,
      ticketId,
      intl,
      viewId,
      displayDs,
      queryFields,
      businessObjectCode,
      tabMenuDataSet,
      numberField,
      shortDescriptionField,
    })), [ticketId, viewId, businessObjectCode]);

    // 监听刷新
    useLcAutoQuery(
      headerDataSet,
      formDataSet,
    );

    const value = {
      ...props,
      intl,
      prefixCls,
      formDataSet,
      viewDataSet,
      tenantId,
      ticketId,
      headerDataSet,
      businessObjectCode,
      dsFieldList,
      businessObjectFieldDataSet,
      formConfigRecord: viewDataSet?.current,
      displayField,
      descriptionField,
      shortDescriptionField,
      attachmentsField,
      numberField,
      showFileList,
      showFavorite,
      showDescription,
      showAttachments,
      getTitleFields,
      getField,
      displayDs,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
