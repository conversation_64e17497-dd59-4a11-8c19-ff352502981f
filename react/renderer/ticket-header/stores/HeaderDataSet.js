import qs from 'qs';

export default ({
  tenantId, ticketId, viewId, queryFields, businessObjectCode,
  tabMenuDataSet, displayDs, numberField, shortDescriptionField,
}) => {
  const urlPrefix = `/itsm/v1/${tenantId}/task/ticketHeaderInfo`;
  const queryString = qs.stringify({
    viewId,
    businessObjectCode,
  });
  return {
    autoQuery: false,
    paging: false,
    selection: false,
    dataKey: null,
    transport: {
      read: () => {
        if (!ticketId) return null;
        if (!viewId && !businessObjectCode) return null;
        return {
          url: `${urlPrefix}/${ticketId}?${queryString}`,
          method: 'post',
          data: queryFields.join(','),
          transformResponse: (res) => {
            const data = JSON.parse(res);
            displayDs && displayDs.loadData([data]);
            const current = tabMenuDataSet?.find(r => r.getState('current'));
            /**
             * 这里有两点说明：
             * 1. itsm 的 tabMenuDataSet 不使用 dataSet.current 指定当前活跃Tab页，而是使用每个Record上挂载的自定义状态 getState('current')
             * 2. 必须有 id 的比对，由于这里异步加载时长无法确定，那么打开一个详情Tab页后，快速切换到其它Tab页，可能出现将menuTabTitle更新到后面这个单据上
             */
            if (current && data?.id === current.get('id')) {
              current.set('menuTabTitle', `${data?.[numberField]} ${data?.[shortDescriptionField] || '-'}`);
            }
            return data;
          },
        };
      },
    },
    events: {

    },
  };
};
