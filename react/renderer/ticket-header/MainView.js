import React, { useCallback, useContext, useEffect, useState } from 'react';
import { ExternalComponent, Icon, StatusTag, YqAvatar, TranslateArea } from '@zknow/components';
import { Spin, Tooltip } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import moment from 'moment';
import classnames from 'classnames';
import { getHtml, quillNullContent, deltaToHtmlStr } from '@/utils';
import AvatarTooltip from '@/components/avatar-tooltip';
import RichTextPreview from '@/renderer/rich-text-preview';
import { getRichJson } from '../utils/utils.js';
import { Favorite } from './components/favorite';
import FileItem from './components/file-item';
import Store from './stores';
import Edit from './components/edit';
import WechatWorkChatIcon from '../wechat-work-chat-icon';
import styles from './TicketHeader.less';
import './index.less';

const COLOR_MAP = {
  remain: '#FF9100',
  on_time: '#1AB335',
  timeout: '#F34C4B',
};

function isJSON(str, type = 'object') {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str);
      // eslint-disable-next-line valid-typeof
      if (typeof obj === type && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  return '';
}

// 富文本是否为空
function getRichTextIsNull(content = '') {
  if (!content) return true;
  if (/* quill对象 */ isJSON(content)) {
    return quillNullContent.includes(content);
  }
  if (/* JSON.stringify 一个String类型的数据 */ isJSON(content, 'string')) {
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = JSON.parse(content).substring(0, JSON.parse(content).indexOf('<p data-json='));
    return quillNullContent.includes(data);
  }
  if (typeof content === 'number') {
    content = content.toString();
  }
  // 兼容对象的quill数据
  if (content.constructor === Object && content.ops) {
    content = deltaToHtmlStr(content);
  }

  if (/* 纯字符串 */ typeof content === 'string' && content.indexOf('<p data-json=') > -1) {
    // 移动端约定数据都放在<p data-json=中，pc端正文使用不到
    const data = content.substring(0, content.indexOf('<p data-json='));
    if (!data) return true;
    return quillNullContent.includes(data);
  }
  return false;
}

export default observer(() => {
  const context = useContext(Store);
  const {
    tenantId,
    ticketId,
    intl,
    prefixCls,
    viewRecord,
    headerDataSet,
    businessObjectCode,
    descriptionField,
    attachmentsField,
    numberField,
    shortDescriptionField,
    showFavorite,
    showDescription,
    showAttachments,
    mode: modeType,
    businessObjectFieldDataSet,
    displayDs,
    udmFlag, // 共享服务项生成的单据无法编辑单据头
    formDataSet,
  } = context;

  const record = headerDataSet?.get(0);

  useEffect(() => {
    const data = formDataSet?.current?.getState('qualityField');
    if (data) {
      Object.keys(data).forEach(v => {
        record.set(v, data[v]);
      });
      formDataSet?.current?.setState('qualityField', null);
    }
  }, formDataSet?.current?.getState('qualityField'));

  const getTimeColor = () => {
    const closedAt = record?.get('closed_at');
    const deliveryDate = record?.get('delivery_date');
    if (!closedAt) return moment().isBefore(deliveryDate) ? 'remain' : 'timeout';
    return moment(closedAt).isBefore(deliveryDate) ? 'on_time' : 'timeout';
  };

  // 渲染中间部分
  function renderContent() {
    if (viewRecord.get('widgetConfig.ticketHeaderDisplayFields')?.length === 0) return null;

    return (
      <div className={`${prefixCls}-content`}>
        {viewRecord.get('widgetConfig.ticketHeaderDisplayFields')?.map((v) => {
          const { code: _code, name, widgetType, relationObjectCode, widgetConfig, path } = v.field;
          const isDeliveryDate = _code === 'delivery_date';
          const code = path || _code;
          const codes = code?.split(':');
          const endCode = codes?.[codes?.length - 1];
          const { relationLovNameFieldCode, relationLovValueFieldCode } = widgetConfig || {};
          const label = businessObjectFieldDataSet?.find((i) => i.get('code') === code)?.get('name');
          if (!code) return null;
          let content;
          // 文本类型
          if (['Input', 'Radio', 'TextArea'].includes(widgetType)) {
            const text = record?.get(code);
            content = (
              <span className="header-item-text">
                <span style={{ marginLeft: '4px' }}>{text || '-'}</span>
              </span>
            );
          }
          // 选项集
          if (['Select'].includes(widgetType)) {
            const options = displayDs?.getField(code)?.options?.toData() || [];
            const text = options?.find((i) => i.value === record?.get(code))?.meaning || record?.get(code);
            content = (
              <span className="header-item-text">
                <span style={{ marginLeft: '4px' }}>{text || '-'}</span>
              </span>
            );
          }
          // 日期时间
          if (['Date', 'DateTime'].includes(widgetType)) {
            let time = record?.get(code);
            // 特殊处理的日期类型，展示x天前，悬浮上去展示具体的时间
            const needFormNow = ['submitted_at'].includes(code);
            if (needFormNow) {
              // NOTE 精确时间显示，否则默认会使用一个时间范围，例如 45~89 分钟都被称作 「1 小时前」
              moment.relativeTimeRounding(Math.floor);
              moment.relativeTimeThreshold('s', 60);
              moment.relativeTimeThreshold('m', 60);
              moment.relativeTimeThreshold('h', 24);
              moment.relativeTimeThreshold('d', 30);
              moment.relativeTimeThreshold('w');
              moment.relativeTimeThreshold('M', 12);
              time = moment(time)
                .locale(intl?.locale || 'zh-CN')
                .fromNow();
            }
            const color = getTimeColor(record);
            content = (
              <Tooltip title={needFormNow && record?.get(code)}>
                <span className={classnames('header-item-text', {
                  [styles[`calTime_${color}`]]: isDeliveryDate,
                })}
                >
                  <Icon
                    style={{ marginRight: '4px' }}
                    type="time"
                    theme="filled"
                    size="16"
                    fill={isDeliveryDate ? COLOR_MAP[color] : '#c0c6d2'}
                  />
                  <span>{time || '-'}</span>
                </span>
              </Tooltip>
            );
          }

          // 多对一类型
          if (['Lov', 'MasterDetail'].includes(widgetType)) {
            const text = record?.get(`${code}:${relationLovNameFieldCode}`) || record?.get(`${code}:name`) || record?.get(`${code}:real_name`);
            content = (
              <span className="header-item-text">
                <span>{text || '-'}</span>
              </span>
            );
          }
          // 几个特殊显示字段
          if (endCode === 'state_id') {
            const text = record?.get(`${code}:${relationLovNameFieldCode}`);
            const color = record?.get(`${code}:color`) || '#8c8c8c';
            content = (
              <div className="header-item-tag">
                <StatusTag color={color}>{text || '-'}</StatusTag>
              </div>
            );
          }
          if (['priority_id', 'impact_level_id', 'urgency_id'].includes(endCode)) {
            const text = record?.get(`${code}:${relationLovNameFieldCode}`);
            const color = record?.get(`${code}:color`) || '#2979FF';
            const mode = endCode === 'priority_id' ? 'icon' : 'border';
            content = (
              <div className="header-item-tag-icon">
                <StatusTag mode={mode} color={color}>
                  {text || '-'}
                </StatusTag>
              </div>
            );
          }

          if (
            [
              'caller_id',
              'assignee_person_id',
              'closed_by',
              'coordinator_id',
              'submitted_by',
              'last_updated_by',
              'created_by',
            ].includes(endCode) || relationObjectCode === 'IAM_USER'
          ) {
            const id = record?.get(code);
            const text = record?.get(`${code}:real_name`);
            const imageUrl = record?.get(`${code}:image_url`);
            content = (
              <span className="header-item-text">
                <AvatarTooltip id={id}>
                  <YqAvatar src={imageUrl} size={22}>
                    {text}
                  </YqAvatar>
                </AvatarTooltip>
                <span style={{ marginLeft: '4px' }}>{text || '-'}</span>
                <WechatWorkChatIcon personId={id} />
              </span>
            );
          }

          return (
            <span className="header-item">
              <span className="header-item-title"> {label || name}：</span>
              {record?.get(code) ? content : '-'}
            </span>
          );
        })}
      </div>
    );
  }

  // 获取富文本的附加信息
  function getExtraInfo() {
    try {
      const content = record?.get(descriptionField);
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  }

  // 渲染音频文件
  function renderAudioArea(audios) {
    try {
      if (audios?.length === 0) return null;
      return (
        <div className="reply-audio">
          {audios.map((i, index) => {
            const className = classnames({
              'yq-audio': true,
              'yq-audio-last': index === audios?.length - 1,
            });
            return (
              <ExternalComponent className={className} system={{ scope: 'itsm', module: 'YqAudio' }} fileInfo={i} />
            );
          })}
        </div>
      );
    } catch {
    //
    }
  }

  // 描述
  const renderDescription = useCallback(() => {
    const description = record?.get(descriptionField);
    const fileList = renderFileList();
    const descriptionExtraInfo = getExtraInfo();
    const { audios = [] } = descriptionExtraInfo || {};
    if ((!description || getRichTextIsNull(description)) && !fileList && audios.length === 0) return null;
    if (!showDescription && !fileList && audios.length === 0) return null;

    let htmlContent;
    try {
      const jsonData = JSON.parse(description);
      htmlContent = getHtml(jsonData);
    } catch (e) {
      htmlContent = description;
    }

    return (
      <>
        <div className={`${prefixCls}-description`}>
          {showDescription && <>
            {!getRichTextIsNull(htmlContent) && <RichTextPreview
              ocrFlag
              data={htmlContent}
              htmlData={description?.includes('<p data-json=') ? description : ''}
              preview
              minHeight={1}
            />}
            {renderAudioArea(audios)}
          </>}
          {fileList}
        </div>
        <TranslateArea
          name={descriptionField}
          intl={intl}
          formDataSet={formDataSet}
          message={intl.formatMessage({ id: 'itsm.common.translate.progress' })}
        />
      </>
    );
  }, [record?.get(descriptionField)]);

  function renderFileList() {
  // 描述上的附件
    const { attachments } = getExtraInfo();
    const attachmentsJSON = record?.get(attachmentsField);
    const fileList = [];
    try {
      if (showDescription) {
        fileList.push(...attachments);
      }
      if (showAttachments) {
        fileList.push(...JSON.parse(attachmentsJSON));
      }
    } catch (e) {
    //
    }

    const length = fileList.length;
    return length > 0 && (
      <div className={`${prefixCls}-fileList`}>
        {fileList?.map((i, index) => {
          // 邮件提单的附件列表是fileKey字符串数组
          const file = typeof i === 'string' ? { fileKey: i } : i;
          return (
            <FileItem
              data={file}
              isLast={(length - 1) === index}
              tenantId={tenantId}
              intl={intl}
              prefixCls={`${prefixCls}-file`}
            />
          );
        })}
      </div>
    );
  }

  if (!record && modeType !== 'PREVIEW') {
    return (
      <div className={`${prefixCls}-loading`}>
        <Spin />
      </div>
    );
  }

  return (
    <div className={prefixCls}>
      <div className={`${prefixCls}-top`}>
        <div>
          {showFavorite && (
            <Favorite
              className={`${prefixCls}-top-favorite`}
              tenantId={tenantId}
              ticketId={ticketId}
              businessObjectCode={businessObjectCode}
              intl={intl}
            />
          )}
          <span className={`${prefixCls}-top-num`}>
            {`${record?.get(numberField) || '-'}`}
          </span>
          {/* 双击编号时不选中空格和标题 */}
          <span className={`${prefixCls}-top-empty`}> </span>
          <span className={`${prefixCls}-top-title`}>
            { record?.get(shortDescriptionField) || record?.get('shortDescription') || record?.get('short_description') || record?.get('req_item_id:item_id:variable_view_id:_variable')?.short_description || '-'}
          </span>
        </div>
        {
          record?.get('total_price') && (
            <div className={`${prefixCls}-top-totalPrice`}>
              {intl.formatMessage({ id: 'lcr.renderer.ticketHeader.total.price' })}
              <span className="unit">￥</span>
              <span className="totalPrice">{record?.get('total_price')}</span>
            </div>
          )
        }
        {!udmFlag && <Edit className={`${prefixCls}-top-edit`} />}
      </div>
      <TranslateArea
        name="short_description"
        intl={intl}
        formDataSet={formDataSet}
        style={{ marginBottom: 16 }}
        prefixStr={`${record?.get(numberField) || '-'} `}
        message={intl.formatMessage({ id: 'itsm.common.translate.progress' })}
      />
      {renderContent()}
      {renderDescription()}
    </div>
  );
});
