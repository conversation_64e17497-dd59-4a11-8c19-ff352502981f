@import '~choerodon-ui/lib/style/themes/default';

.ticket-header-renderer {
  &-fileList {
    margin-top: 8px;
  }

  &:hover {
    .ticket-header-renderer-top-edit {
      display: block;
    }
  }

  &-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    padding: 16px;
  }

  &-top {
    position: relative;

    margin-bottom: 12px;

    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: #12274d;

    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    &-totalPrice {
      font-size: 14px;
      font-weight: 400;
      color: #12274d;
      line-height: 20px;

      .totalPrice {
        font-size: 22px;
        font-weight: 600;
        color: @primary-color;
        line-height: 30px;
      }

      .unit {
        font-size: 14px;
        font-weight: 600;
        color: @primary-color;
        line-height: 30px;
      }
    }

    &-favorite {
      margin-right: 8px;

      color: #bfbfbf;

      cursor: pointer;
    }

    &-num {
      margin-right: 8px;
    }

    &-empty {
      user-select: none;
    }

    &-title {
      word-break: break-all;
    }

    &-edit {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 15;

      display: none;
      padding-left: 10px;

      background: #fff;

      .i-icon,
      .yqcloud-icon-park-wrapper {
        padding: 8px;

        color: @primary-color;

        background: @minor-color;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          color: #fff;

          background-color: @primary-6;
        }
      }
    }
  }

  &-content {
    margin-bottom: -2px; // 本来是12px 由于内部加了14px 最终变成-2px;

    .header-item {
      display: inline-flex;
      align-items: center;
      margin-right: 40px;
      margin-bottom: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #6b7285;

      &-tag {
        display: inline-block;

        vertical-align: text-bottom;

        &-icon {
          display: flex;
          align-items: center;

          font-weight: 400;
          font-size: 14px;
          line-height: 22px;
          color: #2b2d38;

          .yq-cmp-status-border-tag {
            line-height: 18px;
          }
        }
      }

      &-title {
        font-size: 14px;
        color: rgba(18, 39, 77, 0.65);
        white-space: nowrap;
      }

      &-text {
        display: flex;
        align-items: center;
        color: #12274d;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;

        .@{c7n-prefix}-avatar {
          display: flex;
          justify-content: center;
          align-items: center;

          .@{c7n-prefix}-avatar-string {
            font-size: 11px;
          }
        }
      }
    }
  }

  &-description {
    padding: 14px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(18, 39, 77, 0.85);
    word-break: break-all;

    background: #f7f9fc;
    border-radius: 4px;

    &-null {
      color: #bfbfbf;
    }

    &-hasFile {
      padding-bottom: 16px;
    }

    .reply-audio {
      padding-bottom: 6px;

      .yq-audio-last {
        margin-bottom: 0;
      }
    }
  }

  table,
  tr,
  td,
  th {
    border-collapse: collapse;

    border: 1px #bfbfbf solid;
  }

  table td {
    min-width: 2em;
    padding: 0.4em;
  }

  &-modal {
    .lc-page-loader-upload {
      &-btn {
        position: relative;
        top: 1px;
      }
    }
  }

  &-repeat {
    background: linear-gradient(135deg, #fafffd 0%, #f5fcff 52%, #fbf7ff 100%);
    border-radius: 4px;
    border: 1px solid rgba(203, 210, 220, 0.25);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    margin-bottom: 8px;

    &-left {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    &-img {
      width: 24px;
      height: 24px;
    }

    &-title {
      font-size: 14px;
      font-weight: bold;
      color: #12274d;
      margin: 0 16px 0 12px;
    }

    &-info {
      color: rgba(18, 39, 77, 0.85);
      margin-right: 16px;
      font-size: 14px;
    }

    &-close {
      cursor: pointer;
    }
  }
}
