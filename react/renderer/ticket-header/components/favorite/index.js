import React from 'react';
import { Icon } from '@zknow/components';
import clx from 'classnames';
import { Tooltip } from 'choerodon-ui/pro';
import useFavorite from '../../hooks/useFavorite';

/**
 * 收藏 Icon 组件
 */
export function Favorite({
  className,
  tenantId,
  ticketId,
  businessObjectCode,
  intl,
}) {
  const { isFavorite, toggleFavorite } = useFavorite({
    tenantId,
    ticketId,
    businessObjectCode,
  });

  return <span className={clx('lc-ticket-header-favorite-root', className)}>
    <Tooltip title={isFavorite ? intl?.formatMessage({ id: 'lcr.renderer.ticketHeader.unfavorite' }) : intl?.formatMessage({ id: 'lcr.renderer.ticketHeader.favorite' })}>
      <Icon
        type="star"
        size={16}
        strokeLinecap="square"
        onClick={toggleFavorite}
        {...(isFavorite ? { fill: '#F4D12D', theme: 'filled' } : {})}
      />
    </Tooltip>
  </span>;
}
