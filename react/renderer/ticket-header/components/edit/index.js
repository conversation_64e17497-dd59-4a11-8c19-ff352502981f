import React, { useEffect, useState, useContext, useMemo } from 'react';
import clx from 'classnames';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import { Modal, Form, message } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import Store from '../../stores';
import {
  renderField,
  getActionFormDataSet,
  runAction,
} from '@/renderer/utils/utils.js';

const modalKey = Modal.key();

/**
 * 编辑单据头组件
 */
function Edit({
  className,
}) {
  const context = useContext(Store);
  const {
    tenantId,
    formDataSet,
    ticketId,
    intl,
    prefixCls,
    viewRecord,
    businessObjectCode,
    dsFieldList,
    businessObjectFieldDataSet,
    formConfigRecord,
    getTitleFields,
    getField,
    mode,
  } = context;
  // 是否可编辑
  const [isEditable, setIsEditable] = useState(false);
  const fieldCodeList = useMemo(() => {
    const fields = [];
    const allFields = getTitleFields();
    ['shortDescription', 'description', 'attachments'].forEach(key => {
      const { key: _field, field: config } = allFields.find(i => i.key === key) || {};
      const field = getField(_field);
      if (field && field.indexOf(':') === -1) {
        fields.push({ field, config });
      }
    });
    return fields;
  }, [viewRecord]);

  const checkIsEditable = async () => {
    if (!ticketId) return;
    if (mode === 'PREVIEW') return;
    try {
      const editablePerson = viewRecord?.get('widgetConfig.ticketHeaderEditablePerson') || [];
      const editableRole = JSON.parse(viewRecord?.get('widgetConfig.ticketHeaderEditableRole')) || [];

      const data = {
        specialList: editablePerson?.map((v) => ({
          receiverFieldId: v?.id,
          receiverFieldFullId: v?.fullId,
          receiverField: v?.path,
        })),
        roleIdList: editableRole?.map((v) => v?.id),
        skipOrgAdmin: true,
      };

      const res = await axios.post(
        `/itsm/v1/${tenantId}/resolution/${businessObjectCode}/${ticketId}/check`,
        data
      );
      if (res?.failed) {
        setIsEditable(false);
      } else {
        res && businessObjectFieldDataSet.query();
        setIsEditable(res?.editFlag);
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error(e);
      setIsEditable(false);
    }
  };
  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      checkIsEditable();
    }
  }, [ticketId, formDataSet?.status]);

  function handleEdit() {
    const transformDataSet = getActionFormDataSet({
      fieldList: fieldCodeList.map(i => i.field),
      fieldsConfig: fieldCodeList,
      formDataSet,
      dsFieldList,
      businessObjectFieldDataSet,
      formConfigRecord,
      tenantId,
      intl,
    });
    // 动作字段的表单
    const FieldFormView = (
      <Form record={transformDataSet.current} labelWidth="auto" className="lc-model-detail-form">
        {fieldCodeList.map((i, index) => renderField({
          fieldItem: i,
          formDs: transformDataSet,
          formDataSet,
          dsFieldList,
          intl,
          businessObjectFieldDataSet,
          businessObjectCode,
          autoFocus: index === 0,
          tenantId,
        }))}
      </Form>
    );
    Modal.open({
      title: intl.formatMessage({ id: 'zknow.common.button.modify' }),
      children: FieldFormView,
      key: modalKey,
      drawer: false,
      style: { width: '800px' },
      bodyStyle: { maxHeight: '55vh', overflow: 'scroll' },
      className: `${prefixCls}-modal`,
      destroyOnClose: true,
      onOk: async () => {
        return runAction({
          transformDataSet,
          formDataSet,
          fieldCodeList: fieldCodeList.map(i => i.field),
          intl,
        });
      },
      onCancel: () => {
        formDataSet.reset();
        return true;
      },
    });
  }

  return isEditable && fieldCodeList?.length ? <div className={clx('lc-ticket-header-edit-root', className)}>
    <Icon type="write" size={16} onClick={handleEdit} />
  </div> : null;
}

export default observer(Edit);
