/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2021-04-07 10:32:08
 * @Description: 展示附件
 */
import React from 'react';
import { Icon, YqPreview, ExternalComponent } from '@zknow/components';
import axios from 'axios';
import { message, Modal, Tooltip } from 'choerodon-ui/pro';
import './index.less';

const modalKey = Modal.key();

function FileItem({
  data,
  tenantId,
  prefixCls,
  intl,
}) {
  function getFileName(fileKey) {
    if (!fileKey) return '';
    const nameStrList = fileKey?.split('@');
    return nameStrList?.length ? nameStrList[nameStrList.length - 1] : fileKey;
  }

  const fileName = getFileName(data?.fileKey) || '';

  // 下载附件
  const downloadFile = async () => {
    const res = await axios.get(`/hfle/yqc/v1/${tenantId}/files/download-by-key?fileKey=${encodeURIComponent(data.fileKey)}`, { responseType: 'blob' });
    if (res.failed) {
      message.error(res.message);
    } else {
      const blobUrl = window.URL.createObjectURL(res);
      if ('msSaveOrOpenBlob' in navigator) {
        // 判断是ie的浏览器，调用ie文件下载的方法
        const blob = new Blob([res], { type: 'application/pdf' });
        window.navigator.msSaveOrOpenBlob(blob, fileName);
      } else {
        downloadPdfWithUrl(blobUrl, fileName);
      }
    }
  };

  // 预览附件
  const previewFile = () => {
    const udmFileTenantId = data?.udmTenantId || data?.response?.udmTenantId;
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.ticketHeader.file.preview' }),
      children: (
        <YqPreview fileKey={data.fileKey} udmTenantId={udmFileTenantId} />
      ),
      destroyOnClose: true,
      fullScreen: true,
      footer: null,
    });
  };

  const downloadPdfWithUrl = (blobUrl, _fileName) => {
    const downloadName = _fileName;
    const a = document.createElement('a');
    a.style.display = 'none';
    a.download = downloadName;
    a.href = blobUrl;
    a.click();
    document.body.removeChild(a);
  };

  const renderFileSize = (size) => {
    if (!size) {
      return size;
    }
    if (size / (1024 * 1024) > 1) {
      return `${Math.round((size / (1024 * 1024)) * 100) / 100} MB`;
    } else {
      return `${Math.round((size / 1024) * 100) / 100} KB`;
    }
  };

  return (
    <div className={prefixCls}>
      <div className={`${prefixCls}-left`}>
        <ExternalComponent
          system={{
            scope: 'knowledge',
            module: 'knowledge-icon',
          }}
          style={{ marginRight: 8 }}
          fileType={fileName?.split('.')?.[fileName?.split('.')?.length - 1]}
        />
        <span style={{ marginRight: 8, wordWrap: 'break-word', wordBreak: 'break-all' }} onClick={() => previewFile()}>
          {fileName}
          {data?.fileSize ? <span className={`${prefixCls}-left-size`}>({renderFileSize(data?.fileSize)})</span> : null}
        </span>
      </div>
      <div className={`${prefixCls}-right`} style={{ whiteSpace: 'nowrap', marginLeft: '8px' }}>
        <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.download' })}>
          <Icon type="download" style={{ marginRight: 12 }} onClick={() => downloadFile()} />
        </Tooltip>
        <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.preview' })}>
          <Icon type="preview-open" onClick={() => previewFile()} />
        </Tooltip>
      </div>
    </div>
  );
}

export default FileItem;
