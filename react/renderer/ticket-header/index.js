import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import MainView from './MainView';

export default inject('AppState')(formatterCollections({
  code: ['zknow.common', 'lcr.renderer', 'itsm.common'],
})((props) => (
  <StoreProvider {...props}>
    <MainView {...props} />
  </StoreProvider>
)));

/* externalize: TicketHeaderRenderer */
