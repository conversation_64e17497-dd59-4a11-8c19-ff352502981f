import { useState, useMemo, useEffect, useCallback } from 'react';
import { message } from 'choerodon-ui/pro';
import axios from 'axios';

/**
 * 收藏逻辑
 */
export default function useFavorite({
  tenantId,
  ticketId,
  businessObjectCode,
}) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 查询单据是否被收藏过
  const getFavorite = useCallback(async () => {
    if (!tenantId || !ticketId || !businessObjectCode) return;
    const url = `/itsm/v1/${tenantId}/sc_ticket_collects/check?ticketId=${ticketId}&businessObjectCode=${businessObjectCode}`;
    if (!url) return;
    const res = await axios.get(url);
    if (res?.failed) {
      message.error(res.message);
    } else {
      setIsFavorite(res);
    }
  }, [tenantId, ticketId, businessObjectCode]);

  // 切换收藏
  const toggleFavorite = useCallback(async () => {
    if (!tenantId || !ticketId || !businessObjectCode || isLoading) return;
    setIsLoading(true);
    const url = `/itsm/v1/${tenantId}/sc_ticket_collects?ticketId=${ticketId}&businessObjectCode=${businessObjectCode}`;
    const res = await (isFavorite ? axios.delete : axios.post)(url);
    if (res?.failed) {
      message.error(res.message);
    } else {
      await getFavorite();
    }
    setIsLoading(false);
  }, [isFavorite, tenantId, ticketId, businessObjectCode, isLoading]);

  // 第一次加载获取收藏flag
  useEffect(() => {
    if (tenantId && ticketId && businessObjectCode) {
      getFavorite();
    }
  }, [tenantId, ticketId, businessObjectCode]);

  return { isFavorite, toggleFavorite };
}
