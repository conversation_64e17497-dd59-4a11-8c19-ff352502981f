@import '~choerodon-ui/lib/style/themes/default';

.participants-renderer {
  padding: 16px 0;
  .@{c7n-prefix}-avatar-string {
    transform: scale(0.875) translateX(-50%) !important;
    font-size: 15px !important;
  }
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-dropdown-menu {
    .@{c7n-prefix}-menu-item-selected {
      background-color: @primary-6 !important;
      color: #12274d !important;
    }
  }
  &-header {
    padding: 0 16px;
    .header-top {
      justify-content: space-between;
      margin-bottom: 8px;

      .flex-center;

      &-title {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #12274d;
      }
      &-icon {
        .flex-center;
        .filter-icon {
          padding: 8px;
          line-height: 1;
          height: 32px;
          color: @primary-color;
          background: @minor-color;
          border-radius: 4px;
          cursor: pointer;
          &:hover {
            color: #fff;
            background-color: @primary-6;
          }
        }
      }
    }
    .header-bottom {
      margin-bottom: 9px;

      color: #12274d;

      .flex-center;

      &-item {
        margin-right: 24px;
      }
    }
  }
  // 滚动内容
  &-content {
    .participants-card {
      justify-content: space-between;
      margin: 2px 0;
      padding: 0 16px;
      position: relative;
      overflow: hidden;

      .flex-center;

      &:hover {
        background: #f2f3f5;
        .participants-card-right {
          display: flex;
          position: absolute;
          right: 5px;
        }
      }

      &-divider {
        margin: 14px 16px;

        border-bottom: 1px solid rgba(203, 210, 220, 0.5);
      }

      &-left {
        .flex-center;
        overflow: hidden;
      }
      &-right {
        display: none;
        justify-content: flex-end;
        align-items: center;
        width: 1px;

        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #f43636;
        white-space: nowrap;

        cursor: pointer;

        .content {
          display: contents;
          align-items: center;

          background: #f2f3f5;
          .right-icon {
            background: #f2f3f5;
          }
          &-text {
            padding-left: 8px;

            background: #f2f3f5;
          }
        }
      }
      &-avatar {
        user-select: none;
        .c7n-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 26px;
          height: 26px;

          background-color: #2979ff;
          border-radius: 6px;
        }
      }
      &-name {
        margin-right: 8px;
        padding-left: 4px;

        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: #12274d;
        white-space: nowrap;
      }
      &-email {
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(18, 39, 77, 0.65);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    &-empty {
      &-buttons {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.participants-card-tooltip {
  padding: 0 !important;

  background-color: #fff;
  .c7n-popover-arrow {
    display: none;
  }
  .c7n-popover-inner-content {
    padding: 0;
  }
  &-main {
    width: 300px;
    .tooltip-top-divider {
      margin: 0 16px;
      border-bottom: 1px solid rgba(203, 210, 220, 0.5);
    }
    .tooltip-top {
      display: flex;
      align-items: center;
      padding: 12px 16px !important;

      background: white;

      .participants-card-avatar {
        margin-right: 6px !important;

        user-select: none;
        .c7n-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        &-icon {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 32px;
          height: 32px;

          background-color: #2979ff;
          border-radius: 6px;
        }
      }
      .participants-card-name {
        overflow: hidden;

        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        color: #12274d;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .tooltip-content {
      display: table;
      padding: 6px 16px !important;
      &-row {
        display: table-row;
      }
      &-cell {
        display: table-cell;
        padding: 6px 16px 6px 0;

        text-align: right;
        &-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 18px !important;
          color: rgba(18, 39, 77, 0.65);
          white-space: nowrap;
          text-shadow: none !important;
        }
        &-value {
          font-weight: 400;
          font-size: 14px !important;
          line-height: 18px;
          text-align: left;
          color: #12274d;
          word-break: break-word;
          text-shadow: none !important;
        }
      }
    }
  }
}
