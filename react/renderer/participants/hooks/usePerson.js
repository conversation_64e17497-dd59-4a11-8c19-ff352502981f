import axios from 'axios';
import { useState } from 'react';
import _ from 'lodash';

export default function usePerson({ tenantId }) {
  const [cache, setCache] = useState({});

  const getInitData = (groupId) => {
    const initData = { page: -1, isAll: false, data: [], date: new Date() };
    const max = 3 * 60 * 1000;
    if (cache[groupId] && new Date() - cache[groupId].date < max) {
      return cache[groupId];
    }
    return initData;
  };
  const getPerson = async ({ groupId, before = () => { }, after = () => { } }) => {
    if (!groupId) return false;
    const data = getInitData(groupId);
    if (data.isAll) return false;
    before();
    const nextPage = data.page + 1;
    const res = await axios.get(
      `/iam/yqc/${tenantId}/userGroups/queryUserForGroup?groupId=${groupId}&departmentPathFlag=true&page=${nextPage}&size=15`
    );
    if (!res.failed) {
      data.data = _.uniqBy(
        [
          ...data.data,
          ...res?.content?.map(({ id, imageUrl, realName, email, phone }) => ({
            id,
            imageUrl,
            realName,
            email,
            phone,
          })),
        ],
        'id'
      );
      data.totalElements = res.totalElements;
      data.page = nextPage;

      cache[groupId] = data;
      if (data.totalElements === data.data?.length || res?.content?.length === 0) {
        data.isAll = true;
      }
      setCache({ ...cache });
      after();
      return true;
    } else {
      after();
    }
  };
  return { cache, getPerson };
}
