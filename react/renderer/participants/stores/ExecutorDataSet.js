const ExecutorDataSet = ({
  userLovDataSet,
  groupLovDataSet,
  url,
}) => {
  return {
    autoQuery: false,
    paging: false,
    transport: {
      read: {
        url,
        method: 'get',
        transformResponse(response) {
          try {
            const res = JSON.parse(response);
            if (res?.failed) {
              return response;
            } else {
              const GROUP = res.group.map(item => {
                item.name = item.participant;
                item.type = 'GROUP';
                item.primaryIdKey = item.id;
                item.primaryKey = item.participantId;
                item.id = item.participantId;
                return item;
              });
              const USER = res.user.map(item => {
                item.realName = item.participant;
                item.real_name = item.participant;
                item.name = item.participant;
                item.type = 'USER';
                item.primaryIdKey = item.id;
                item.primaryKey = item.participantId;
                item.id = item.participantId;
                return item;
              });
              userLovDataSet.loadData([{ USER }]);
              userLovDataSet.setState('count', res?.userTotal);
              groupLovDataSet.loadData([{ GROUP }]);
              groupLovDataSet.setState('count', res?.groupTotal);

              if (USER.length > 0 && GROUP.length > 0) {
                return [...USER, { type: 'DIVIDER' }, ...GROUP];
              }
              return [...USER, ...GROUP];
            }
          } catch (e) {
            return response;
          }
        },
      },
    },
  };
};

export default ExecutorDataSet;
