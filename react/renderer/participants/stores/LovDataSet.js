import { toJS } from 'mobx';
import { getQueryParams } from '@zknow/utils';
import { transformResponse, transformField } from '@/utils/lovConfig';

export default function LovDataSet({
  type = 'USER',
  ticketId,
  businessObjectCode,
  participantGroupLovId,
  participantUserLovId,
  participantUserCondition,
  participantGroupCondition,
  tenantId,
  intl,
  formDataSet,
}) {
  const userCondition = participantUserCondition ? toJS(participantUserCondition) : [];
  const groupCondition = participantGroupCondition ? toJS(participantGroupCondition) : [];

  const config = {
    lovDefineAxiosConfig: lovCode => ({
      url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
      method: 'GET',
      transformResponse: data => transformResponse(data, data?.name, (map, f) => transformField(map, f), intl, tenantId),
    }),
    lovQueryAxiosConfig: (lovCode, lovConfig = {}, { data, params }) => {
      lovConfig.method = 'POST';

      return {
        url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
        method: 'POST',
        data: {
          conditions: [
            ...(type === 'USER' ? userCondition : groupCondition),
            { condition: 'AND', filters: [{ condition: 'AND', filter: 'is not', widgetType: 'Lov', componentType: 'Lov', field: 'id', fieldValue: ticketId }] },
          ],
          params: {
            ...(formDataSet?.current?.toData?.() || {}),
            ...getQueryParams(data),
            __page_params: data?.__page_params,
          },
        },
        params,
        transformResponse: (originData) => {
          try {
            const jsonData = JSON.parse(originData);

            return {
              ...jsonData,
              content: jsonData?.content?.map(item => {
                return {
                  ...item,
                  primaryKey: item.id,
                };
              }) || [],
            };
          } catch (error) {
            return [];
          }
        },
      };
    },
  };

  function getExtraProps() {
    if (type === 'USER' && participantUserLovId) {
      return config;
    }
    if (type === 'GROUP' && participantGroupLovId) {
      return config;
    }
    return {};
  }

  const extraProps = getExtraProps();

  return {
    autoCreate: true,
    autoQuery: false,
    fields: [
      {
        name: type,
        lovCode: type === 'USER' ? (participantUserLovId || 'AT_PERSON') : (participantGroupLovId || 'PARTICIPANT_GROUP'),
        type: 'object',
        lovPara: type === 'USER' ? {
          taskId: ticketId,
          businessObjectCode,
        } : {},
        ...extraProps,
      },
    ],
  };
}
