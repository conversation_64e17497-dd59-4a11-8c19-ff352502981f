import React, { createContext, useMemo, useEffect, useState } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { DataSet } from 'choerodon-ui/pro';
import axios from 'axios';
import pick from 'lodash/pick';
import ExecutorDataSet from './ExecutorDataSet';
import LovDataSet from './LovDataSet';
import usePerson from '../hooks/usePerson';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')(
    observer((props) => {
      const { AppState, intl, viewDataSet, formDataSet, children, viewRecord, ticketId } = props;
      const [defaultData, setDefaultData] = useState({});
      const { businessObjectId } = viewDataSet?.current?.toData() || {};

      const intlPrefix = 'lc.components.participants';
      const prefixCls = 'participants-renderer';

      const tenantId = AppState.currentMenuType.organizationId;
      const taskId = formDataSet?.current?.get('id');
      const businessObjectCode = viewDataSet?.current?.get('businessObjectCode')?.toLowerCase();
      const participantUserLovId = viewRecord?.get('widgetConfig.participantUserLovId');
      const participantGroupLovId = viewRecord?.get('widgetConfig.participantGroupLovId');
      const participantUserCondition = viewRecord?.get('widgetConfig.participantUserCondition');
      const participantGroupCondition = viewRecord?.get('widgetConfig.participantGroupCondition');

      const url = `/itsm/v1/${tenantId}/${businessObjectCode}/${taskId}/participants`;

      const getDataWithCache = async () => {
        const key = businessObjectId;
        const cache = AppState?.customConfig[key];
        if (cache) {
          return cache;
        }
        if (!ticketId || !tenantId) return;
        const res = await axios.get(
          `/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`
        );
        AppState?.setCustomConfig(key, res);
        AppState?.setCustomConfig(`${businessObjectId}-${ticketId}`, res);
        return res;
      };

      const initConfig = async () => {
        const res = await getDataWithCache();
        setDefaultData(pick(res, ['addParticipantFlag', 'deleteParticipantFlag']));
      };

      useEffect(() => {
        if (ticketId && businessObjectId) {
          initConfig();
        }
      }, [ticketId, businessObjectId]);

      // 参与人
      const userLovDataSet = useMemo(() => new DataSet(LovDataSet({
        type: 'USER',
        ticketId: taskId,
        businessObjectCode: viewDataSet?.current?.get('businessObjectCode'),
        participantGroupLovId,
        participantUserLovId,
        participantUserCondition,
        tenantId,
        intl,
        formDataSet,
      })), [
        taskId,
        tenantId,
        businessObjectCode,
        participantGroupLovId,
        participantUserLovId,
      ]);

      // 参与组
      const groupLovDataSet = useMemo(() => new DataSet(LovDataSet({
        type: 'GROUP',
        participantGroupLovId,
        participantGroupCondition,
        participantUserLovId,
        tenantId,
        intl,
        ticketId: taskId,
        formDataSet,
      })), [
        taskId,
        tenantId,
        participantGroupLovId,
        participantUserLovId,
      ]);

      const executorDataset = useMemo(
        () => new DataSet(
          ExecutorDataSet({
            userLovDataSet,
            groupLovDataSet,
            url,
          })
        ),
        [url]
      );
      const { cache, getPerson } = usePerson({ tenantId });

      const value = {
        ...props,
        taskId,
        intlPrefix,
        prefixCls,
        tenantId,
        userLovDataSet,
        groupLovDataSet,
        executorDataset,
        url,
        formDataSet,
        cache,
        getPerson,
        defaultData,
      };

      return <Store.Provider value={value}>{children}</Store.Provider>;
    })
  )
);
