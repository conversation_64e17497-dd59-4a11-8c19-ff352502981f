import React, { useContext, useEffect, useMemo, useState } from 'react';
import { Spin, Tooltip } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { Icon, YqAvatar } from '@zknow/components';
import Store from '../stores';
import './PersonInGroup.less';
import useScrollToBottom from '../hooks/useScrollToBottom';

const PersonInGroup = ({ record }) => {
  const { intl, intlPrefix, cache, getPerson } = useContext(Store);
  const [loading, setLoading] = useState(false);
  const query = async () => {
    await getPerson({
      groupId: record.get('id'),
      before: () => setLoading(true),
      after: () => setLoading(false),
    });
  };

  useEffect(() => {
    query();
  }, []);
  const ref = useScrollToBottom({
    offset: 20,
    callback: query,
  });

  const clx = (c) => `participants-person-in-group${c}`;

  const data = useMemo(() => cache[record.get('id')]?.data || [], [cache]);

  return (
    <Spin spinning={loading}>
      <div className={clx('')}>
        <div className={clx('-header')}>
          <div className={clx('-header-top')}>
            <div className={clx('-header-top-left')}>
              <div className={clx('-header-top-left-avatar')}>
                <Icon type="PeoplesTwo" theme="outline" size="16" fill="#ffffff" />
              </div>
              {record.get('name')}
            </div>
            <div className={clx('-header-top-right')}>
              {intl.formatMessage({ id: 'lcr.components.desc.lc.components.participants.group.manager', defaultMessage: '负责人' })}
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              {'：'}
              <span style={{ color: '#12274D' }}>{record.get('ownerName') || '-'}</span>
            </div>
          </div>
          {record?.get('groupDescription') && (
            <div className={clx('-header-bottom')}>
              {intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' })}
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              {'：'}
              {record?.get('groupDescription')}
            </div>
          )}
        </div>
        {data?.length ? <div className={clx('-divider')} /> : null}
        {data?.length ? <div ref={ref} className={clx('-content')}>
          {data.map((v) => {
            const { id, imageUrl, realName, email, phone } = v;
            return (
              <div key={id} className={clx('-content-item')}>
                <span className={clx('-content-item-left')}>
                  <YqAvatar src={imageUrl} size={26}>
                    {realName}
                  </YqAvatar>
                  <span className={clx('-content-item-left-name')} title={realName}>
                    {realName || '-'}
                  </span>
                  <Tooltip placement="top" title={email}>
                    <span className={clx('-content-item-left-email')} title={email}>
                      {email || '-'}
                    </span>
                  </Tooltip>
                </span>
                <span className={clx('-content-item-right')}> {phone || '-'}</span>
              </div>
            );
          })}
        </div> : null}
      </div>
    </Spin>
  );
};

export default observer(PersonInGroup);
