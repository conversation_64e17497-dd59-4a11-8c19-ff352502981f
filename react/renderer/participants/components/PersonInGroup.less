@import '~choerodon-ui/lib/style/themes/default';

.participants-person-in-group {
  display: flex;
  flex-direction: column;
  width: 380px;
  max-height: 380px;
  &-divider {
    margin: 0 16px;

    border-bottom: 1px solid rgba(203, 210, 220, 0.5);
  }
  &-header {
    padding: 0.12rem 0.16rem;
    &-top {
      display: flex;
      justify-content: space-between;
      align-items: center;

      &-left {
        display: flex;
        align-items: center;

        font-weight: 500;
        color: #12274d;
        &-avatar {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 32px;
          height: 32px;
          margin-right: 0.06rem;

          background: #2979ff;
          border-radius: 6px;
        }
      }
      &-right {
        font-size: 13px;
        line-height: 22px;
        color: rgba(18, 39, 77, 0.65);
      }
    }
    &-bottom {
      margin: .08rem 0 -.04rem;
      line-height: 22px;
      color: rgba(18, 39, 77, 0.65);
      font-size: 13px;
      box-shadow: none !important;
    }
  }

  &-content {
    height: 100%;
    max-height: 250px;
    padding: 6px 0 10px 0;
    overflow: auto;
    &-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.06rem 0.16rem;
      &-left {
        display: flex;
        align-items: center;
        max-width: calc(100% - 88px);
        .@{c7n-prefix}-avatar {
          min-width: 0.26rem;
        }
        .@{c7n-prefix}-avatar-string {
          font-size: 13px;
        }
        &-name {
          display: inline-block;
          margin: 0 .04rem;
          overflow: hidden;
          width: 60px;
          color: #12274d;
          white-space: noWrap;
          text-overflow: ellipsis;
        }
        &-email {
          display: inline-block;
          width: 150px;
          overflow: hidden;

          font-size: 13px;
          color: rgba(18, 39, 77, 0.65);
          white-space: noWrap;
          text-overflow: ellipsis;
        }
      }
      &-right {
        display: inline-block;
        max-width: 84px;

        font-size: 0.13rem;
        color: rgba(18, 39, 77, 0.65);
        white-space: noWrap;
      }
    }
  }
}
