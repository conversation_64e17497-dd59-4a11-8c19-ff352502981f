import React, { useContext, useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { TextField, Dropdown, Menu, message, Lov, Tooltip, Button } from 'choerodon-ui/pro';
import { Popover } from 'choerodon-ui';
import { Icon, YqAvatar, Empty } from '@zknow/components';
import axios from 'axios';
import { Permission } from '@yqcloud/apps-master';
import classnames from 'classnames';
import AvatarTooltip from '@/components/avatar-tooltip';
import PersonInGroup from './components/PersonInGroup';
import Store from './stores';

import './index.less';
import WechatWorkChatIcon from '../wechat-work-chat-icon';

const Participants = () => {
  const {
    intl,
    intlPrefix,
    prefixCls,
    formDataSet,
    userLovDataSet,
    groupLovDataSet,
    executorDataset,
    activeKey,
    url,
    taskId,
    defaultData,
    udmFlag,
  } = useContext(Store);

  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();
  const userLovRef = useRef();
  const groupLovRef = useRef();
  const { addParticipantFlag, deleteParticipantFlag } = defaultData;

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!taskId) {
      // NOTE: undefined 目前针对于门户， participants拷过来的时间原因不敢删
      if (activeKey === 'participants' || activeKey === undefined) {
        executorDataset.query();
      }
    }
  }, [activeKey, formDataSet?.status, taskId]);

  async function handleDeleteParticipants(ids = [], needMessage = true) {
    if (ids?.length === 0) { return; }
    const res = await axios.delete(url, { data: JSON.stringify(ids) });
    if (res?.failed) {
      message.error(res?.message);
    } else {
      if (needMessage) {
        message.success(intl.formatMessage({ id: 'zknow.common.success.delete', defaultMessage: '删除成功' }));
        formDataSet.current?.setState('dynamicRefreshCount', Math.round(Math.random() * 10000));
      }
      await executorDataset.query();
    }
  }

  // 添加参与人
  async function handleAddParticipants({ type, data }) {
    const postData = data?.map((i) => {
      return {
        participantId: i.id,
        type,
      };
    }) || [];
    const res = await axios.post(url, JSON.stringify(postData));
    if (res?.failed) {
      message.error(res?.message);
    } else {
      message.success(intl.formatMessage({ id: 'zknow.common.success.save', defaultMessage: '保存成功' }));
      await executorDataset.query();
      formDataSet.current?.setState('dynamicRefreshCount', Math.round(Math.random() * 10000));
    }
  }

  const typeContent = () => (
    <Menu className="yq-dynamic-main-dropdown-menu participants-renderer-dropdown-menu" selectable={false}>
      <Menu.Item>
        <div className="temp" onClick={() => { handleAddParticipantsClick({ type: 'USER' }); }}>
          {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.add.user', defaultMessage: '添加参与人' })}
        </div>
      </Menu.Item>
      <Menu.Item>
        <div className="temp" onClick={() => { handleAddParticipantsClick({ type: 'GROUP' }); }}>
          {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.add.group', defaultMessage: '添加参与组' })}
        </div>
      </Menu.Item>
    </Menu>
  );

  function handleAddParticipantsClick({ type }) {
    const ref = type === 'USER' ? userLovRef : groupLovRef;
    ref.current?.openModal();
  }

  // 渲染头部
  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: '7px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              executorDataset.setQueryParameter('param', e);
              executorDataset.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              executorDataset.setQueryParameter('param', e.target.value);
              executorDataset.query();
            }
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            executorDataset.setQueryParameter('param', value);
            executorDataset.query();
          }}
        />
      );
    }
    return (
      <div className="header-top">
        <Lov
          multiple
          type="button"
          style={{ display: 'none' }}
          ref={userLovRef}
          dataSet={userLovDataSet}
          name="USER"
          onChange={async (value = [], oldValue = []) => {
            const data = value === null ? [] : value;
            const oldData = oldValue === null ? [] : oldValue;
            const userList = userLovDataSet?.current?.toData()?.USER || [];
            const postData = [...userList, ...data];
            const newArr = [];
            const deleteArr = [];
            postData.forEach((item) => {
              if (!newArr?.map((i) => i.id).includes(item.id)) {
                newArr.push(item);
              }
            });
            // 找到需要删除的数据
            oldData.forEach((i) => {
              if (!value?.map((j) => j.id)?.includes(i.participantId)) {
                deleteArr.push(i);
              }
            });
            if (deleteParticipantFlag) {
              await handleDeleteParticipants(deleteArr?.map((i) => i.primaryIdKey), false);
            }
            handleAddParticipants({ type: 'USER', data: newArr });
          }}
        />
        <Lov
          multiple
          type="button"
          style={{ display: 'none' }}
          ref={groupLovRef}
          dataSet={groupLovDataSet}
          name="GROUP"
          tableProps={{
            selectionMode: 'rowbox',
          }}
          onChange={async (value = [], oldValue = []) => {
            const data = value === null ? [] : value;
            const oldData = oldValue === null ? [] : oldValue;
            const groupList = groupLovDataSet?.current?.toData()?.GROUP || [];
            const postData = [...groupList, ...data];
            const newArr = [];
            const deleteArr = [];
            postData.forEach((item) => {
              if (!newArr?.map((i) => i.id).includes(item.id)) {
                newArr.push(item);
              }
            });
            // 找到需要删除的数据
            oldData.forEach((i) => {
              if (!value?.map((j) => j.id)?.includes(i.participantId)) {
                deleteArr.push(i);
              }
            });
            await handleDeleteParticipants(deleteArr?.map((i) => i.primaryIdKey), false);
            handleAddParticipants({ type: 'GROUP', data: newArr });
          }}
        />
        <span className="header-top-title">
          {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.title', defaultMessage: '参与人/组' })}
        </span>
        <div className="header-top-icon">
          {addParticipantFlag && !udmFlag && <Permission service={['edit_participant']}>
            <Dropdown overlay={typeContent()}>
              <Icon className="filter-icon" type="add-one" size={16} />
            </Dropdown>
          </Permission>}
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  // 渲染提示内容
  function renderCardTooltip(record) {
    if (!record) return null;
    // const type = record.get('type');
    // 人员信息有新的卡片展示了
    const type = record.get('type');
    if (type === 'GROUP') {
      return <PersonInGroup record={record} />;
    }

    if (type === 'USER') {
      return null;
    }

    const name = type === 'USER' ? record.get('realName') : record.get('name');
    const titleCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-title': true,
    });
    const valueCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-value': true,
    });

    return (
      <div className="participants-card-tooltip-main">
        <div className="tooltip-top">
          <span className="participants-card-avatar">
            {type === 'USER' && (
              <YqAvatar src={record.get('imageUrl')} size={32}>
                {name}
              </YqAvatar>
            )}
            {type === 'GROUP' && (
              <div className="participants-card-avatar-icon">
                <Icon type="peoples" theme="outline" size="16" fill="#ffffff" />
              </div>
            )}
          </span>
          <span className="participants-card-name">{name}</span>
        </div>
        <div className="tooltip-top-divider" />
        <div className="tooltip-content">
          {type === 'USER' && [
            record?.get('phone') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.phone', defaultMessage: '手机号' })}</div>
                <div className={valueCLassName}>{record?.get('phone')}</div>
              </div>
            ),
            record?.get('email') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' })}</div>
                <div className={valueCLassName}>{record?.get('email')}</div>
              </div>
            ),
            record?.get('departmentName') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.department', defaultMessage: '部门' })}</div>
                <div className={valueCLassName}>{record?.get('departmentName')}</div>
              </div>
            ),
            record?.get('companyName') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.company', defaultMessage: '公司' })}</div>
                <div className={valueCLassName}>{record?.get('companyName')}</div>
              </div>
            ),
          ]}
          {type === 'GROUP' && [
            record?.get('ownerName') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>
                  {intl.formatMessage({ id: 'lcr.components.desc.lc.components.participants.group.manager', defaultMessage: '负责人' })}
                </div>
                <div className={valueCLassName}>{record?.get('ownerName')}</div>
              </div>
            ),
            record?.get('email') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.email', defaultMessage: '邮箱' })}</div>
                <div className={valueCLassName}>{record?.get('email')}</div>
              </div>
            ),
            record?.get('description') && (
              <div className="tooltip-content-row">
                <div className={titleCLassName}>{intl.formatMessage({ id: 'zknow.common.model.description', defaultMessage: '描述' })}</div>
                <div className={valueCLassName}>{record?.get('description')}</div>
              </div>
            ),
          ]}
        </div>
      </div>
    );
  }

  // 渲染每一项处理人处理组
  function renderCardItem(record) {
    if (!record) return null;
    const type = record.get('type');
    if (type === 'DIVIDER') {
      return <div key={`${type}:-`} className="participants-card-divider" />;
    }
    const name = type === 'USER' ? record.get('realName') : record.get('name');
    const popoverClassName = classnames('participants-card-tooltip', {
      'participants-card-tooltip-group': type === 'GROUP',
    });
    return (
      <div key={`${type}:${record.get('id')}`} className="participants-card">
        <div className="participants-card-left">
          <Popover
            hidden={false}
            overlayClassName={popoverClassName}
            placement="bottomLeft"
            content={renderCardTooltip(record)}
            trigger="hover"
            mouseEnterDelay={type === 'USER' ? 0.2 : 0.5}
          >
            <span
              style={{
                display: 'flex',
                alignItems: 'center',
                padding: '5px 0',
              }}
            >
              <span className="participants-card-avatar">
                {type === 'USER' && (
                  <AvatarTooltip id={record.get('participantUserId')}>
                    <YqAvatar src={record.get('imageUrl')} size={26}>
                      {name}
                    </YqAvatar>
                  </AvatarTooltip>
                )}
                {type === 'GROUP' && (
                  <div className="participants-card-avatar-icon">
                    <Icon type="PeoplesTwo" theme="outline" size="14" fill="#ffffff" />
                  </div>
                )}
              </span>
              <span className="participants-card-name">{name}</span>
              <WechatWorkChatIcon key={record.get('participantUserId')} personId={record.get('participantUserId')} />
            </span>
          </Popover>

          <span className="participants-card-email">{record.get('email')}</span>
        </div>
        {deleteParticipantFlag && !udmFlag && <Permission service={['edit_participant']}>
          <div
            className="participants-card-right"
            onClick={() => handleDeleteParticipants([record.get('primaryIdKey')])}
          >
            <span className="content">
              <Icon className="right-icon" type="delete" />
              <span className="content-text">{intl.formatMessage({ id: 'zknow.common.button.delete', defaultMessage: '删除' })}</span>
            </span>
          </div>
        </Permission>}
      </div>
    );
  }

  const renderEmpty = (
    <div className={`${prefixCls}-content-empty`}>
      <Empty
        description={intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.empty', defaultMessage: '暂无参与人/组' })}
        style={{
          padding: '20px 0 12px 0',
          paddingTop: '28px',
        }}
      />
      {!searching && (
        <div className={`${prefixCls}-content-empty-buttons`}>
          {addParticipantFlag && !udmFlag && <Button
            key="confirm"
            funcType="raised"
            color="primary"
            onClick={() => {
              handleAddParticipantsClick({ type: 'USER' });
            }}
          >
            {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.add.user', defaultMessage: '添加参与人' })}
          </Button>}
          {addParticipantFlag && !udmFlag && <Button
            key="cancel"
            funcType="raised"
            color="secondary"
            onClick={() => {
              handleAddParticipantsClick({ type: 'Group' });
            }}
          >
            {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.add.group', defaultMessage: '添加参与组' })}
          </Button>}
        </div>
      )}
    </div>
  );

  return (
    <div className={prefixCls}>
      <div className={`${prefixCls}-header`}>
        {renderHeader()}
        {!searching && executorDataset.length !== 0 && (
          <div className="header-bottom">
            <div className="header-bottom-item">
              {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.current.user', defaultMessage: '当前参与人：' })}
              {userLovDataSet?.getState('count')
                || executorDataset?.filter((i) => i.get('type') === 'USER')?.length
                || 0}
            </div>
            <div className="header-bottom-item">
              {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.participants.current.group', defaultMessage: '当前参与组：' })}
              {groupLovDataSet?.getState('count')
                || executorDataset?.filter((i) => i.get('type') === 'GROUP')?.length
                || 0}
            </div>
          </div>
        )}
      </div>
      <div className={`${prefixCls}-content`}>
        {executorDataset.length > 0
          ? executorDataset?.map(renderCardItem)
          : executorDataset.status === 'ready' && renderEmpty}
      </div>
    </div>
  );
};

export default observer(Participants);
