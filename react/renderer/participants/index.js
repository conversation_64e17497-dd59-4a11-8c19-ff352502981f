import React, { useMemo } from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import Participants from './Participants';

export default inject('AppState')(
  formatterCollections({
    code: 'lcr.renderer',
  })((props) => (
    <StoreProvider {...props}>
      <Participants />
    </StoreProvider>
  ))
);
/* externalize: ParticipantsRenderer */
