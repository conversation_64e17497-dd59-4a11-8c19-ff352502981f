import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Button, Icon } from '@zknow/components';
import { message, Menu, Modal } from 'choerodon-ui/pro';
import Actions from './actions';
import './index.less';

const modalKey = Modal.key();

function TicketBatchProcessing(props) {
  const { tenantId, tableDataSet, viewDataSet, config, feature, intl, currentTableBusinessObjectId } = props;
  const { icon = 'list', name, id, color = 'default', widgetConfig: { customConfig = [] } } = config;
  const actionListId = customConfig?.find(r => r.key === 'actionListId')?.value;
  const actionId = customConfig?.find(r => r.key === 'actionId')?.value;
  const skipCheckFlag = customConfig?.find(r => r.key === 'skipCheckFlag')?.value;
  const delayFlag = customConfig?.find(r => r.key === 'delayFlag')?.value;
  const placeholder = intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.title', defaultMessage: '批量处理' });

  function process() {
    const [view] = viewDataSet.toData();
    // 表格如果放在详情视图中，viewDataSet 就不再是当前表格的视图，所以这里获取的业务对象也是详情的业务对象而不是表格的
    //  所以从渲染时就要获取当前表格关联的业务对象 currentTableBusinessObjectId
    const businessObjectId = currentTableBusinessObjectId || view?.businessObjectId;
    // 后端【白】说viewid 实际没有作用，所以去详情的视图 id 也可以
    const viewId = view?.id;

    if (!businessObjectId) {
      return;
    } else if (!tableDataSet.selection || !tableDataSet.selected.length) {
      message.info(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.selection', defaultMessage: '请勾选单据' }));
      return;
    }

    Modal.open({
      title: name || placeholder,
      key: modalKey,
      children: (
        <Actions
          parentTableDataSet={tableDataSet}
          businessObjectId={businessObjectId}
          tenantId={tenantId}
          viewId={viewId}
          intl={intl}
          actionListId={actionListId}
          actionId={actionId}
          skipCheckFlag={skipCheckFlag}
          delayFlag={delayFlag}
        />
      ),
      className: 'lc-ticket-processing-modal',
    });
  }

  if (!tableDataSet?.selection) return null;

  return feature === 'table-action' ? (
    <Menu.Item
      key={id}
      className="c7n-menu-item lc-ticket-processing-item"
      onClick={process}
    >
      <span className="lc-ticket-processing-item-icon">{icon ? <Icon type={icon} /> : null}</span>
      {name || placeholder}
    </Menu.Item>
  ) : (
    <Button
      key={id}
      onClick={process}
      funcType="raised"
      color={color}
      icon={icon}
    >{name || placeholder}</Button>
  );
}

export default injectIntl(observer(TicketBatchProcessing));

/**
 * 提交传参：单据编号，乐观锁版本号，单据id，业务对象id
 */
