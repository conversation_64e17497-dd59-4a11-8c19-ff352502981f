import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { runInAction } from 'mobx';
import axios from 'axios';
import { Form, Select, DataSet, Progress, Spin, Modal } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { transformField } from '@/components/page-loader/stores/DataSetManager';
import WidgetField from '@/components/widget-field';
import Stores from './stores';
import './index.less';

const { Option } = Select;
const ACTION_FIELD_NAME = '_action_id';

const formatBoolValue = (value) => {
  const boolValue = {
    true: true,
    false: false,
  };
  const result = boolValue[value];
  return typeof boolValue[value] === 'boolean' ? result : value;
};

function Actions() {
  const {
    parentTableDataSet,
    actionsDataset,
    businessObjectFieldDataSet,
    tenantId,
    businessObjectId,
    modal,
    intl,
    viewId,
    intlPrefix,
    actionListId,
    actionId,
    skipCheckFlag,
    delayFlag,
  } = useContext(Stores);
  const [options, setOptions] = useState([]);
  const [listOptions, setListOptions] = useState([]);
  const [extraField, setExtraField] = useState([]);
  const [finished, setFinished] = useState(false);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState([]);
  const [total, setTotal] = useState(0);
  const [failedNum, setFailedNum] = useState(0);
  const [percent, setPercent] = useState(0);
  const [displayFailed, setDisplayFailed] = useState(false);

  useEffect(() => {
    (async () => {
      try {
        const res = await axios({
          url: `/lc/v1/${tenantId}/lc_actions/action_list/${businessObjectId}`,
          method: 'get',
        });
        const data = res?.failed || !Array.isArray(res) ? [] : res;
        setListOptions(data);
        if (actionListId) {
          const defaultActionList = data.find(r => r.id === actionListId);
          if (defaultActionList) {
            actionsDataset.current.set('action_list', defaultActionList.id);
            actionsDataset.setState('lockActionList', true);
            actionsDataset?.current?.init(ACTION_FIELD_NAME, undefined);
            const actions = (data || []).find(r => r.id === actionListId)?.actionList || [];
            setOptions(actions);
            if (actionId) {
              const defaultAction = actions.find(r => r.id === actionId);
              if (defaultAction) {
                actionsDataset.current.set(ACTION_FIELD_NAME, actionId);
                actionsDataset.setState('lockAction', true);
                handleChange(actionId, defaultAction);
              }
            }
          }
        }
      } catch (e) {
        setListOptions([]);
      }
    })();
  }, [tenantId, businessObjectId]);

  modal.handleOk(runAction);

  async function runAction() {
    if (!businessObjectId || !viewId) {
      return false;
    }
    try {
      const res = await actionsDataset.validate();
      if (res) {
        const [data] = actionsDataset.toJSONData();
        const records = parentTableDataSet.selected;
        const newData = records.map(record => ({
          id: record.get('id'),
          object_version_number: record.get('object_version_number'),
          code: record.get('number'),
          _status: 'update',
          ...data,
        }));
        setLoading(true);
        const response = await axios({
          url: `/lc/v1/${tenantId}/lc_actions/${businessObjectId}/${viewId}/batch?skipCheckFlag=${skipCheckFlag === 'true' ? 'true' : 'false'}&delayFlag=${delayFlag === 'true' ? 'true' : 'false'}`,
          method: 'post',
          data: newData,
        });
        if (response?.failed) {
          parentTableDataSet.query();
          return true;
        }
        // 后台执行，后面还需要补一个进度页面【益丰批量执行的优化】
        if (delayFlag === 'true') {
          Modal.info({
            title: intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.bk', defaultMessage: '后台执行中' }),
            children: (
              <div>{intl.formatMessage({
                id: 'lcr.renderer.desc.ticket.process.bk.info',
                defaultMessage: '批量执行动作已经在后台运行，可能需要较长一段时间，请稍后刷新页面查看',
              })}</div>
            ),
          });
          parentTableDataSet.query();
          return true;
        }
        const failed = Array.isArray(response) ? response : [];
        const percentNum = (1 - (failed.length / newData.length)).toFixed(2) * 100;
        setFailedNum(failed.length);
        setTotal(newData.length);
        setPercent(percentNum);
        setResult(failed);
        setFinished(true);
        setLoading(false);
        parentTableDataSet.query();
        modal.update({
          footer: null,
        });
        return false;
      } else {
        setLoading(false);
        return false;
      }
    } catch (e) {
      setLoading(false);
      throw new Error(e);
    }
  }

  function renderField(fieldItem) {
    const { field } = fieldItem;
    const record = actionsDataset.current;
    const fieldDataSet = new DataSet();
    const matchRecord = businessObjectFieldDataSet.find(r => r.get('code') === field);
    if (!matchRecord) return null;
    const matchField = matchRecord.toData();
    const widgetType = matchField.widgetType;
    const name = matchField.name;
    const placeHolder = matchField.placeholder;
    const fieldRecord = fieldDataSet.create(matchField);
    return (
      <WidgetField
        record={fieldRecord}
        widgetType={widgetType}
        label={name}
        key={field}
        name={field}
        code={field}
        intl={intl}
        formDs={actionsDataset}
        disabled={false}
        readOnly={false}
        placeholder={placeHolder}
        tenantId={tenantId}
      />
    );
  }

  /**
   * 动作项切换
   * @param value
   */
  function handleChange(value, defaultAction) {
    runInAction(() => {
      const actionList = actionsDataset?.current?.get('action_list');
      actionsDataset.removeAll();
      actionsDataset.create({
        [ACTION_FIELD_NAME]: value || undefined,
        action_list: actionList,
      });
      actionsDataset.current.set(ACTION_FIELD_NAME, value);
      const action = options.find(option => option.id === value) || defaultAction;
      if (action) {
        const { windowShowField, popupWindowFlag } = action;
        let fieldList;
        try {
          fieldList = JSON.parse(windowShowField) ?? []; // may be null
        } catch (e) {
          fieldList = [];
        }
        setExtraField(fieldList);
        if (popupWindowFlag && fieldList.length) {
          fieldList.forEach(fieldItem => {
            const { field, required } = fieldItem;
            // 单据没有的字段用业务对象的配置
            const businessObjectField = businessObjectFieldDataSet.find(r => r.get('code') === field);
            if (businessObjectField) {
              const fieldProps = transformField({
                fieldMap: { [field]: businessObjectField.toData() },
                field: businessObjectField.toData(),
                viewId,
                tenantId,
                intl,
              });
              const newFieldConfig = {
                ...fieldProps,
                dynamicProps: {
                  ...fieldProps.dynamicProps,
                  required: () => required,
                  disabled: () => false,
                },
                disabled: false,
                required,
              };
              actionsDataset.current.addField(field, {
                ...newFieldConfig,
                required,
              });
              if (businessObjectField.get('defaultValue') !== undefined) {
                actionsDataset.current.init(field, formatBoolValue(businessObjectField.get('defaultValue')));
              }
            }
          });
        }
      }
    });
  }

  function displayFailedList() {
    setDisplayFailed(!displayFailed);
  }

  function renderProcess() {
    const text = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.success', defaultMessage: '操作完成' })}`;
    const color = '#7bc95a';
    const type = 'check-one';
    const totalText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.total', defaultMessage: '共' })}`;
    const paperText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.paper', defaultMessage: '条单据' })}`;
    const successText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.success.num', defaultMessage: '条成功' })}`;
    const failedText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.failed.num', defaultMessage: '条失败' })}`;
    const detailsText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.details', defaultMessage: '点击查看失败详情' })}`;
    const failedListText = `${intl.formatMessage({ id: 'lcr.renderer.desc.ticket.process.list', defaultMessage: '执行失败的单据：' })}`;
    if (!finished) {
      return false;
    }
    return (
      <div className="lc-ticket_process-wrapper">
        <div className="lc-ticket_process-wrapper-top">
          <div className="result"><Icon type={type} style={{ color }} />{text}</div>
          <div>{totalText}<span>{total || 0}</span>{paperText}</div>
          <div><span className="success">{(total - failedNum) || 0}</span>{successText},</div>
          <div><span className="failed">{failedNum || 0}</span>{failedText}</div>
        </div>
        <Progress value={percent} />
        {result.length ? (
          <div className="lc-ticket_process-wrapper-bottom">
            <div className="details" onClick={displayFailedList}>{detailsText}</div>
            {displayFailed
              && <>
                <div>{failedListText}</div>
                <div className="failed-list">
                  {result.map(item => (<div>{item}</div>))}
                </div>
              </>}
          </div>
        ) : null}
      </div>
    );
  }
  /**
   * @description 动作集 Select, 级联
   */
  const handleChangeAction = (value) => {
    // init 净值 避免 set 直接触发校验
    actionsDataset?.current?.init(ACTION_FIELD_NAME, undefined);
    const actions = (listOptions || []).find(r => r.id === value)?.actionList || [];
    setOptions(actions);
  };

  return (
    <div className="lc-ticket_process">
      <Form
        dataSet={actionsDataset}
        labelLayout="horizontal"
        labelWidth="auto"
        disabled={finished}
      >
        <Select name="action_list" onChange={handleChangeAction} disabled={actionsDataset?.getState('lockActionList')}>
          {listOptions.map(({ id, name }) => <Option value={id}>{name}</Option>)}
        </Select>
        <Select name={ACTION_FIELD_NAME} onChange={handleChange} disabled={actionsDataset?.getState('lockAction')}>
          {options.map(({ id, name }) => <Option value={id}>{name}</Option>)}
        </Select>
        {extraField.map(field => renderField(field))}
      </Form>
      {/* TODO: 结果展示  */}
      <Spin spinning={loading}>
        {renderProcess()}
      </Spin>
    </div>
  );
}

export default observer(Actions);
