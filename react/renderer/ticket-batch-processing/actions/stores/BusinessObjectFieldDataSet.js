// 获取业务对象的字段
const BusinessObjectFieldDataSet = ({ tenantId, businessObjectId }) => ({
  autoQuery: true,
  autoLocateFirst: false,
  paging: false,
  transport: {
    read: {
      url: businessObjectId ? `/lc/v1/${tenantId}/object_fields/all/${businessObjectId}` : '',
      method: 'get',
    },
  },
  fields: [
    { name: 'id', type: 'string' },
    { name: 'code', type: 'string' },
    { name: 'name', type: 'string' },
  ],
});
export default BusinessObjectFieldDataSet;
