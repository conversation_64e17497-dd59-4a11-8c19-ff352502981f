import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import ActionsDataSet from './ActionsDataSet';
import BusinessObjectFieldDataSet from './BusinessObjectFieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = (props) => {
  const {
    children,
    tenantId,
    businessObjectId,
    intl,
    actionListId,
    actionId,
  } = props;

  const intlPrefix = 'renderer.ticket_process';

  const actionsDataset = useMemo(() => new DataSet(ActionsDataSet({ tenantId, intl, businessObjectId })), [tenantId, businessObjectId]);
  const businessObjectFieldDataSet = useMemo(() => new DataSet(BusinessObjectFieldDataSet({ tenantId, businessObjectId })), [tenantId, businessObjectId]);
  const value = {
    ...props,
    actionsDataset,
    businessObjectFieldDataSet,
    intlPrefix,
    actionListId,
    actionId,
  };

  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
};
