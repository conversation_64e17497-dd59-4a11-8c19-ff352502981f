export default ({ intl, tenantId, businessObjectId }) => ({
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'action_list',
      type: 'string',
      label: intl.formatMessage({ id: 'lcr.renderer.model.ticket.process.action.list', defaultMessage: '动作集' }),
      required: true,
      ignore: 'always', // 不需要传参
    },
    {
      name: '_action_id',
      type: 'string',
      label: intl.formatMessage({ id: 'lcr.renderer.model.ticket.process.select', defaultMessage: '选择操作' }),
      required: true,
      dynamicProps: {
        disabled: ({ record }) => !record?.get('action_list'),
      },
    },
  ],
});
