import { getQueryParams } from '@zknow/utils';
import { transformField, transformResponse } from '@/utils/lovConfig';
import { ACTION, calculateConditions } from '@/components/page-loader/utils';

export default ({ tenantId, intl, viewId, multipleConfig = {}, funcConfig = {}, formDataSet, variableParams }) => {
  const hasDefaultParams = variableParams?.length > 0 || multipleConfig.conditions?.length > 0;
  return {
    autoQuery: false,
    selection: false,
    // autoCreate: false,
    paging: false,
    autoCreate: true,
    transport: {
    },
    fields: [
      {
        name: multipleConfig.multipleCode,
        label: multipleConfig.multipleLabel,
        type: 'object',
        idField: 'id',
        parentField: 'parentId',
        multiple: true,
        computedProps: {
          disabled: () => {
            let disabled = multipleConfig?.editType === 'ALWAYS_NOT_EDIT';
            if (multipleConfig?.editType === 'CONDITION') {
              if ((multipleConfig.editType === 'CONDITION' && multipleConfig?.editAction === ACTION.READONLY)) {
                disabled = calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig.editType === 'CONDITION' ? multipleConfig.editCondition : null,
                  funcConfig,
                );
              }
              if (multipleConfig.editType === 'CONDITION' && multipleConfig.editAction === ACTION.EDITABLE && multipleConfig?.editCondition?.length) {
                disabled = !calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig.editType === 'CONDITION' ? multipleConfig?.editCondition : null,
                  funcConfig,
                );
              }
            }
            return disabled;
          },
          required: () => {
            let required = multipleConfig?.requiredType === 'ALWAYS_REQUIRED';
            if (multipleConfig?.requiredType === 'CONDITION') {
              // 必填校验
              if (multipleConfig?.requiredType === 'CONDITION' && multipleConfig?.requiredAction === ACTION.REQUIRED && multipleConfig?.requiredCondition?.length) {
                required = calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig?.requiredType === 'CONDITION' ? multipleConfig?.requiredCondition : null,
                  funcConfig,
                );
              }
              if (multipleConfig?.requiredType === 'CONDITION' && multipleConfig?.requiredAction === ACTION.Not_REQUIRED && multipleConfig?.requiredCondition?.length) {
                required = !calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig?.requiredType === 'CONDITION' ? multipleConfig?.requiredCondition : null,
                  funcConfig,
                );
              }
            }
            return required;
          },
          lovDefineAxiosConfig: () => {
            if (multipleConfig?.multipleLovVariableCode) {
              return (lovCode) => ({
                url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
                method: 'GET',
                transformResponse: data => transformResponse(data, data?.name, (map, f) => transformField(map, f, viewId, tenantId), intl, tenantId),
              });
            }
            return undefined;
          },
          lovQueryAxiosConfig: () => {
            if (multipleConfig?.multipleLovVariableCode) {
              return (lovCode, lovConfig = {}, { data, params }) => {
                lovConfig.method = 'POST';
                const { conditions, currentParams, ...otherData } = data;
                return {
                  url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
                  method: 'POST',
                  data: {
                    params: {
                      ...currentParams,
                      ...getQueryParams(otherData, ['__page_params']),
                      __page_params: otherData?.__page_params,
                    },
                    conditions,
                  },
                  params,
                  transformResponse: (originData) => {
                    try {
                      const jsonData = JSON.parse(originData);
                      return {
                        ...jsonData,
                        content: jsonData?.content?.map(item => {
                          return {
                            ...item,
                            primaryKey: item.id,
                          };
                        }) || [],
                      };
                    } catch (error) {
                      return [];
                    }
                  },

                };
              };
            }
            return undefined;
          },
          lovCode: () => {
            if (multipleConfig?.multipleLovVariableCode) {
              return multipleConfig?.multipleLovVariableCode;
            }
            return undefined;
          },
          transformRequest: () => {
            return (value) => {
              return value?.id;
            };
          },
          transformResponse: ({ record }) => {
            return (value, data) => {
              if (!value) {
                return undefined;
              }
              return {
                id: value,
                [record.get('widgetConfig.relationLovNameFieldCode')]: data?.widgetConfig?.defaultValueName,
              };
            };
          },
          lovPara: () => ({
            currentParams: hasDefaultParams ? formDataSet.current?.toData() : {},
            __page_params: variableParams,
            conditions: multipleConfig.conditions,
          }),
        },
      },
    ],
  };
};
