import React, { createContext, useMemo, useEffect, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import MultipleChoiceDataSet from './MultipleChoiceDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { personId, person } },
      viewCode,
      // ticketId,
      formDataSet,
      viewDataSet,
      viewRecord,
      expressionDefaultValue,
    } = props;
    const prefixCls = 'multiple-choice-renderer';
    const intlPrefix = 'lc.components.multipleChoice';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];
    const ticketId = formDataSet?.current?.get('id') || formDataSet?.current?.get('_id') || expressionDefaultValue?._id; // 新建视图中，使用 calculate 的 _id
    const [variableParams, setVariableParams] = useState([]);

    const multipleModelId = useMemo(() => viewRecord?.get('widgetConfig.multipleModelId'), [viewRecord?.get('widgetConfig.multipleModelId')]);
    const multipleRelatedFieldCode = useMemo(() => viewRecord?.get('widgetConfig.multipleRelatedFieldCode'), [viewRecord?.get('widgetConfig.multipleRelatedFieldCode')]);
    const multipleTargetFieldCode = useMemo(() => viewRecord?.get('widgetConfig.multipleTargetFieldCode'), [viewRecord?.get('widgetConfig.multipleTargetFieldCode')]);
    const multipleLovVariableCode = useMemo(() => viewRecord?.get('widgetConfig.multipleLovVariableCode'), [viewRecord?.get('widgetConfig.multipleLovVariableCode')]);
    const multipleLabel = useMemo(() => viewRecord?.get('name'), [viewRecord?.get('name')]);
    const multipleCode = useMemo(() => viewRecord?.get('code'), [viewRecord?.get('code')]);
    const nameFieldCode = useMemo(() => viewRecord?.get('widgetConfig.nameFieldCode'), [viewRecord?.get('widgetConfig.nameFieldCode')]);
    const conditions = useMemo(() => viewRecord?.get('widgetConfig.condition'), [viewRecord?.get('widgetConfig.condition')]);
    const variableFilter = useMemo(() => viewRecord?.get('widgetConfig.variableFilter'), [viewRecord?.get('widgetConfig.variableFilter')]);
    // 添加、删除选项前的校验
    const multipleBeforeConfirm = useMemo(() => viewRecord?.get('widgetConfig.multipleBeforeConfirm'), [viewRecord?.get('widgetConfig.multipleBeforeConfirm')]);
    const multipleBeforeConfirmTitle = useMemo(() => viewRecord?.get('widgetConfig.multipleBeforeConfirmTitle'), [viewRecord?.get('widgetConfig.multipleBeforeConfirmTitle')]);
    const multipleBeforeConfirmScope = useMemo(() => viewRecord?.get('widgetConfig.multipleBeforeConfirmScope'), [viewRecord?.get('widgetConfig.multipleBeforeConfirmScope')]);
    const multipleBeforeConfirmModule = useMemo(() => viewRecord?.get('widgetConfig.multipleBeforeConfirmModule'), [viewRecord?.get('widgetConfig.multipleBeforeConfirmModule')]);
    const multipleBeforeConfig = useMemo(() => viewRecord?.get('widgetConfig.multipleBeforeConfig'), [viewRecord?.get('widgetConfig.multipleBeforeConfig')]);
    const multipleAfterConfirm = useMemo(() => viewRecord?.get('widgetConfig.multipleAfterConfirm'), [viewRecord?.get('widgetConfig.multipleAfterConfirm')]);
    const multipleAfterConfirmTitle = useMemo(() => viewRecord?.get('widgetConfig.multipleAfterConfirmTitle'), [viewRecord?.get('widgetConfig.multipleAfterConfirmTitle')]);
    const multipleAfterConfirmScope = useMemo(() => viewRecord?.get('widgetConfig.multipleAfterConfirmScope'), [viewRecord?.get('widgetConfig.multipleAfterConfirmScope')]);
    const multipleAfterConfirmModule = useMemo(() => viewRecord?.get('widgetConfig.multipleAfterConfirmModule'), [viewRecord?.get('widgetConfig.multipleAfterConfirmModule')]);
    const multipleAfterConfig = useMemo(() => viewRecord?.get('widgetConfig.multipleAfterConfig'), [viewRecord?.get('widgetConfig.multipleAfterConfig')]);
    // 必填、可编辑、可见
    const requiredType = useMemo(() => viewRecord?.get('requiredType'), [viewRecord?.get('requiredType')]);
    const requiredAction = useMemo(() => viewRecord?.get('widgetConfig.requiredAction'), [viewRecord?.get('widgetConfig.requiredAction')]);
    const requiredCondition = useMemo(() => viewRecord?.get('widgetConfig.requiredCondition'), [viewRecord?.get('widgetConfig.requiredCondition')]);
    const editType = useMemo(() => viewRecord?.get('editType'), [viewRecord?.get('editType')]);
    const editAction = useMemo(() => viewRecord?.get('widgetConfig.editAction'), [viewRecord?.get('widgetConfig.editAction')]);
    const editCondition = useMemo(() => viewRecord?.get('widgetConfig.editCondition'), [viewRecord?.get('widgetConfig.editCondition')]);
    const visibleType = useMemo(() => viewRecord?.get('widgetConfig.visibleType'), [viewRecord?.get('widgetConfig.visibleType')]);
    const visibleAction = useMemo(() => viewRecord?.get('widgetConfig.visibleAction'), [viewRecord?.get('widgetConfig.visibleAction')]);
    const visibleCondition = useMemo(() => viewRecord?.get('widgetConfig.visibleCondition'), [viewRecord?.get('widgetConfig.visibleCondition')]);
    const multipleId = useMemo(() => viewRecord?.get('id'), [viewRecord?.get('id')]);

    const funcConfig = { personId, person, tenantId };

    useEffect(() => {
      const data = {};
      variableFilter?.forEach(({ variable, relatedFieldCode }) => {
        data[variable] = formDataSet?.current?.get(relatedFieldCode)?.id || formDataSet?.current?.get(relatedFieldCode);
      });
      setVariableParams(data);
    }, [formDataSet?.current]);

    const multipleConfig = {
      multipleModelId,
      multipleRelatedFieldCode,
      multipleTargetFieldCode,
      multipleLovVariableCode,
      multipleLabel,
      multipleCode,
      nameFieldCode,
      requiredType,
      requiredAction,
      requiredCondition,
      editType,
      editAction,
      editCondition,
      visibleType,
      visibleAction,
      visibleCondition,
      multipleId,
      conditions,
      multipleBeforeConfirm,
      multipleBeforeConfirmTitle,
      multipleBeforeConfirmScope,
      multipleBeforeConfirmModule,
      multipleBeforeConfig,
      multipleAfterConfirm,
      multipleAfterConfirmTitle,
      multipleAfterConfirmScope,
      multipleAfterConfirmModule,
      multipleAfterConfig,
    };

    const multipleChoiceDataSet = useMemo(() => new DataSet(MultipleChoiceDataSet({
      tenantId,
      intl,
      viewId,
      multipleConfig,
      funcConfig,
      formDataSet,
      variableParams,
    })), [multipleModelId, variableParams]);

    const value = {
      ...props,
      formDataSet,
      dsFieldList,
      prefixCls,
      intlPrefix,
      formConfig,
      tenantId,
      ticketId,
      multipleChoiceDataSet,
      multipleConfig,
      funcConfig,
      viewId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
