import React, { useState, useEffect, useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import { ExternalComponent } from '@zknow/components';
import { Form, Lov, message, Modal } from 'choerodon-ui/pro';
import { ACTION, calculateConditions } from '@/components/page-loader/utils';
import Store from './stores';
import './index.less';

const MultipleChoice = observer(() => {
  const context = useContext(Store);
  const {
    formDataSet,
    multipleChoiceDataSet,
    ticketId,
    multipleConfig = {},
    tenantId,
    labelWidth = 100,
    funcConfig,
    viewId,
    expressionDefaultValue,
  } = context;
  const {
    multipleModelId,
    multipleRelatedFieldCode,
    multipleTargetFieldCode,
    nameFieldCode,
    visibleType,
    visibleAction,
    visibleCondition,
    multipleId,
    multipleLabel,
    multipleCode,
    variableFilter,
    conditions,
    multipleBeforeConfirm,
    multipleBeforeConfirmTitle,
    multipleBeforeConfirmScope,
    multipleBeforeConfirmModule,
    multipleBeforeConfig,
    multipleAfterConfirm,
    multipleAfterConfirmTitle,
    multipleAfterConfirmScope,
    multipleAfterConfirmModule,
    multipleAfterConfig,
  } = multipleConfig || {};
  const [defaultValue, setDefaultValue] = useState([]);

  useEffect(() => {
    // 新建视图使用 invoke （调用的 executeButton 接口）需要传入 _id  作为关联
    if (formDataSet?.current && !formDataSet?.current?.get('id') && expressionDefaultValue?._id) {
      formDataSet.current.init('_id', expressionDefaultValue._id);
    }
  }, [expressionDefaultValue?._id, formDataSet?.current]);

  useEffect(() => {
    const id = formDataSet?.current?.get('id');
    if (multipleConfig?.multipleModelId && id) {
      getDefaultValue(id);
    }
    if (formDataSet?.current) {
      formDataSet.current.addField(multipleCode, {
        name: multipleCode,
        label: multipleLabel,
        type: 'object',
        idField: 'id',
        parentField: 'parentId',
        multiple: true,
        ignore: 'always',
        lovPara: {
          __page_params: 0,
        },
        computedProps: {
          required: () => {
            let required = multipleConfig?.requiredType === 'ALWAYS_REQUIRED';
            if (multipleConfig?.requiredType === 'CONDITION') {
              // 必填校验
              if (multipleConfig?.requiredType === 'CONDITION' && multipleConfig?.requiredAction === ACTION.REQUIRED && multipleConfig?.requiredCondition?.length) {
                required = calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig?.requiredType === 'CONDITION' ? multipleConfig?.requiredCondition : null,
                  funcConfig,
                );
              }
              if (multipleConfig?.requiredType === 'CONDITION' && multipleConfig?.requiredAction === ACTION.Not_REQUIRED && multipleConfig?.requiredCondition?.length) {
                required = !calculateConditions(
                  null,
                  null,
                  null,
                  formDataSet?.current,
                  multipleConfig?.requiredType === 'CONDITION' ? multipleConfig?.requiredCondition : null,
                  funcConfig,
                );
              }
            }
            return required;
          },
        },
      });
    }
  }, [multipleConfig?.multipleModelId, formDataSet?.current]);

  useEffect(() => {
    if (defaultValue) {
      const multipleChoiceList = [];
      defaultValue?.forEach((i) => {
        const data = {};
        data.id = i[multipleTargetFieldCode];
        data.name = i[`${multipleTargetFieldCode}:${nameFieldCode}`];
        data[nameFieldCode] = i[`${multipleTargetFieldCode}:${nameFieldCode}`];
        multipleChoiceList.push(data);
      });
      multipleChoiceDataSet?.current?.set(multipleCode, multipleChoiceList);
      formDataSet?.current?.init(multipleCode, multipleChoiceList);
    }
  }, [defaultValue]);

  // 获取当前多选储存的值
  async function getDefaultValue(id) {
    const searchParam = `fields=${multipleRelatedFieldCode},${multipleTargetFieldCode},${multipleTargetFieldCode}:${nameFieldCode}&businessObjectId=${multipleModelId}`;
    const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/searchByParamsForFields?${searchParam}`, JSON.stringify({
      [multipleRelatedFieldCode]: id,
    }));
    if (res?.failed) {
      setDefaultValue([]);
    } else {
      setDefaultValue(res);
    }
  }

  // 计算最后的值
  function calculationRequestData(value) {
    const data = value === null ? [] : value;
    const newArr = [];
    const deleteArr = [];
    const submitList = [];
    data.forEach(item => {
      if (!defaultValue?.map((i) => i[multipleTargetFieldCode]).includes(item.id)) {
        newArr.push(item);
      }
    });
    // 找到需要删除的数据
    defaultValue.forEach((i) => {
      if (!value?.map((j) => j.id)?.includes(i[multipleTargetFieldCode])) {
        deleteArr.push(i);
      }
    });
    newArr.map((i) => {
      const relatedObj = {};
      relatedObj[multipleRelatedFieldCode] = ticketId;
      relatedObj[multipleTargetFieldCode] = i.id;
      relatedObj._status = 'create';
      submitList.push(relatedObj);
      return i;
    });
    deleteArr.map((i) => {
      const relatedObj = {};
      relatedObj[multipleRelatedFieldCode] = ticketId;
      relatedObj[multipleTargetFieldCode] = i.id;
      relatedObj._status = 'delete';
      relatedObj.object_version_number = i.object_version_number;
      relatedObj.id = i.id;
      submitList.push(relatedObj);
      return i;
    });
    if (multipleBeforeConfirm && newArr.length > 0) {
      Modal.open({
        title: multipleBeforeConfirmTitle,
        children: (
          <ExternalComponent
            system={{ scope: multipleBeforeConfirmScope, module: multipleBeforeConfirmModule }}
            tenantId={tenantId}
            config={multipleBeforeConfig}
            data={submitList}
            callback={() => handleSubmit(submitList)}
          />
        ),
        style: {
          width: 800,
        },
      });
      getDefaultValue(ticketId);
    } else if (multipleAfterConfirm && deleteArr.length > 0) {
      Modal.open({
        title: multipleAfterConfirmTitle,
        children: (
          <ExternalComponent
            system={{ scope: multipleAfterConfirmScope, module: multipleAfterConfirmModule }}
            tenantId={tenantId}
            config={multipleAfterConfig}
            data={submitList}
            dataSet={multipleChoiceDataSet}
            callback={() => handleSubmit(submitList)}
          />
        ),
        style: {
          width: 800,
        },
      });
      getDefaultValue(ticketId);
    } else {
      handleSubmit(submitList);
    }
  }

  // 提交信息
  async function handleSubmit(data) {
    const searchParam = `fields=${multipleRelatedFieldCode},${multipleTargetFieldCode}&businessObjectId=${multipleModelId}&multipleRelatedFieldCode=${multipleRelatedFieldCode}&multipleTargetFieldCode=${multipleTargetFieldCode}&name=${multipleLabel}&titleModuleId=${multipleId}&viewId=${viewId}`;
    const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/submitByParamsForFields?${searchParam}`, JSON.stringify(data));
    if (res?.failed) {
      message.error(res?.error);
    } else {
      getDefaultValue(ticketId);
    }
  }

  const renderMain = () => {
    let hideFieldFlag = visibleType === 'ALWAYS_NOT_VISIBLE';
    // 根据UI规则控制字段显示隐藏
    if (visibleType === 'CONDITION') {
      // 隐藏条件判断
      if (visibleAction === ACTION.HIDE) {
        hideFieldFlag = calculateConditions(null, null, null, formDataSet?.current, visibleCondition, funcConfig);
      }
      // 显示条件判断
      if (visibleAction === ACTION.SHOW && visibleCondition?.length) {
        hideFieldFlag = !calculateConditions(null, null, null, formDataSet?.current, visibleCondition, funcConfig);
      }
    }
    if (hideFieldFlag) {
      return null;
    }

    if (!formDataSet?.current) return null;
    return (
      <Form
        dataSet={multipleChoiceDataSet}
        labelWidth={labelWidth}
        labelLayout="horizontal"
      >
        <Lov
          record={multipleChoiceDataSet?.current}
          dataSet={multipleChoiceDataSet}
          name={multipleCode}
          label={multipleLabel}
          onChange={(value) => {
            calculationRequestData(value);
          }}
        />
      </Form>
    );
  };

  return renderMain();
});

const MultipleChoiceRenderer = injectIntl((props) => {
  return <MultipleChoice {...props} />;
});

export default MultipleChoiceRenderer;
