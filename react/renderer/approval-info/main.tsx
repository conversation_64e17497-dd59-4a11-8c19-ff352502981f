import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Tooltip, Dropdown, Menu } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import StatusTag from './status';
import Store from './stores';
import styles from './styles/main.module.less';

const COLOR_MAP = {
  processed: '#79c729',
  processing: '#5c5cf9',
  'to be processed': '#a8d1f5',
  'not processed': '#c9c9c9',
  automaticallyProcessed: '#79c729',
};

// 产品要求：以下几种状态特殊显示
const GREY_COLOR_MAP = {
  INTERRUPT: '#81888f',
  WITHDRAW: '#81888f',
  REJECTED: '#81888f',
  PROCESSED: '#81888f',
};

export default observer(() => {
  const {
    data = [],
    config, // 配置
    intl,
    wfInstanceId, // 流程一览选中的审批实例
    wfInstance, // 组件当前选中的实例
    setWfInstance,
    wfInstances, // 当前单据所有可选实例
  } = useContext(Store);

  const customConfig = config?.customConfig || [];
  const height = customConfig.find(configItem => configItem.key === 'height')?.value;
  const marginTop = customConfig.find(configItem => configItem.key === 'marginTop')?.value;

  const STATUS_MAP = useMemo(() => ({
    AUTO_SKIP: intl.formatMessage({ id: 'lcr.renderer.desc.approve.skipped', defaultMessage: '自动跳过' }),
    REJECTED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.rejected', defaultMessage: '审批拒绝' }),
    APPROVED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.approved', defaultMessage: '审批通过' }),
    RETURN: intl.formatMessage({ id: 'lcr.renderer.desc.approve.return', defaultMessage: '审批驳回' }),
    WITHDRAW: intl.formatMessage({ id: 'lcr.renderer.desc.approve.withdraw', defaultMessage: '撤回' }),
    UNPROCESSED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.unprocessed', defaultMessage: '未处理' }),
    PENDING: intl.formatMessage({ id: 'lcr.renderer.desc.approve.pending', defaultMessage: '将要处理' }),
    PROCESSING: intl.formatMessage({ id: 'lcr.renderer.desc.approve.processing', defaultMessage: '审批中' }),
    INTERRUPT: intl.formatMessage({ id: 'lcr.renderer.desc.approve.interrupt', defaultMessage: '中断' }),
    PROCESSED: intl.formatMessage({ id: 'lcr.renderer.desc.approve.interrupt', defaultMessage: '中断' }),
    AUTOMATICALLY: intl.formatMessage({ id: 'lcr.renderer.desc.approve.automatically', defaultMessage: '已自动处理' }),
    COMPLETE: intl.formatMessage({ id: 'lcr.renderer.desc.approve.complete', defaultMessage: '处理完成' }),
    END: intl.formatMessage({ id: 'lcr.renderer.desc.approve.end', defaultMessage: '节点自动完成' }),
  }), []);

  function renderItem(item) {
    const {
      nodeCode, nodeName, assignee, endDate, commentContent,
      status, stage,
    } = item;
    return (
      <div key={nodeCode} className={styles.item}>
        <div className={styles.flex}>
          <span className={styles.name}>{nodeName}</span>
          <StatusTag
            color={GREY_COLOR_MAP[status] || COLOR_MAP[stage]}
            // 产品要求（拒绝、驳回、中断为白色；完成时，字体跟边框同色；其他显示为黑色）
            fontColor={GREY_COLOR_MAP[status] ? '#fff' : (stage === 'processed' ? COLOR_MAP[stage] : undefined)}
            backgroundColor={GREY_COLOR_MAP[status]}
          >
            {STATUS_MAP[status || 'approve_withdraw']}
          </StatusTag>
        </div>
        {assignee
          ? (
            <div className={styles.line}>
              {`${intl.formatMessage({ id: 'lcr.renderer.desc.approval.info.approver', defaultMessage: '审批人：' })}`}
              <span className={styles.text}>
                <Tooltip title={assignee}>
                  {assignee}
                </Tooltip>
              </span>
            </div>
          ) : null}
        {endDate
          ? (
            <div className={styles.line}>
              {`${intl.formatMessage({ id: 'lcr.renderer.desc.approval.info.approval.time', defaultMessage: '审批时间：' })}`}
              {endDate}
            </div>
          ) : null}
        {commentContent
          ? (
            <div className={styles.line}>
              {`${intl.formatMessage({ id: 'lcr.renderer.desc.approval.info.approval.options', defaultMessage: '审批意见：' })}`}
              <span className={styles.text}>
                <Tooltip title={commentContent}>
                  {commentContent}
                </Tooltip>
              </span>
            </div>
          ) : null}
      </div>
    );
  }

  function renderInfo(info) {
    if (info.taskHistoryList) {
      if (info.status === 'processed') {
        // 已处理，需要按照已处理的逐个展示（过滤掉自动跳过）
        return info.taskHistoryList
          .filter(history => history.status !== 'AUTO_SKIP')
          .map(history => {
            return renderItem({
              ...info,
              stage: info.status || 'not processed',
              taskHistoryList: null,
              ...history,
              status: history.status === 'automaticallyProcessed' ? 'AUTOMATICALLY' : history.status || 'UNPROCESSED',
            });
          });
      } else {
        // 未处理或当前待处理的，汇总显示
        return renderItem({
          ...info,
          stage: info.status || 'to be processed',
          taskHistoryList: null,
          status: info.status === 'processing' ? 'PROCESSING' : 'PENDING',
          assignee: info.taskHistoryList.map(history => history.assignee).join('\\'),
        });
      }
    } else {
      // 无审批人信息，默认显示为将要处理
      return renderItem({
        ...info,
        stage: info.status || 'to be processed',
        // eslint-disable-next-line no-nested-ternary
        status: info.status === 'processing' ? 'PROCESSING' : (info.status === 'processed' ? 'PROCESSED' : 'PENDING'),
      });
    }
  }

  function handleMenuClick(e) {
    if (e.item && e.item.props) {
      setWfInstance(e.item.props.data || {});
    }
  }

  function getMenu() {
    return (
      <Menu onClick={handleMenuClick}>
        {wfInstances.map((item => (<Menu.Item data={item} key={item.id}>{item.workflowName}</Menu.Item>)))}
      </Menu>
    );
  }

  return (
    <div className={styles.wrapper} style={{ height: height ? `${height}px` : undefined }}>
      <div className={styles.title}>{config.name}</div>
      {data && data.length > 1 && !wfInstanceId
        ? (
          <div className={styles.flex}>
            <Dropdown overlay={getMenu()} placement="bottomLeft">
              <div className={styles.switch}>
                <span>{wfInstance.workflowName || ''}</span>
                <Icon type="DownOne" theme="filled" />
              </div>
            </Dropdown>
            <div />
          </div>
        ) : null}
      <div
        className={styles.list}
        style={{
          height: height ? `${height - 52}px` : undefined,
          marginTop: marginTop ? `${marginTop}px` : undefined,
        }}
      >
        {data.map(renderInfo)}
      </div>
    </div>
  );
});
