@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.wrapper {
  height: calc(100vh - 1.7rem);
}

.title {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 16px 0;
  margin-bottom: 12px;
}

.list {
  height: calc(100vh - 2.22rem);
  margin-left: 16px;
  overflow-y: scroll;
}

.item {
  background: #f8f8f8;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
}

.flex {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.name {
  font-size: 0.16rem;
  font-weight: 500;
}

.line {
  font-weight: 400;
  color: @yq-text-6;
  line-height: 20px;
}

.text {
  display: inline-block;
  width: calc(100% - 1rem);
  overflow: hidden;
  text-wrap: nowrap;
  vertical-align: top;
  font-weight: 500;
}

.switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  background: #F2F5FA;
  padding: 0 8px;
  border-radius: 4px;
  margin-left: 16px;
  margin-bottom: 12px;
}
