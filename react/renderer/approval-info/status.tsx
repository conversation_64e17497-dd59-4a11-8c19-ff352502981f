import React from 'react';
import { observer } from 'mobx-react-lite';
import { color as colorUtils } from '@zknow/utils';
import styles from './styles/status.module.less';

export default observer((props) => {
  const { children, color, fontColor, backgroundColor } = props;

  return (
    <div
      className={styles.status}
      style={{
        color: fontColor,
        border: `1px solid ${color}`,
        backgroundColor: backgroundColor || colorUtils.colorOpacityRGB(color, 0.2),
      }}
    >
      <div>{children}</div>
    </div>
  );
});
