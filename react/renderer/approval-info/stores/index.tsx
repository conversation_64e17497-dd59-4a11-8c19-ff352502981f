import React, { createContext, useEffect, useMemo, useState } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { useRequest } from 'ahooks';
import { getApprovalInfo, getWfInstances } from '@/service';

const Store = createContext<any>({});

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formDataSet,
      instanceId,
      config,
    } = props;

    const customConfig = config?.customConfig || [];
    const idFieldCode = customConfig.find(configItem => configItem.key === 'idFieldCode')?.value;
    const ticketId = idFieldCode && formDataSet.current?.get(idFieldCode) || instanceId;
    const intlPrefix = 'approval.info';
    // 默认跟随流程一览联动
    const wfInstanceId = formDataSet.getState('wfInstanceId'); // 流程一览选中的审批实例
    const wfInstanceName = formDataSet.getState('wfInstanceName');
    // 组件当前选中的实例
    const [wfInstance, setWfInstance] = useState({ id: wfInstanceId, workflowName: wfInstanceName });

    // 当前单据所有可选实例
    const { data: wfInstances, refresh: refreshWfData } = useRequest(() => !wfInstanceId && getWfInstances({ tenantId, id: ticketId }));
    const { data, refresh: refreshData } = useRequest(
      () => wfInstance.id && getApprovalInfo({ tenantId, instanceId: wfInstance.id }),
      { refreshDeps: [wfInstance.id] },
    );

    useEffect(() => {
      if (wfInstanceId && wfInstanceId !== wfInstance?.id) {
        setWfInstance({ id: wfInstanceId, workflowName: wfInstanceName });
      }
    }, [wfInstanceId]);

    useEffect(() => {
      // 如果当前未选中实例，则默认取第一个
      if (!wfInstance.id && wfInstances?.length > 0) {
        setWfInstance(wfInstances[0]);
      }
    }, [wfInstances?.length]);

    useEffect(() => {
      // 单据更新时，查询工作流
      if (wfInstance.id) {
        setTimeout(() => {
          refreshData();
        }, 500);
      }
      setTimeout(() => {
        refreshWfData();
      }, 500);
    }, [formDataSet?.current?.get('last_update_date')]);

    const value = {
      ...props,
      tenantId,
      refreshData,
      wfInstanceId,
      data,
      wfInstance,
      setWfInstance,
      wfInstances,
      intlPrefix,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
