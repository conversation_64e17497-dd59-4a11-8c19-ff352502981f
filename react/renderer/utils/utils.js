/* eslint-disable no-useless-escape */
/*
 * @Author: xia<PERSON>ya
 * @Date: 2022-03-24 16:59:16
 * @Description: 表单工具包
 */
import React from 'react';
import Quill from 'quill';
import { DataSet, message } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import WidgetField from '@/components/widget-field';
import { transformField } from '@/components/page-loader/stores/DataSetManager';
import { transformResponse, transformField as lovTransformField } from '@/utils/lovConfig';

/**
 * 根据字段类型渲染字段
 * @param fieldItem
 * @returns {null|*}
 */
function renderField(props) {
  const {
    fieldItem,
    formDs,
    formDataSet,
    dsFieldList,
    intl,
    businessObjectFieldDataSet,
    viewCode,
    businessObjectCode,
    ...rest
  } = props;
  const {
    field,
    config,
  } = fieldItem;
  const _record = formDataSet.current;
  // 用于渲染不在当前表单的字段
  let fieldRecord = null;
  const fieldConfig = _record.getField(field);

  let widgetType;
  let name;
  let placeHolder;

  const dsFieldConfig = dsFieldList.find(dsFieldItem => dsFieldItem.code === field);
  const businessObjectField = businessObjectFieldDataSet.find(r => r.get('code') === field);
  const fieldDataSet = new DataSet();
  if (fieldConfig && dsFieldConfig) {
    // 当前单据已经有的字段，取当前单据字段的配置
    widgetType = dsFieldConfig.widgetType;
    name = dsFieldConfig.name;
    placeHolder = dsFieldConfig.placeholder;
    fieldRecord = fieldDataSet.create(dsFieldConfig);
  } else if (businessObjectField || config) {
    // 当前单据没有的字段，取业务对象字段的配置
    const matchField = businessObjectField?.toData() || config;
    widgetType = matchField.widgetType;
    name = matchField.name;
    placeHolder = matchField.placeholder;
    fieldRecord = fieldDataSet.create(matchField);
  } else {
    // 没有任何一个地方可以匹配到字段
    return null;
  }
  return (
    <WidgetField
      record={fieldRecord || _record}
      viewCode={viewCode}
      businessObjectCode={businessObjectCode}
      widgetType={widgetType}
      label={name}
      key={field}
      name={field}
      code={field}
      intl={intl}
      formDs={formDs}
      disabled={false} // 动作集弹窗字段默认全部可以编辑
      readOnly={false}
      placeholder={placeHolder}
      {...rest}
    />
  );
}

/**
 * 由于Action需要单独控制必填，新建一个DataSet
 * @returns {DataSet}
 */
function getActionFormDataSet({
  fieldList,
  fieldsConfig,
  formDataSet,
  dsFieldList,
  businessObjectFieldDataSet,
  formConfigRecord,
  tenantId,
  intl,
}) {
  const _record = formDataSet.current;
  const fields = [];
  fieldList.forEach(fieldCode => {
    // const fieldItem = _record.getField(fieldCode);
    // const { field, required } = fieldItem;
    const field = fieldCode;
    // 单据已经有的字段
    const widgetType = dsFieldList?.find(dsFieldItem => dsFieldItem.code === field)?.widgetType;
    // const fieldConfig = formDataSet.getField(field);
    // 2022年7月28日 猪齿鱼UI升级了，获取字段方式变了
    const props = formDataSet?.getField(field)
      ?.props
      ?.toJSON();
    const config = fieldsConfig?.find(i => i?.field === fieldCode)?.config;
    if (props && widgetType) {
      const newFieldConfig = {
        ...props,
        dynamicProps: {
          ...props.dynamicProps,
          // required: () => required,
          disabled: () => false,
        },
        disabled: false,
        // required,
      };
      // 富文本这里不做处理，formDataSet中提交时统一处理成字符串
      if (widgetType === 'RichText') {
        newFieldConfig.transformRequest = undefined;
      }
      fields.push(newFieldConfig);
    } else {
      // 单据没有的字段用业务对象的配置
      const businessObjectField = businessObjectFieldDataSet.find(r => r.get('code') === field);
      if (businessObjectField || config) {
        const data = businessObjectField?.toData() || config;
        const fieldProps = transformField({
          fieldMap: { [field]: data },
          field: data,
          viewId: formConfigRecord?.get('pageId'),
          tenantId,
          intl,
        });
        const newFieldConfig = {
          ...fieldProps,
          dynamicProps: {
            ...fieldProps.dynamicProps,
            // required: () => required,
            disabled: () => false,
          },
          disabled: false,
          // required,
        };
        if (widgetType === 'RichText') {
          newFieldConfig.transformRequest = undefined;
        }
        fields.push({
          ...newFieldConfig,
          // required,
        });
      }
    }
  });
  const ds = new DataSet({
    fields,
    data: [_record?.toData()],
  });
  ds.setState('sourceFrom', 'customer');
  return ds;
}

/**
 * 执行Action
 * @param id
 * @param actionFormDataSet
 * @param fieldList
 * @returns {Promise<boolean>}
 */
async function runAction({
  transformDataSet: _solutionDataSet,
  formDataSet,
  fieldCodeList,
  intl,
  submitSuccess,
}) {
  // 设置当前执行Action
  const _record = formDataSet?.current;
  // 更新字段值
  if (_solutionDataSet) {
    const res = await _solutionDataSet?.current?.validate(true);
    if (res) {
      const actionFormData = _solutionDataSet?.current?.toData();
      fieldCodeList.map((fieldCode) => {
        // eslint-disable-next-line no-prototype-builtins
        if (fieldCode && actionFormData?.hasOwnProperty(fieldCode)) {
          _record.set(fieldCode, actionFormData[fieldCode]);
        }
        return fieldCode;
      });
    } else {
      message.error(intl.formatMessage({ id: 'zknow.common.validate.failed' }));
      return false;
    }
  }
  const res = await formDataSet.submit();
  if (res) {
    await formDataSet.query();
    if (typeof submitSuccess === 'function') {
      submitSuccess();
    }
    formDataSet?.current?.setState(
      'approvalHistoryDynamicRefreshCount',
      formDataSet?.current?.getState('approvalHistoryDynamicRefreshCount') + 1 || new Date().getTime(),
    );
  }
  return true;
}

const isJSON = str => {
  if (typeof str === 'string') {
    try {
      const obj = JSON.parse(str);
      if (typeof obj === 'object' && obj) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
  return '';
};

function htmlToDelta(html) {
  const isExist = document.getElementById('htmlToDelta');
  let div;
  if (isExist) {
    div = document.getElementById('htmlToDelta');
    div.setAttribute('style', 'display:none');
    div.innerHTML = `<div id='quillEditor'>${html}</div>`;
  } else {
    div = document.createElement('div');
    div.setAttribute('id', 'htmlToDelta');
    div.setAttribute('style', 'display:none');
    div.innerHTML = `<div id='quillEditor'>${html}</div>`;
    document.body.appendChild(div);
  }
  const quill = new Quill('#quillEditor', {
    theme: 'snow',
  });
  const delta = quill.getContents();
  // 不删了，否则性能有问题
  // document.getElementById('htmlToDelta').remove();
  return delta;
}

function getRichJson(html) {
  if (!html) return null;
  // 兼容旧版quillData格式的数据
  if (isJSON(html) || typeof html === 'object') {
    return html;
  }
  // 兼容旧版html格式的数据，但没有存储quill源数据
  if (!html?.includes('<p data-json=')) {
    const quillData = htmlToDelta(html);
    return {
      quillData,
      attachments: [],
      audios: [],
    };
  }

  const setCookieMetaRegExp = /<p data-json=[\'](.*)[\'].*>/gi;
  const matches = [];
  while (setCookieMetaRegExp.exec(html)) {
    matches.push(RegExp.$1);
  }
  if (!matches.length) return null;
  try {
    return isJSON(matches[0]) ? JSON.parse(matches[0]) : null;
  } catch (error) {
    return null;
  }
}

function getLovConfig(tenantId, ticketId, intl) {
  const config = {
    lovDefineAxiosConfig: lovCode => ({
      url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
      method: 'GET',
      transformResponse: data => transformResponse(data, data?.name, (map, f) => lovTransformField(map, f), intl, tenantId),
    }),
    lovQueryAxiosConfig: (lovCode, lovConfig = {}, {
      data,
      params,
    }) => {
      lovConfig.method = 'POST';
      return {
        url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
        method: 'POST',
        data: {
          conditions: [{
            condition: 'AND',
            filters: [{
              condition: 'AND',
              filter: 'is not',
              widgetType: 'Lov',
              componentType: 'Lov',
              field: 'id',
              fieldValue: ticketId,
            }],
          }],
          params: {
            ...getQueryParams(data),
            __page_params: data?.__page_params,
          },
        },
        params,
        transformResponse: (originData) => {
          try {
            const jsonData = JSON.parse(originData);

            return {
              ...jsonData,
              content: jsonData?.content?.map(item => {
                return {
                  ...item,
                  primaryKey: item.id,
                };
              }) || [],
            };
          } catch (error) {
            return [];
          }
        },
      };
    },
  };

  return config;
}

// 提取文本
const fomartRichText = (value) => {
  let res = value;
  if (typeof value === 'string') {
    res = res.replace(new RegExp('<[^>]*>', 'g'), ' ');
    res = res.replace(new RegExp('\\s+', 'g'), ' ');
    res = res.replace(new RegExp('&nbsp;', 'g'), '');
    res = res.trim();
  }
  return res;
};

export {
  getRichJson,
  renderField,
  getActionFormDataSet,
  runAction,
  getLovConfig,
  fomartRichText,
};
