import React, { useEffect, useContext, useState, useMemo, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { DataSet, Form, Lov } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import { MobXProviderContext } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import omit from 'lodash/omit';
import { transformResponse } from '@/components/page-loader/lovConfig';
import { transformField } from '@/components/page-loader/stores/DataSetManager';
import { getWidgetData } from '@/components/page-loader/utils';

function TicketTemplate(props) {
  const { context, cpmRecord, formDataSet } = props;
  const { viewDataSet, udmFlag, intl, viewId, dsManager } = context;
  const { AppState: { currentMenuType: { tenantId } } } = useContext(MobXProviderContext);
  const ticketRelationLovId = cpmRecord?.get?.('widgetConfig.ticketRelationLovId');
  const relationLovNameFieldCode = cpmRecord?.get?.('widgetConfig.relationLovNameFieldCode');
  const relationLovValueFieldCode = cpmRecord?.get?.('widgetConfig.relationLovValueFieldCode');

  const getTaskTable = (dsId) => dsManager?.pageRef.current?.formTableDsList.filter(ds => {
    const widgetData = getWidgetData(viewDataSet?.current?.get('jsonData'), ds.id);
    return widgetData?.widgetConfig?.modelId === dsId;
  });

  const ds = useMemo(() => new DataSet({
    autoCreate: true,
    fields: [
      {
        name: 'template',
        type: 'object',
        label: cpmRecord?.get?.('name') || intl.formatMessage({ id: 'lcr.renderer.desc.ticketTemplate.label', defaultMessage: '选择工单模版' }),
        lovCode: ticketRelationLovId,
        textField: relationLovNameFieldCode,
        valueField: relationLovValueFieldCode || 'id',
        lovDefineAxiosConfig: lovCode => ({
          url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
          method: 'GET',
          transformResponse: data => transformResponse(
            data,
            data?.name,
            (map, f) => transformField(
              {
                fieldMap: map,
                field: f,
                viewId,
                tenantId,
                intl,
              },
            ),
            intl,
            tenantId,
          ),
        }),
        lovQueryAxiosConfig: (lovCode, lovConfig = {}, { data, params }) => {
          lovConfig.method = 'POST';
          const { parentIdField, treeFlag, idField } = lovConfig || {};
          let searchFlag = false;
          const queryParams = getQueryParams(data, ['current_params', '__page_params', parentIdField]);

          Object.keys(queryParams).forEach((v) => {
            if (v.indexOf('search_') !== -1) {
              searchFlag = true;
            }
          });

          if (treeFlag === 'Y' && parentIdField) {
            if (data[parentIdField]) {
              params.size = 999;
            } else if (!searchFlag) {
              queryParams[parentIdField] = '0';
            }
          }

          // eslint-disable-next-line camelcase
          const data_params = {
            ...queryParams,
            ...(data?.current_params || {}),
            ...(params || {}),
          };
          delete data_params?.current_params;

          return {
            url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
            method: 'POST',
            data: {
              params: data_params,
              conditions: [],
            },
            params,
            transformResponse: (originData) => {
              try {
                const jsonData = JSON.parse(originData);
                return {
                  ...jsonData,
                  content: jsonData?.content?.map(item => {
                    if (searchFlag) {
                      // 搜索时，树形结构打平显示
                      return {
                        ...item,
                        isLeaf: true,
                        [parentIdField]: null,
                        id: item[idField] || item.id,
                        primaryKey: item.id,
                      };
                    }
                    return {
                      ...item,
                      id: item[idField] || item.id,
                      primaryKey: item.id,
                    };
                  }) || [],
                };
              } catch (error) {
                return [];
              }
            },
          };
        },
      },
    ],
  }), [ticketRelationLovId]);

  return viewDataSet?.current?.get('viewType') === 'INSERT' && !udmFlag ? (
    <Form
      labelWidth="100"
      dataSet={ds}
      labelLayout="horizontal"
    >
      <Lov
        name="template"
        onChange={async (value) => {
          if (formDataSet?.current) {
            const subTableObId = cpmRecord.get('widgetConfig.subTemplateObjectId');
            const taskTables = subTableObId ? getTaskTable(subTableObId) : [];

            // 视图没有行表
            if (!taskTables?.length) {
              return;
            }

            // 新建视图的头行结构里面需要这个临时变量
            //  它会判断行表的 ds 上有没有 businessObjectId 临时变量，没有则不会将数据放入 _children 中
            //   relatedFieldCode 临时变量是用来关联到头上的，如果没有，即使提交了数据，也无法关联
            taskTables.forEach(table => {
              const widgetData = getWidgetData(viewDataSet?.current?.get('jsonData'), table.id);
              table.setState('businessObjectId', subTableObId);
              table.setState('relatedFieldCode', widgetData?.widgetConfig?.relatedFieldCode);
            });

            if (value) {
              const subTemplateFlag = value.apply_sub_template_flag;
              // 添加任务项的按钮需要用到
              formDataSet.current.setState('subTemplateFlag', subTemplateFlag);
              // 工单模版没有开启子模版
              if (!subTemplateFlag) {
                return;
              }

              taskTables.forEach(table => {
                table.loadData([]);
              });

              const templateId = value?.[relationLovValueFieldCode];
              if (templateId) {
                formDataSet.status = 'loading';
                const templates = await axios.post(`/itsm/v1/${tenantId}/task/templates/apply/calculate?templateId=${templateId}`, formDataSet.current.toData()).catch(e => {
                  formDataSet.status = 'ready';
                });
                formDataSet.status = 'ready';

                if (templates && !templates.failed) {
                  const lineData = templates._children;
                  if (lineData?.length) {
                    taskTables.forEach(table => {
                      // 为什么不用 loadData ?
                      // loadData 会将数据写进 ds 缓存，和新建的行为不一致
                      lineData.forEach((item) => {
                        const r = table.create({});
                        r.set(item);
                      });
                      // table.loadData(lineData);
                    });
                  }
                  const templateData = omit(templates, '_children', 'submitted_by', 'submitted_by:real_name');
                  const keys = Object.keys(templateData);
                  keys.forEach(key => {
                    if (key?.includes(':')) {
                      const [k1, k2] = key.split(':');
                      const temp = templateData[k1];
                      templateData[k1] = {
                        id: temp,
                        [k2]: templateData[key],
                      };
                      delete templateData[key];
                    }
                  });
                  formDataSet.current.set({
                    task_template_id: templateId,
                    ...templateData,
                  });
                }
              }
            } else {
              formDataSet.current.setState('subTemplateFlag', undefined);
            }
          }
        }}
      />
    </Form>
  ) : null;
}

export default injectIntl(observer(TicketTemplate));
