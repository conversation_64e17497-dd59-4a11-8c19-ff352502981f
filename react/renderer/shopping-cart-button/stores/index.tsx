import React, { createContext } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
// @ts-ignore
import * as utils from '@/utils';

type ServiceAgreementProps = {
  intl: any,
  history: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  path?: string,
  match?: any,
  formConfig?: any,
  children?: any,
  AppState: any,
  formDataSet: any,
  viewDataSet: any,
  goalsDataSet: any,
  ticketId: string,
  viewRecord: any,
  mode?: string,
  pageRef?: any,
  HeaderStore?: any,
  setRefreshShoppingCartCount?: any,
  shoppingCartFlag?: any,
};

const Store = createContext<ServiceAgreementProps>({
  intl: undefined,
  history: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  viewDataSet: '',
  goalsDataSet: '',
  ticketId: '',
  viewRecord: '',
  HeaderStore: '',
  setRefreshShoppingCartCount: undefined,
  shoppingCartFlag: false,
});

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props: ServiceAgreementProps) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
    } = props;
    const prefixCls = 'shopping-cart-button';
    const ticketId = formDataSet && formDataSet.current && formDataSet.current.get('id');
    const { businessObjectCode } = (viewDataSet && viewDataSet.current && viewDataSet.current.toData()) || {};

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      businessObjectCode,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
