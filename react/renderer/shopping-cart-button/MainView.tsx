// @ts-nocheck
import React, { useContext, useRef, useEffect } from 'react';
import axios from 'axios';
import { Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { Modal, message, Form, DataSet, TextField, Lov, TextArea } from 'choerodon-ui/pro';
import { addToShoppingCart, buyNowWithShoppingCart } from '@/service';
import PageLoader from '@/components/page-loader/index';
import Store from './stores';
import style from './index.module.less';
import { validateMessage } from '../../components/page-loader/utils';

const modalKey = Modal.key();

const MainView = observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    viewRecord,
    mode,
    formDataSet,
    pageRef,
    tenantId,
    HeaderStore,
    shoppingCartFlag,
  } = context;

  useEffect(() => {
    queryLastPerson();
  }, [pageRef?.current?.serviceItemDetailDataSet?.current, tenantId]);

  const queryLastPerson = async () => {
    const serviceItem = pageRef?.current?.serviceItemDetailDataSet?.current;
    if (serviceItem?.get('id') && serviceItem?.get('principalCartFlag')) {
      (async () => {
        try {
          const principal = await axios.get(`/itsm/v1/${tenantId}/principalConfig/queryLatestPerson?itemId=${serviceItem.get('id')}&cartFlag=false`);
          if (principal?.id && !principal?.failed) {
            serviceItem.setState('cartPrincipal', principal);
          }
        } catch (e) {
          // 不处理
        }
      })();
    }
  };

  const extraViewPageRef: any = useRef({});

  if (!shoppingCartFlag) return null;
  if (!viewRecord) return null;
  const id = viewRecord.get('id');
  const text = viewRecord.get('name');
  const icon = viewRecord.get('icon');
  const type = viewRecord.get('widgetConfig.shoppingCartType');
  // 按钮类型。如果未设置，“直接购买”按钮默认为主按钮；加入购物车默认为 默认按钮样式。
  const color = viewRecord.get('color') || (type === 'BUY_NOW' ? 'primary' : 'default');
  // 确认信息
  const confirmFlag = viewRecord.get('confirmFlag');
  const confirmText = viewRecord.get('confirmText');
  const okText = viewRecord.get('okText');
  const cancelText = viewRecord.get('cancelText');
  // 弹窗视图信息
  const openViewId = viewRecord.get('viewId');
  const openViewType = viewRecord?.get('openType');
  const viewSize = viewRecord?.get('viewSize');
  const viewName = viewRecord?.get('viewName');
  const btnName = viewRecord?.get('name');

  async function getPostData(optionType, selectPrincipal = false) {
    const salesAttributeFormDataSet = pageRef?.current?.salesAttributeFormDataSet;
    const shoppingCartCustomAreaDataSet = pageRef?.current?.shoppingCartCustomAreaDataSet;
    const serviceItemDetailDataSet = pageRef?.current?.serviceItemDetailDataSet;
    // 没有salesAttributeFormDataSet 就直接为true
    const isSalesAttributeValid = salesAttributeFormDataSet ? await validateMessage(salesAttributeFormDataSet, intl) : true;
    // 没有shoppingCartCustomAreaDataSet 就直接为true
    const isShoppingCartCustomAreaValid = shoppingCartCustomAreaDataSet ? await validateMessage(shoppingCartCustomAreaDataSet, intl) : true;
    const isFormDataSetValid = await validateMessage(formDataSet, intl);
    const history = pageRef?.current?.history;
    const historyState = history?.location?.state;
    const extraViewData = extraViewPageRef?.current?.formDataSet?.current?.toData() || {};
    delete extraViewData.__dirty;
    delete extraViewData.__id;
    delete extraViewData._id;
    delete extraViewData._status;
    // 委托人组件dataset
    const principalDataSet = serviceItemDetailDataSet?.current?.getState('principalDataSet');

    if (!isFormDataSetValid) {
      // 字段校验不通过
      // 正常情况也不用处理，但是有种情况，服务项里面将「必填」字段设置为「不可见」了
      //   这样的确校验不通过，但是页面又看不到这个字段，也就没有红框框，导致不知道发生了什么错
      // 但是加 messages 也不好，无法区分字段是否被隐藏了
    }

    // 校验委托人组件必填
    if (principalDataSet && !await principalDataSet?.current?.validate()) {
      return false;
    }

    if (isSalesAttributeValid && isShoppingCartCustomAreaValid && isFormDataSetValid) {
      const itemId = serviceItemDetailDataSet?.current?.get('id');
      const { purchaseDuration, quantity, priceRuleId } = shoppingCartCustomAreaDataSet?.current?.toData() || {};
      let postData = {};
      const relationList = Object.entries(salesAttributeFormDataSet?.current?.toData() || {}) || [];
      const propertyRelationList: any = [];
      relationList.forEach((i) => {
        if (i?.[0] && i?.[0] !== '__dirty') {
          propertyRelationList.push(
            {
              propertyId: i[0],
              propertyValueId: i[1],
            }
          );
        }
      });

      const jsonData = Object.assign(extraViewData, formDataSet?.current?.toData());
      if (serviceItemDetailDataSet?.current?.getState('_principal_person_id')) {
        jsonData._principal_person_id = serviceItemDetailDataSet?.current?.getState('_principal_person_id');
      }

      // 带__custom，将id赋给当前
      Object.keys(jsonData).forEach(i => {
        if (jsonData[i]?.__custom && jsonData[i]?.id) jsonData[i] = jsonData[i]?.id;
      });

      const commonData = {
        itemId,
        propertyRelationList,
        quantity,
        purchaseDuration,
        priceRuleId,
        jsonData: JSON.stringify(jsonData),
      };
      if (serviceItemDetailDataSet?.current?.getState('_principal_person_id')) {
        commonData.submittedBy = serviceItemDetailDataSet?.current?.getState('_principal_person_id');
      } else if (serviceItemDetailDataSet?.current?.get('request_for.id')) {
        commonData.submittedBy = serviceItemDetailDataSet.current.get('request_for.id');
      }

      if (/* 加入购物车 */ optionType === 'ADD_TO_SHOPPING_CART') {
        postData = {
          serviceCatalogId: historyState?.catalogId || serviceItemDetailDataSet?.current?.get('catalogs')?.[0]?.id,
          ...commonData,
        };
        return postData;
      } else if (/* 立即购买 */ optionType === 'BUY_NOW') {
        const skuData = pageRef?.current?.skuData;
        let totalPrice = 0;
        let price = 0;
        (skuData || []).map((i: any, index) => {
          if (totalPrice) return;
          const skuType = i?.priceRule?.type;
          if (skuType === 'PRICE') {
            totalPrice = i?.totalPrice || 0;
            price = i?.value || 0;
          }
          return null;
        });
        postData = [{
          serviceCatalogId: serviceItemDetailDataSet?.current?.get('catalogs')?.[0]?.id,
          totalPrice,
          serviceItem: serviceItemDetailDataSet?.current?.toData(),
          price,
          ...commonData,
        }];
        return [{ cartList: postData, serviceCatalogName: serviceItemDetailDataSet?.current?.get('categoryName'), serviceCatalogId: serviceItemDetailDataSet?.current?.get('categoryId') }];
      }
    } else if (!isShoppingCartCustomAreaValid) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.cartButton.over.max' }));
    }
    return false;
  }

  async function handleBuyNow() {
    const serviceItemPreview = pageRef?.current?.serviceItemDetailDataSet?.getState('serviceItemPreview');
    if (serviceItemPreview) return;
    let postData: any = false;
    postData = await getPostData('BUY_NOW');
    if (!postData) return;
    // 外面的表单校验通过后，弹出弹窗, 然后重新计算值
    const openViewRes = await loadExpression();
    if (!openViewRes) return;
    postData = await getPostData('BUY_NOW', true);
    if (postData) {
      const history = pageRef?.current?.history;
      await refreshId();
      await queryLastPerson();
      // 直接购买跳转结算页
      history.push({
        pathname: '/cart/check',
        search: history?.location?.search,
        state: {
          postData,
          catalogId: pageRef?.current?.history?.location?.state?.catalogId,
        },
      });
    }
  }

  // 立即购买和加入购物车后要刷新_id
  async function refreshId() {
    if (!formDataSet?.current?.get('_id')) return;
    const createViewId = pageRef?.current?.pageData?.id;
    const fieldMap = {};
    const r = await axios.post(`lc/v1/engine/${tenantId}/dataset/${createViewId}/${createViewId}/calculate`, JSON.stringify(fieldMap));
    if (r && !r.failed) {
      formDataSet?.current?.set('_id', r._id);
    }
  }

  async function handleAddToShoppingCart() {
    const serviceItemPreview = pageRef?.current?.serviceItemDetailDataSet?.getState('serviceItemPreview');
    if (serviceItemPreview) return;
    let postData: any = false;
    postData = await getPostData('ADD_TO_SHOPPING_CART');
    if (!postData) return;
    // 外面的表单校验通过后，弹出弹窗, 然后重新计算值
    const openViewRes = await loadExpression();
    if (!openViewRes) return;
    postData = await getPostData('ADD_TO_SHOPPING_CART', true);
    if (postData) {
      try {
        const res = await addToShoppingCart(tenantId, postData);
        if (res?.failed) {
          // 全局捕获异常
          // message.error(res?.message);
        } else {
          message.success(intl.formatMessage({ id: 'lcr.renderer.cartButton.addToShoppingCart.success' }));
          await refreshId();
          await queryLastPerson();
          if (HeaderStore?.setRefreshShoppingCartCount) {
            HeaderStore?.setRefreshShoppingCartCount(Math.round(Math.random() * 10000));
          }
        }
      } catch (e) {
        //
      }
    }
  }

  const loadExpression = async () => {
    if (!openViewId) return true;
    const fieldMap = {
      // _parentId: instanceId, // 计算默认值将父级id传入
    };
    const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${openViewId}/${openViewId}/calculate`, JSON.stringify(fieldMap));
    let returnRes;
    if (result && !result.failed) {
      returnRes = await handleOpenExtraView(result);
    } else {
      returnRes = await handleOpenExtraView(undefined);
    }
    return returnRes;
  };

  async function handleOpenExtraView(defaultData) {
    const viewModalStyle = { width: Number(viewSize) };
    return new Promise((resolve) => {
      Modal.open({
        title: openViewType === 'MIDDLE' ? (viewName || btnName) : '',
        children: (
          <PageLoader
            instanceId={undefined}
            viewId={openViewId}
            pageRef={extraViewPageRef}
            mode={mode}
            openType={openViewType}
            defaultData={defaultData}
          />
        ),
        key: modalKey,
        drawer: openViewType === 'RIGHT',
        style: viewModalStyle,
        destroyOnClose: true,
        onOk: async () => {
          if (await validateMessage(extraViewPageRef.current?.formDataSet, intl)) {
            resolve(true);
            return true;
          }
          resolve(false);
          return false;
        },
        onCancel: () => {
          resolve(false);
        },
      });
    });
  }

  async function handleClick() {
    if (mode === 'PREVIEW') {
      return null;
    }
    switch (type) {
      case 'BUY_NOW':
        await preConfirm(handleBuyNow);
        break;
      case 'ADD_TO_SHOPPING_CART':
        await preConfirm(handleAddToShoppingCart);
        break;
      default:
        break;
    }
    return true;
  }

  async function preConfirm(executionFun) {
    if (confirmFlag) {
      Modal.confirm({
        title: intl.formatMessage({ id: 'zknow.common.button.confirm' }),
        children: (
          <div>{confirmText}</div>
        ),
        okText,
        cancelText,
      }).then((button) => {
        if (button === 'ok') {
          executionFun();
        }
      });
    } else {
      await executionFun();
    }
  }

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <Button
          funcType="raised"
          color={color}
          icon={icon}
          onClick={handleClick}
          id={id}
          key={id}
          className={style[`${prefixCls}-item`]}
        >
          {text}
        </Button>
      </div>
    );
  };

  return renderMain();
});

export default MainView;
