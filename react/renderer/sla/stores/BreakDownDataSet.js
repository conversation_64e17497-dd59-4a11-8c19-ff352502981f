import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';

export default ({
  intl,
  tenantId,
  businessObjectCode,
}) => {
  // 处理组、处理人、单据状态、开始时间、结束时间、总时长、是否超时、sla记录状态
  const assignmentGroupName = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assignmentGroupName' });
  const assignmentPersonName = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.assignmentPersonName' });
  const startTime = intl.formatMessage({ id: 'zknow.common.model.startTime' });
  const stopTime = intl.formatMessage({ id: 'zknow.common.model.endTime' });
  const businessElapsedDuration = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.totalTime' });
  const ticketStatus = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.ticketStatus' });
  const breachedFlag = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breachedFlag' });
  const slaStatus = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.slaStatus' });

  return {
    autoQuery: false,
    paging: false,
    selection: false,
    queryDataSet: new DataSet(),
    transport: {
      read: ({ data }) => {
        return {
          url: `itsm/v1/${tenantId}/incident_task_sla/detail?businessObjectCode=${businessObjectCode}`,
          method: 'get',
          data: getQueryParams(data, ['taskSlaId']),
        };
      },
    },
    fields: [
      // 处理组
      { name: 'assignmentGroupName', label: assignmentGroupName },
      // 处理人
      { name: 'assigneePersonName', label: assignmentPersonName },
      // 开始时间
      { name: 'startTime', label: startTime },
      // 停止时间
      { name: 'endTime', label: stopTime },
      // 总时长
      { name: 'businessElapsedDuration', label: businessElapsedDuration },
      // 单据状态
      { name: 'ticketStatusName', label: ticketStatus },
      // 是否超时
      { name: 'breachedFlag', label: breachedFlag },
      // sla记录状态
      { name: 'slaStatus', label: slaStatus, lookupCode: 'TASK_SLA_STATE' },
    ],
    queryFields: [
      // 处理人
      { name: 'assigneePersonName', label: assignmentPersonName },
      // 处理组
      { name: 'assignmentGroupName', label: assignmentGroupName },
    ],
    events: {

    },
  };
};
