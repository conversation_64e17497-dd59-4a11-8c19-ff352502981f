import { DataSet } from 'choerodon-ui/pro';

export default ({ intl, tenantId, autoLocateFirst, ticketId, businessObjectCode }) => {
  const urlPrefix = `/itsm/v1/${tenantId}/incident_task_sla`;

  const name = intl.formatMessage({ id: 'zknow.common.model.name' });
  const type = intl.formatMessage({ id: 'zknow.common.model.type' });
  const use = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.use' });
  const status = intl.formatMessage({ id: 'zknow.common.model.status' });
  const duration = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.duration' });
  const remainingWorkingDate = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.remainingWorkingDate' });
  const consumedWorkingDate = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.consumedWorkingDate' });
  const startDate = intl.formatMessage({ id: 'zknow.common.model.startTime' });
  const stopDate = intl.formatMessage({ id: 'zknow.common.model.endTime' });
  const timeOut = intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' });

  return {
    autoQuery: true,
    autoLocateFirst,
    paging: false,
    totalKey: 'totalElements',
    dataKey: 'content',
    primaryKey: 'id',
    queryDataSet: new DataSet(),
    selection: false,
    transport: {
      read: ({ data }) => {
        if (!ticketId || !businessObjectCode) return null;
        return {
          url: `${urlPrefix}?incidentId=${ticketId}&businessObjectCode=${businessObjectCode}`,
          method: 'get',
        };
      },
    },
    queryFields: [
      // 名称
      { name: 'slaDefinitionName', label: name },
      // 类型
      { name: 'type', label: type, lookupCode: 'SLA_TYPE' },
      // 用途
      { name: 'target', label: use, lookupCode: 'SLA_TARGET' },
      // 状态
      { name: 'state', label: status, lookupUrl: `/hpfm/v1/${tenantId}/lookup/queryByCode?lookupTypeCode=TASK_SLA_STATE` },
    ],
    fields: [
      // 名称
      { name: 'slaDefinitionName', label: name },
      // 类型
      { name: 'type', label: type, required: true, lookupCode: 'SLA_TYPE' },
      // 用途
      { name: 'target', label: use, required: true, lookupCode: 'SLA_TARGET' },
      // 状态
      { name: 'state', label: status, lookupUrl: `/hpfm/v1/${tenantId}/lookup/queryByCode?lookupTypeCode=TASK_SLA_STATE` },
      // 超时
      { name: 'breachedFlag', label: timeOut },
      // 期限
      { name: 'duration', label: duration },
      // 剩余工作时间
      { name: 'businessLeftDuration', label: remainingWorkingDate },
      // 已消耗工作时间
      { name: 'businessElapsedDuration', label: consumedWorkingDate },
      // 开始时间
      { name: 'startTime', label: startDate },
      // 停止时间
      { name: 'stopTime', label: stopDate },
      // 版本号
      { name: 'objectVersionNumber' },
    ],
    events: {

    },
  };
};
