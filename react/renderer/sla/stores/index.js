import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { durationToDays, daysToMinues } from '@/utils';
import SlaDataSet from './SlaDataSet';
import BreakDownDataSet from './BreakDownDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
      ticketId,
      ticketType,
    } = props;
    const prefixCls = 'sla-renderer';
    const businessObjectCode = ticketType;

    const slaDataSet = useMemo(() => new DataSet(SlaDataSet({
      tenantId,
      businessObjectCode,
      ticketId,
      intl,
    })), [businessObjectCode, ticketId]);

    const breakDownDataSet = useMemo(() => new DataSet(BreakDownDataSet({
      intl,
      tenantId,
      businessObjectCode,
    })), [businessObjectCode]);

    const renderMinutesToDays = (duration) => {
      let value = '-';
      const res = durationToDays(duration || 0);
      if (res?.days !== 0) {
        value = `${res?.days}${intl.formatMessage({ id: 'zknow.common.model.day' })}${res?.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res?.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else if (res?.hours !== 0) {
        value = `${res?.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res?.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else if (res?.minutes !== 0) {
        value = `${res?.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      } else {
        value = `${res?.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
      }
      return value;
    };

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      slaDataSet,
      ticketId,
      breakDownDataSet,
      renderMinutesToDays,
      businessObjectCode,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
