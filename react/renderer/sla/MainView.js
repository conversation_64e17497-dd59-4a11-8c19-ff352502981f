/* eslint-disable react/no-danger */
import React, {
  useState,
  useEffect,
  useContext,
  useRef,
} from 'react';
import { Icon, Empty } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { TextField, Modal, Spin, Tooltip } from 'choerodon-ui/pro';
import { Permission } from '@yqcloud/apps-master';
import Store from './stores';
import SLACard from './components/sla-card/SlaCard';
import FormView from './components/fix-sla';
import './index.less';

const modalKey = Modal.key();

const MainView = observer((props) => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    slaDataSet,
    ticketId,
  } = context;
  const [initCount, setInitCount] = useState(0);
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();
  const repairModalRef = useRef();
  useEffect(() => {
    return () => {
      if (window.slaInterval) {
        clearInterval(window.slaInterval);
      }
    };
  }, []);

  useEffect(() => {
    if (slaDataSet?.status === 'ready' && !!ticketId && initCount === 0) {
      setInitCount(1);
      // 一分钟刷一次
      window.slaInterval = setInterval(() => {
        slaDataSet.query();
      }, 1000 * 60);
    }
  }, [slaDataSet?.status, initCount]);

  // 修复SLA
  function fixSLA() {
    Modal.confirm({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.warning.title' }),
      okFirst: true,
      children: <FormView repairModalRef={repairModalRef} {...context} />,
      destroyOnClose: true,
      onOk: () => {
        repairModalRef?.current?.handleOk();
      },
      onClose: async () => {
        repairModalRef?.current?.handleCancel();
      },
    });
  }

  const renderSLAHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: '20px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              slaDataSet.setQueryParameter('param', e);
              slaDataSet.query();
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              slaDataSet.setQueryParameter('param', e.target.value);
              slaDataSet.query();
            }
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            slaDataSet.setQueryParameter('param', value);
            slaDataSet.query();
          }}
        />
      );
    }
    return (
      <div className="header-top">
        <span className="header-top-title">{intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.title' })}</span>
        <div className="header-top-icon">
          <Permission service={['yqcloud-itsm.task-sla.repairSla']}>
            <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.repair' })}>
              <Icon
                className="filter-icon"
                style={{ marginLeft: '8px' }}
                type="change"
                size={16}
                onClick={() => fixSLA()}
              />
            </Tooltip>
          </Permission>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className="filter-icon"
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  const renderSLAList = () => {
    // 响应
    const RESPONSE_LIST = slaDataSet.filter((i) => i.get('target') === 'RESPONSE') || [];
    // 解决
    const RESOLUTION_LIST = slaDataSet.filter((i) => i.get('target') === 'RESOLUTION') || [];
    if (RESPONSE_LIST.length === 0 && RESOLUTION_LIST.length === 0) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px' }}
          innerStyle={{ width: '80px', height: '80px' }}
          type="empty"
        />
      );
    }
    return (
      <div className={`${prefixCls}-content`}>
        <SLACard
          data={RESPONSE_LIST}
          title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.response' })}
          {...context}
        />
        <SLACard
          data={RESOLUTION_LIST}
          title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution' })}
          {...context}
        />
      </div>
    );
  };

  const renderSLACard = () => {
    if (slaDataSet?.length === 0) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '20px' }}
          innerStyle={{ width: '80px', height: '80px' }}
          type="empty"
        />
      );
    }
    return (
      <div className={`${prefixCls}-content`}>
        {slaDataSet?.map((i) => {
          return (
            <SLACard
              record={i}
              title={intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution' })}
              {...context}
            />
          );
        })}
      </div>
    );
  };

  const renderMain = () => {
    return (
      <div className={prefixCls}>
        <div className={`${prefixCls}-header`}>
          {renderSLAHeader()}
        </div>
        <Spin dataSet={slaDataSet}>
          {renderSLACard()}
        </Spin>
      </div>
    );
  };

  return renderMain();
});

export default MainView;
