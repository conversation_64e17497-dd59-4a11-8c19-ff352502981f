/*
 * @Author: x<PERSON><PERSON>ya
 * @Date: 2022-01-13 14:57:41
 * @Description: sla break down
 */
import React from 'react';
import { Icon, Empty, YqAvatar } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import './index.less';

const SLABreakdown = observer((props) => {
  const {
    record,
    intl,
    intlPrefix,
    renderMinutesToDays,
  } = props;

  // 渲染提示内容
  function renderMain() {
    if (!record) return null;
    const breakDownList = record.get('breakDownList') || [];
    const titleCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-title': true,
    });
    const valueCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-value': true,
      'tooltip-content-cell-name': true,
    });

    const timeCLassName = classnames({
      'tooltip-content-cell': true,
      'tooltip-content-cell-value': true,
      'tooltip-content-cell-time': true,
    });

    return (
      <div className="sla-card-tooltip-main">
        <div className="tooltip-top">
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.breakdown.title' })}
        </div>
        <div className="tooltip-content">
          {
            breakDownList?.length === 0
              ? (
                <Empty
                  style={{ padding: '8px 50px' }}
                  innerStyle={{ width: '80px', height: '80px' }}
                  type="empty"
                />
              )
              : breakDownList.map((i) => {
                return (
                  <div className="tooltip-content-row">
                    <div className={titleCLassName}>{i.assignmentGroupName || '-'}</div>
                    <div className={valueCLassName}>
                      <span className="tooltip-content-cell-flex">
                        {i.assigneePersonName && (
                          <>
                            <YqAvatar src={i.imageUrl} size={22} style={{ marginRight: '4px' }}>
                              {i.assigneePersonName}
                            </YqAvatar>
                          </>
                        )}
                        {i.assigneePersonName || '-'}
                      </span>
                    </div>
                    <div className={timeCLassName}>
                      <span className="tooltip-content-cell-flex">
                        {
                          parseInt(i.totalTime || 0, 10) !== 0 && (
                            <>
                              <Icon
                                type="time"
                                size={16}
                                style={{
                                  marginRight: '4px',
                                }}
                              />
                              {renderMinutesToDays(parseInt(i.totalTime || 0, 10)) || '-'}
                            </>
                          )
                        }
                      </span>
                    </div>
                  </div>
                );
              })
          }
        </div>
      </div>
    );
  }

  return renderMain();
});

export default SLABreakdown;
