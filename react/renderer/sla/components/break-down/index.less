@import '~choerodon-ui/lib/style/themes/default';

.sla-card-tooltip {
  background-color: #ffffff;
  min-width: 200px;
  .@{c7n-pro-prefix}-tooltip-popup-inner {
    background: #FFFFFF !important;
    box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 0px;
  }
  .@{c7n-pro-prefix}-tooltip-popup-arrow {
    display: none;
    &-dark {
      display: none;
    }
  }
  .@{c7n-prefix}-avatar-string {
    transform: scale(0.875) translateX(-50%) !important;
    font-size: 14px !important;
  }
  &-main {
    .tooltip-top {
      padding: 12px 16px;
      display: flex;
      align-items: center;
      // background: rgba(41, 121, 255, 0.1);
      // box-shadow: 0px 3px 12px 0px rgba(0, 0, 0, 0.12);
      font-size: 14px;
      font-weight: 500;
      // color: #2B2D38;
      color: #12274D;
      line-height: 20px;
    }
    .tooltip-content {
      display: table;
      padding: 0px 16px 12px 16px;
      &-row {
        display: table-row;
      }
      &-cell {
        display: table-cell;
        text-align: right;
        padding: 5px 0px;
        padding-right: 16px;
        text-align: left;
        vertical-align: middle;
        text-align: start;
        min-width: 106px;
        &-title {
          font-size: 14px;
          font-weight: 400;
          // color: #12274d;
          color: #12274D;
          line-height: 22px;
          text-shadow: 0px 3px 12px rgba(0, 0, 0, 0.12);
          // white-space: nowrap;
          white-space: unset;
          width: 3000px; // 尽可能让第一列宽一点
        }
        &-value {
          font-size: 14px;
          font-weight: 400;
          // color: #12274d;
          color: #12274D;
          line-height: 22px;
          text-shadow: 0px 3px 12px rgba(0, 0, 0, 0.12);
          white-space: nowrap;
        }
        &-time {
          // display: flex;
          // align-items: center;
          // justify-content: flex-end;
          text-align: right;
          padding-right: 0px;
          color: rgba(18, 39, 77, 0.85);
        }
        &-flex {
          display: inline-flex;
          align-items: center;
        }
      }
    }

  }
}
