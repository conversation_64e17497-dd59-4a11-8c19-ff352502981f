/*
 * @Author: x<PERSON><PERSON>ya
 * @Date: 2022-01-17 15:47:38
 * @Description: SLA详情。
 */
import { Empty } from '@zknow/components';
import {
  Table,
} from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import React from 'react';

const { Column } = Table;

function DetailView(props) {
  const {
    intl,
    dataSet,
    renderMinutesToDays,
  } = props;

  const renderNullValue = ({ text }) => {
    if (text) {
      return text;
    }
    return '-';
  };

  const renderBreachedFlag = ({ record }) => {
    if (record.get('breachedFlag')) {
      return (
        <span style={{ color: '#F8353F' }}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' })}
        </span>
      );
    }
    if (record.get('state') === 'COMPLETED' && !record.get('breachedFlag')) {
      return (
        <span style={{ color: '#75C940' }}>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.done' })}
        </span>
      );
    }
    return '-';
  };

  return (
    <Table
      dataSet={dataSet}
      condition="param"
      // autoHeight={{ type: 'minHeight', diff: 40 }}
      empty={<Empty type="empty" />}
      queryBarProps={{
        queryFieldsStyle: {
          assignmentGroupName: { width: 140 },
          assigneePersonName: { width: 140 },
        },
      }}
    >
      <Column name="assignmentGroupName" renderer={({ text }) => (text || '-')} />
      <Column name="assigneePersonName" renderer={({ text }) => (text || '-')} />
      <Column name="startTime" width={180} renderer={({ record, text }) => renderNullValue({ record, text })} />
      <Column name="endTime" width={180} renderer={({ record, text }) => renderNullValue({ record, text })} />
      <Column name="businessElapsedDuration" renderer={({ record, text }) => renderMinutesToDays(parseInt(text || 0, 10)) || '-'} />
      <Column name="ticketStatusName" renderer={({ text }) => (text || '-')} />
      <Column name="breachedFlag" width={80} renderer={({ record }) => renderBreachedFlag({ record })} />
      <Column name="slaStatus" />
    </Table>
  );
}

export default observer(DetailView);
