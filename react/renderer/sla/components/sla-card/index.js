/* eslint-disable jsx-a11y/alt-text */
/*
 * @Author: xiaoreya
 * @Date: 2021-12-23 10:34:49
 * @Description: 每一项SLA
 */
import React, { useMemo } from 'react';
import { Icon, Empty } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { Modal, Tooltip } from 'choerodon-ui/pro';
import FormTitle from '@/components/title';
import DetailView from './DetailView';
import SLABreakdown from '../break-down';
import BAEACHED_FILL from '../../images/breached-fill.gif';
import IN_PROCESS_FILL from '../../images/inprocess-fill.gif';
import './index.less';

const modalKey = Modal.key();

const SLACard = observer((props) => {
  const {
    data = [], // record集合
    title,
    intl,
    prefixCls,
    renderMinutesToDays,
    breakDownDataSet,
    tenantId,
  } = props;

  const areaTitle = useMemo(() => {
    return <FormTitle className="lc-ticket-area-title" title={title} />;
  }, [title]);

  async function getBreakDownDetail(record) {
    if (!record?.get('id')) return;
    breakDownDataSet.setQueryParameter('taskSlaId', record?.get('id'));
    await breakDownDataSet.query();
  }

  async function handleOpenDetail({ record }) {
    if (!record) return null;
    await getBreakDownDetail(record);
    Modal.open({
      key: modalKey,
      title: record?.get('slaDefinitionName'),
      className: `${prefixCls}-modal`,
      okFirst: true,
      style: { width: '1200px' },
      children: (
        <DetailView
          dataSet={breakDownDataSet}
          intl={intl}
          prefixCls={prefixCls}
          tenantId={tenantId}
          renderMinutesToDays={renderMinutesToDays}
        />
      ),
      destroyOnClose: true,
      footer: (okBtn, cancelBtn) => cancelBtn,
      afterClose: () => {
        breakDownDataSet?.queryDataSet?.reset();
        breakDownDataSet?.reset();
      },
    });
  }

  const renderDuration = ({ record }) => {
    const durationJSON = record.get('duration');
    const res = durationJSON && JSON.parse(durationJSON);
    let value = '-';
    if (res?.days && res?.days !== 0) {
      value = `${res?.days}${intl.formatMessage({ id: 'zknow.common.model.day' })}${res?.hours || 0}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res?.minutes || 0}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else if (res?.hours && res?.hours !== 0) {
      value = `${res?.hours}${intl.formatMessage({ id: 'zknow.common.model.hour' })}${res?.minutes || 0}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else if (res?.minutes && res?.minutes !== 0) {
      value = `${res?.minutes}${intl.formatMessage({ id: 'zknow.common.model.minute' })}`;
    } else {
      value = '-';
    }
    return value;
  };

  const renderTimeout = ({ record }) => {
    if (/* 进行中 */ record.get('state') === 'IN_PROGRESS') {
      if (record.get('breachedFlag')) {
        const time = renderMinutesToDays(parseInt(record.get('businessElapsedDuration') || 0, 10) - parseInt(record?.get('durationCount') || 0, 10));
        return (
          <>
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.comma' })}
            <span className="card-bottom-right-timeout">
              {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution.timeout' }, { time })}
            </span>
          </>
        );
      }
      return (
        <>
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.comma' })}
          <span className="card-bottom-right-rightOff">
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.resolution.surplus' }, { time: renderMinutesToDays(parseInt(record.get('businessLeftDuration') || 0, 10)) })}
          </span>
        </>
      );
    }
  };

  const renderBreachedFlag = ({ record }) => {
    const iconStyle = {
      style: { marginRight: '4px' },
      theme: 'multi-color',
      size: '14px',
      strokeLinecap: 'square',
    };
    if (/* 完成 */ record.get('state') === 'COMPLETED') {
      // 超时
      if (record.get('breachedFlag')) {
        return (
          <span className="card-top-status-breached">
            <Icon {...iconStyle} type="attention" fill={['#FFFFFF', '#FFFFFF', '#F8353F', '#FFFFFF']} />
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.timeOut' })}
            {renderMinutesToDays(parseInt(record.get('businessElapsedDuration') || 0, 10) - parseInt(record?.get('durationCount') || 0, 10))}
          </span>
        );
      }
      // 达标
      return (
        <span className="card-top-status-done">
          <Icon {...iconStyle} type="check-one" fill={['#FFFFFF', '#FFFFFF', '#7BC95A', '#FFFFFF']} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.done' })}
        </span>
      );
    }
    if (/* 进行中 */ record.get('state') === 'IN_PROGRESS') {
      // 进行中超时
      if (record.get('breachedFlag')) {
        return (
          <span className="card-top-status-breached">
            <img className="status-image" src={BAEACHED_FILL} />
            {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.inProgress' })}
          </span>
        );
      }
      return (
        <span className="card-top-status-breached card-top-status-breached-inProgress">
          <img className="status-image" src={IN_PROCESS_FILL} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.inProgress' })}
        </span>
      );
    }
    if (/* 暂挂 */ record.get('state') === 'PAUSED') {
      return (
        <span className="card-top-status-breached card-top-status-breached-paused">
          <Icon {...iconStyle} type="ReduceOne" fill={['#FFFFFF', '#FFFFFF', '#6454F4', '#FFFFFF']} />
          {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.paused' })}
        </span>
      );
    }
    if (/* 取消 */ record.get('state') === 'CANCEL') {
      return (
        <span className="card-top-status-breached card-top-status-breached-cancel">
          <Icon {...iconStyle} type="CloseOne" fill={['#FFFFFF', '#FFFFFF', '#8C8C8C', '#FFFFFF']} />
          {intl.formatMessage({ id: 'zknow.common.button.cancel' })}
        </span>
      );
    }
    return '-';
  };

  // 截取时间
  function subStringTime(time) {
    if (!time) return null;
    return time?.substring(5)?.substring(-1, 11);
  }

  // 渲染提示内容
  function renderCardTooltip(record) {
    if (!record) return null;
    return <SLABreakdown record={record} {...props} />;
  }

  const renderCardItem = () => {
    if (data?.length === 0) {
      return (
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '36px' }}
          innerStyle={{ width: '80px', height: '80px' }}
          type="empty"
        />
      );
    }
    return data.map((i) => {
      const target = (i.getField('target')?.props?.lookupData || [])?.find((j) => j.code === i.get('target'))?.meaning;
      return (
        <div className={`${prefixCls}-area-card`}>
          <div className="card-top">
            <Tooltip placement="bottomLeft" title={renderCardTooltip(i)} popupClassName="sla-card-tooltip">
              <div className="card-top-title">{i.get('slaDefinitionName')}</div>
            </Tooltip>
            <div className="card-top-status">{renderBreachedFlag({ record: i })}</div>
          </div>
          <div className="card-bottom">
            <div className="card-bottom-left">
              <span className="card-bottom-left-target">{target}</span>
              <Tooltip
                title={
                  <span className="card-bottom-left-time">
                    {i?.get('startTime')}
                    {i?.get('stopTime') && ` ~ ${i?.get('stopTime')}`}
                  </span>
                }
              >
                <span className="card-bottom-left-time">
                  {subStringTime(i?.get('startTime'))}
                  {i?.get('stopTime') && ` ~ ${subStringTime(i?.get('stopTime'))}`}
                </span>
              </Tooltip>
            </div>
            <div className="card-bottom-right">
              <span className="card-bottom-right-content">
                {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.duration' })}
                {renderDuration({ record: i })}
                {renderTimeout({ record: i })}
              </span>
              <span className="card-bottom-right-extra" onClick={() => handleOpenDetail({ record: i })}>
                <Icon type="view-grid-detail" size="16" style={{ marginRight: '8px' }} />
                {intl.formatMessage({ id: 'zknow.common.model.detail' })}
              </span>
            </div>
          </div>
        </div>
      );
    });
  };

  const renderMain = () => {
    if (data?.length === 0) return null;
    return (
      <div className={`${prefixCls}-area`}>
        {areaTitle}
        {renderCardItem()}
      </div>
    );
  };
  return renderMain();
});

export default SLACard;
