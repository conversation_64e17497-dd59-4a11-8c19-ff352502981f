/*
 * @Author: xia<PERSON>ya
 * @Date: 2021-12-24 10:11:30
 * @Description: 修复sla
 */
import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { ModalProvider, Modal, Progress, message } from 'choerodon-ui/pro';
import axios from 'axios';

const modalProgressKey = Modal.key();

const ProgressContent = (props) => {
  const { value, status, modal } = props;
  return <Progress value={value} status={status} />;
};

export default forwardRef((props) => {
  const { modal, intl, intlPrefix, slaDataSet, tenantId, ticketId, repairModalRef, businessObjectCode } = props;
  const [completed, setCompleted] = useState(0);
  const [status, setStatus] = useState('active');

  useImperativeHandle(repairModalRef, () => ({
    handleOk: () => handleRepairSLA(),
    handleCancel: () => {
      setCompleted(0);
      setStatus('active');
    },
  }));

  useEffect(() => {
    if (completed !== 0) {
      handleOpenProgressModal();
    }
  }, [completed, status]);

  function handleRepairSLA() {
    handleOpenProgressModal();
    progress(completed);
    axios.post(`/itsm/v1/${tenantId}/incident_task_sla/sla/repair?businessObjectCode=${businessObjectCode}`, JSON.stringify(
      [ticketId]
    )).then((res) => {
      if (res?.failed) {
        message.error(res.message);
        setStatus('exception');
      } else {
        progress(100);
        message.success(intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.fix.success' }));
        slaDataSet.query();
      }
    }).catch((err) => {
      // console.log('err', err);
    });
    return true;
  }

  function handleOpenProgressModal(data) {
    const { _completed, _status } = data || {};
    Modal.open({
      key: modalProgressKey,
      title: intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.repair.title' }),
      okFirst: true,
      children: <ProgressContent value={_completed || completed} status={_status || status} />,
      footer: (okBtn, cancelBtn) => [
        cancelBtn,
      ],
      destroyOnClose: true,
      cancelText: intl.formatMessage({ id: 'zknow.common.button.close' }),
      onCancel: async () => {
        setCompleted(0);
        setStatus('active');
      },
      onClose: async () => {
        setCompleted(0);
        setStatus('active');
      },
    });
  }

  function progress(data) {
    if (data === 100) {
      setStatus('success');
      setCompleted(100);
      handleOpenProgressModal({ _completed: 100, _status: 'success' });
    } else if (data > 90) {
      // NONE
    } else {
      setCompleted(data);
      const diff = Math.round(Math.random() * 50);
      setTimeout(() => progress(data + diff), 10);
    }
  }

  return (
    <div style={{ display: 'flex', justifyContent: 'center' }} ref={repairModalRef}>
      {intl.formatMessage({ id: 'lcr.renderer.serviceAgreement.warning.content' })}
    </div>
  );
});
