@import '~choerodon-ui/lib/style/themes/default';

.sla-renderer {
  padding: 16px;
  .flex-center {
    display: flex;
    align-items: center;
  }
  &-header {
    .header-top {
      .flex-center;
      justify-content: space-between;
      margin-bottom: 16px;
      &-title {
        font-size: 16px;
        font-weight: 500;
        color: #12274d;
        line-height: 22px;
      }
      &-icon {
        .flex-center;
        .filter-icon {
          cursor: pointer;
          background: @minor-color;
          border-radius: 4px;
          padding: 8px;
          line-height: 1;
          height: 32px;
          color: @primary-color;
          &:hover {
            background-color: @primary-6;
            color: #fff;
          }
        }
      }
    }
    .header-bottom {
      .flex-center;
      margin-bottom: 21px;
      &-item {
        margin-right: 24px;
      }
    }
  }
}
