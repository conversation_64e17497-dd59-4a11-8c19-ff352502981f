import React, { useEffect, useMemo, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent, Button, Icon } from '@zknow/components';
import axios from 'axios';
import classnames from 'classnames';
import { Modal, message } from 'choerodon-ui/pro';
import styles from './SplitTicket.module.less';

const SplitModal = (props) => {
  const { modal, viewCode, ticketData, tenantId, intl, detailViewId, tabMenuDataSet } = props;

  const count = useRef(1); // 单据计数
  const [ticketDataList, setDataList] = useState([]);
  const [currentTicket, setCurrentTicket] = useState(); // setCurrentTicket必须从ticketDataList中set（而不是从createViewPageRef），因为必须要包含_id信息

  const createViewPageRef = useRef();

  const [viewId, setViewId] = useState();

  const handleConfirm = async () => {
    const flag = await createViewPageRef?.current?.formDataSet?.validate();
    if (!flag) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.cannot.confirm', defaultMessage: '不能保存，因为尚有必填字段为空' }));
      return false;
    }
    const current = createViewPageRef?.current?.formDataSet?.toData()?.[0]; // 目前编辑过的单据信息
    const currentId = currentTicket._id; // 目前单据的_id
    const newList = ticketDataList.map((v) => {
      if (v._id === currentId) {
        return { ...current }; // 保存刚刚的数据，因为oldCurrent没有_id信息，要额外加上
      }
      return { ...v };
    });
    const datasetId = createViewPageRef?.current?.pageData?.id;
    const ticketList = newList.map(({ _id, id, ...rest }) => rest);
    const data = {
      ticketList,
      originTicket: ticketData,
    };
    const res = await axios.post(`/itsm/v1/${tenantId}/tms/${viewId}/${datasetId}/split`, data);
    if (!res?.failed && res?.length > 0) {
      message.success(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.success', defaultMessage: '拆分成功' }));
      res.forEach((ticket, i) => {
        const openedTab = tabMenuDataSet.create({ ...ticket, extraInstanceId: ticket.id });
        openedTab.setState('viewId', detailViewId); // 打开新标签页并设置viewId
        openedTab.setState('isLoadDefaultData', true); // 不设置的话在服务工作台上就打不开
        if (i === res.length - 1) { // 最后一张单子被打开
          tabMenuDataSet.forEach(r => r.setState('current', false));
          openedTab.setState('current', true);
          openedTab.setState('needRefreshTask', true);
          openedTab.setState('approval', true);
          openedTab.setState('viewCode', viewCode);
        }
      });
      return true;
    } else {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.failed', defaultMessage: '拆分失败，请稍后重试' }));
    }
    return false;
  };

  useEffect(() => {
    modal.update({
      onCancel: () => {
        Modal.open({
          title: intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.close', defaultMessage: '关闭确认' }),
          children: <div>{intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.close.confirm', defaultMessage: '确定退出拆分单据吗？已填写内容将不会保存' })}</div>,
          onOk: () => {
            modal.close();
            return true;
          },
        });
        return false;
      },
      onOk: () => {
        handleConfirm().then((res) => {
          if (res) modal.close();
        });
        return false;
      },
    });
  }, [ticketDataList, currentTicket?._id]);

  useEffect(() => {
    initView().then(() => {
      // 1 设置好了viewId
      const initList = [
        { ...ticketData, _id: 0, choose: true },
        { ...ticketData, _id: 1, choose: false },
      ];
      setDataList(initList); // 2 第一、二张单子
      setCurrentTicket(initList[0]); // 3 设置currentTicket
    });
  }, []);

  const fetchNewViewId = async () => {
    const createCodeMap = {
      INCIDENT: 'INCIDENT_NEW',
      CHANGE: 'CHANGE_NEW',
      CHANGE_TASK: 'CTASK_NEW',
      PROBLEM: 'PROBLEM_NEW',
      PROBLEM_TASK: 'PTASK_NEW',
    };
    const newViewCode = `${viewCode}_NEW`;
    const newView = await axios.get(
      `/lc/v1/${tenantId}/views/form/code/${createCodeMap[viewCode] || newViewCode}`
    );
    return newView?.id;
  };

  const initView = async () => {
    const id = await fetchNewViewId();
    setViewId(id);
  };
  const renderPageLoader = () => {
    if (viewId) {
      return (
        <ExternalComponent
          system={{
            scope: 'lcr',
            module: 'PageLoader',
          }}
          viewId={viewId}
          defaultData={currentTicket}
          mode="CREATE"
          ticketFlag
          disableACL
          modal={modal}
          pageRef={createViewPageRef}
          submissionChannel="COPY_FROM_INCIDENT"
          showHeaderFlag={false}
          hiddenFooter
        />
      );
    }
    return null;
  };
  const handleAddTicket = async () => {
    const res = await createViewPageRef?.current?.formDataSet?.validate();
    if (!res) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.cannot.split', defaultMessage: '不能拆分新单据，因为尚有必填字段为空' }));
      return;
    }
    if (ticketDataList.length < 5) {
      setDataList([...ticketDataList, { ...ticketData, _id: count.current + 1, choose: false }]); // ticketDataList中后续加入的单据，加入的时候就有_id
      count.current += 1;
    } else {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.max.split', defaultMessage: '最多支持同时拆分5个单据' }));
    }
  };
  const handleDeleteTicket = (e, id) => {
    e.stopPropagation();
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.delete', defaultMessage: '删除确认' }),
      children: <div>{intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.delete.confirm', defaultMessage: '确定要删除单据吗？已填写内容将不会保存' })}</div>,
      onOk: () => {
        let index = ticketDataList.findIndex((v) => v._id === id);
        if (index === ticketDataList.length - 1) { // 最后一个
          index -= 1;
        }
        const newList = ticketDataList.filter((ticket) => ticket._id !== id);
        newList.forEach((v, i) => {
          v.choose = (i === index); // 当前index的choose置为true
        });
        setDataList(newList);
        setCurrentTicket(newList[index]);
      },
    });
  };

  const handleSwitchTicket = async (newId) => {
    const res = await createViewPageRef?.current?.formDataSet?.validate();
    if (!res) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.cannot.switch', defaultMessage: '不能切换，因为尚有必填字段为空' }));
      return;
    }
    const oldCurrent = createViewPageRef?.current?.formDataSet?.toData()?.[0]; // 目前编辑过的单据信息
    const oldId = currentTicket._id; // 目前单据的_id
    let newCurrent; // 将要切换到的单据信息
    const newList = ticketDataList.map((v) => {
      if (v._id === newId) {
        newCurrent = { ...v, choose: true }; // 切换新的tab
        return newCurrent;
      }
      if (v._id === oldId) {
        return { ...oldCurrent, _id: oldId, choose: false }; // 保存刚刚的数据，因为oldCurrent没有_id信息，要额外加上
      }
      return { ...v, choose: false };
    });
    setDataList(newList);
    setCurrentTicket(newCurrent);
  };

  return (
    <div className={styles.modalFrame}>
      <div className={styles.leftPart}>
        <div>
          {ticketDataList?.map((ticket, index) => {
            return (
              <div
                className={classnames({
                  [styles.splitItem]: true,
                  [styles.hoverOnSplitItem]: ticket.choose,
                })}
                onClick={() => handleSwitchTicket(ticket._id)}
              >
                <div>{intl.formatMessage({ id: 'lcr.components.desc.ticket', defaultMessage: '单据' })}{(index || 0) + 1}</div>
                <div
                  className={classnames({
                    [styles.deleteBtn]: true,
                    [styles.deleteHidden]: ticketDataList.length <= 2,
                  })}
                >
                  <Icon
                    type="delete"
                    fill="#12274D"
                    size={14}
                    onClick={(e) => handleDeleteTicket(e, ticket._id)}
                  />
                </div>
              </div>
            );
          })}
        </div>
        <Button className={styles.addButton} icon="plus" onClick={handleAddTicket}>
          {intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split.add', defaultMessage: '继续拆分' })}
        </Button>
      </div>
      <div className={styles.rightPart} key={currentTicket?._id}>
        {renderPageLoader()}
      </div>
    </div>
  );
};

export default observer(SplitModal);
