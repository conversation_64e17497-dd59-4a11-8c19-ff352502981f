import React, { useMemo, useContext } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { Menu, Modal } from 'choerodon-ui/pro';
import { Icon, Button } from '@zknow/components';
import styles from './SplitTicket.module.less';
import SplitModal from './SplitModal';
import Store from '@/components/page-loader/stores';
import { StoreProvider } from '@/components/page-loader/stores';

const modalKey = Modal.key();

const MainView = observer((props) => {
  const {
    intl,
    tenantId,
    formDataSet,
    viewDataSet,
    record,
    viewCode,
    feature,
    config,
  } = props;
  const context = useContext(Store);
  const { tabMenuDataSet } = context;
  function handleSplitTicket() {
    Modal.open({
      className: styles.modalOuterFrame,
      key: modalKey,
      style: { width: 1200, top: 55 },
      title: intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split', defaultMessage: '拆分单据' }),
      children: (
        <SplitModal
          viewCode={viewCode || viewDataSet?.current?.get('businessObjectCode')}
          ticketData={formDataSet?.toData()?.[0]}
          tenantId={tenantId}
          intl={intl}
          detailViewId={viewDataSet?.current?.get('id')}
          tabMenuDataSet={tabMenuDataSet}
        />
      ),
      destroyOnClose: true,
    });
  }

  const buttonText = config?.name || intl.formatMessage({ id: 'lcr.renderer.desc.ticket.split', defaultMessage: '拆分单据' });

  return feature === 'table-action' ? (
    <Menu.Item key={config?.id || 'ticketSplit'}>
      <div key="copy" onClick={handleSplitTicket} className={styles.buttonFrame}>
        <Icon
          className="icon"
          size="16"
          type={config?.icon || 'copy-one'}
          style={{ marginRight: '8px' }}
        />
        {buttonText}
      </div>
    </Menu.Item>
  ) : (
    <Button
      funcType="raised"
      color={config?.color || 'default'}
      onClick={handleSplitTicket}
      icon={config?.icon || 'copy-one'}
    >
      {buttonText}
    </Button>
  );
});

export default inject('AppState')(observer((props) => (
  <StoreProvider {...props}>
    <MainView {...props} />
  </StoreProvider>
)));

/* externalize: SplitTicket */
