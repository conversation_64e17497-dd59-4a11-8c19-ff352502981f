.buttonFrame {
  display: flex;
  align-items: center;
}
.modalFrame {
  display: flex;
  //height: 100%;
  .leftPart {
    height: calc(100vh - 3rem);
    width: 200px;
    flex-grow: 0;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 4px 16px;
    .addButton {
      width: 168px;
      height: 32px;
    }
    .splitItem {
      width: 168px;
      height: 32px;
      border-radius: 2px;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      box-sizing: border-box;
      position: relative;
      align-items: center;
      &:hover {
        background: #f3f5f8;
      }
    }
    .hoverOnSplitItem {
      background: #e9f1ff;
    }
    .deleteBtn {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      box-sizing: border-box;
      height: 100%;
      width: 90%;
      left: 0;
      opacity: 0;
      &:hover {
        opacity: 1;
      }
    }
    .deleteHidden {
      display: none;
    }
  }
  .rightPart {
    max-height: calc(100vh - 3rem);
    overflow: hidden;
    width: 1000px;
    flex-grow: 0;
    flex-shrink: 0;
  }
}
.modalOuterFrame {
  :global {
    .c7n-pro-modal-body {
      padding: 0 !important;
      position: relative;
    }
  }
}
