import React, { useState, useMemo } from 'react';
import { Modal } from 'choerodon-ui/pro';
import { Icon, YqAvatar } from '@zknow/components';
import classnames from 'classnames';
import AvatarTooltip from '@/components/avatar-tooltip';
import { sec2string } from '@/utils';
import Table from './Table';

import styles from './Card.less';

export default ({ intl, task, titleFlag }) => {
  const {
    name,
    sla: {
      breachTime,
      businessElapsedDuration,
      breakDownList,
      breachedFlag,
      durationCount,
      businessLeftDuration,
      state,
    },
    allSlas,
  } = task;
  const [collpased, setCollpase] = useState(true);

  const listData = useMemo(
    () => allSlas
      ?.reduce((pre, cur) => [...pre, ...cur?.breakDownList], []) || breakDownList,
    [task]
  );

  const allBreakDownList = useMemo(
    () => allSlas
      ?.reduce((pre, cur) => [...pre, {
        ...cur?.breakDownList?.[cur?.breakDownList.length - 1],
        businessElapsedDuration: cur.businessElapsedDuration,
      }], []) || breakDownList, [task]
  );

  const handleOpenModal = () => {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.desc.time.consuming.detail', defaultMessage: '耗时明细' }),
      children: <Table data={listData} intl={intl} />,
      className: styles.tableModal,
      style: { width: 1000 },
    });
  };

  const getState = () => {
    let _dr = 0;
    let _state = 'ON_TIME';
    if (breachedFlag) { // 超时的任务
      _dr = businessElapsedDuration - durationCount;
      _state = 'OVERTIME';
    } else if (['IN_PROGRESS', 'PAUSED'].includes(state)) { // 处理中 还未超时
      _dr = businessLeftDuration;
      _state = 'REMAIN';
    } else {
      _dr = businessLeftDuration;
      _state = 'ON_TIME';
    }
    return {
      duration: sec2string(_dr * 60, intl),
      state: _state,
    };
  };

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <div className={styles.task}>
          {titleFlag ? <div className={styles.taskTitle}>{name}</div> : null}
          <RemainingTime intl={intl} {...getState()} />
        </div>
        <div className={styles.detail} onClick={handleOpenModal}>{intl.formatMessage({ id: 'lcr.renderer.desc.time.consuming.detail', defaultMessage: '耗时明细' })}</div>
      </div>
      <div className={styles.completedTime}>
        {intl.formatMessage({ id: 'lcr.renderer.desc.estimated.time.of.completion', defaultMessage: '预计完成时间：{time}' }, { time: breachTime })}
      </div>
      <div className={styles.duration}>
        <span><Icon type="ArrowRight" />
          {intl.formatMessage({ id: 'lcr.renderer.desc.consumed.duration', defaultMessage: '已消耗{dr}' }, { dr: sec2string(businessElapsedDuration * 60, intl) })}
        </span>
        {breakDownList?.length
          ? <Icon type={collpased ? 'down' : 'up'} onClick={() => setCollpase(!collpased)} />
          : null}
      </div>
      <div className={classnames(styles.people, {
        [styles.display]: !collpased,
      })}
      >
        {allBreakDownList.map(i => {
          return (
            <div className={styles.person}>
              {i.assigneePersonId
                ? (
                  <AvatarTooltip id={i.assigneePersonId}>
                    <span className={styles.personName}>
                      <YqAvatar size={22}>{i.assigneePersonName}</YqAvatar>
                      <span>{i.assigneePersonName}</span>
                    </span>
                  </AvatarTooltip>
                ) : (allBreakDownList.length > 1 ? <span>-</span> : '')}
              <span>{i.assignmentGroupName}</span>
              <span><Icon type="time" /><span>{sec2string(i.businessElapsedDuration * 60, intl)}</span></span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

const STATUS_MAP = {
  REMAIN: {
    icon: 'time',
    background: '#FF9500',
    fill: ['#fff', '#fff', '#FF9500', '#fff'],
    textIntlId: 'lcr.renderer.desc.onTheLock',
    durationIntlId: 'lcr.renderer.desc.leftDuration',
  },
  OVERTIME: {
    icon: 'attention',
    background: '#F8353F',
    fill: ['#fff', '#fff', '#F8353F', '#fff'],
    textIntlId: 'lcr.renderer.desc.processingTimeout',
    durationIntlId: 'lcr.renderer.desc.overTime',
  },
  ON_TIME: {
    icon: 'check-one',
    background: '#7BC95A',
    fill: ['#fff', '#fff', '#7BC95A', '#fff'],
    textIntlId: 'lcr.renderer.desc.treatmentUpToStandard',
    durationIntlId: 'lcr.renderer.desc.inAdvanceTime',
  },
};

const RemainingTime = ({ state: _state, duration: _dr, intl }) => {
  const state = STATUS_MAP[_state];
  const duration = `${intl.formatMessage({ id: state.durationIntlId }, { dr: _dr })}`;
  return (
    <div className={styles.remainTime}>
      <div className={styles.remainStatus} style={{ background: state.background }}>
        <Icon type={state.icon} theme="multi-color" fill={state.fill} size={14} />
        <span className={styles.statusText}>{intl.formatMessage({ id: state.textIntlId })}</span>
      </div>
      <div className={styles.remainDr} style={{ color: state.background }}>
        {duration}
      </div>
    </div>
  );
};
