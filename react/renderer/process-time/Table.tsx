import React, { useMemo } from 'react';
import { Table, DataSet } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { sec2string } from '@/utils';

import styles from './Table.less';

export default ({ data, intl }) => {
  const intlPrefix = 'lc.components.sla';
  const boolOptions = useMemo(() => new DataSet({
    data: [
      { meaning: intl.formatMessage({ id: 'zknow.common.status.yes', defaultMessage: '是' }), value: true },
      { meaning: intl.formatMessage({ id: 'zknow.common.status.no', defaultMessage: '否' }), value: false },
    ],
  }), []);
  const ds = useMemo(() => {
    const assignmentGroupName = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.group.name', defaultMessage: '处理组' });
    const assignmentPersonName = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.person.name', defaultMessage: '处理人' });
    const startTime = intl.formatMessage({ id: 'zknow.common.model.startTime', defaultMessage: '开始时间' });
    const stopTime = intl.formatMessage({ id: 'zknow.common.model.endTime', defaultMessage: '结束时间' });
    const businessElapsedDuration = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.sla.total.time', defaultMessage: '总时长' });
    const ticketStatus = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.sla.ticket.status', defaultMessage: '单据状态' });
    const breachedFlag = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.sla.breached.flag', defaultMessage: '是否超时' });
    const slaStatus = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.sla.sla.status', defaultMessage: 'SLA记录状态' });

    return new DataSet({
      data,
      paging: false,
      selection: false,
      fields: [
        { name: 'assignmentGroupName', label: assignmentGroupName },
        { name: 'assigneePersonName', label: assignmentPersonName },
        { name: 'startTime', label: startTime },
        { name: 'endTime', label: stopTime },
        { name: 'breachedFlag', label: breachedFlag, options: boolOptions },
        { name: 'ticketStatusName', label: ticketStatus },
        { name: 'slaStatus', label: slaStatus, lookupCode: 'TASK_SLA_STATE' },
        { name: 'businessElapsedDuration', label: businessElapsedDuration },
      ],
    });
  }, []);

  const renderDuration = ({ value, record }) => {
    return <span className={styles.duration} style={{ color: record?.get('breachedFlag') ? '#F34C4B' : '#2979FF' }}>
      <Icon type="time" />
      {sec2string(value * 60, intl)}
    </span>;
  };

  const columns = useMemo(() => [
    { name: 'assigneePersonName', lock: 'left' },
    { name: 'assignmentGroupName' },
    { name: 'startTime', width: 180 },
    { name: 'endTime', width: 180 },
    { name: 'breachedFlag' },
    { name: 'ticketStatusName' },
    { name: 'slaStatus' },
    { name: 'businessElapsedDuration', renderer: renderDuration },
  ], []);

  return (
    <Table
      dataSet={ds}
      columns={columns}
      autoHeight
      queryBar="none"
    />
  );
};
