.card {
  background: #FFFFFF;
  padding: 12px;
  margin-top: 12px;
  border-radius: 4px;
  border: 1px solid rgba(203,210,220,0.5);
  &:has(.display),
  &:hover {
    background: #F7F9FC;
  }
}
.header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.task {
  display: flex;
  flex-direction: column;
}
.taskTitle {
  font-weight: 500;
  color: #12274D;
  line-height: 22px;
  margin-bottom: 12px;
}
.detail {
  color: @primary-color;
  line-height: 22px;
  height: 22px;
  cursor: pointer;
}
.remainTime {
  display: flex;
  align-items: center;
}
.remainStatus {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 22px;
  background: #FF9500;
  border-radius: 11px;
  padding: 4px;
}
.statusText {
  margin: 1px 4px;
  color: #fff;
  font-size: 12px;
}
.remainDr {
  font-weight: 500;
  color: #FF9100;
  line-height: 22px;
  font-size: 14px;
  height: 22px;
  margin-left: 8px;
}
.completedTime {
  height: 22px;
  color: rgba(18,39,77,0.65);
  line-height: 22px;
  margin: 4px 0;
}
.duration {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  color: rgba(18,39,77,0.85);
  line-height: 22px;
  height: 22px;
  & > span:first-child {
    display: flex;
    align-items: center;
    font-weight: 500;
    &>span {
      margin-right: 4px;
    }
  }
  & > span:last-child {
    cursor: pointer;
  }
}
.people {
  height: 0px;
  padding: 0px;
  overflow: hidden;
  background: #FFFFFF;
  border-radius: 4px;
}
.person {
  height: 22px;
  display: flex;
 
  align-items: center;
  justify-content: space-between;
  color: rgba(18,39,77,0.65);
  line-height: 22px;
  margin-bottom: 8px;
  & > * {
    flex: 1 1 0;
  }
  &>span:last-child {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    &>span:last-child {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  &>span:nth-child(2) {
    margin-left: 10%;
  }
  &:nth-child(2) {
    align-items: center;
  }
  &:last-child {
    margin-bottom: 0px;
  }
  & > span:last-child > span {
    margin-right: 4px;
  }
}
.personName {
  display: flex;
  align-items: center;
  & > span:last-child {
    margin-left: 4px;
  }
}
.display {
  transition: height 1s ease-in-out;
  height: auto;
  padding: 12px;
  margin-top: 12px;
  overflow: visible;
}

.tableModal :global(.c7n-pro-modal-body) {
  height: 61.8vh;
  padding: 0 !important;
}

.tableModal :global(.c7n-pro-modal-footer) {
  border-top: none !important;
}