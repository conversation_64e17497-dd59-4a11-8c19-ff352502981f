import React, { useMemo } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import useRequest from 'ahooks/lib/useRequest';
import { formatterCollections } from '@zknow/utils';
import { Empty } from '@zknow/components';
import useLcAutoQuery from '@/hooks/useLcAutoQuery';
import { getRequestProcess, getReqTaskSla } from '@/service';
import Card from './Card';

import styles from './index.less';

const ProcessTime = observer(props => {
  const {
    intl,
    instanceId,
    AppState: { currentMenuType: { organizationId: tenantId } },
    businessObjectCode,
    formDataSet,
  } = props;

  const isReqItem = businessObjectCode === 'SC_REQ_ITEM';

  const request = isReqItem
    ? () => getRequestProcess({ tenantId, id: instanceId })
    : () => getReqTaskSla({ tenantId, id: instanceId });

  const { data, refresh } = useRequest(instanceId && request);

  // 监听刷新
  useLcAutoQuery(
    refresh,
    formDataSet,
  );
  
  const realData = useMemo(() => {
    return isReqItem
      ? data
      : data?.[0]?.goalItems?.map(i => ({ sla: i, allSlas: data?.[0]?.goalItems }));
  }, [data]);

  if (!Array.isArray(realData)) {
    return (
      <Empty
        style={{ padding: '8px 50px' }}
        innerStyle={{ width: '80px', height: '80px' }}
        type="empty"
      />
    );
  }

  return (
    <div className={styles.wrapper}>
      {realData.filter(i => i.sla).map(i => <Card intl={intl} task={i} titleFlag={isReqItem} />)}
    </div>
  );
});

export default formatterCollections({
  code: ['zknow.common', 'lcr.renderer'],
})(injectIntl(inject('AppState')(ProcessTime)));
