/**
 * 在动作中直接引用了这个组件，所以需要在动作组件的多语言文件请求中添加 lcr.renderer.generateKnowledge 这个 code
 */
import React, { useState, useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Lov, Modal, Select, TextField, SelectBox, message, DataSet } from 'choerodon-ui/pro';
import { Icon as CIcon } from 'choerodon-ui';
import { ExternalComponent, Icon, Button } from '@zknow/components';
import axios from 'axios';
import moment from 'moment';
import lodashFe from 'lodash/forEach';
import pick from 'lodash/pick';
import Feedback from './components/feedback';
import './index.less';
import MetadataForm from './MetadataForm';

const modalKey = Modal.key();
export default observer((props) => {
  const {
    modal,
    dataSet,
    metadataDataSet,
    businessObjectId,
    tenantId,
    ticketId,
    history,
    modifyDefaultFlag = true,
    intl,
    prefixCls,
    themeColor,
    onCreateSuccessCallback = () => { },
    defaultElementMetadata = [],
    newPageFlag,
    uiActionBeforeFlag,
    onCancelCreateSuccess = () => {},
    aiGenerateFlag, // 智能生成知识
    handleSetGenerateKnowledgeConfig,
  } = props;
  const metadataRef = useRef(null);
  const metadataElementRef = useRef([]);

  const checkOriginUrl = `/knowledge/v1/${tenantId}/know/check/homologous`;

  const record = dataSet?.current;

  const [showAlert, setShowAlert] = useState(false);
  const backRef = useRef(null);

  const checkOrigin = async () => {
    const spaceId = record?.get('spaceIdLov')?.id;
    const parentId = record?.get('folderIdLov')?.id || '0';
    let hasOrigin = false;
    if (spaceId) {
      const res = await axios.get(
        `${checkOriginUrl}?spaceId=${spaceId}&parentId=${parentId}&ticketId=${ticketId}`
      );
      hasOrigin = !!res;
    }
    setShowAlert(hasOrigin);
    if (hasOrigin) {
      record.set('sourceCoveredFlag', !!record.getState('defaultSourceCoveredFlag'));
      return;
    }
    // 如果没有同源的把是否同源覆盖置空
    record.set('sourceCoveredFlag', false);
  };

  useEffect(() => {
    checkOrigin();
  }, []);

  const handleCloseModal = () => {
    if (backRef?.current) {
      backRef.current.close();
    }
  };

  async function handleOk(published, aiFlag) {
    try {
      let elementMetadataDTOList = [];
      let check = false;
      if (metadataRef.current?.current) {
        const result = await Promise.all([dataSet.current.validate(), metadataRef.current.current.validate()]);
        check = result.every(i => (i === true));

        const metadata = metadataRef.current.current.toData();
        const requestObjArr = [];
        lodashFe(metadata, (value, key) => {
          if (key !== '__dirty') {
            requestObjArr.push({
              metadataFieldId: key,
              metadataFieldValue: value,
            });
          }
        });
        elementMetadataDTOList = requestObjArr.map(item => {
          const target = (metadataElementRef.current || []).find(ele => ele.metadataFieldId === item.metadataFieldId);
          if (target) {
            target.metadataFieldValue = item.metadataFieldValue;
          }
          return pick(target, ['code', 'metadataFieldId', 'metadataFieldValue']);
        });
      } else {
        check = await dataSet.current.validate();
      }
      if (!check) {
        return false;
      }

      const name = record?.get('name');
      const _spaceId = record?.get('spaceIdLov')?.id;
      const parentId = record?.get('folderIdLov')?.id;
      const templateId = record?.get('templateName')?.id;
      const labels = record?.get('labels')?.map((v) => ({ name: v }));
      const flag = record?.get('sourceCoveredFlag') || false;

      const postData = {
        name,
        spaceId: _spaceId,
        templateId,
        parentId,
        labels,
        ticketId,
      };

      if (elementMetadataDTOList.length) {
        postData.elementMetadataDTOList = elementMetadataDTOList;
      }
      if (!aiFlag) {
        const url = `/knowledge/v1/${tenantId}/know/document/${ticketId}?sourceCoveredFlag=${flag}&published=${published}`;
        const resp = await axios.post(url, postData);
        if (resp?.failed) {
          return message.error(resp?.message);
        }
        if (!published && resp?.id) {
          if (newPageFlag) {
            window.open(`${window.location.origin}/#/itsm/portal/knowledge${history.location?.search}&portal=true&menu=myDraft&knowledgeId=${resp.id}`);
          } else {
            history.push({
              pathname: '/itsm/portal/knowledge',
              search: `${history.location?.search}&portal=true&menu=myDraft&knowledgeId=${resp.id}&type=edit&knowledgeType=${resp.type}`,
            });
          }
          onCreateSuccessCallback();
        } else {
          if (!uiActionBeforeFlag) {
            onCreateSuccessCallback();
          }
          submitKBSuccess(resp);
        }
        modal.close();
      } else {
        const url = `/knowledge/v1/${tenantId}/know/document/${ticketId}/async?sourceCoveredFlag=${flag}&published=${published}`;
        delete postData.name;
        const resp = await axios.post(url, postData);
        window.BackGenerationKnowledge = false;
        try {
          postData.tenantId = tenantId;
          postData.isNotBack = true;
          const newList = [postData];
          if (handleSetGenerateKnowledgeConfig) {
            handleSetGenerateKnowledgeConfig(JSON.stringify(newList));
            window.handleCloseModal = handleCloseModal;
          }
        } catch (error) {
          //
        }
        backRef.current = Modal.open({
          className: 'gen-knowledge-loading-modal',
          title: intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form', defaultMessage: '生成知识' }),
          children: <div className="gen-knowledge-loading-modal-content">
            <img className="gen-knowledge-loading-modal-content-img" src={`${window._env_.ICON_SERVER}/static/intelligent-loading-min.gif`} alt="" />
            <div className="gen-knowledge-loading-modal-content-title">{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.loading', defaultMessage: '智能生成中' })}</div>
            <div className="gen-knowledge-loading-modal-content-text">{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.loading.title', defaultMessage: '正在生成知识，请耐心等待，您也可以通过“后台运行”等待结果' })}</div>
          </div>,
          okText: intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.back', defaultMessage: '后台生成' }),
          onOk: () => {
            try {
              const list = (!localStorage.getItem('GenerateKnowledgeList') || localStorage.getItem('GenerateKnowledgeList') === 'undefined') ? JSON.parse(localStorage.getItem('GenerateKnowledgeList') || '[]') : [];
              postData.tenantId = tenantId;
              postData.isNotBack = false;
              let newList;
              if (!list?.find(i => i.ticketId === ticketId)) {
                newList = [...list, postData];
              }
              localStorage.setItem('GenerateKnowledgeList', JSON.stringify(newList));
              if (handleSetGenerateKnowledgeConfig) {
                window.BackGenerationKnowledge = true;
                handleSetGenerateKnowledgeConfig(JSON.stringify(newList));
              }
              handleCloseModal();
            } catch (error) {
              //
              localStorage.setItem('GenerateKnowledgeList', '[]');
            }
          },
          style: { width: '800px' },
        });
        if (modal) {
          modal.close();
        }
      }
    } catch (e) {
      return false;
    }
  }

  function submitKBSuccess(res) {
    Modal.open({
      className: 'gen-knowledge-success-modal',
      children: (
        <Feedback
          intl={intl}
          prefixCls={prefixCls}
          themeColor={themeColor}
          tenantId={tenantId}
          history={history}
          data={res}
        />
      ),
      border: false,
      closable: true,
      style: { width: 800, textAlign: 'center' },
      footer: null,
      onClose: () => {
        if (uiActionBeforeFlag) {
          onCreateSuccessCallback();
          return true;
        }
      },
      onCancel: () => {
        if (uiActionBeforeFlag) {
          onCreateSuccessCallback();
          return true;
        }
      },
    });
  }

  modal.handleCancel(() => {
    dataSet.deleteAll(false);
    onCancelCreateSuccess();
  });

  useEffect(() => {
    modal.update({
      footer: (_, cancelBtn) => {
        if (aiGenerateFlag) {
          return <>
            <Button key="publish" color="primary" funcType="raised" onClick={() => handleOk(false, true)}>
              {intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.aiGenerate', defaultMessage: '确定' })}
            </Button>
            {cancelBtn}
          </>;
        }
        return (
          <>
            <Button key="edit" color="primary" funcType="raised" onClick={() => handleOk(false)}>
              {intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.edit', defaultMessage: '进入编辑' })}
            </Button>
            <Button key="publish" color="primary" funcType="raised" onClick={() => handleOk(true)}>
              {intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.publish', defaultMessage: '直接发布' })}
            </Button>
            {cancelBtn}
          </>
        );
      },
    });
  }, []);

  const handleSelectTemplate = () => {
    if (!modifyDefaultFlag) return;

    Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.template' }),
      key: modalKey,
      style: {
        width: 1036,
        height: 'calc(100vh - 2rem)',
        minHeight: 300,
      },
      bodyStyle: {
        padding: 0,
        height: 'calc(100% - .55rem)',
        maxHeight: 'unset',
      },
      drawer: false,
      destroyOnClose: true,
      children: (
        <ExternalComponent
          system={{
            scope: 'knowledge',
            module: 'KnowledgeTemplate',
          }}
          fileType="DOCUMENT"
          onOk={async (type, templateId, name) => {
            record.set('templateName', {
              name,
              id: templateId,
            });
          }}
          spaceId={record?.get('spaceIdLov')?.id}
          isGenKnowledge
          smartGenerateFlag={aiGenerateFlag}
          businessObjectId={businessObjectId}
        />
      ),
      footer: null,
    });
  };

  function rendererFolder({ text }) {
    if (!text) return null;
    const obj = text.lastIndexOf('/');
    const folderName = text.substr(obj + 1);
    const folderRoute = text.substr(0, obj + 1);

    return (<>
      <span className="gen-knowledge-form-folderRoute">{folderRoute}</span>
      {folderName}
    </>);
  }

  function getActionFormDataSet(fieldList) {
    const fields = [];
    const data = {};
    fieldList.forEach(fieldItem => {
      const { widgetType, requiredFlag, defaultValue, name, widgetConfig: JsonWidgetConfig, metadataFieldId, maxLength } = fieldItem || {};
      const widgetConfig = JSON.parse(JsonWidgetConfig);
      if (widgetType) {
        const newFieldConfig = {
          disabled: false,
          required: requiredFlag,
          defaultValue,
          label: name,
          name: metadataFieldId,
        };
        // 富文本这里不做处理，formDataSet中提交时统一处理成字符串
        if (widgetType === 'RichText') {
          newFieldConfig.transformRequest = undefined;
        }
        if (widgetType === 'Select') {
          const { dataSource, options = [], lookupCode } = widgetConfig;
          if (dataSource === 'lookup') {
            if (tenantId === 0) {
              newFieldConfig.lookupUrl = `/hpfm/v1/lookup/queryByCode?lookupTypeCode=${lookupCode}`;
            } else {
              newFieldConfig.lookupUrl = `/hpfm/v1/${tenantId}/lookup/queryByCode?lookupTypeCode=${lookupCode}`;
            }
          }
          if (dataSource === 'optionSet') {
            newFieldConfig.options = new DataSet({
              paging: false,
              data: options || [],
            });
          }
        }
        if (widgetType === 'Date' || widgetType === 'DateTime') {
          const { minValue, maxValue, passTimeFlag } = widgetConfig;
          // DatePicker 的校验不支持所有格式，搞成标准格式
          const validateFormat = widgetType === 'Date' ? 'YYYY-MM-DD' : 'YYYY-MM-DD HH:mm:ss';
          const format = widgetConfig.format || validateFormat;

          newFieldConfig.transformRequest = (value) => {
            if (value) {
              return moment(value, format).format(newFieldConfig.format);
            } else {
              return value;
            }
          };
          newFieldConfig.format = format;
          minValue && (newFieldConfig.min = moment(minValue, format)).format(validateFormat);
          maxValue && (newFieldConfig.max = moment(maxValue, format)).format(validateFormat);
          if (!passTimeFlag) {
            if (minValue) {
              newFieldConfig.min = moment(minValue, format).valueOf() > moment.now() ? moment(minValue, format).format(validateFormat) : moment().format(validateFormat);
            } else {
              newFieldConfig.min = moment().format(validateFormat);
            }
          }
        }
        if (widgetType === 'Input' && maxLength) {
          newFieldConfig.maxLength = parseInt(maxLength, 10);
        }
        fields.push(newFieldConfig);
      }
    });
    defaultElementMetadata.map((i) => {
      const { format } = JSON.parse(i.widgetConfig || '{}');
      if (format) {
        data[i.metadataFieldId] = moment(i.metadataFieldValue, format).format(format);
      } else {
        data[i.metadataFieldId] = i.metadataFieldValue;
      }
      return i;
    });
    return new DataSet({
      autoCreate: true,
      autoLocateFirst: true,
      fields,
      data: defaultElementMetadata?.length > 0 ? [{ ...data }] : null,
    });
  }

  function renderMetaForm() {
    if (!dataSet.current?.get('spaceIdLov')) {
      return null;
    }
    const fieldList = metadataDataSet.toData() || [];
    // 没有元数据，则不显示元数据的表单区域
    if (!fieldList.length) {
      return null;
    }
    const actionFormDataSet = getActionFormDataSet(fieldList);
    // handleOk 的时候需要
    metadataRef.current = actionFormDataSet;
    metadataElementRef.current = fieldList;
    return (
      <MetadataForm
        intl={intl}
        dataSet={actionFormDataSet}
        fieldList={fieldList}
      />
    );
  }

  return (
    <div className="gen-knowledge-form">
      {showAlert && (
        <div className="gen-knowledge-alert">
          <Icon type="attention" theme="filled" size="12" fill="#FF9500" />
          <span>{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.originWarn' })}</span>
        </div>
      )}
      <Form record={record} columns={2} labelWidth={100}>
        <Lov name="spaceIdLov" colSpan={1} onChange={checkOrigin} disabled={!modifyDefaultFlag} />
        <Lov
          name="folderIdLov"
          colSpan={1}
          onChange={checkOrigin}
          disabled={!modifyDefaultFlag}
          tooltip="overflow"
          tooltipPlacement="top"
          searchable={false}
          renderer={rendererFolder}
        />
        {!aiGenerateFlag && <TextField name="name" colSpan={1} disabled={!modifyDefaultFlag} />}
        <Select
          name="labels"
          dropdownMenuStyle={{ maxHeight: '300px', maxWidth: '386px' }}
          searchable
          colSpan={1}
          combo
          disabled={!modifyDefaultFlag}
        />
        <TextField
          className="select-knowledge-template-field"
          name="templateName"
          readOnly
          disabled={!modifyDefaultFlag}
          clearButton
          renderer={({ value }) => value?.name}
          suffix={<CIcon type="search" onClick={handleSelectTemplate} />}
        />
        {showAlert && (
          <SelectBox name="sourceCoveredFlag" required={showAlert} disabled={!modifyDefaultFlag} />
        )}
      </Form>
      {renderMetaForm()}
    </div>
  );
});
