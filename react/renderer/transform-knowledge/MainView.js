import { axios, Permission } from '@yqcloud/apps-master';
import { Button, Icon } from '@zknow/components';
import { Menu, message, Modal } from 'choerodon-ui/pro';
import React, { useContext, useEffect, useRef } from 'react';
import AiTag from '@/components/ai-tag';
import GenKnowledgeForm from './GenKnowledgeForm';
import Feedback from './components/feedback';
import Store from './stores';
import './index.less';

const modalKey = Modal.key();

const MainView = () => {
  const {
    tenantId,
    formDataSet: dataSet,
    viewDataSet,
    record: defaultRecord,
    config = {},
    genKnoDataSet,
    history,
    feature,
    defaultData,
    generateKnowledgeRef,
    intl,
    prefixCls,
    themeColor,
    onCreateSuccessCallback = () => {},
    businessObjectId: BusinessObjectId,
    newPageFlag,
    metadataDataSet,
    handleSetGenerateKnowledgeConfig,
    setGenerateKnowledgeFinishFn,
  } = useContext(Store);
  const {
    knowledgeSettingFlag,
    knowledgeSettingVO,
    transformFlag,
    modifyDefaultFlag,
  } = defaultData;

  const { icon, name: btnName, id, color, noNeedIcon } = config;

  const initDefaultData = (_record) => {
    // 服务配置应用
    if (knowledgeSettingFlag) {
      try {
        const { labels, spaceId, spaceName, templateId, templateName, folderId, folderName, sourceCoveredFlag = true } = knowledgeSettingVO || {};
        const presetData = {};
        if (labels) {
          presetData.labels = labels;
        }
        if (templateId) {
          presetData.templateName = { name: templateName, id: templateId };
        }
        if (spaceId) {
          presetData.spaceIdLov = { name: spaceName, id: spaceId };
        }
        if (folderId) {
          presetData.folderIdLov = { path: folderName, name: folderName, id: folderId };
        }
        _record.set(presetData);
        _record.setState('defaultSourceCoveredFlag', sourceCoveredFlag);
      } catch (e) {
        // console.log(e);
      }
    } else {
      _record.setState('defaultSourceCoveredFlag', true);
    }
  };

  useEffect(() => {
    if (generateKnowledgeRef?.current) {
      generateKnowledgeRef.current.onClick = handlerGenKnowledge;
    }
  }, [generateKnowledgeRef?.current, defaultData]);

  function submitKBSuccess(res) {
    Modal.open({
      className: 'gen-knowledge-success-modal',
      children: (
        <Feedback
          intl={intl}
          prefixCls={prefixCls}
          themeColor={themeColor}
          tenantId={tenantId}
          history={history}
          data={res}
        />
      ),
      border: false,
      closable: true,
      // footer: (okBtn, cancelBtn) => okBtn,
      // onOk: () => true,
      footer: null,
      style: { width: 800, textAlign: 'center' },
    });
  }

  const handlerGenKnowledge = async () => {
    try {
      const record = defaultRecord || dataSet?.current || dataSet?.get(0);
      const ticketId = record?.get('id');
      const name = record?.get('short_description')?.slice(0, 100);
      const businessObjectId = BusinessObjectId || viewDataSet?.get(0)?.get('businessObjectId');
      const _record = genKnoDataSet.create({ name });
      genKnoDataSet.current = _record;

      initDefaultData(_record);
      // 直接发布逻辑
      //  「服务配置」中设置了修改默认值的角色，如果当前用户无权限修改默认值，就不会弹出弹框，
      //    而是直接发布知识
      if (knowledgeSettingFlag && !modifyDefaultFlag) {
        const _name = _record?.get('name');
        const _spaceId = _record?.get('spaceIdLov')?.id;
        const parentId = _record?.get('folderIdLov')?.id;
        const templateId = _record?.get('templateName')?.id;
        const labels = _record?.get('labels')?.map((v) => ({ name: v }));

        // 校验同源
        const flag = !!_record.getState('defaultSourceCoveredFlag');

        const url = `/knowledge/v1/${tenantId}/know/document/${ticketId}?sourceCoveredFlag=${flag}&published=true`;
        const resp = await axios.post(url, {
          name: _name,
          spaceId: _spaceId,
          templateId,
          parentId,
          labels,
          ticketId,
        });

        if (resp?.failed) {
          return message.error(resp?.message);
        }
        submitKBSuccess(resp);
        onCreateSuccessCallback();
      } else {
        Modal.open({
          title: <div>{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}{knowledgeSettingVO?.aiGenerateFlag && <AiTag name={intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form.ai', defaultMessage: '智能生成知识' })} />}</div>,
          key: modalKey,
          style: { width: 800 },
          children: (
            <GenKnowledgeForm
              dataSet={genKnoDataSet}
              metadataDataSet={metadataDataSet}
              businessObjectId={businessObjectId}
              tenantId={tenantId}
              ticketId={ticketId}
              history={history}
              modifyDefaultFlag={modifyDefaultFlag}
              intl={intl}
              prefixCls={prefixCls}
              themeColor={themeColor}
              onCreateSuccessCallback={onCreateSuccessCallback}
              newPageFlag={newPageFlag}
              aiGenerateFlag={knowledgeSettingVO?.aiGenerateFlag}
              handleSetGenerateKnowledgeConfig={handleSetGenerateKnowledgeConfig}
              setGenerateKnowledgeFinishFn={setGenerateKnowledgeFinishFn}
            />
          ),
          movable: false,
          destroyOnClose: true,
        });
      }
    } catch (e) {
      return null;
    }
  };

  if (feature === 'table-action') {
    // 服务配置应用
    if (knowledgeSettingFlag) {
      if (!transformFlag) return <HiddenParent />;
      return <Menu.Item key="genKnowledge" onClick={handlerGenKnowledge}>
        <div className="copy-btn-wrapper">
          <Icon className="icon" size="16" type={icon || 'book-open'} />
          <span>{btnName || intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}</span>
        </div>
      </Menu.Item>;
    }

    return (
      <Permission
        service={['yqcloud-knowledge.know-element.createDocumentFromTemplate']}
        noAccessChildren={<HiddenParent />}
      >
        <Menu.Item key="genKnowledge" onClick={handlerGenKnowledge}>
          <div className="copy-btn-wrapper">
            <Icon className="icon" size="16" type={icon || 'book-open'} />
            <span>{btnName || intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}</span>
          </div>
        </Menu.Item>
      </Permission>
    );
  }

  // 服务配置应用
  if (knowledgeSettingFlag) {
    if (!transformFlag) return null;
    if (knowledgeSettingVO?.aiGenerateFlag) {
      return <Button 
        className="transform-knowledge-aibutton"
        icon={noNeedIcon ? null : (icon || 'book-open')}
        color="primary"
        onClick={handlerGenKnowledge}
      >
        {btnName || intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}
      </Button>;
    }

    return (
      <Button
        key={`genKnowledge-${id}`}
        id={id}
        icon={noNeedIcon ? null : (icon || 'book-open')}
        color={color}
        funcType="raised"
        onClick={handlerGenKnowledge}
      >
        {btnName || intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}
      </Button>
    );
  }

  return (
    <Permission service={['yqcloud-knowledge.know-element.createDocumentFromTemplate']}>
      <Button
        key={`genKnowledge-${id}`}
        id={id}
        icon={noNeedIcon ? null : (icon || 'book-open')}
        color={color}
        funcType="raised"
        onClick={handlerGenKnowledge}
      >
        {btnName || intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.form' })}
      </Button>
    </Permission>
  );
};
export default MainView;

const HiddenParent = () => {
  const ref = useRef();
  useEffect(() => {
    try {
      ref.current.parentElement.hidden = true;
    } catch {
      /** */
    }
  }, []);
  return <div ref={ref} />;
};
