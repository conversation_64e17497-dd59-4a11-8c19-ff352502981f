import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, intl, labelDataSet, knowledgeFieldRequired, metadataDataSet, aiGenerateFlag }) => {
  const yes = intl.formatMessage({ id: 'zknow.common.status.yes' });
  const no = intl.formatMessage({ id: 'zknow.common.status.no' });
  const name = intl.formatMessage({ id: 'zknow.common.model.title' });
  const space = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.space' });
  const folder = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.folder' });
  const label = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.label' });
  const template = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.template' });
  const templatePlaceholder = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.template.placeholder' });
  const origin = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.origin' });
  const originHelp = intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.origin.help' });

  const originOptions = new DataSet({
    data: [{ text: yes, value: true }, { text: no, value: false }],
  });

  return {
    autoQuery: false,
    autoCreate: false,
    paging: false,
    fields: [
      {
        name: 'name',
        type: 'string',
        label: name,
        required: !aiGenerateFlag, // 智能生成知识不需要name
        maxLength: 100,
      },
      {
        name: 'spaceIdLov',
        type: 'object',
        label: space,
        lovCode: 'KB_SPACE',
        required: true,
      },
      {
        name: 'folderIdLov',
        label: folder,
        lovCode: 'KB_FOLDER',
        type: 'object',
        textField: 'path',
        required: knowledgeFieldRequired.includes('folder'),
        dynamicProps: {
          disabled: ({ record }) => !record?.get('spaceIdLov'),
          lovPara: ({ record }) => {
            return { spaceId: record?.get?.('spaceIdLov')?.id };
          },
        },
      },
      {
        name: 'labels',
        label,
        type: 'string',
        multiple: true,
        textField: 'name',
        valueField: 'name',
        options: labelDataSet,
        required: knowledgeFieldRequired.includes('labels'),
      },
      {
        name: 'templateName',
        label: template,
        placeholder: templatePlaceholder,
        type: 'object',
        required: true,
      },
      {
        name: 'sourceCoveredFlag',
        label: origin,
        help: originHelp,
        type: 'boolean',
        textField: 'text',
        valueField: 'value',
        options: originOptions,
      },
    ],
    events: {
      update: async ({ record, name: _name, value }) => {
        if (_name === 'spaceIdLov') {
          record.set('folderIdLov', null);
          if (value?.id) {
            metadataDataSet.transport.read.url = `/knowledge/v1/${tenantId}/kb_metadata_fields/space/${value.id}/all?enabledFlag=true`;
            metadataDataSet.query();
          }
        }
      },
    },
  };
};
