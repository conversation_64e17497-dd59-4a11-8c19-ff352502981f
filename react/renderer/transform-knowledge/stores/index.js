import React, { createContext, useMemo, useState, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { injectIntl } from 'react-intl';
import { withRouter } from 'react-router-dom';
import axios from 'axios';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import LabelDataSet from './LabelDataSet';
import GenKnoDataSet from './GenKnoDataSet';
import MetadataDataSet from './MetadataDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = withRouter(
  injectIntl(inject('AppState', 'HeaderStore')(observer((props) => {
    const {
      children,
      AppState: {
        currentMenuType: { tenantId }, handleSetGenerateKnowledgeConfig,
      },
      AppState,
      intl,
      ticketId,
      businessObjectId,
      HeaderStore: {
        getTenantConfig: { themeColor = '#2979ff' },
      },
    } = props;
    const [defaultData, setDefaultData] = useState({});
    const [knowledgeFieldRequired, setKnowledgeFieldRequired] = useState([]);

    const getDataWithCache = async () => {
      const cache = AppState?.customConfig[`${businessObjectId}-${ticketId}`];
      if (cache) {
        return cache;
      }
      if (!ticketId || !tenantId) return;
      const res = await axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`);
      AppState?.setCustomConfig(businessObjectId, res);
      return res;
    };

    const initConfig = async () => {
      const res = await getDataWithCache();
      try {
        if (res?.knowledgeSettingVO) {
          res.knowledgeSettingVO.useDefaultValueFlag = JSON.parse(res.knowledgeSetting)?.useDefaultValueFlag;
        }
      } catch {
        //
      }
      // modifyDefaultFlag 是后端根据 knowledgeSetting.modifyDefaultValueRole 计算得来的
      //   也就是当前用户是否有权限修改知识
      const { knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag, knowledgeDefaultVO = {} } = res;
      setKnowledgeFieldRequired(knowledgeSettingVO?.required || []);
      // 是否使用默认值
      if (knowledgeSettingVO?.useDefaultValueFlag) {
        setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO, transformFlag, modifyDefaultFlag });
      } else {
        // aiGenerateFlag存在knowledgeSettingVO但不应该被默认值逻辑影响
        const extraKnowledgeSetting = {
          aiGenerateFlag: knowledgeSettingVO?.aiGenerateFlag,
        };
        // 如果不使用默认值，后端就会查询出用户上次输入的值，存放在knowledgeDefaultVO中
        setDefaultData({ knowledgeSettingFlag, knowledgeSettingVO: { ...knowledgeDefaultVO, ...extraKnowledgeSetting }, transformFlag, modifyDefaultFlag });
      }
    };

    useEffect(() => {
      if (ticketId && businessObjectId) {
        initConfig(ticketId, businessObjectId);
      }
    }, [ticketId, businessObjectId]);

    const prefixCls = 'transform-knowledge';

    const labelDataSet = useMemo(() => new DataSet(LabelDataSet({ tenantId })), [tenantId]);

    const metadataDataSet = useMemo(() => new DataSet(MetadataDataSet()), []);
    const genKnoDataSet = useMemo(
      () => new DataSet(GenKnoDataSet({ intl, tenantId, labelDataSet, knowledgeFieldRequired, metadataDataSet, aiGenerateFlag: defaultData?.knowledgeSettingVO?.aiGenerateFlag })),
      [labelDataSet, knowledgeFieldRequired, defaultData?.knowledgeSettingVO?.aiGenerateFlag]
    );

    const value = {
      ...props,
      prefixCls,
      tenantId,
      labelDataSet,
      genKnoDataSet,
      defaultData,
      intl,
      themeColor,
      businessObjectId,
      metadataDataSet,
      handleSetGenerateKnowledgeConfig,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })))
);
