@import '~choerodon-ui/lib/style/themes/default';

.gen-knowledge-form {
  &-more {
    display: flex;
    align-items: center;
    color: @primary-color;
    cursor: pointer;

    padding: 0 0 8px 0;
    margin-top: 16px;
  }

  .c7n-pro-form .c7n-pro-field-label ~ .c7n-pro-field-label {
    padding-left: 24px;
  }

  .select-knowledge-template-field {
    input {
      z-index: 2;

      width: calc(100% - 0.32rem);
      padding-right: 18px;

      border-radius: 4px 0 0 4px;
      cursor: default;
    }

    .c7n-pro-input-suffix {
      top: 0;
      right: 0;

      display: flex;
      justify-content: center;
      align-items: center;
      width: auto;
      height: auto;
      min-height: 0.32rem;
      margin: 0;
      padding: 0.08rem 0.08rem 0.08rem 0.07rem;

      border: 0.01rem solid #e0e0e0;
      border-radius: 0 0.04rem 0.04rem 0;
    }
  }

  .gen-knowledge-alert {
    display: flex;
    align-items: center;
    height: 32px;
    margin-top: -8px;
    margin-bottom: 12px;

    background: #fff4e5;
    border: 1px solid #ffeacc;
    border-radius: 4px;

    span:first-child {
      margin: 0 9px;
    }

    span:last-child {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #12274d;
    }
  }

  .c7n-pro-select-wrapper {
    max-width: 389px;
  }

  .c7n-pro-select {
    .c7n-pro-select-multiple-block {
      max-width: 300px;
      height: 22px;

      color: #2979ff;

      background: #f0f8ff;
      border-radius: 2px;
      line-height: 22px;

      div {
        height: 20px;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #2979ff;
      }

      i {
        width: 10px;
        height: 22px;
        margin-left: 8px;
      }
    }
  }

  &-folderRoute {
    color: #939aaa;
  }
}

.gen-knowledge-success-modal {
  .c7n-pro-modal-movable {
    padding: 12px 24px;
  }

  .c7n-pro-modal-header-buttons.only-close {
    width: 57px;
    height: 57px;

    .c7n-pro-modal-header-button {
      color: rgba(0, 0, 0, 0.25);
      position: relative;
      top: 10px;
    }
  }

  &-title {
    margin-top: 20px;
    margin-bottom: 8px;

    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }

  &-content {
    max-width: 408px;
    margin: auto;
    margin: 0 32px;

    font-size: 12px;
    line-height: 20px;
    color: #8c8c8c;
  }

  .c7n-pro-modal-footer {
    padding: 8px 24px 32px 24px;

    text-align: center;
  }
}

.gen-knowledge-form-success {
  .c7n-pro-modal-header {
    padding: 32px;
    padding-bottom: 12px;

    border-bottom: none;
  }

  &-help-text {
    height: 24px;
    margin-left: 16px;

    font-weight: 500;
    line-height: 22px;
    vertical-align: super;
  }

  .c7n-pro-modal-header-buttons {
    width: 16px;
    height: 16px;
    margin-top: 32px;
    margin-right: 32px;
  }

  &-help .i-icon {
    margin: 0;
  }

  .c7n-pro-modal-body {
    padding: 0 0 0 72px;

    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.65);
  }

  .c7n-pro-modal-footer {
    padding: 32px;

    border-top: none;
  }
}

.itsm-copy-ticket-modal {
  .page-head {
    display: none;
  }
}

.copy-btn-wrapper {
  display: flex;
  align-items: center;
}

.transform-knowledge {
  &-aibutton {
    background: linear-gradient(135deg, #53ffc6 0%, #439cff 52%, #bb4bff 100%) !important;
    border-radius: 4px;
    border: none !important;
  }
}

.gen-knowledge-loading-modal {
  &-content {
    background: #f7f9fc;
    border-radius: 4px;
    border: 1px solid #d7e2ec;
    padding: 45px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-img {
      width: 72px;
      height: 72px;
    }
    &-title {
      font-weight: bold;
      font-size: 20px;
      margin: 20px 0 12px 0;
    }
  }
}