@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.transform-knowledge {
  &-feedback {
    margin: auto;
    overflow: hidden;
  }
  &-wrapper {
    text-align: center;
    margin: auto;
    margin-top: 26px;
    
    .feedbackIcon {
      margin-bottom: 21px;
    }
    .title {
      font-size: 20px;
      font-weight: 500;
      color: #12274D;
      line-height: 28px;
      margin-bottom: 12px;
    }

    .description {
      font-size: 16px;
      font-weight: 400;
      color: rgba(18,39,77,0.65);
      line-height: 28px;
      margin-bottom: 20px;
    }

    .splitLine {
      width: 100%;
      height: 1px;
      background: rgba(203,210,220,0.5);
    }

    .ticketLine {
      margin-top: 17px;
      margin-bottom: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      &-label {
        font-size: 14px;
        font-weight: 400;
        color: rgba(18,39,77,0.65);
        line-height: 22px;
      }
      &-number {
        color: @primary-color;
        margin-right: 8px;
      }
    }

    .actions {
      padding-bottom: 24px;
      .actions-item {
        padding: 8px 16px;
        height: 38px;
        min-width: 88px;
        &-seeDetail {
          margin-right: 2px;
        }
      }
    }

  }
}