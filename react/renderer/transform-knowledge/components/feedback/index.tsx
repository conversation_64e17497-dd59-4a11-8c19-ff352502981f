import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { message } from 'choerodon-ui/pro';
import querystring from 'query-string';
// @ts-ignore
import { Icon, Button } from '@zknow/components';
// @ts-ignore
import { getQueryString } from '@/utils';
import style from './index.module.less';

type KnowledgeFeedbackProps = {
  match: any,
  history: any,
  intl: any,
  prefixCls: string,
  tenantId: string,
  themeColor: string,
  data: any,
  modal?: any,
}

const KnowledgeFeedback = observer((props: KnowledgeFeedbackProps) => {
  const {
    intl,
    prefixCls,
    tenantId,
    themeColor,
    data: feedbackInfo,
    modal,
  } = props;

  const renderFeedbackIcon = useMemo(() => {
    return (
      <div className={style.feedbackIcon}>
        <Icon
          type="check-one"
          style={{ fontSize: '70px' }}
          fill="#7BC95A"
          theme="filled"
        />
      </div>
    );
  }, []);

  function handleSeeDetail() {
    const solutionId = getQueryString('solutionId');
    const queryObj = {
      solutionId,
      tenantId,
      menu: 'knowledge',
      knowledgeId: feedbackInfo?.id,
    };
    const url = `${window.location.origin}/#/itsm/portal/knowledge?${querystring.stringify(queryObj)}&sourceModule=TICKETS&sourceFunction=RELATED_KNOWLEDGE_CREATED&sourceId=${feedbackInfo?.ticketId}`;
    window.open(url);
  }

  function handleGoBack() {
    modal?.close();
  }

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}-feedback`]}>
        <div className={style[`${prefixCls}-wrapper`]}>
          {renderFeedbackIcon}
          <div className={style.title}>{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.successMsg' })}</div>
          <div className={style.description}>
            {intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.pathMsg' })}{': '}{feedbackInfo.path}
          </div>
          <div className={style.splitLine} />
          <div className={style.ticketLine}>
            {/* eslint-disable-next-line no-chinese/no-chinese */}
            <span className={style['ticketLine-label']}>{intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.knowledge.code' })}</span>：
            <span className={style['ticketLine-number']}>{feedbackInfo?.number || '-'}</span>
            <CopyToClipboard
              text={feedbackInfo?.number || '-'}
              onCopy={() => message.success(intl.formatMessage({ id: 'zknow.common.success.copy' }))}
            >
              <Icon
                type="Copy"
                size="14"
                fill={themeColor}
                style={{ cursor: 'pointer' }}
              />
            </CopyToClipboard>
          </div>
          <div className={style.actions}>
            <Button
              funcType="raised"
              color="primary"
              onClick={() => handleSeeDetail()}
              className={`${style['actions-item']} ${style['actions-item-seeDetail']}`}
            >
              {intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.feedback.seeDetail' })}
            </Button>
            <Button
              funcType="raised"
              color="secondary"
              onClick={() => handleGoBack()}
              className={style['actions-item']}
            >
              {intl.formatMessage({ id: 'zknow.common.button.back' })}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return renderMain();
});

export default KnowledgeFeedback;
