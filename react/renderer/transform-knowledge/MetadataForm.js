import React, { useState } from 'react';
import { Form, Select, TextField, DateTimePicker, DatePicker } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';

function MetadataForm({ intl, dataSet, fieldList }) {
  const [expend, setExpend] = useState(false);

  const getDateTimeMode = (fieldItem) => {
    try {
      const { format } = JSON.parse(fieldItem.widgetConfig);
      if (format === 'YYYY') {
        return 'year';
      }
      if (format === 'YYYY-MM') {
        return 'month';
      }
      if (['YYYY-MM-DD', 'YYYYMMDD'].includes(format)) {
        return 'date';
      }
      if (['HH:mm', 'HH:mm:ss'].includes(format)) {
        return 'time';
      }
      return 'dateTime';
    } catch (e) {
      return 'dateTime';
    }
  };
  /**
   * 根据字段类型渲染字段
   * @param fieldItem
   * @returns {null|*}
   */
  function renderField(fieldItem) {
    const { widgetType, metadataFieldId: id } = fieldItem || {};
    if (widgetType === 'Date') {
      return (
        <DatePicker
          mode={getDateTimeMode(fieldItem)}
          name={id}
        />
      );
    }
    if (widgetType === 'DateTime') {
      return (
        <DateTimePicker
          mode={getDateTimeMode(fieldItem)}
          name={id}
        />
      );
    }
    if (widgetType === 'Input') {
      return <TextField name={id} />;
    }
    if (widgetType === 'Select') {
      return (
        <Select
          name={id}
          searchable
          optionsFilter={(r) => r.get('enabledFlag')}
        />
      );
    }
    return null;
  }

  return (
    <>
      <div className="gen-knowledge-form-more" onClick={() => setExpend(!expend)}>
        {expend ? intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.collapse' }) : intl.formatMessage({ id: 'lcr.renderer.generateKnowledge.viewMore' })}
      </div>
      <Form
        style={expend ? {} : { display: 'none' }}
        dataSet={dataSet}
        labelWidth={100}
        columns={2}
      >
        {fieldList.map((field) => renderField(field))}
      </Form>
    </>
  );
}

export default observer(MetadataForm);
