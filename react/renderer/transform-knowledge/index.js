/*
 * @Description: 生成知识，单据转化为知识
 */
import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import MainView from './MainView';
import { StoreProvider } from './stores';

export default inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })((props) => {
    const { instanceId, viewDataSet, originBusinessObjectId } = props;
    const { businessObjectId } = viewDataSet?.current?.toData() || {};

    return (
      <StoreProvider {...props} ticketId={instanceId} businessObjectId={originBusinessObjectId || businessObjectId}>
        <MainView />
      </StoreProvider>
    );
  })
);

/* externalize: TransformKnowledge */
