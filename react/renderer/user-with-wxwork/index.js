import React from 'react';
import { observer } from 'mobx-react-lite';
import WechatWorkChatIcon from '../wechat-work-chat-icon';

export default observer((props) => {
  const { record, text, value, name } = props;
  const idField = name.includes(':') ? name.split(':').slice(0, -1).join(':') : name;
  const personId = value?.id || record.get(idField)?.id || record.get(idField);

  return (
    <div>
      {text}
      <WechatWorkChatIcon personId={personId} />
    </div>
  );
});

/* externalize: UserWithWxwork */
