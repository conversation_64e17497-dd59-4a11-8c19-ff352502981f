import React from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Button } from '@zknow/components';
import { message } from 'choerodon-ui/pro';
import axios from 'axios';

export default injectIntl(observer((props) => {
  const { tenantId, tableDataSet, config, intl, tableLineRecord, formDataSet } = props;
  const data = tableLineRecord?.toData();
  const parentData = formDataSet?.current?.toData();

  if (!data) {
    return null;
  }

  const { icon, name, id, color = 'default' } = config;
  const customConfig = config?.widgetConfig?.customConfig || [];

  const { requestUrl, method = 'GET', queryParams, bodyParams, requestParams, parentParams } = customConfig.reduce((prev, { key, value }) => ({
    ...prev,
    [key]: value,
  }), {});

  const handleClick = async () => {
    if (!requestUrl || !requestParams) {
      return true;
    }

    const queryObj = queryParams?.split(',').reduce((prev, item) => {
      const itemArr = item.split('=');
      return {
        ...prev,
        [itemArr[0]]: itemArr[1],
      };
    }, {});

    const bodyObj = bodyParams?.split(',').reduce((prev, item) => {
      const itemArr = item.split('=');
      return {
        ...prev,
        [itemArr[0]]: itemArr[1],
      };
    }, {});

    const requestObj = requestParams?.split(',').reduce((prev, item) => {
      const itemArr = item.split('=');
      return {
        ...prev,
        [itemArr[0]]: data[itemArr[1]],
      };
    }, {});

    const parentObj = parentParams?.split(',').reduce((prev, item) => {
      const itemArr = item.split('=');
      return {
        ...prev,
        [itemArr[0]]: parentData[itemArr[1]],
      };
    }, {});

    const res = await axios({
      url: requestUrl.replace(/\${tenantId}/, tenantId),
      method,
      params: !['put', 'PUT', 'post', 'POST'].includes(method) ? {
        ...queryObj,
        ...requestObj,
        ...parentObj,
      } : {
        ...queryObj,
      },
      data: ['put', 'PUT', 'post', 'POST'].includes(method) ? {
        ...bodyObj,
        ...requestObj,
        ...parentObj,
      } : {},
    });

    if (res?.failed) {
      message.error(res.message);
    } else {
      tableDataSet.query();
      message.success(intl.formatMessage({ id: 'success' }));
    }
  };

  return (
    <Button
      funcType="flat"
      color={color}
      icon={icon}
      key={id}
      onClick={handleClick}
    >
      {name || intl.formatMessage({ id: 'lcr.renderer.desc.inline.button', defaultMessage: '行内操作按钮' })}
    </Button>
  );
}));
