import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import MainView from './MainView';
import { StoreProvider } from './stores';

export default inject('AppState')(formatterCollections({
  code: ['zknow.common', 'lcr.renderer'],
})((props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
)));

/* externalize: TicketRelation */
