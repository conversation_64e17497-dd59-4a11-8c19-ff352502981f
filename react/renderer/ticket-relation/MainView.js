import React, { useContext, useEffect, useRef, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { message, TextField, Tooltip, Lov } from 'choerodon-ui/pro';
import { debounce } from 'lodash';
import { Empty, Icon, SectionHeaderLine } from '@zknow/components';
import axios from 'axios';
import { lovStore } from 'choerodon-ui/dataset';
import Store from './stores';
import { Incident, Change, Problem, Others } from './ListItem';
import './index.less';

function MainView() {
  const {
    intl,
    prefixCls,
    tenantId,
    name,
    handleOpenView,
    relationDataSet,
    addDataSet,
    businessObjectCode,
    ticketId,
    ticketFieldCode,
    lovCode,
    ticketRelationObjectCode,
    ticketRelationFieldCode,
    formDataSet,
    viewId,
  } = useContext(Store);

  const [searching, setSearching] = useState(false);
  const [expand, setExpand] = useState(true);
  const [searchValue, setSearchValue] = useState();
  const ref = useRef();
  const inputRef = useRef();

  useEffect(() => {
    if (formDataSet?.current?.getState('relationTicketRefreshCount')) {
      relationDataSet.query();
    }
  }, [formDataSet?.current?.getState('relationTicketRefreshCount')]);

  const handleSearch = debounce(async (value) => {
    relationDataSet.setQueryParameter('param', value);
    await relationDataSet.query();
  }, 500);

  const deleteRelation = async (record) => {
    const data = [record?.get('ticketId')];
    const url = `/itsm/v1/${tenantId}/relation/ticket/${businessObjectCode}/${ticketId}/${ticketFieldCode}/relation?ticketRelationObjectCode=${ticketRelationObjectCode}&ticketRelationFieldCode=${ticketRelationFieldCode}&disassociateFlag=true`;
    const res = await axios.post(url, data);
    if (res?.failed) {
      message.error(res?.message);
    } else {
      message.success(intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete.success' }));
      relationDataSet.query();
      return true;
    }
  };

  useEffect(() => {
    lovStore.clearCache(['relation']);
    addDataSet.getField('relation').set('lovCode', '');
    addDataSet.getField('relation').set('lovCode', lovCode);
  }, []);

  const renderHeader = () => {
    if (searching) {
      return (
        <TextField
          style={{ width: '100%', marginBottom: expand ? '16px' : '0px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          ref={inputRef}
          onChange={(e) => {
            setSearchValue(e);
          }}
          onBlur={(e) => {
            const value = e.target.value;
            if (!value) {
              setSearching(false);
              handleSearch(null);
            }
          }}
          onClear={() => {
            handleSearch(null);
            inputRef?.current.element.focus();
          }}
          onEnterDown={(e) => {
            const value = e.target.value;
            handleSearch(value);
          }}
        />
      );
    }

    const addIcon = (
      <Tooltip
        title={ticketFieldCode === 'id' ? intl.formatMessage({ id: 'lcr.renderer.ticketRelation.create' }) : intl.formatMessage({ id: 'lcr.renderer.ticketRelation.edit' })}
      >
        <Lov
          ref={ref}
          dataSet={addDataSet}
          className="header-top-icon-lov"
          name="relation"
          mode="button"
          clearButton={false}
          primitiveValue
          onChange={async (value, oldValue) => {
            if (value?.id) {
              const obj = value;
              value = [obj];
            }
            const _data = value?.map((v) => v?.id) || [];
            if (_data?.length === 0 && oldValue?.length === 0) {
              return;
            }
            if (_data) {
              const url = `/itsm/v1/${tenantId}/relation/ticket/${businessObjectCode}/${ticketId}/${ticketFieldCode}/relation?ticketRelationObjectCode=${ticketRelationObjectCode}&ticketRelationFieldCode=${ticketRelationFieldCode}`;
              const res = await axios.post(url, _data);
              if (res?.failed) {
                message.error(res?.message);
              } else {
                const msgId = ticketFieldCode === 'id' ? 'lcr.renderer.ticketRelation.create.success' : 'lcr.renderer.ticketRelation.edit.success';
                message.success(
                  intl.formatMessage({ id: msgId })
                );
                relationDataSet.query();
                formDataSet.query();
                return true;
              }
            }
          }}
          noCache
        >
          <Icon className="filter-icon" type="add-one" size={16} />
        </Lov>
      </Tooltip>
    );

    return (
      <div className={`${prefixCls}-header`} style={{ marginBottom: expand ? '16px' : '0px' }}>
        <div className="header-top">
          <span className="header-top-title">
            <SectionHeaderLine />
            {name}
            <Icon
              className="header-top-title-icon"
              onClick={() => {
                setExpand(!expand);
              }}
              type={expand ? 'DownOne' : 'RightOne'}
              theme="filled"
              size={14}
            />
          </span>
          <div className="header-top-icon">
            {addIcon}
            <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
              <Icon
                className="filter-icon"
                style={{ marginLeft: '8px' }}
                type="icon-search"
                size={16}
                onClick={() => setSearching(true)}
              />
            </Tooltip>
          </div>
        </div>
      </div>
    );
  };
  const TicketMap = { INCIDENT: Incident, CHANGE: Change, PROBLEM: Problem };
  const renderList = relationDataSet.map((record) => {
    const Ticket = TicketMap[ticketRelationObjectCode] || Others;
    if (!Ticket) return null;
    return (
      <Ticket
        key={record?.get('ticketId')}
        record={record}
        intl={intl}
        prefixCls={prefixCls}
        deleteRelation={deleteRelation}
        onClick={viewId ? () => {
          handleOpenView(record);
        } : null}
      />
    );
  });

  const actions = [
    {
      text: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.create' }),
      onClick: () => {
        ref.current?.openModal();
      },
    },
  ];

  return (
    <div className={prefixCls}>
      {renderHeader()}
      {expand && (
        <>
          {relationDataSet?.length === 0 && (
            <Empty
              description={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.empty' })}
              style={{ padding: '0px', paddingTop: '36px' }}
              actions={actions}
            />
          )}
          {renderList}
        </>
      )}
    </div>
  );
}

export default observer(MainView);
