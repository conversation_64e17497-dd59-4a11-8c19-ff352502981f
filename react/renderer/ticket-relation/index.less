@import '~choerodon-ui/lib/style/themes/default';

.lc-components-ticket-relation {
  margin: 0.2rem 0.16rem;
  margin-bottom: 0;

  .flex-center {
    display: flex;
    align-items: center;
  }

  &-header {
    .header-top {
      justify-content: space-between;

      .flex-center;

      &-title {
        font-weight: 500;
        font-size: 0.16rem;
        line-height: 0.22rem;
        color: @text-color;

        .flex-center;

        &-line {
          width: 0.03rem;
          background: #2979ff;
          display: inline-block;
          height: 0.14rem;
          line-height: 0.22rem;
          margin-right: 8px;
        }

        &-icon {
          cursor: pointer;
          margin-left: 4px;
        }
      }

      &-icon {
        .flex-center;

        .c7n-pro-btn {
          padding: 0;
        }

        .filter-icon {
          padding: 0.08rem;
          color: @primary-color;
          background: @minor-color;
          border-radius: 0.04rem;
          line-height: 1;
          height: 32px;
          cursor: pointer;

          &:hover {
            background-color: @primary-6;
            color: #fff;
          }
        }

        &-lov {
          border: none !important;
        }
      }
    }

    .header-bottom {
      height: 0.22rem;

      font-weight: 500;
      font-size: 0.14rem;
      line-height: 0.22rem;

      .flex-center;
    }
  }

  &-card {
    border-radius: 0.04rem;
    border: 0.01rem solid #e8e8e8;
    padding: 0.16rem;
    margin-bottom: 0.16rem;

    &:hover {
      background: #f5f5f5;

      .lc-components-ticket-relation-card-footer-delete {
        .flex-center;
      }
    }

    &-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.08rem;

      &-title {
        font-weight: 500;
        width: calc(100% - 118px);
        .flex-center;

        span {
          margin-right: 0.08rem;
        }

        span:last-child {
          margin-right: 0;
          color: #2979ff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          padding-right: 0.08rem;
        }

        &-noview {
          color: @text-color  !important;
        }
      }

      &-state {
        border-radius: 0.02rem;
        color: #fff;
        font-size: 0.12rem;
        padding: 0 0.05rem;
        height: 0.22rem;
        line-height: 0.22rem;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 110px;

        &-tooltip {
          display: inline-block;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
    }

    &-content {
      div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-bottom: 0.06rem;
      }
    }

    &-footer {
      position: relative;
      .flex-center;

      &-person {
        color: #595959;
        display: flex;
        align-items: center;
        width: 100%;

        &>span:first-child {
          margin-right: 0.08rem;
          min-width: 22px;
          .c7n-avatar-string {
            transform: scale(1) translateX(-50%) !important;
          }
        }

        .assignmentPerson {
          margin-right: 0.16rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline;
        }

        .assignmentGroup {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline;
        }
      }

      &-delete {
        background: #f5f5f5;
        display: none;
        color: #f43636;
        cursor: pointer;
        position: absolute;
        right: 0;
        bottom: 0;

        span:first-child {
          margin-right: 0.08rem;
        }
      }
    }
  }
}