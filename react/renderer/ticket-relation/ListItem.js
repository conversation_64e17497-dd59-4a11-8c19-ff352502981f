import React, { useRef, useState, useEffect } from 'react';
import './index.less';
import { Icon, YqAvatar } from '@zknow/components';
import { Tooltip } from 'choerodon-ui/pro';
import AvatarTooltip from '@/components/avatar-tooltip';

const dataToHtml = (data) => {
  try {
    data = JSON.parse(data);
  } catch (e) {
    //
  }
  let res = '';
  if (typeof data === 'string') {
    res = data.replace(new RegExp('<[^>]*>', 'g'), ' ');
    res = res.replace(new RegExp('\\s+', 'g'), ' ');
    res = res.replace(new RegExp('&nbsp;', 'g'), '');
  } else {
    const deltaValue = data?.ops || data;
    if (deltaValue && deltaValue[1] === '[' && Array.isArray(deltaValue.slice())) {
      deltaValue.forEach((v) => {
        res += v?.insert?.image || v?.insert;
      });
    }
    res = res.replace('\n', '');
  }
  return res;
};

function Card({
  number,
  title,
  stateColor,
  stateName,
  fields,
  assignmentGroup,
  assignmentPerson,
  imgUrl,
  prefixCls,
  intl,
  onClick,
  handleDelete,
  assigneePersonId,
}) {
  const stateRef = useRef();

  return (
    <div className={`${prefixCls}-card`}>
      <div className={`${prefixCls}-card-header`}>
        <div
          className={`${prefixCls}-card-header-title`}
          style={{
            cursor: onClick ? 'pointer' : undefined,
          }}
          onClick={onClick}
        >
          <Icon type="icon-document" theme="filled" size={16} fill="#2979FF" />
          <span>{number}</span>
          <Tooltip title={title}>
            <span className={onClick ? '' : `${prefixCls}-card-header-title-noview`}>
              {title || '-'}
            </span>
          </Tooltip>
        </div>
        {stateName
        && <Tooltip title={stateName} placement="topLeft">
          <div
            ref={stateRef}
            className={`${prefixCls}-card-header-state`}
            style={{ background: stateColor }}
          >
            {stateName}
          </div>
        </Tooltip>}
      </div>
      <div className={`${prefixCls}-card-content`}>
        {fields?.map(({ name, value }) => {
          return value && <div key={name}>{`${name}: ${dataToHtml(value)}`}</div>;
        })}
      </div>
      <div className={`${prefixCls}-card-footer`}>
        <div className={`${prefixCls}-card-footer-person`}>
          {[
            assignmentPerson && <AvatarTooltip id={assigneePersonId}>
              <YqAvatar src={imgUrl} size={22}>
                {assignmentPerson}
              </YqAvatar>
            </AvatarTooltip>,
            assignmentPerson && <span className="assignmentPerson">{assignmentPerson} </span>,
            assignmentGroup && <span className="assignmentGroup">{assignmentGroup} </span>,
          ]}
        </div>
        <div className={`${prefixCls}-card-footer-delete`} onClick={handleDelete}>
          <Icon type="LinkBreak" theme="filled" size="16" fill="#F43636" />
          {intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete' })}
        </div>
      </div>
    </div>
  );
}

function transFormProps({ record, prefixCls, intl, deleteRelation }) {
  const number = record?.get('number');
  const title = record?.get('shortDescription');
  const imgUrl = record?.get('imageUrl');
  const assignmentGroup = record?.get('assignmentGroupName');
  const assignmentPerson = record?.get('assigneePersonName');
  const stateColor = record?.get('stateColor');
  const stateName = record?.get('stateName');
  const assigneePersonId = record?.get('assigneePersonId');
  const handleDelete = () => {
    deleteRelation(record);
  };
  return {
    number,
    title,
    stateColor,
    stateName,
    imgUrl,
    assignmentGroup,
    assignmentPerson,
    prefixCls,
    intl,
    handleDelete,
    assigneePersonId,
  };
}

export function Incident(props) {
  const { record, intl, onClick } = props;
  const description = record?.get('description');
  const resolution = record?.get('resolution');

  const cardProps = {
    ...transFormProps(props),
    fields: [
      { name: intl.formatMessage({ id: 'zknow.common.model.description' }), value: description },
      { name: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.ticket.solution' }), value: resolution },
    ],
    onClick,
  };
  return <Card {...cardProps} />;
}

export function Change(props) {
  const { record, intl, onClick } = props;
  const type = record?.get('type');
  const description = record?.get('description');
  const changePlan = record?.get('changePlan');

  const cardProps = {
    ...transFormProps(props),
    fields: [
      { name: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.change.type' }), value: type },
      { name: intl.formatMessage({ id: 'zknow.common.model.description' }), value: description },
      { name: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.change.plan' }), value: changePlan },
    ],
    onClick,
  };
  return <Card {...cardProps} />;
}

export function Problem(props) {
  const { record, intl, onClick } = props;
  const description = record?.get('description');
  const causeNotes = record?.get('causeNotes');
  const cardProps = {
    ...transFormProps(props),
    fields: [
      { name: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.cause.notes' }), value: causeNotes },
      { name: intl.formatMessage({ id: 'zknow.common.model.description' }), value: description },
    ],
    onClick,
  };
  return <Card {...cardProps} />;
}

export function Others(props) {
  const { onClick } = props;
  const cardProps = {
    ...transFormProps(props),
    onClick,
  };
  return <Card {...cardProps} />;
}
