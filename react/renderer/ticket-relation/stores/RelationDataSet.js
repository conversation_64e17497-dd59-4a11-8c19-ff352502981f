export default ({
  tenantId,
  businessObjectCode,
  ticketId,
  ticketFieldCode,
  ticketRelationObjectCode,
  ticketRelationFieldCode,
  addDataSet,
}) => {
  const url = `/itsm/v1/${tenantId}/relation/ticket/${businessObjectCode}/${ticketId}/${ticketFieldCode}/relation?ticketRelationObjectCode=${ticketRelationObjectCode}&ticketRelationFieldCode=${ticketRelationFieldCode}`;
 
  return {
    autoQuery: !!ticketId,
    paging: false,
    transport: {
      read: {
        url,
        method: 'get',
        transformResponse(response) {
          try {
            const res = JSON.parse(response);
            if (res?.failed) {
              return response;
            } else {
              const ticket = res.map(item => {
                item.name = item.shortDescription;
                item.id = item.ticketId;
                item.primaryKey = item.ticketId;
                return item;
              });

              addDataSet.loadData([{ relation: ticket }]);

              return res;
            }
          } catch (e) {
            return response;
          }
        },
      },
    },
    fields: [
    ],
  };
};
