import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import RelationDataSet from './RelationDataSet';
import AddDataSet from './AddDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config: {
        name,
        widgetConfig: {
          viewId,
          viewName,
          tableLinkViewType,
          tableLinkViewSize,
          ticketRelationObjectId,
          ticketRelationObjectCode,
          ticketRelationLovId,
          ticketFieldCode,
          ticketRelationFieldCode,
        },
      },
      formDataSet,
      viewDataSet,
      openView,
    } = props;

    const prefixCls = 'lc-components-ticket-relation';

    const formConfig = viewDataSet?.current?.toData() || {};
    const { businessObjectCode, businessObjectId } = formConfig;
    // 'INCIDENT',
    const codeArr = ['INCIDENT', 'CHANGE', 'PROBLEM'];
    // 关联的单据有一个code为主键才能正常使用
    const show = (ticketFieldCode === 'id' || ticketRelationFieldCode === 'id');

    // 没有配置值列表就先使用平台的LOV
    const isOld = !ticketRelationLovId;
    const lovCode = isOld ? ticketRelationObjectCode : ticketRelationLovId;
    const addDataSet = useMemo(
      () => new DataSet(AddDataSet({ intl, ticketId, multiple: ticketFieldCode === 'id', tenantId, isOld, ticketRelationLovId })),
      [intl, isOld, tenantId]
    );

    const relationDataSet = useMemo(() => new DataSet(
      RelationDataSet({
        tenantId,
        businessObjectCode,
        ticketId,
        ticketFieldCode,
        ticketRelationObjectCode,
        ticketRelationFieldCode,
        addDataSet,
      })
    ), [
      addDataSet,
      businessObjectCode,
      ticketId,
      ticketFieldCode,
      ticketRelationObjectCode,
      ticketRelationFieldCode,
    ]);

    const handleOpenView = (record) => {
      record.set('id', record?.get('ticketId'));
      record.set('short_description', record?.get('shortDescription'));
      openView({
        viewSize: tableLinkViewSize,
        openType: tableLinkViewType || 'MIDDLE',
        viewId,
        viewName,
      }, relationDataSet, record);
    };

    if (!show) {
      return null;
    }

    const value = {
      ...props,
      formDataSet,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      businessObjectCode,
      businessObjectId,
      name,
      viewId,
      viewName,
      tableLinkViewType,
      tableLinkViewSize,
      ticketRelationObjectId,
      ticketRelationObjectCode,
      lovCode,
      ticketFieldCode,
      ticketRelationFieldCode,
      relationDataSet,
      addDataSet,
      handleOpenView,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
