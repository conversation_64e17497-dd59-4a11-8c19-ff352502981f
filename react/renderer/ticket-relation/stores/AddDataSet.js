import { getQueryParams } from '@zknow/utils';
import { transformResponse, transformField } from '@/utils/lovConfig';

export default ({
  intl,
  ticketId,
  multiple,
  isOld,
  tenantId,
  ticketRelationLovId,
}) => {
  const config = isOld ? {} : {
    lovDefineAxiosConfig: lovCode => ({
      url: `/lc/v1/${tenantId}/object_options/id/${lovCode}`,
      method: 'GET',
      transformResponse: data => transformResponse(data, data?.name, (map, f) => transformField(map, f), intl, tenantId),
    }),
    lovQueryAxiosConfig: (lovCode, lovConfig = {}, { data, params }) => {
      lovConfig.method = 'POST';
      return {
        url: `/lc/v1/engine/${tenantId}/options/${lovCode}/queryWithCondition`,
        method: 'POST',
        data: {
          conditions: [{ condition: 'AND', filters: [{ condition: 'AND', filter: 'is not', widgetType: 'Lov', componentType: 'Lov', field: 'id', fieldValue: ticketId }] }],
          params: {
            ...getQueryParams(data),
            __page_params: data?.__page_params,
          },
        },
        params,
        transformResponse: (originData) => {
          try {
            const jsonData = JSON.parse(originData);
            return {
              ...jsonData,
              content: jsonData?.content?.map(item => {
                return {
                  ...item,
                  primaryKey: item.id,
                };
              }) || [],
            };
          } catch (error) {
            return [];
          }
        },
      };
    },
  };

  return {
    autoQuery: false,
    paging: false,
    fields: [{
      name: 'relation',
      type: 'object',
      lovCode: isOld ? 'TICKET' : ticketRelationLovId,
      lovPara: { incidentId: ticketId },
      multiple,
      validate: () => true,
      ...config,
    }],
  };
};
