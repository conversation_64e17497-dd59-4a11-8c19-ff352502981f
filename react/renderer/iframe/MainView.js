import React from 'react';
import { observer } from 'mobx-react-lite';

export default observer((props) => {
  const { config, formDataSet } = props;
  const arr = config?.customConfig || [];
  const customProps = arr.reduce((pre, cur) => {
    pre[cur.key] = cur.value;
    return pre;
  }, {});

  let url = customProps.url;
  // 通过字段决定链接
  if (customProps.urlField) {
    url = formDataSet?.current?.get(customProps.urlField) || url;
  }
  // 字段参数
  if (customProps.paramFields) {
    // 拼接参数前，先拼接?
    if (!url?.includes('?')) {
      url = `${url}?`;
    }
    customProps.paramFields.split(',')?.map(key => {
      url += `&${key}=${formDataSet?.current?.get(key)}`;
      return key;
    });
  }

  return (
    <iframe
      title="iframe"
      id="iframe"
      width={customProps.width || '100%'}
      height={customProps.height || '100%'}
      frameBorder="0"
      // scrolling="no"
      src={url}
    />
  );
});
