/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2021-12-09 11:23:13
 * @Description: 头部组件
 */
import React, { useEffect, useState, useMemo } from 'react';
import axios from 'axios';
import { observer } from 'mobx-react-lite';
import {
  Modal,
  Form,
  DataSet,
  Lov,
  Select,
  message,
  SelectBox,
  Tooltip,
  TextArea,
} from 'choerodon-ui/pro';
import { Icon, Button } from '@zknow/components';
import { getLovConfig } from '../utils/utils';
import './index.less';

const RENDERER_CODE = 'approval-action-renderer';
const agreeModalKey = Modal.key();
const { Option } = Select;

export default observer((props) => {
  const {
    tenantId,
    formDataSet,
    intl,
    tabMenuDataSet,
    viewRecord,
    config, // 老单据头上传过来的
  } = props;
  const record = formDataSet?.get(0);
  const ticketId = record?.get('id');
  const [historyNodeList, setHistoryNodeList] = useState([]);
  const [actionList, setActionList] = useState([]);

  const participantUserLovId = viewRecord?.get('widgetConfig.approvalActionUserLovId') || config?.widgetConfig?.approvalActionUserLovId;

  const ApprovalMethodType = new DataSet({
    data: [
      {
        text: intl.formatMessage({ id: 'lcr.renderer.approval.or_sign' }),
        value: 'OR_SIGN',
      },
      {
        text: intl.formatMessage({ id: 'lcr.renderer.approval.countersign' }),
        value: 'COUNTER_SIGN',
      },
    ],
  });

  const AddSignMethodType = new DataSet({
    data: [
      {
        text: intl.formatMessage({ id: 'lcr.renderer.approval.pre_signed' }),
        value: 'BEFORE',
      },
      {
        text: intl.formatMessage({ id: 'lcr.renderer.approval.post_sign' }),
        value: 'AFTER',
      },
    ],
  });

  function getExtraProps() {
    if (participantUserLovId) {
      return getLovConfig(tenantId, ticketId, intl);
    }
    return {};
  }

  const approvalDataSet = useMemo(() => {
    return new DataSet({
      autoCreate: true,
      fields: [
        { name: 'remark', label: intl.formatMessage({ id: 'lcr.renderer.approval.remark' }) },
        { name: 'transfer_remark', label: intl.formatMessage({ id: 'lcr.renderer.approval.transfer.remark' }) },
        { name: 'reject_activity_id', label: intl.formatMessage({ id: 'lcr.renderer.approval.rejectTo' }) },
        {
          name: 'person',
          type: 'object',
          lovCode: participantUserLovId || 'USER',
          label: intl.formatMessage({ id: 'lcr.renderer.approval.user' }),
          dynamicProps: {
            required: ({ record: _record }) => {
              return _record.get('buttonType') === 'TRANSFER';
            },
          },
          ...getExtraProps(),
        },
        // 加签审批方式
        {
          name: 'signType',
          type: 'string',
          label: intl.formatMessage({ id: 'lcr.renderer.approval.approval_method' }),
          options: ApprovalMethodType,
          textField: 'text',
          valueField: 'value',
          defaultValue: 'OR_SIGN',
        },

        // 加签类型
        {
          name: 'addSignType',
          type: 'string',
          label: intl.formatMessage({ id: 'lcr.renderer.approval.addsign_method' }),
          options: AddSignMethodType,
          textField: 'text',
          valueField: 'value',
          defaultValue: 'BEFORE',
        },
        {
          name: 'candidateUsers',
          type: 'object',
          lovCode: participantUserLovId || 'USER',
          label: intl.formatMessage({ id: 'lcr.renderer.approval.candidateUsers' }),
          multiple: true,
          dynamicProps: {
            required: ({ record: _record }) => {
              return _record.get('buttonType') === 'ADD_SIGN';
            },
          },
          ...getExtraProps(),
        },
      ],
    });
  }, [participantUserLovId]);

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      const {
        // 'wf_instance_id:id': workflowInstanceId,
        wf_instance_id: workflowInstanceId,
        id,
        wf_task_id: wfTaskId,
      } = formDataSet.current?.toData() || {};
      getActionList(id);
      getApprovalHistory(workflowInstanceId);
    }
  }, [formDataSet?.status, ticketId]);

  function refresh() {
    const {
      // 'wf_instance_id:id': workflowInstanceId,
      wf_instance_id: workflowInstanceId,
      id,
    } = formDataSet.current?.toData() || {};
    getActionList(id);
    getApprovalHistory(workflowInstanceId);
  }

  async function getApprovalHistory(id) {
    if (id) {
      const res = await axios.get(`workflow/v1/workflowInstances/${id}`);
      if (res?.failed) {
        message.error(res.message);
      } else {
        const currentRunningNodes = res.currentRunningNodes || [];
        const nodeList = (res.historyActivities || []).filter(r => r?.type === 'userTask') || [];
        const list = [];
        // 排除当前节点
        nodeList.forEach((i) => {
          if (currentRunningNodes.find((j) => j.id !== i.id)) {
            if (list.length === 0) list.push(i);
            if (!list?.find((k) => k.id === i.id)) list.push(i);
          }
        });
        setHistoryNodeList(list);
      }
    }
  }

  // 获取按钮集合
  async function getActionList(id) {
    if (id) {
      const res = await axios.get(`workflow/v1/${tenantId}/taskInstances/button_permissions/${id}`);
      if (res?.failed) {
        message.error(res.message);
      } else {
        setActionList(res || []);
      }
    }
  }

  // 下划线转换驼峰
  function toHump(name) {
    // eslint-disable-next-line no-useless-escape
    return name.replace(/\_(\w)/g, (all, letter) => {
      return letter.toUpperCase();
    });
  }

  async function handleSubmit(button) {
    // 如果是加签类型的
    if (button === 'ADD_SIGN') {
      const addSignType = formDataSet?.current?.get('addSignType');
      const signType = formDataSet?.current?.get('signType');
      formDataSet?.current?.set('addSignType', addSignType || 'BEFORE');
      formDataSet?.current?.set('signType', signType || 'OR_SIGN');
    }
    const data = formDataSet?.current?.toData() || {};
    const requestObj = {};
    // NOTE: 由于只是单层级数据，所以可以这么写
    const keys = Object.keys(data);
    const values = Object.values(data);
    keys.forEach((i, index) => {
      requestObj[toHump(i)] = values[index];
    });
    const res = await axios.post(`workflow/v1/${tenantId}/taskInstances/button_submit/${button}`, JSON.stringify(requestObj));
    if (res?.failed) {
      message.error(res?.message);
    } else {
      message.success(intl.formatMessage({ id: 'lcr.renderer.approval.submit.success' }));
    }
  }

  // 打开弹窗
  function openModal(type) {
    const children = [];
    approvalDataSet?.current?.set('buttonType', type);
    if (type === 'APPROVE') {
      getApproveFormFields(children);
    } else if (type === 'REJECT') {
      getRejectFormFields(children);
    } else if (type === 'ADD_SIGN') {
      getAddSignFormFields(children);
    } else {
      getTransferFormFields(children);
    }
    Modal.open({
      key: agreeModalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.approval.title' }),
      okFirst: true,
      children: (
        <Form dataSet={approvalDataSet} labelWidth="auto">
          {children}
        </Form>
      ),
      destroyOnClose: true,
      onOk: async () => {
        // const res = type === 'TRANSFER' ? await approvalDataSet.validate() : true;
        const res = await approvalDataSet.validate();
        if (res) {
          await handleSubmit(type);
          // 转交，加签完成之后直接跳回列表
          if (['ADD_SIGN', 'TRANSFER'].includes(type)) {
            handleGotoListPage();
          } else {
            await formDataSet.query();
            refresh();
            approvalDataSet.reset();
            // 刷新动态记录
            formDataSet.current?.setState({
              dynamicRefreshCount: Math.round(Math.random() * 10000),
              approvalHistoryDynamicRefreshCount: Math.round(Math.random() * 10000),
            });
          }
          handleRefreshListPage();
          return true;
        }
        return false;
      },
      onCancel: () => {
        formDataSet.current.reset();
        return true;
      },
    });
  }

  // 跳转到列表页面
  function handleGotoListPage() {
    if (tabMenuDataSet) {
      tabMenuDataSet?.setState('removeTabId', formDataSet?.current?.get('id'));
    }
  }

  // 跳转到列表页面
  function handleRefreshListPage() {
    if (tabMenuDataSet) {
      tabMenuDataSet?.setState('refreshListPage', formDataSet?.current?.get('id'));
    }
  }

  function getRadioTooltip(value) {
    if (value === 'OR_SIGN') {
      return intl.formatMessage({ id: 'lcr.renderer.approval.approver.one' });
    } else if (value === 'COUNTER_SIGN') {
      return intl.formatMessage({ id: 'lcr.renderer.approval.approver.all' });
    } else if (value === 'BEFORE') {
      return intl.formatMessage({ id: 'lcr.renderer.approval.pre_signed.description' });
    } else if (value === 'AFTER') {
      return intl.formatMessage({ id: 'lcr.renderer.approval.post_sign.description' });
    }
    return null;
  }

  // 单选框渲染
  const optionRenderer = ({ text, value }) => (
    <div className={`${RENDERER_CODE}-radio-text`} style={{ display: 'inline-flex', alignItems: 'center' }}>
      <span>{text}</span>
      <Tooltip placement="top" title={getRadioTooltip(value)}>
        <Icon style={{ marginLeft: 4, color: 'rgba(0, 0, 0, 0.45)' }} type="help" />
      </Tooltip>
    </div>
  );

  // 加签的表单
  function getAddSignFormFields(children = []) {
    children.push(
      <Lov
        name="candidateUsers"
        onChange={(value) => {
          const candidateUsers = value?.map((i) => i.id) || [];
          approvalDataSet?.current?.set('candidateUsers', value);
          formDataSet?.current?.set('candidateUsers', candidateUsers);
        }}
      />
    );
    children.push(
      <TextArea
        onChange={(value) => {
          approvalDataSet?.current?.set('remark', value);
          formDataSet?.current?.set('remark', value);
        }}
        label={intl.formatMessage({ id: 'lcr.renderer.approval.addSign.remark' })}
        name="remark"
      />
    );
    children.push(
      <SelectBox
        className={`${RENDERER_CODE}-radio`}
        name="addSignType"
        optionRenderer={optionRenderer}
        onChange={(value) => {
          approvalDataSet?.current?.set('addSignType', value);
          formDataSet?.current?.set('addSignType', value);
        }}
      />
    );
    children.push(
      <SelectBox
        className={`${RENDERER_CODE}-radio`}
        name="signType"
        optionRenderer={optionRenderer}
        onChange={(value) => {
          approvalDataSet?.current?.set('signType', value);
          formDataSet?.current?.set('signType', value);
        }}
      />
    );
  }

  // 审批的表单
  function getApproveFormFields(children = []) {
    children.push(
      <TextArea
        onChange={(value) => {
          approvalDataSet?.current?.set('remark', value);
          formDataSet?.current?.set('remark', value);
        }}
        name="remark"
      />
    );
  }

  // 拒绝的表单
  function getRejectFormFields(children = []) {
    if (actionList.includes('RETURN')) {
      children.push(
        <Select
          onChange={(value) => {
            approvalDataSet?.current?.set('reject_activity_id', value);
            formDataSet?.current?.set('reject_activity_id', value);
          }}
          name="reject_activity_id"
        >
          {historyNodeList.map((i) => <Option key={i.activityId} value={i.activityId}>{i.name}</Option>)}
        </Select>
      );
    }
    children.push(
      <TextArea
        onChange={(value) => {
          approvalDataSet?.current?.set('remark', value);
          formDataSet?.current?.set('remark', value);
        }}
        name="remark"
      />
    );
  }

  // 转交的表单
  function getTransferFormFields(children = []) {
    children.push(
      <Lov
        onChange={(value) => {
          approvalDataSet?.current?.set('person', value);
          formDataSet?.current?.set('person_id', value?.id);
        }}
        name="person"
      />
    );
    children.push(
      <TextArea
        onChange={(value) => {
          approvalDataSet?.current?.set('transfer_remark', value);
          formDataSet?.current?.set('transfer_remark', value);
        }}
        name="transfer_remark"
      />
    );
  }

  if (!record) return null;
  // TODO 审批单头部需要加上节点
  return (
    <>
      {actionList.includes('APPROVE') && <Button color="primary" icon="check-small" onClick={() => openModal('APPROVE')}>{intl.formatMessage({ id: 'zknow.common.status.agree' })}</Button>}
      {actionList.includes('REJECT') && <Button color="red" icon="close" onClick={() => openModal('REJECT')}>{intl.formatMessage({ id: 'zknow.common.status.reject' })}</Button>}
      {actionList.includes('ADD_SIGN') && <Button color="secondary" icon="add-one" onClick={() => openModal('ADD_SIGN')}>{intl.formatMessage({ id: 'lcr.renderer.approval.addSignature' })}</Button>}
      {actionList.includes('TRANSFER') && <Button color="secondary" icon="share-two" onClick={() => openModal('TRANSFER')}>{intl.formatMessage({ id: 'lcr.renderer.approval.transfer' })}</Button>}
    </>
  );
});
