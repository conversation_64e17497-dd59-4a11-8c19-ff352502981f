import React, { createContext, useMemo, useState } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { ListDataSet, lnspectionDataSet } from './DataSet';

type ServiceAgreementProps = {
  intl: any,
  history: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  path?: string,
  match?: any,
  formConfig?: any,
  children?: any,
  AppState: any,
  formDataSet: any,
  viewDataSet: any,
  goalsDataSet: any,
  ticketId: string,
  viewRecord: any,
  mode?: string,
  pageRef?: any,
  HeaderStore?: any,
  setRefreshShoppingCartCount?: any,
  shoppingCartFlag?: any,
  aiSuggestionsFlag?: any,
  labelName?: any,
  enableChatGptFlag?: any,
  gptTenantFlag?: any,
  promptTemplateCode?: any,
};

const Store = createContext<ServiceAgreementProps>({
  intl: undefined,
  history: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  viewDataSet: '',
  goalsDataSet: '',
  ticketId: '',
  viewRecord: '',
  HeaderStore: '',
  setRefreshShoppingCartCount: undefined,
  shoppingCartFlag: false,
  aiSuggestionsFlag: false,
  labelName: '',
  enableChatGptFlag: false,
  gptTenantFlag: false,
  promptTemplateCode: '',
});

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props: ServiceAgreementProps) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      HeaderStore: { getTenantConfig: { enableChatGptFlag, gptTenantFlag } },
      formConfig,
      formDataSet,
      viewDataSet,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
    } = props;
    const prefixCls = 'quality-inspection-score';
    const ticketId = formDataSet && formDataSet.current && formDataSet.current.get('id');
    const { businessObjectCode } = (viewDataSet && viewDataSet.current && viewDataSet.current.toData()) || {};
    const solutionId = viewRecord && viewRecord.get('widgetConfig.solutionId');
    const aiSuggestionsFlag = viewRecord && viewRecord.get('widgetConfig.aiSuggestionsFlag');
    const promptTemplateId = viewRecord && viewRecord.get('widgetConfig.promptTemplateId');
    const promptTemplateCode = viewRecord && viewRecord.get('widgetConfig.promptTemplateObj.code');
    const labelName = viewRecord && viewRecord.get('name');
    const [solutionCacheData, setSolutionCacheData] = useState({});
    
    const options = useMemo(() => new DataSet(ListDataSet({ tenantId, businessObjectCode })), [businessObjectCode]);
    const scoreDataSet = useMemo(() => new DataSet(lnspectionDataSet({ tenantId, intl, ticketId, options, solutionId, setSolutionCacheData, solutionCacheData })), [options, solutionId]);

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      businessObjectCode,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
      scoreDataSet,
      options,
      promptTemplateId,
      aiSuggestionsFlag,
      labelName,
      enableChatGptFlag,
      gptTenantFlag,
      solutionCacheData,
      setSolutionCacheData,
      promptTemplateCode,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
