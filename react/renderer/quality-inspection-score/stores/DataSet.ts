// @ts-nocheck
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/interface';

const ListDataSet = ({
  tenantId,
  autoLocateFirst,
  businessObjectCode,
}): DataSetProps => {
  return {
    autoQuery: true,
    autoLocateFirst,
    paging: true,
    primaryKey: 'id',
    selection: false,
    pageSize: 9999,
    dataKey: 'content',

    transport: {
      read: () => {
        if (!businessObjectCode) return null;
        return {
          url: `/intelligent/v1/${tenantId}/checkSolution?page=0&scope=${businessObjectCode}&size=9999`,
          method: 'GET',
        };
      },
    },
    fields: [

    ],
    events: {

    },
  };
};

const lnspectionDataSet = ({
  intl,
  tenantId,
  autoLocateFirst,
  ticketId,
  solutionId,
  options,
  setSolutionCacheData,
}): DataSetProps => {
  return {
    autoQuery: true,
    autoLocateFirst: true,
    paging: false,

    transport: {
      read: () => {
        if (!solutionId || !ticketId) {
          return null;
        }
        return {
          url: `/ticket/v1/${tenantId}/ticket/checks/standards?ticketId=${ticketId}&solutionId=${solutionId}`,
          method: 'GET',
          transformResponse: (data) => {
            try {
              const res = JSON.parse(data);
              res.totalScore = res?.checkStandards?.reduce((prev, curr) => prev + (curr?.value || 0), 0);
              setSolutionCacheData(res);
              return res;
            } catch (e) {
              return data;
            }
          },
        };
      },
    },
    fields: [
      { name: 'solutionId', options, required: true, label: intl.formatMessage({ id: 'lcr.renderer.qualityInspection.plan' }), textField: 'name', valueField: 'id' },
      { name: 'totalScore', label: intl.formatMessage({ id: 'lcr.renderer.qualityInspection.CurrentPoints' }) },
      { name: 'comment', label: intl.formatMessage({ id: 'lcr.renderer.qualityInspection.Comments' }), dynamicProps: { required: ({ record }) => { const currentSolution = options?.find((i) => { return i?.get('id') === record?.get('solutionId'); }); return currentSolution && currentSolution?.get('remarkRequiredFlag'); } } },
      { name: 'aiFlag' },
    ],
  };
};

export {
  ListDataSet,
  lnspectionDataSet,
};
