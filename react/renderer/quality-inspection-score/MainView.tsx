import React, { useContext, useRef, useEffect, useMemo, useState } from 'react';
import { Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import {
  message,
  Form,
  TextField,
  TextArea,
  Select,
  NumberField,
  Skeleton,
  DataSet,
  CheckBox,
} from 'choerodon-ui/pro';
import lodashGet from 'lodash/get';
import Store from './stores';
import style from './index.module.less';
import { scoreQualityInspection } from '@/service';
import AiTag from '@/components/ai-tag';
import { getAiSummaryPoll, checkQualityInspectionPermissions, getQualityAiSummaryAsync } from '@/service';

const MainView = observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    ticketId,
    viewRecord,
    mode,
    formDataSet,
    pageRef,
    tenantId,
    scoreDataSet,
    options,
    promptTemplateId,
    businessObjectCode,
    aiSuggestionsFlag,
    labelName,
    enableChatGptFlag,
    gptTenantFlag,
    solutionCacheData,
    setSolutionCacheData,
    promptTemplateCode,
  } = context;

  const timer = useRef<any>();
  const count = useRef(30);
  const [hasPermission, setHasPermission] = useState(false);
  const [loading, setLoading] = useState(false);
  const [originResult, setOriginResult] = useState({});

  useEffect(() => {
    getCheckPremission();
  }, []);

  const getCheckPremission = async () => {
    const res = await checkQualityInspectionPermissions({
      tenantId,
      data: ['yqcloud-ticket.ticket-check.submitCheckScore'],
    });
    if (res && !res.failed) {
      setHasPermission(res[0]?.approve);
    }
  };

  const getScoreList = () => {
    const solutionId = scoreDataSet.current?.get('solutionId');
    const standardList = options.find(i => i.get('id') === solutionId)?.get('standardList') || [];
    return standardList.map(i => {
      let scoreValue;
      let scoreTextValue;
      switch (i.widgetType) {
        case 'CheckBox':
          scoreValue = 0; 
          scoreTextValue = scoreDataSet.current?.get(i.standardCode);
          break;
        case 'Select':
          scoreValue = 0;
          scoreTextValue = scoreDataSet.current?.get(i.standardCode);
          break;
        default:
          scoreValue = scoreDataSet.current?.get(i.standardCode);
          break;
      }
      return {
        scoreItem: i.scoreItem,
        standardCode: i.standardCode,
        value: scoreValue,
        id: i.id,
        aiFlag: originResult?.[i.standardCode] === scoreDataSet.current?.get(i.standardCode),
        scoreTextValue,
      };
    });
  };

  const handleGetScore = async () => {
    if (loading) return;
    if (!scoreDataSet?.current?.get('solutionId')) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.plan.need' }));
      return;
    }
    setLoading(true);
    getQualityAiSummaryAsync({
      tenantId,
      aiTemplateId: promptTemplateId,
      ticketId,
      solutionId: scoreDataSet?.current?.get('solutionId'),
    }).then(result => {
      const uuid = typeof result === 'string' ? result : '';
      if (uuid) {
        timer.current = setInterval(() => {
          if (count.current === 0) {
            clearInterval(timer.current);
          } else {
            count.current -= 1;
            getAiSummaryPoll({ tenantId, uuid }).then(async resp => {
              if (resp && !resp?.failed) {
                try {
                  const changedParams = lodashGet(resp, 'changedParams', {});
                  const changedParamsArr = Object.keys(changedParams);
                  changedParamsArr.forEach(v => {
                    if (v === promptTemplateCode) {
                      const data = JSON.parse(changedParams[v]);
                      const { comment, ...rest } = data;
                      setOriginResult(rest);
                      scoreDataSet.current?.set(rest);
                      const scores: number[] = Object.values(rest);
                      const score = scores.reduce((sum, cur) => {
                        const isNumber = Number.isFinite(cur);
                        return isNumber ? sum + (cur || 0) : sum;
                      }, 0);
                      scoreDataSet.current?.set({
                        totalScore: score,
                        comment,
                        aiFlag: true,
                      });
                    }
                  });
                  setLoading(false);
                  clearInterval(timer.current);
                } catch (error) {
                  setLoading(false);
                  clearInterval(timer.current);
                }
              }
            });
          }
        }, 5 * 1000);
      } else {
        //
        setLoading(false);
      }
    });
  };

  const updateScore = (value, oldValue, item) => {
    const cur = scoreDataSet.current;
    if (!cur) return;
    const {
      standardCode: name, maxScore: max, minScore: min,
    } = item;
    if (value && (value > max || value < min)) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.ScoreOutOfRange' }));
      cur.set(name, oldValue);
    } else {
      const score = getScoreList().reduce((sum, { value: v, widgetType }) => {
        if (widgetType === 'Select') {
          return sum;
        } else {
          return sum + (v || 0); 
        }
      }, 0);
      scoreDataSet.current?.set('totalScore', score);
      scoreDataSet.current?.set('aiFlag', false);
    }
  };

  // 添加字段
  const handleAddField = (i) => {
    const {
      standardCode: name, scoreItem: label, 
      description: help, maxScore: max, minScore: min, widgetType, widgetConfig,
    } = i;
    try {
      const config = widgetConfig ? JSON.parse(widgetConfig) : {};
      switch (widgetType) {
        case 'Select':
          if (config?.dataSource === 'optionSet') {
            const requiredOptionDataSet = new DataSet({
              paging: false,
              data: config?.customOptions,
            });
            scoreDataSet.addField(name, { label, help, required: true, options: requiredOptionDataSet });
          } else if (config?.dataSource === 'lookup') {
            scoreDataSet.addField(name, { label, help, required: true, lookupCode: config?.lookupCode.typeCode });
          }
          break;
        case 'CheckBox':
          scoreDataSet.addField(name, { type: 'boolean', label, help, max, min, required: true });
          break;
        default:
          scoreDataSet.addField(name, { type: 'number', label, help, max, min, required: true });
          break;
      }
    } catch (error) {
      scoreDataSet.addField(name, { type: 'number', label, help, max, min, required: true });
    }
  };

  const scoreList = useMemo(() => {
    const solutionId = scoreDataSet.current?.get('solutionId');
    const standardList = scoreDataSet.current?.get('checkStandards') || options.find(i => i.get('id') === solutionId)?.get('standardList') || [];
    return <>
      {standardList.map(i => {
        const {
          standardCode: name, scoreItem: label,
          description: help, maxScore: max, minScore: min, value: defaultValue, widgetType,
        } = i;

        // FIXME: 不要在这里给 ds 添加字段
        handleAddField(i);
        if (typeof defaultValue === 'number') {
          scoreDataSet.current.set(name, defaultValue);
        }

        switch (widgetType) {
          case 'CheckBox':
            return <CheckBox name={name} onChange={(value, oldValue) => updateScore(value, oldValue, i)} />;
          case 'Select':
            return <Select name={name} />;
          default:
            return (
              <NumberField
                name={name}
                step={1}
                placeholder={hasPermission ? `${intl.formatMessage({ id: 'lcr.renderer.qualityInspection.ScoreRange' })}${min}~${max}` : ''}
                addonAfter={intl.formatMessage({ id: 'lcr.renderer.qualityInspection.Score' })}
                required
                onChange={(value, oldValue) => updateScore(value, oldValue, i)}
              />
            );
        }
      })}
    </>;
  }, [scoreDataSet?.current?.get('solutionId'), options?.length]);

  const handleSubmit = async () => {
    const curData = scoreDataSet.current.toData();
    const checkStandards = getScoreList();
    if (scoreDataSet.current?.get('comment') === undefined) {
      scoreDataSet.current?.set('comment', null);
    }
    await scoreDataSet.current.validate();
    if (checkStandards.find(v => v.value === undefined)) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.AllScoreRequired' }));
      return null;
    } else if (!scoreDataSet.current?.get('comment') || !scoreDataSet.current?.get('solutionId')) {
      return null;
    }
    const aiFlag = !checkStandards.find(i => !i.aiFlag);
    const data = {
      solutionId: curData.solutionId,
      comment: curData.comment,
      aiFlag,
      checkStandards,
      totalScore: curData.totalScore,
    };
    const res = await scoreQualityInspection({
      tenantId,
      ticketId,
      data,
    });
    if (res && !res.failed) {
      message.success(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.submitSuccess' }));
      setSolutionCacheData(data);
      formDataSet?.query();
    }
  };

  const handleChangeSolution = (value) => {
    if (value === solutionCacheData?.solutionId) {
      scoreDataSet.current?.set({
        checkStandards: solutionCacheData?.checkStandards,
        comment: solutionCacheData?.comment,
        totalScore: solutionCacheData?.totalScore,
      });
    } else {
      scoreDataSet.current?.set({ checkStandards: null, comment: null, totalScore: null });
      (options.find(i => i.get('id') === value)?.get('standardList') || []).forEach(i => {
        scoreDataSet.current?.set(i.standardCode, null);
      });
    }
  };

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <div className={style[`${prefixCls}-header`]}>
          <div className={style[`${prefixCls}-header-left`]}>{labelName}</div>
          {hasPermission && aiSuggestionsFlag && enableChatGptFlag && gptTenantFlag && <AiTag
            name={loading ? intl.formatMessage({ id: 'lcr.renderer.qualityInspection.ai.ing' }) : intl.formatMessage({ id: 'lcr.renderer.qualityInspection.ai' })}
            onClick={handleGetScore}
          />}
        </div>
        <Skeleton loading={loading} active>
          <Form
            className={style[`${prefixCls}-form`]}
            dataSet={scoreDataSet}
            header={intl.formatMessage({ id: 'lcr.renderer.qualityInspection.score' })}
            disabled={!hasPermission}
          >
            <Select name="solutionId" onChange={handleChangeSolution} />
            {scoreList}
          </Form>
          <Form
            className={style[`${prefixCls}-form`]}
            dataSet={scoreDataSet}
            header={intl.formatMessage({ id: 'lcr.renderer.qualityInspection.score' })}
            disabled
          >
            <TextField name="totalScore" />
          </Form>
          <Form
            className={style[`${prefixCls}-form`]}
            dataSet={scoreDataSet}
            header={intl.formatMessage({ id: 'lcr.renderer.qualityInspection.Comments' })}
            disabled={!hasPermission}
          >
            <TextArea
              name="comment"
              resize="vertical"
              autoSize={{ minRows: 8 }}
            />
          </Form>
          {hasPermission && <div className={style[`${prefixCls}-submit`]}>
            <Button
              color="primary"
              funcType="raised"
              onClick={handleSubmit}
            >{intl.formatMessage({ id: 'zknow.common.button.submit' })}</Button>
          </div>}
        </Skeleton>
      </div>
    );
  };

  return renderMain();
});

export default MainView;
