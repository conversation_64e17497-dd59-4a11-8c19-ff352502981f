import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { DataSet } from 'choerodon-ui/pro';
import { Icon, ClickText } from '@zknow/components';
import { queryTicketMergeData } from '@/service';
import Store from './stores';
import Styles from './MainView.module.less';

function MainView() {
  const {
    intl,
    tenantId,
    config,
    ticketId,
    onJumpNewPage,
    viewId,
    businessObjectCode,
  } = useContext(Store);
  const ticketReminderBanner = config?.widgetConfig?.ticketReminderBanner || [];
  const [mergeData, setMergeData] = useState({});
  const [hiddenData, setHiddenData] = useState([]);
  useEffect(() => {
    (async () => {
      if (ticketReminderBanner.includes('MERGE')) {
        const res = await queryTicketMergeData({ ticketId, tenantId, businessObjectCode });
        if (res && !res.failed) {
          setMergeData(res);
        }
      }
    })();
  }, []);

  const handleOpenTicket = () => {
    if (mergeData?.mergeTicketId && viewId) {
      const pageDataSet = new DataSet({
        data: [{
          id: mergeData?.mergeTicketId,
        }],
        paging: false,
      });
      onJumpNewPage({
        viewId,
        instanceId: mergeData?.mergeTicketId,
        record: pageDataSet?.current,
      });
    }
  };

  const handleHidden = (i) => {
    const arr = [...hiddenData, i];
    setHiddenData(arr);
  };

  // 提醒展示
  const rendererReminderList = () => {
    return ticketReminderBanner.map(i => {
      if (i === 'MERGE' && mergeData?.id && !hiddenData?.includes(i)) {
        return <div className={Styles.item}>
          <Icon type="info" fill="#FD7D23" theme="filled" />
          <div className={Styles.text}>
            {intl.formatMessage({ id: 'lcr.renderer.desc.ticket.reminder.merge', defaultMessage: '{name}于{date}合并了该单据，请前往合并后单据' }, { name: mergeData?.updatePersonName, date: mergeData?.updatedAt })}
            <div className={Styles.link}>
              <ClickText onClick={handleOpenTicket}>{mergeData?.mergeTicketNumber}</ClickText>
            </div>
            {intl.formatMessage({ id: 'lcr.renderer.desc.ticket.reminder.merge.process', defaultMessage: '查看进度' })}
          </div>
          <Icon className={Styles.close} type="close" fill="#65728B" theme="filled" onClick={() => handleHidden(i)} />
        </div>;
      } else {
        return '';
      }
    });
  };

  return (
    <div className={Styles.ticketReminder}>
      {rendererReminderList()}
    </div>
  );
}

export default observer(MainView);
