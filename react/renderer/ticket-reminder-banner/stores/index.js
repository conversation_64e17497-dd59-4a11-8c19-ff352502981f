import { inject } from 'mobx-react';
import React, { createContext } from 'react';
import { injectIntl } from 'react-intl';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config,
      viewDataSet,
    } = props;
    const formConfig = viewDataSet?.current?.toData() || {};
    const { businessObjectId, businessObjectCode, id: viewId } = formConfig;
    const { name, widgetConfig: {
      ticketReminderBanner,
    } } = config;

    const value = {
      ...props,
      intl,
      tenantId,
      ticketId,
      name,
      businessObjectId,
      ticketReminderBanner,
      businessObjectCode,
      viewId,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
