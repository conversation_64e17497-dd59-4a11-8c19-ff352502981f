/* eslint-disable react/no-danger */
import React from 'react';
import { observer } from 'mobx-react-lite';
import { ExternalComponent } from '@zknow/components';
import { ModalProvider, message } from 'choerodon-ui/pro';
import { associationTicketWithKnowledge } from '@/service';

type RelevanceKnowledgeProps = {
  intl: any,
  prefixCls: string,
  ticketId: any,
  tenantId: any,
  businessObjectCode: string,
  modal?: any,
  relevanceDataSet: any,
}

const RelevanceKnowledgeModal = observer((props: RelevanceKnowledgeProps) => {
  const {
    intl,
    ticketId,
    tenantId,
    businessObjectCode,
    modal,
    relevanceDataSet,
  } = props;

  async function handleAfterAddShortCut(data) {
    try {
      const knowledgeIdList = data?.map(i => i.id) || [];
      if (knowledgeIdList?.length === 0) return false;
      const res = await associationTicketWithKnowledge(tenantId, ticketId, businessObjectCode, knowledgeIdList);
      if (!res?.failed) {
        message.success(intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.association.success' }));
        await relevanceDataSet.query();
        modal?.close();
      }
    } catch {
      //
      return false;
    }
  }

  const renderMain = () => {
    return (
      <ModalProvider>
        <ExternalComponent
          system={{
            scope: 'knowledge',
            module: 'KnowledgeShortcutAdd',
          }}
          handleAfterAddShortCut={handleAfterAddShortCut}
          onlyNeedReturnSelected
          enableOpenDetail
          {...props}
        />
      </ModalProvider>
    );
  };

  return renderMain();
});

export default RelevanceKnowledgeModal;
