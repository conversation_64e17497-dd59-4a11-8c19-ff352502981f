/* eslint-disable react/no-danger */
import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import classnames from 'classnames';
import querystring from 'query-string';
// @ts-ignore
import { StatusTag, ExternalComponent, Icon } from '@zknow/components';
import { message, Spin } from 'choerodon-ui/pro';
// @ts-ignore
import { disassociateTicketWithKnowledge, getAssociateTicketWithKnowledge } from '@/service';
// @ts-ignore
import { getQueryString } from '@/utils';
import style from './index.module.less';

const KnowledgeIcon = (props) => {
  return (
    <ExternalComponent
      system={{
        scope: 'knowledge',
        module: 'knowledge-icon',
      }}
      {...props}
    />
  );
};

type CardItemProps = {
  intl: any,
  prefixCls: string,
  dataSet: any,
  relationType: string,
  ticketId: any,
  tenantId: any,
  businessObjectCode: string,
  relevantKnowledgeStore: any,
  history: any,
}

const CardItem = observer((props: CardItemProps) => {
  const {
    intl,
    prefixCls,
    dataSet,
    relationType,
    tenantId,
    businessObjectCode,
    ticketId,
    relevantKnowledgeStore,
  } = props;

  const [loading, setLoading] = useState(false); // Spin 加载动画

  if (!dataSet?.length) return null;

  async function handleDisassociateTicketWithKnowledge(item) {
    const knowledgeId = item?.get('ticketElementId');
    if (!knowledgeId) return;
    try {
      const res = await disassociateTicketWithKnowledge(tenantId, businessObjectCode, knowledgeId);
      if (!res?.failed) {
        message.success(intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.disassociate.success' }));
        await dataSet.query();
      }
    } catch {
      //
    }
  }

  function handleGotoKnowledgeDetail(item) {
    const elementId = item?.get('elementId');
    if (!elementId) return null;
    const solutionId = getQueryString('solutionId');
    const queryObj = {
      solutionId,
      tenantId,
      menu: 'knowledge',
      knowledgeId: elementId,
      sourceModule: 'TICKETS',
      sourceFunction: 'RELATED_KNOWLEDGE',
      sourceId: ticketId,
    };
    const url = `${window.location.origin}/#/itsm/portal/knowledge?${querystring.stringify(queryObj)}`;
    window.open(url);
  }

  async function handleLoadMore() {
    if (dataSet?.status !== 'ready') return;
    const page = dataSet?.getState('page') || 0;
    const nextPage = page + 1;
    const pageSize = dataSet?.pageSize || 30;
    const totalPages = (dataSet.totalPage || 999) - 1;
    if (page < totalPages) {
      setLoading(true);
      const res = await getAssociateTicketWithKnowledge(tenantId, businessObjectCode, ticketId, relationType, nextPage, pageSize);
      if (!res.failed) {
        const newData = (res.content || res)?.filter(
          (item) => !dataSet.find((r) => r.get('id') === item.id),
        );
        dataSet.appendData(newData);
        dataSet.setState('page', page + 1);
        dataSet.totalPages = res.totalPages;
      }
      setLoading(false);
    }
  }

  function renderLoadMore() {
    const page = dataSet?.getState('page') || 0;
    const totalPages = (dataSet.totalPage || 999) - 1;
    if (page >= totalPages) return null;
    return (
      <Spin spinning={loading}>
        <div className={style['load-more-wrapper']}>
          <div className={style['load-more-line']}>
            <div className={style['load-more-content']} onClick={() => handleLoadMore()}>
              {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.load.more' })}
            </div>
          </div>
        </div>
      </Spin>
    );
  }

  function renderTitle() {
    return (
      <div className={style['card-header']}>
        <div className={style['card-header-left']}>{relationType === 'GENERATE' ? intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.generate' }) : intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.relevance' })}</div>
        <div className={style['card-header-right']}>{intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.totalCount', defaultMessage: `共${dataSet?.totalCount}条` }, { totalCount: dataSet?.totalCount })}</div>
      </div>
    );
  }

  function renderItem(item, index) {
    const itemClass = classnames({
      [`${style['card-content-item']}`]: true,
      [`${style['card-content-item-last']}`]: index === dataSet?.length - 1,
    });
    const statusColor = (relevantKnowledgeStore.getKnowledgeStatus)?.find((i) => i?.code === item?.get('publishStatus'))?.color;
    const publishStatusName = (relevantKnowledgeStore.getKnowledgeStatus)?.find((i) => i?.code === item?.get('publishStatus'))?.value;
    return (
      <div className={itemClass}>
        <div className={style['item-top']}>
          <div className={style['item-top-left']} onClick={() => handleGotoKnowledgeDetail(item)}>
            <KnowledgeIcon record={item} size={14} style={{ marginRight: '4px' }} />
            <span className={style.title}>{item?.get('name')}</span>
          </div>
          <div className={style['item-top-right']}>
            <StatusTag color={statusColor || '#2979ff'}>{publishStatusName}</StatusTag>
          </div>
        </div>
        <div className={style['item-bottom']}>
          <div className={style['item-bottom-item']}>
            <div className={style['item-bottom-item-left']}>
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              <div className={style['item-label']}>{intl.formatMessage({ id: 'zknow.common.model.code' })}：</div>
              <div className={style['item-value']}>{item?.get('number') || '-'}</div>
            </div>
            <div className={style['item-bottom-item-right']}>
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              <div className={style['item-label']}>{intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.createby' })}：</div>
              <div className={style['item-value']}>{item?.get('createdByName') || '-'}</div>
            </div>
          </div>
          <div className={style['item-bottom-item']}>
            <div className={style['item-bottom-item-left']}>
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              <div className={style['item-label']}>{intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.spaceName' })}：</div>
              <div className={style['item-value']}>{item?.get('spaceName') || '-'}</div>
            </div>
            <div className={style['item-bottom-item-right']}>
              {/* eslint-disable-next-line no-chinese/no-chinese */}
              <div className={style['item-label']}>{intl.formatMessage({ id: 'zknow.common.model.creationDate' })}：</div>
              <div className={style['item-value']}>{item?.get('creationDate') || '-'}</div>
            </div>
          </div>
          {
             item?.get('cancelFlag')
             && (
               <div className={style.actions}>
                 <div className={style['actions-content']} onClick={() => handleDisassociateTicketWithKnowledge(item)}>
                   <Icon type="LinkBreak" style={{ marginRight: 4 }} />
                   {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.relation.delete' })}
                 </div>
               </div>
             )
          }
        </div>
      </div>
    );
  }

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}-cart-wrapper`]}>
        <div className={style[`${prefixCls}-card`]}>
          {renderTitle()}
          <div className={style['card-content']}>
            {dataSet?.map(renderItem)}
          </div>
        </div>
        {renderLoadMore()}
      </div>
    );
  };

  return renderMain();
});

export default CardItem;
