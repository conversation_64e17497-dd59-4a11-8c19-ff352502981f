@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.relevant-knowledge {
  &-cart-wrapper {
    margin-bottom: 16px;
    .load-more {

      &-wrapper {
        margin-top: 15px;
        margin-bottom: 27px;
      }

      &-line {
        height: 1px;
        background-color: @primary-color;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      &-content {
        font-size: 12px;
        font-weight: 400;
        color: @primary-color;
        line-height: 22px;
        padding: 1px 8px;
        background-color: #fff;
        cursor: pointer;
      }
    }

  }

  &-card {
    border-radius: 6px;
    border: 1px solid #E5E6EB;
    .card-header {
      background: #EDF2FF;
      border-radius: 5px 5px 0px 0px;
      padding: 5px 11px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      &-left {
        font-size: 14px;
        font-weight: 500;
        color: #12274D;
        line-height: 22px;
      }

      &-right {
        font-size: 12px;
        font-weight: 400;
        color: rgba(18,39,77,0.65);
        line-height: 22px;
      }
    }

    .card-content {
      padding: 12px;
      &-item {
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px solid rgba(203, 210, 220, 0.5);

        .actions {
          display: none;
        }
        &:hover {
          .actions {
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            position: relative;
            bottom: 11px;
            height: 0px;
            background: #FBFCFD;
            color: #F83552;
            font-weight: 400;
            font-size: 14px;

            &-content {
              display: flex;
              align-items: center;
              background: #fff;
              padding-left: 8px;
            }
          }
        }

        &-last {
          border-bottom: none;
          margin-bottom: 0px;
          padding-bottom: 0px;
        }

        .item-top {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;

          &-left {
            font-size: 14px;
            font-weight: 500;
            color: @primary-color;
            line-height: 22px;
            display: flex;
            align-items: center;

            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 8px;
            cursor: pointer;

            .title {
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

        }

        .item-bottom {
          &-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 2px;
            font-size: 12px;
            font-weight: 400;
            color: rgba(18,39,77,0.65);
            line-height: 17px;

            &-left, &-right {
              display: inline-flex;
            }

            &-left {
              margin-right: 8px;
            }

            .item-label {
              white-space: nowrap;
            }
            .item-value {
              font-size: 12px;
              font-weight: 400;
              color: rgba(18,39,77,0.85);
              line-height: 17px;
            }

          }
        }
      }

    }
  }
}
