@import "~choerodon-ui/lib/style/themes/default";
@import "~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables";

.relevant-knowledge {
  padding: 0.16rem;
  .flex-center {
    display: flex;
    align-items: center;
  }

  &-header {
    .header-top {
      justify-content: space-between;
      margin-bottom: 8px;

      .flex-center;

      &-title {
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        color: #12274d;
      }
      &-icon {
        .flex-center;
        .filter-icon {
          padding: 8px;
          line-height: 1;
          height: 32px;
          color: @primary-color;
          background: @minor-color;
          border-radius: 4px;
          cursor: pointer;
          &:hover {
            color: #fff;
            background-color: @primary-6;
          }
        }
      }
    }
    .header-bottom {
      margin-bottom: 9px;

      color: #12274d;

      .flex-center;

      &-item {
        margin-right: 24px;
      }
    }
  }

  &-content-empty {
    &-buttons {
      display: flex;
      justify-content: center;
    }
  }
}
