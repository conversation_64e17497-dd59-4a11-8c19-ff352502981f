import React, { createContext, useMemo, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { withRouter } from 'react-router-dom';
import RelevantKnowledgeStore from './RelevantKnowledgeStore';
import { ListDataSet } from './DataSet';

type ServiceAgreementProps = {
  intl: any,
  history: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  path?: string,
  match?: any,
  formConfig?: any,
  children?: any,
  AppState: any,
  formDataSet: any,
  viewDataSet: any,
  generateDataSet: any,
  relevanceDataSet: any,
  ticketId: string,
  viewRecord: any,
  businessObjectCode: string,
  relevantKnowledgeStore: any,
};

const Store = createContext<ServiceAgreementProps>({
  intl: undefined,
  history: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  viewDataSet: '',
  generateDataSet: '',
  relevanceDataSet: '',
  ticketId: '',
  viewRecord: '',
  businessObjectCode: '',
  relevantKnowledgeStore: '',
});

export default Store;

export const StoreProvider = withRouter(injectIntl(inject('AppState')(
  observer((props: ServiceAgreementProps) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formConfig,
      formDataSet,
      viewDataSet,
      viewRecord,
      AppState,
    } = props;
    const prefixCls = 'relevant-knowledge';
    const ticketId = formDataSet && formDataSet.current && formDataSet.current.get('id');
    const { businessObjectCode } = (viewDataSet && viewDataSet.current && viewDataSet.current.toData()) || {};

    // 工具包
    const relevantKnowledgeStore = RelevantKnowledgeStore(AppState);

    useEffect(() => {
      relevantKnowledgeStore.getLookup();
    }, []);

    const generateDataSet = useMemo(() => new DataSet(ListDataSet({
      tenantId,
      autoLocateFirst: false,
      ticketId,
      businessObjectCode,
      relationType: 'GENERATE',
    })), [businessObjectCode, ticketId]);

    const relevanceDataSet = useMemo(() => new DataSet(ListDataSet({
      tenantId,
      autoLocateFirst: false,
      ticketId,
      businessObjectCode,
      relationType: 'RELATED',
    })), [businessObjectCode, ticketId]);

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      generateDataSet,
      ticketId,
      businessObjectCode,
      relevanceDataSet,
      relevantKnowledgeStore,
      viewRecord,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
)));
