/*
 * @Author: xia<PERSON><PERSON> <<EMAIL>>
 * @Date: 2022-12-08 15:23:06
 * @Description: 工具包
 */
import { useLocalStore } from 'mobx-react-lite';
// @ts-ignore
import { getLookUpValueByCode } from '@/service';

export default function useStore(props: any) {
  const {
    currentMenuType: { organizationId: tenantId },
  } = props;

  return useLocalStore(() => ({

    // 全部选中的服务目录的id
    knowledgeStatus: [],

    setKnowledgeStatus(data) {
      this.knowledgeStatus = data;
    },

    get getKnowledgeStatus() {
      return this.knowledgeStatus;
    },

    async getLookup() {
      try {
        const res = await getLookUpValueByCode(tenantId, { lookupTypeCode: 'KNOWLEDGE_PUBLISH_STATE' });
        if (!res?.failed) {
          this.setKnowledgeStatus(res);
        }
      } catch {
        //
        this.setKnowledgeStatus([]);
      }
    },
    
  }));
}
