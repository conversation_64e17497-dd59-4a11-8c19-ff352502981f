/*
 * @Author: xia<PERSON>ya <<EMAIL>>
 * @Date: 2022-10-19 11:02:29
 * @Description:
 */
// @ts-nocheck
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/interface';

const ListDataSet = ({
  tenantId,
  autoLocateFirst,
  ticketId,
  businessObjectCode,
  relationType,
}): DataSetProps => {
  return {
    autoQuery: true,
    autoLocateFirst,
    paging: true,
    primaryKey: 'id',
    selection: false,
    pageSize: 3,
    dataKey: 'content',
    totalKey: 'totalElements',

    transport: {
      read: () => {
        if (!ticketId || !businessObjectCode) return null;
        return {
          url: `/itsm/v1/${tenantId}/ticket/element/${ticketId}?businessObjectCode=${businessObjectCode}&relationType=${relationType}`,
          method: 'GET',
        };
      },
    },
    fields: [

    ],
    events: {

    },
  };
};

export {
  ListDataSet,
};
