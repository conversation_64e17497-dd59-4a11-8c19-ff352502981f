/* eslint-disable react/no-danger */
import React, {
  useState,
  useContext,
  useRef,
} from 'react';
// @ts-ignore
import { Icon, Empty, Button } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { TextField, Modal, Tooltip, Dropdown, Menu } from 'choerodon-ui/pro';
import GenerateKnowledge from '../transform-knowledge';
import RelevanceKnowledgeModal from './components/relevance-knowledge';
import CardItem from './components/card-item';
import Store from './stores';
import style from './index.module.less';

const modalKey = Modal.key();

const MainView = observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    generateDataSet,
    relevanceDataSet,
    ticketId,
    viewRecord,
    tenantId,
    businessObjectCode,
    relevantKnowledgeStore,
    history,
    formDataSet,
    viewDataSet,
  } = context;

  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState();
  const generateKnowledgeRef = useRef({});

  function handleQuery(value) {
    generateDataSet.setQueryParameter('param', value);
    generateDataSet.query();
    relevanceDataSet.setQueryParameter('param', value);
    relevanceDataSet.query();
    relevanceDataSet.setState('page', 0);
    generateDataSet.setState('page', 0);
  }

  function handleRelevanceKnowledge() {
    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.relevance' }),
      style: { width: '1000px' },
      destroyOnClose: true,
      children: (
        <RelevanceKnowledgeModal
          relevanceSearch
          intl={intl}
          prefixCls={prefixCls}
          ticketId={ticketId}
          tenantId={tenantId}
          businessObjectCode={businessObjectCode}
          relevanceDataSet={relevanceDataSet}
        />
      ),
      onOk: () => {

      },
      onCancel: () => {

      },
    });
  }

  function handleGenerateKnowledge() {
    // @ts-ignore
    generateKnowledgeRef?.current?.onClick();
  }

  function onCreateSuccessCallback() {
    generateDataSet.query();
  }

  const typeContent = () => (
    <Menu className="yq-dynamic-main-dropdown-menu" selectable={false}>
      <Menu.Item>
        <div className="temp" onClick={() => { handleGenerateKnowledge(); }}>
          {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.generate' })}
        </div>
      </Menu.Item>
      <Menu.Item>
        <div className="temp" onClick={() => { handleRelevanceKnowledge(); }}>
          {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.relevance' })}
        </div>
      </Menu.Item>
    </Menu>
  );

  const renderEmpty = (
    <div className={style[`${prefixCls}-content-empty`]}>
      <Empty
        description={intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.empty' })}
        style={{ padding: '20px 0 12px 0', paddingTop: '28px' }}
      />
      <div className={style[`${prefixCls}-content-empty-buttons`]}>
        <Button
          key="generate"
          funcType="raised"
          color="primary"
          onClick={() => handleGenerateKnowledge()}
        >
          {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.generate' })}
        </Button>
        {/* {renderGenKnowledge('')} */}
        <Button
          key="relevance"
          funcType="raised"
          color="secondary"
          onClick={() => handleRelevanceKnowledge()}
        >
          {intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.relevance' })}
        </Button>
      </div>
    </div>
  );

  // 渲染头部
  const renderHeader = () => {
    if (searching) {
      return (
        // @ts-ignore
        <TextField
          style={{ width: '100%', marginBottom: '7px' }}
          value={searchValue}
          prefix={<Icon type="icon-search" />}
          autoFocus
          clearButton
          onChange={(e) => {
            if (!e) {
              setSearching(false);
              handleQuery(e);
            }
            setSearchValue(e);
          }}
          onBlur={(e) => {
            if (!e.target.value) {
              setSearching(false);
              handleQuery(e.target.value);
            }
          }}
          onEnterDown={(e) => {
            // @ts-ignore
            const value = e.target.value;
            handleQuery(value);
          }}
        />
      );
    }
    return (
      <div className={style['header-top']}>
        <span className={style['header-top-title']}>
          {viewRecord?.get('name') || intl.formatMessage({ id: 'lcr.renderer.relevantKnowledge.title' })}
        </span>
        <div className={style['header-top-icon']}>
          <Dropdown overlay={typeContent()}>
            <Icon className={style['filter-icon']} type="add-one" size={16} />
          </Dropdown>
          <Tooltip title={intl.formatMessage({ id: 'zknow.common.placeholder.search' })}>
            <Icon
              className={style['filter-icon']}
              style={{ marginLeft: '8px' }}
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>
      </div>
    );
  };

  function renderGenKnowledge(feature) {
    return (
      <div style={{ display: 'none' }}>
        <GenerateKnowledge
          instanceId={ticketId}
          ticketId={ticketId}
          viewDataSet={viewDataSet}
          formDataSet={formDataSet}
          viewRecord={viewRecord}
          generateKnowledgeRef={generateKnowledgeRef}
          feature={feature}
          onCreateSuccessCallback={() => onCreateSuccessCallback()}
        />
      </div>
    );
  }

  function renderItem(ds, relationType) {
    return (
      <CardItem
        intl={intl}
        prefixCls={prefixCls}
        dataSet={ds}
        relationType={relationType}
        ticketId={ticketId}
        tenantId={tenantId}
        businessObjectCode={businessObjectCode}
        relevantKnowledgeStore={relevantKnowledgeStore}
        history={history}
      />
    );
  }

  function renderContent() {
    if (generateDataSet.length > 0 || relevanceDataSet.length > 0) {
      return (
        <>
          {renderItem(generateDataSet, 'GENERATE')}
          {renderItem(relevanceDataSet, 'RELATED')}
        </>
      );
    }
    return renderEmpty;
  }

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <div className={style[`${prefixCls}-header`]}>
          {renderHeader()}
        </div>
        <div className={style[`${prefixCls}-content`]}>
          {renderContent()}
        </div>
        {renderGenKnowledge('')}
      </div>
    );
  };

  return renderMain();
});

export default MainView;
