import React, { useContext, useState, useRef, useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Tooltip, DataSet } from 'choerodon-ui/pro';
import { Icon, StatusTag, Empty, Button, ClickText } from '@zknow/components';
import _ from 'lodash';
import Header from '../ticket-relation-header';
import handleCommonRequest from '@/service';
import YanCopilot from '@/assets/images/duplicateProblem/yan-copilot-2.svg';
import Store from './stores';

import './index.less';

export default observer(() => {
  const {
    intl,
    prefixCls,
    listDataSet,
    ticketId,
    businessObjectId,
    tenantId,
    formDataSet,
    onJumpNewPage,
    relatedTicketLovId,
    config,
  } = useContext(Store);

  const [expand, setExpand] = useState(true);
  const addBtnRef = useRef();

  useEffect(() => {
    if (formDataSet?.status === 'ready' && ticketId) {
      listDataSet.query();
    }
  }, [formDataSet?.status]);

  function handleCancelLink(_data) {
    Modal.confirm({
      title: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.action.tip' }),
    }).then(async btn => {
      if (btn === 'ok') {
        await handleCommonRequest('handleDeleteTicketLink', {
          params: {
            tenantId,
            linkId: _data._link_id,
          },
          dataSet: listDataSet,
          successTip: intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete.success' }),
        });
      }
    }).catch(e => {
      throw Error(e);
    });
  }

  function handleOpenDetail(_record) {
    const viewId = _record._view_id;
    const id = _record?.id;
    if (viewId && id) {
      const _ds = new DataSet();
      _ds.create({ ..._.pick(_record, ['id', 'number', 'short_description']) });
      onJumpNewPage({ record: _ds.current, viewId });
    }
  }

  function renderNumber(record) {
    if (!record?._view_id) {
      return <span style={{ color: 'rgba(18,39,77,0.8500)' }}>{record.number}</span>;
    }
    return (
      <ClickText onClick={() => handleOpenDetail(record)}>
        {record.number}
      </ClickText>
    );
  }

  const renderCustomerColumns = (col, record) => {
    const { code, path, name } = col;
    const label = (col.parentName ? `(${col.parentName})${name}` : name) || code;

    return (
      <div className={`${prefixCls}-list-customer-item`}>
        <span className={`${prefixCls}-list-customer-item-label`}>{label}</span>
        <span className={`${prefixCls}-list-customer-item-value`}>{record?.[path]}</span>
      </div>
    );
  };

  const ListItem = ({ isLast, record }) => {
    return (
      <div className={`${prefixCls}-list-item`}>
        <header className={`${prefixCls}-list-item-header`}>
          <div className={`${prefixCls}-list-item-header-left`}>
            {record?.repeatAiFlag && (
              <Tooltip
                title={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.DuplicateProblemAnalysis.result' })}>
                <img src={YanCopilot} alt="" className={`${prefixCls}-list-item-header-img`} />
              </Tooltip>
            )}
            <Tooltip title={record.short_description}>
              <div className={`${prefixCls}-list-item-header-desc`}>{record.short_description}</div>
            </Tooltip>
          </div>
          <div>
            <StatusTag color={record['state_id:color']}>{record['state_id:name']}</StatusTag>
          </div>
        </header>
        <footer className={`${prefixCls}-list-item-footer`}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Icon type="icon-document" theme="filled" size={16} fill="#2979FF" style={{ marginRight: 4 }} />
            {renderNumber(record)}
          </div>
          <div className={`${prefixCls}-list-item-footer-person`}>
            {record['submitted_by:real_name']
              && <div>
                {/* eslint-disable-next-line no-chinese/no-chinese */}
                <span>{intl.formatMessage({ id: 'lcr.renderer.ticketRelation.submittedBy' })}：</span>
                {record['submitted_by:real_name']}
              </div>}
            {record['assignee_person_id:real_name']
              && <div style={{ marginLeft: 16 }}>
                {/* eslint-disable-next-line no-chinese/no-chinese */}
                <span>{intl.formatMessage({ id: 'lcr.renderer.ticketRelation.processedBy' })}：</span>
                {record['assignee_person_id:real_name']}
              </div>}
          </div>
        </footer>
        {(config?.widgetConfig?.ticketRelationCardColumns || []).map((col) => renderCustomerColumns(col, record))}
        <div className={`${prefixCls}-list-item-btn`} onClick={() => handleCancelLink(record)}>
          <Icon type="LinkBreak" style={{ marginRight: 4 }} />
          {intl.formatMessage({ id: 'lcr.renderer.ticketRelation.delete' })}
        </div>
        {!isLast && <div className={`${prefixCls}-list-item-footer-divider`} />}
      </div>
    );
  };

  const Card = ({ record }) => {
    const title = record.get('linkName');
    const ticketList = record.get('ticketList');
    if (!ticketList.length) return null;
    return (
      <div className={`${prefixCls}-wrapper`}>
        <header className={`${prefixCls}-header`}>{title}</header>
        <content>
          {ticketList.map((item, index) => <ListItem record={item} isLast={index === ticketList?.length - 1} />)}
        </content>
      </div>
    );
  };

  function handleOpenAdd() {
    addBtnRef?.current?.click?.();
  }

  return (
    <div>
      <Header
        dataSet={listDataSet}
        ticketId={ticketId}
        businessObjectId={businessObjectId}
        onExpand={setExpand}
        addBtnRef={addBtnRef}
        title={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.ticket.relation' })}
        mode="card"
        lovId={relatedTicketLovId}
        defaultRelatedTicketType={config?.widgetConfig?.relatedTicketLovType || null}
      />
      {expand && (
        <>
          {expand && listDataSet.map(record => <Card record={record} />)}
          {
            listDataSet?.length
              ? null
              : (
                <div style={{ textAlign: 'center' }}>
                  <Empty
                    description={intl.formatMessage({ id: 'lcr.renderer.ticketRelation.empty' })}
                    style={{ padding: '0px', paddingTop: '24px' }}
                  />
                  {ticketId && <Button color="primary" onClick={handleOpenAdd} style={{ marginTop: 12 }}>{intl.formatMessage({ id: 'lcr.renderer.ticketRelation.create' })}</Button>}
                </div>
              )
          }
        </>
      )}
    </div>
  );
});
