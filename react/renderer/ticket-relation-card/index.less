@import '~choerodon-ui/lib/style/themes/default';

.lc-components-ticket-relation-card-new {
  &-wrapper {
    margin: 18px;
    margin-top: 0;
    border: 1px solid #E5E6EB;
    border-radius: 6px;
  }

  &-header {
    background: #EDF2FF;
    border-radius: 5px 5px 0 0;
    height: 33px;
    padding-left: 14px;
    line-height: 33px;
    margin-bottom: .12rem;
  }

  &-list-item {
    position: relative;
    margin: 12px;

    &-btn {
      display: none;
    }

    &:hover {
      .lc-components-ticket-relation-card-new-list-item-btn {
        cursor: pointer;
        display: flex;
        align-items: center;
        position: absolute;
        right: 0;
        bottom: 0;
        height: 24px;
        background: #FBFCFD;
        color: #F83552;
        font-weight: 400;
        font-size: 14px;
      }
    }

    &-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      &-desc {
        height: 22px;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 12px;
      }

      &-img {
        width: 22px;
        height: 22px;
        margin-right: 5px;
      }

      &-left {
        display: flex;
        align-items: center;
        max-width: 70%;
      }
    }

    &-footer {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;

      &-person {
        display: flex;

        & > div {
          font-size: 12px;
          font-weight: 400;
          color: rgba(18, 39, 77, 0.8500);
          line-height: 17px;

          & > span {
            font-weight: 400;
            color: rgba(18, 39, 77, 0.6500);
            line-height: 17px;
            font-size: 12px;
          }
        }
      }

      &-divider {
        width: 100%;
        height: 1px;
        background-color: rgba(203, 210, 220, 0.5000);
      }
    }
  }

  &-list-customer-item {
    display: grid;
    grid-template-columns: 120px 1fr;
    grid-gap: 8px;

    font-size: 13px;

    &-label {
      color: rgba(18, 39, 77, 0.65);
    }
  }
}
