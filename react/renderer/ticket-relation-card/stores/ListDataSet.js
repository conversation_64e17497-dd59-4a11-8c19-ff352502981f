export default ({
  tenantId,
  ticketId,
  customerFields,
}) => {
  const url = `lc/v1/${tenantId}/svs_ticket_links/${ticketId}/query`;
  return {
    autoQuery: !!ticketId,
    paging: false,
    transport: {
      read: ({ dataSet }) => {
        const param = dataSet.queryParameter?.param;
        return {
          url: `${url}${param ? `?param=${param}` : ''}`,
          method: 'post',
          data: ['submitted_by:real_name', 'assignee_person_id:real_name', 'state_id:name', 'short_description', 'number', ...customerFields],
          transformResponse(response) {
            try {
              const res = JSON.parse(response);
              if (res?.failed) {
                return response;
              } else {
                return res?.content;
              }
            } catch (e) {
              throw Error(e);
            }
          },
        };
      },
    },
  };
};
