import { inject } from 'mobx-react';
import React, { createContext, useMemo } from 'react';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import ListDataSet from './ListDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      ticketId,
      AppState: {
        currentMenuType: { organizationId: tenantId },
      },
      config,
      viewDataSet,
    } = props;
    const prefixCls = 'lc-components-ticket-relation-card-new';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { businessObjectId } = formConfig;
    const {
      name,
      widgetConfig: {
        ticketRelationListFields,
        relatedTicketLovId,
        ticketRelationCardColumns,
      },
    } = config;
    const customerFields = [];
    (ticketRelationCardColumns || []).forEach(col => {
      customerFields.push(col.path || col.code);
      // if (col.parent) {
      //   customerFields.push(col.parent);
      // }
    });

    const getFields = () => {
      try {
        return JSON.parse(ticketRelationListFields) || [];
      } catch (e) {
        return [];
      }
    };
    const fields = getFields();

    const listDataSet = useMemo(() => new DataSet(ListDataSet({ ticketId, tenantId, customerFields })), [ticketId, tenantId]);

    const value = {
      ...props,
      prefixCls,
      intl,
      tenantId,
      ticketId,
      listDataSet,
      name,
      fields,
      businessObjectId,
      relatedTicketLovId,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
