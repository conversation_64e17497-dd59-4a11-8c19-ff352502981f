import React, { useContext, useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import { SelectBox, Form, Lov, Output, Modal } from 'choerodon-ui/pro';
import OptionTable from '@/components/option-table';
import store, { StoreProvider } from './stores';
import Style from './Index.module.less';

const MainView = () => {
  const { intl, formDataSet, optionsetDataSet, customOptionDataSet, modal } = useContext(store);
  const [currentDataSource, setCurrentDataSource] = useState(optionsetDataSet?.current?.get('dataSource'));
  useEffect(() => {
    initDataSet();
  }, [formDataSet?.get(0)]);

  const initDataSet = () => {
    try {
      if (formDataSet?.get(0)?.get('widget_config')) {
        const formData = JSON.parse(formDataSet?.current?.get('widget_config'));
        customOptionDataSet.loadData(formData?.customOptions || []);
        optionsetDataSet?.current?.set(formData);
        setCurrentDataSource(formData?.dataSource);
      }
    } catch (error) {
      //
    }
  };

  function openModal() {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.render.model.option.set.setting', defaultMessage: '设置选项集' }),
      children: (
        <OptionTable
          fieldRecord={optionsetDataSet?.current}
          dataSet={customOptionDataSet}
          intl={intl}
        />
      ),
      destroyOnClose: true,
      style: { width: 800 },
    });
  }

  const handleSubmit = () => {
    const customOptionSet = customOptionDataSet?.toData() || [];
    optionsetDataSet?.current?.set('customOptions', customOptionSet);
    const dataJson = JSON.stringify(optionsetDataSet?.current?.toData() || {});
    formDataSet?.current?.set('widget_config', dataJson);
  };

  modal.handleOk(handleSubmit);

  const update = (value) => {
    setCurrentDataSource(value);
  };
  if (formDataSet?.get(0)?.get('widget_type') !== 'Select') {
    return null;
  }

  return <Form dataSet={optionsetDataSet}>
    <SelectBox name="dataSource" onChange={update} />
    {currentDataSource === 'lookup' && <Lov name="lookupCode" />}
    {currentDataSource === 'optionSet' && <Output
      onClick={openModal}
      name="optionset"
      style={{ paddingTop: 2 }}
      renderer={() => (
        <span className={Style.optionset}>
          {intl.formatMessage({ id: 'lcr.render.model.option.set', defaultMessage: '选项集' })}
        </span>
      )}
    />}
  </Form>;
};

export default inject('AppState')(observer((props) => (
  <StoreProvider {...props}>
    <MainView />
  </StoreProvider>
)));

/* externalize: OptionsetComponent */
