import { verifyCode } from '@/utils';

export default ({ intl, formData }) => {
  const value = intl.formatMessage({ id: 'lcr.render.optionset.model.code', defaultMessage: '值编码' });
  const meaning = intl.formatMessage({ id: 'lcr.render.optionset.model.meaning', defaultMessage: '值含义' });
  const color = intl.formatMessage({ id: 'zknow.common.model.color', defaultMessage: '颜色' });

  return {
    autoQuery: false,
    selection: false,
    paging: false,
    idField: 'code',
    fields: [
      { name: 'value', type: 'string', label: value, maxLength: 32, required: true, unique: true, validator: (v) => verifyCode(v, intl.formatMessage({ id: 'lc.common.model.code.error.warning', defaultMessage: '编码只能由大写字母,数字和下划线构成' })) },
      { name: 'meaning', type: 'string', label: meaning, required: true, maxLength: 32 },
      { name: 'color', type: 'string', label: color },
    ],
  };
};
