import { DataSet } from 'choerodon-ui/pro';

export default ({ tenantId, intl }) => {
  const dataSourceOptionDataSet = new DataSet({
    paging: false,
    data: [
      { meaning: intl.formatMessage({ id: 'lcr.render.optionset.model.custom.option.set', defaultMessage: '自定义选项集' }), value: 'optionSet' },
      { meaning: intl.formatMessage({ id: 'lcr.render.optionset.model.existing.option.set', defaultMessage: '使用已有选项集' }), value: 'lookup' },
    ],
  });
  return {
    autoQuery: false,
    selection: false,
    paging: false,
    autoCreate: true,
    transport: {
    },
    fields: [
      {
        name: 'lookupCode', // 选项集编码
        type: 'object',
        label: intl.formatMessage({ id: 'lcr.render.model.option.set', defaultMessage: '选项集' }),
        lovCode: 'LOOKUP_TYPE',
        textField: 'typeName',
        valueField: 'typeCode',
      },
      {
        name: 'dataSource', // 字段值集来源：选项集或自定义
        type: 'string',
        label: intl.formatMessage({ id: 'lcr.render.model.option.set.from', defaultMessage: '值集来源' }),
        options: dataSourceOptionDataSet,
        defaultValue: 'lookup',
      },
      {
        name: 'customOptions',
        label: intl.formatMessage({ id: 'lcr.render.model.option.set', defaultMessage: '选项集' }),
        type: 'object',
      },
      {
        name: 'optionset',
        label: intl.formatMessage({ id: 'lcr.render.model.option.set', defaultMessage: '选项集' }),
      },
    ],
  };
};
