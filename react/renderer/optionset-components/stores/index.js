import React, { createContext, useMemo, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';
import OptionsetDataSet from './OptionsetDataSet';
import CustomOptionDataSet from './CustomOptionDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = inject('AppState')((formatterCollections({ code: ['lcr.render', 'zknow.common'] }))(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      formDataSet,
    } = props;
    const formData = formDataSet?.current?.get('widget_config') || '';
    useEffect(() => {
      // console.log(99, formDataSet?.get(0)?.get('widget_type'));
    }, [formDataSet?.get(0)?.get('widget_type')]);

    const optionsetDataSet = useMemo(() => new DataSet(OptionsetDataSet({
      tenantId,
      intl,
      formDataSet,
      formData,
    })), [formData]);

    const customOptionDataSet = useMemo(() => new DataSet(CustomOptionDataSet({
      tenantId,
      intl,
      formDataSet,
      formData,
    })), [formData]);

    const value = {
      ...props,
      formDataSet,
      tenantId,
      optionsetDataSet,
      customOptionDataSet,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
