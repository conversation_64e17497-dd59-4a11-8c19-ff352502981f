import React, { useContext, useEffect, useRef, useState } from 'react';
import { observer, useLocalStore } from 'mobx-react-lite';
import { MobXProviderContext } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { useSize } from 'ahooks';
import classnames from 'classnames';
import { message, Spin, Tooltip } from 'choerodon-ui/pro';
import { Icon, LoadingGPT, Button } from '@zknow/components';
import RichTextPreview from '@/renderer/rich-text-preview';
import Wysiwyg from '@/components/wysiwyg';
import { querySummaryAsync, querySummaryResult, querySummaryInfo } from '@/service';
import styles from './TicketSummary.module.less';

const MAX_HEIGHT = 260;

function TicketSummary({ intl, ticketId, formDataSet, viewDataSet }) {
  const {
    AppState: { currentMenuType: { organizationId: tenantId } },
    HeaderStore: { getTenantConfig: { enableChatGptFlag, gptTenantFlag } },
  } = useContext(MobXProviderContext);
  const [edit, setEdit] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [expanded, setExpanded] = useState(false);

  const contentRef = useRef(null);
  const contentSize = useSize(contentRef);
  const countRef = useRef(30);
  const timer = useRef(null); // 总结的更新人获取是有延时的，记录定时器便于清除

  useEffect(() => () => {
    clearTimeout(timer.current);
  }, []);

  const summaryStore = useLocalStore(() => ({
    summary: '',
    loading: false,
    updateInfo: {},
    get getSummary() {
      return summaryStore.summary;
    },
    setSummary(data) {
      summaryStore.summary = data;
    },
    get getLoading() {
      return summaryStore.loading;
    },
    setLoading(data) {
      summaryStore.loading = data;
    },
    get getUpdateInfo() {
      return summaryStore.updateInfo;
    },
    setUpdateInfo(data) {
      summaryStore.updateInfo = data;
    },
  }));

  React.useEffect(() => {
    return () => {
      countRef.current = -1;
    };
  }, []);

  const queryUpdateInfo = async () => {
    try {
      const businessObjectCode = viewDataSet?.get(0)?.get?.('businessObjectCode');
      if (businessObjectCode && ticketId) {
        const res = await querySummaryInfo({ tenantId, businessObjectCode, ticketId });
        summaryStore.setUpdateInfo(res);
      }
    } catch (e) {
      //
    }
  };

  useEffect(() => {
    queryUpdateInfo();
  }, [ticketId, tenantId]);

  const getSummaryResult = async (uuid) => {
    if (countRef.current === -1) {
      summaryStore.setLoading(false);
      return false;
    }
    const res = await querySummaryResult(tenantId, uuid);
    if (countRef.current === -1) {
      summaryStore.setLoading(false);
      return false;
    }
    if (res?.failed) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.summary.error', defaultMessage: '生成错误，请稍后重试' }));
      summaryStore.setLoading(false);
    } else if (res?.changedParams) {
      const summary = res.changedParams?.summary;
      if (typeof summary === 'string') {
        summaryStore.setSummary(summary);
        queryUpdateInfo();
      } else {
        message.error(intl.formatMessage({ id: 'lcr.renderer.desc.summary.error', defaultMessage: '生成错误，请稍后重试' }));
      }
      summaryStore.setLoading(false);
    } else if (countRef.current > 0) {
      countRef.current -= 1;
      setTimeout(() => {
        getSummaryResult(uuid);
      }, 3000);
    } else {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.summary.error', defaultMessage: '生成错误，请稍后重试' }));
      summaryStore.setLoading(false);
      setEdit(false);
    }
  };

  const handleClick = async () => {
    const businessObjectCode = viewDataSet?.get(0)?.get?.('businessObjectCode');
    if (!(businessObjectCode && ticketId)) {
      return false;
    }
    try {
      setEdit(true);
      summaryStore.setLoading(true);
      const summaryRes = await querySummaryAsync({ tenantId, ticketId, businessObjectCode });
      if (typeof summaryRes === 'string') {
        getSummaryResult(summaryRes);
      } else {
        message.error(intl.formatMessage({ id: 'lcr.renderer.desc.summary.error', defaultMessage: '生成错误，请稍后重试' }));
        summaryStore.setLoading(false);
        setEdit(false);
      }
    } catch (e) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.summary.error', defaultMessage: '生成错误，请稍后重试' }));
      summaryStore.setLoading(false);
      setEdit(false);
    }
  };

  const handleChange = (e, editor) => {
    const htmlData = editor.getData();
    summaryStore.setSummary(htmlData || '');
  };

  const submit = async () => {
    try {
      setSubmitting(true);
      formDataSet.current.set('summary', summaryStore.getSummary);
      await formDataSet.submit();
      formDataSet.query();
      setEdit(false);
      setSubmitting(false);
      timer.current = setTimeout(() => {
        // 动态记录有延时
        queryUpdateInfo();
      }, 2000);
    } catch (e) {
      setSubmitting(false);
    }
  };

  const cancel = () => {
    const prevSummary = formDataSet?.current?.get('summary') || '';
    summaryStore.setSummary(prevSummary);
    setEdit(false);
  };

  const renderTrigger = () => {
    const summary = summaryStore.getSummary || formDataSet?.current?.get('summary');
    const noContent = !summaryStore.getLoading && !summary;
    return noContent && (
      <div className={styles.trigger}>
        <h3 className={styles.head_3_inline}>{intl.formatMessage({ id: 'lcr.renderer.desc.summary', defaultMessage: '工单摘要' })}</h3>
        <div
          className={styles.button}
          onClick={handleClick}
        >
          <span className={styles.buttonText}>{intl.formatMessage({ id: 'lcr.renderer.desc.summary.placeholder', defaultMessage: '点击可智能生成摘要，包括问题现象、成因和解决方法等信息' })}</span>
        </div>
      </div>
    );
  };

  const renderToggle = () => {
    if (contentSize?.height > MAX_HEIGHT) {
      return (
        <div
          className={styles.toggle}
          onClick={() => {
            setExpanded(prevState => !prevState);
          }}
        >
          {intl.formatMessage({ id: expanded ? 'foldMore' : 'expandMore' })}
          <span className={classnames(styles.toggleIcon, { [styles.iconHidden]: expanded })}><Icon type="DoubleDown" /></span>
          <span className={classnames(styles.toggleIcon, { [styles.iconHidden]: !expanded })}><Icon type="DoubleUp" /></span>
        </div>
      );
    }
  };

  const renderInfo = () => {
    const updateInfo = summaryStore.getUpdateInfo;
    const user = updateInfo?.lastUpdatedName;
    const date = updateInfo?.lastUpdateDate;

    return (user || date) && (
      <div className={styles.bottom}>
        <span>
          <span>{user}</span>
          <span>{intl.formatMessage({ id: 'lcr.renderer.desc.updated.at', defaultMessage: '更新于' })} {date}</span>
        </span>
      </div>
    );
  };

  const renderContent = () => {
    const summary = summaryStore.getSummary || formDataSet?.current?.get('summary');
    const isEditing = edit && (summary || summaryStore.getLoading);
    const isPreview = !edit && summary;

    if (isEditing) {
      return (
        <>
          <Wysiwyg
            maxHeight={240}
            data={summaryStore.getLoading ? '' : summary}
            onChange={handleChange}
            uploadFlag={false}
            disabled={summaryStore.getLoading}
          />
          {summaryStore.getLoading && (
            <div className={styles.loading}>
              <LoadingGPT message={intl.formatMessage({ id: 'lcr.renderer.desc.summary.progress', defaultMessage: '工单总结生成中...' })} />
            </div>
          )}
        </>
      );
    } else if (isPreview) {
      const extraClass = !edit ? {
        [styles.contentMore]: expanded,
        [styles.hasPadding]: contentSize?.height > MAX_HEIGHT && expanded,
      } : {};

      const regex = /tenantid="(\d+)"/;
      const tenantIdInContent = (summaryStore.getSummary || '').match(regex)?.[1];

      return (
        <div
          ref={contentRef}
          className={classnames(styles.content, extraClass)}
        >
          <h3 className={styles.head_3}>{intl.formatMessage({ id: 'lcr.renderer.desc.summary', defaultMessage: '工单摘要' })}</h3>
          <RichTextPreview
            data={summary}
            preview
            minHeight={1}
            ticketId={ticketId}
            tenantId={tenantIdInContent || tenantId}
          />
          <div className={styles.line} />
          {renderInfo()}
          {renderToggle()}
        </div>
      );
    }

    return null;
  };

  const renderButtons = () => {
    const summary = summaryStore.getSummary || formDataSet?.current?.get('summary');
    const editing = summary && edit && !summaryStore.getLoading;
    return editing && (
      <div className={styles.footer}>
        <Button
          loading={submitting}
          funcType="raised"
          color="primary"
          onClick={submit}
        >{intl.formatMessage({ id: 'zknow.common.button.confirm', defaultMessage: '确认' })}</Button>
        <Button
          disabled={submitting}
          funcType="raised"
          onClick={cancel}
        >{intl.formatMessage({ id: 'zknow.common.button.cancel', defaultMessage: '取消' })}</Button>
      </div>
    );
  };

  const handleEdit = () => {
    setEdit(true);
  };

  const renderEditor = () => {
    const summary = summaryStore.getSummary || formDataSet?.current?.get('summary');
    const isPreview = !edit && summary;
    return isPreview && (
      <>
        <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.desc.regenerate', defaultMessage: '重新生成' })}>
          <div className={styles.regenerate}>
            <Icon
              type="Redo"
              size={16}
              onClick={handleClick}
            />
          </div>
        </Tooltip>
        <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.edit', defaultMessage: '编辑' })}>
          <div className={styles.edit}>
            <Icon
              type="write"
              size={16}
              onClick={handleEdit}
            />
          </div>
        </Tooltip>
      </>
    );
  };

  return enableChatGptFlag && gptTenantFlag ? (
    <div className={styles.wrap}>
      <div className={styles.avatar}>
        <Icon type="icon-yan-copilot-icon" size={28} />
      </div>
      <div className={styles.main}>
        {renderEditor()}
        {renderTrigger()}
        {renderContent()}
        {renderButtons()}
        {submitting && (
          <div className={styles.spinning}>
            <Spin />
          </div>
        )}
      </div>
    </div>
  ) : null;
}

export default injectIntl(observer(TicketSummary));
