@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.wrap {
  display: flex;
  margin: 0 16px 16px;
  padding: 12px 12px 0;
  width: calc(100% - 32px);
  background: #f7f9fc;
  border-radius: 4px;

  .avatar {
    display: flex;
    flex: 0 0 28px;
    margin: 2px 12px 2px 0;
    width: 28px;
  }

  .main {
    position: relative;
    flex: 1 1 calc(100% - 40px);
    width: calc(100% - 40px);
    padding-bottom: 12px;

    .edit {
      position: absolute;
      top: 0;
      right: 0;
      width: 22px;
      height: 22px;
      display: none;
      cursor: pointer;
      border-radius: 4px;
      align-items: center;
      justify-content: center;

      &:hover {
        color: @primary-color;
        background-color: @yq-primary-color-10;
      }
    }

    .regenerate {
      position: absolute;
      top: 0;
      right: 26px;
      width: 22px;
      height: 22px;
      display: none;
      cursor: pointer;
      border-radius: 4px;
      align-items: center;
      justify-content: center;

      &:hover {
        color: @primary-color;
        background-color: @yq-primary-color-10;
      }
    }

    .trigger {
      display: flex;
      flex-wrap: nowrap;
      width: 100%;
      align-items: center;
    }

    .head_3_inline {
      display: inline-flex;
      align-items: center;
      margin: 0 12px 0 0;
      font-weight: 500;
      font-size: 16px;
      white-space: nowrap;
      color: rgba(18, 39, 77, 0.85);
    }

    .head_3 {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      height: 30px;
      font-weight: 500;
      font-size: 16px;
      color: rgba(18, 39, 77, 0.85);
    }

    .line {
      height: 1px;
      margin: 12px 0 8px 0;
      background: rgba(203, 210, 220, 0.5);
    }

    .button {
      display: flex;
      align-items: center;
      padding: 0 8px;
      width: 100%;
      height: 32px;
      background: #fff;
      border: 1px solid #d7e2ec;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      color: rgba(18, 39, 77, 0.45);
      cursor: pointer;

      .buttonText {
        display: inline-block;
        max-width: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }

      &:hover {
        color: rgba(18, 39, 77, 0.65);
        border-color: @primary-color;
      }
    }

    .loading {
      position: absolute;
      top: 52px;
      left: 12px;
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;
    }

    .spinning {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;

      :global {
        .c7n-spin-spinning {
          display: inline-block !important;
        }
      }
    }

    .bottom {
      display: flex;
      align-items: center;
      width: 100%;
      height: 30px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.45);
      line-height: 20px;

      .splitDot {
        margin-right: 6px;
      }
    }

    &:hover {
      .edit,
      .regenerate {
        display: flex;
      }
    }
  }
}

.content {
  font-size: 14px;
  font-weight: 400;
  color: #2b2d38;
  line-height: 22px;
  word-break: break-all;
  max-height: 300px;
  overflow: hidden;

  img {
    width: 100%;
    max-width: 500px;
    max-height: 450px;
    display: block;
  }

  table,
  tr,
  td,
  th {
    border: 1px #bfbfbf solid;
    border-collapse: collapse;
  }

  table td {
    min-width: 2em;
    padding: 0.4em;
  }

  .audio {
    margin-top: 4px;
  }

  &.contentMore {
    max-height: unset !important;
  }

  &.hasPadding {
    padding-bottom: 24px;
  }

  .toggle {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 40px;
    background: linear-gradient(180deg, rgba(249, 251, 255, 0.85) 0%, rgba(248, 251, 255, 0.94) 20%, #f4f8ff 100%);
    font-size: 14px;
    font-weight: 400;
    color: rgba(18, 39, 77, 0.45);
    cursor: pointer;

    &Icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      height: 100%;

      &.iconHidden {
        display: none;
      }
    }

    &:hover {
      color: @primary-color;
    }
  }
}
