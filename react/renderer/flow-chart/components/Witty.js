import React, { useMemo } from 'react';
import { Modal } from 'choerodon-ui/pro';
import { Button } from '@zknow/components';
import { getEnv, getCookieToken } from '@zknow/utils';

import './witty.less';

const modalKey = Modal.key();

export default (props) => {
  const { wfInstanceId, intl } = props;
  const modalStyle = useMemo(() => ({ width: '100%', height: '100%' }), []);
  const modalBodyStyle = useMemo(() => ({ padding: 0, height: '100%' }), []);

  const witty = useMemo(() => (
    <iframe
      style={{
        width: '100%',
        height: '100%',
        border: 'none',
      }}
      src={`${getEnv('HZERO_FRONT')}/pub/hwkf/task/approve-history?instanceId=${wfInstanceId}&tabFlag=flowChart&access_token=${getCookieToken()}`}
    />
  ), [wfInstanceId]);

  function openModal() {
    Modal.open({
      title: intl.formatMessage({ id: 'lcr.renderer.desc.stage.expand', defaultMessage: '审批流程' }),
      children: (
        <div className="witty-full-wrapper">
          {witty}
        </div>
      ),
      key: modalKey,
      style: modalStyle,
      bodyStyle: modalBodyStyle,
      destroyOnClose: true,
      footer: null,
      fullScreen: true,
    });
  }

  return (
    <div className="witty-wrapper mt-32">
      <div className="actions-wrapper">
        <Button
          key="fullscreen"
          icon="FullScreen"
          funcType="raised"
          color="secondary"
          onClick={openModal}
          className="actions-button"
        />
      </div>
      {witty}
    </div>
  );
};
