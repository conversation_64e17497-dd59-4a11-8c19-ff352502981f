import React, { useEffect, useState } from 'react';
import xml2js from 'xml2js';
import { observer } from 'mobx-react-lite';
import { useRequest } from 'ahooks';
import { getWfProcess } from '@/service';
import FlowRender from '@/renderer/flow-render';
import { drawIo2Flow } from '../utils';

export default observer((props) => {
  const { wfInstanceId, intl, predictionFlag, onlyShowPredictionFlag, multipleFlag, formDataSet } = props;
  const { data, run, loading } = useRequest(() => getWfProcess({ wfInstanceId, predictionFlag }), { manual: true });
  const [flowData, setFlowData] = useState();
  const parseString = xml2js.parseString;

  const nodeMap = {
    START: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.start.node', defaultMessage: '开始' }),
      icon: 'Play',
    },
    APPROVE: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.approve.node', defaultMessage: '审批节点' }),
      icon: 'PersonalPrivacy',
    },
    CATALOG_TASK: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.catalog.task.node', defaultMessage: '目录任务' }),
      icon: 'ListSuccess',
    },
    EXCLUSIVE: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.exclusive.node', defaultMessage: '条件判断' }),
      icon: 'TreeDiagram',
    },
    SET_VALUE: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.set.value.node', defaultMessage: '设置值' }),
      icon: 'Edit',
    },
    SUCCESS: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.success.node', defaultMessage: '结束' }),
      icon: 'Flag',
    },
    END: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.end.node', defaultMessage: '结束' }),
      icon: 'Flag',
    },
    PARALLEL: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.parallel.node', defaultMessage: '并行分支' }),
      icon: 'TreeDiagram',
    },
    CONVERGE: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.converge.node', defaultMessage: '并行汇合' }),
      icon: 'Fork',
    },
    NOTIFY: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.notify.node', defaultMessage: '通知' }),
      icon: 'Mail',
    },
    SLA_TIMER: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.sla.node', defaultMessage: 'SLA计时' }),
      icon: 'History',
    },
    SERVICE_UPGRADE: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.service.node', defaultMessage: '服务升级' }),
      icon: 'PeopleUpload',
    },
    TICKET_JOURNAL: {
      name: intl.formatMessage({ id: 'lcr.renderer.desc.ticket.journal', defaultMessage: '回复' }),
      icon: 'Comments',
    },
  };

  useEffect(() => {
    // 单据更新时，查询工作流
    if (data) {
      setTimeout(() => {
        run();
      }, 3000);
    }
  }, [formDataSet?.current?.get('last_update_date')]);

  useEffect(() => {
    run();
  }, [wfInstanceId]);

  useEffect(() => {
    if (data && data.diagramData) {
      parseString(data.diagramData, { explicitArray: false }, (err, result) => {
        if (err) {
          console.log(err);
        } else {
          setFlowData(drawIo2Flow(result, nodeMap, data, predictionFlag ? data.predictConditionEdges : false, onlyShowPredictionFlag));
        }
      });
    }
  }, [data, onlyShowPredictionFlag]);

  if (!flowData) {
    return null;
  }

  return (
    <FlowRender
      intl={intl}
      defaultData={flowData}
      fitView
      predictionFlag={predictionFlag}
      onlyShowPredictionFlag={onlyShowPredictionFlag}
      multipleFlag={multipleFlag}
    />
  );
});
