@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.wrapper {
  height: calc(100vh - 1.7rem);
}

.title {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 16px 0;
  margin-bottom: 12px;
}

.flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  margin-right: 40px;

  :global {
    .c7n-pro-radio-checked {
      .c7n-pro-radio-inner {
        background: @yq-primary-color-12 !important;
      }
    }
  }
}

.placeholder {
  height: 32px;
}

.status {
  margin-right: 24px;
}

.statusLabel {
  margin-right: 12px;
}

.switch {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  background: #F2F5FA;
  padding: 0 8px;
  border-radius: 4px;
}

.modal {
  height: calc(100% - 10px);
}
