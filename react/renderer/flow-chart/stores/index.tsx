import React, { createContext, useEffect } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { useRequest } from 'ahooks';
import { formatterCollections } from '@zknow/utils';
import { getWfInstances } from '@/service';

const Store = createContext<any>({});

export default Store;

export const StoreProvider = formatterCollections({ code: ['lcr.renderer'] })(injectIntl(inject('AppState')(
  observer((props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      instanceId,
      formDataSet,
    } = props;

    const { data, refresh: refreshData } = useRequest(() => getWfInstances({ tenantId, id: instanceId }));

    useEffect(() => {
      // 单据更新时，查询工作流
      if (data) {
        setTimeout(() => {
          refreshData();
        }, 500);
      }
    }, [formDataSet?.current?.get('last_update_date')]);

    const value = {
      ...props,
      tenantId,
      refreshData,
      data,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
)));
