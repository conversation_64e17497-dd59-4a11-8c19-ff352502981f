import React, { useContext, useState, useEffect } from 'react';
import { Icon, Empty } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { Menu } from 'choerodon-ui'
import { Dropdown, SelectBox } from 'choerodon-ui/pro';
import Store from './stores';
import Chart from './components/Chart';
import Witty from './components/Witty';
import styles from './styles/main.module.less';

const { Option } = SelectBox;

export default observer(() => {
  const {
    data = [], intl,
    predictionFlag = false,
    flowPredictionDefault = 'PREDICTION',
    formDataSet,
  } = useContext(Store);
  const [wfInstance, setWfInstance] = useState({ id: '', workflowType: '', workflowName: '' });
  const [predictionMode, setPredictionMode] = useState(flowPredictionDefault);

  useEffect(() => {
    if (!wfInstance?.id && data && data.length > 0) {
      setWfInstance(data[0]);
      formDataSet.setState('wfInstanceId', data[0].id);
      formDataSet.setState('wfInstanceName', data[0].name);
    }
  }, [data.length]);

  function handleMenuClick(e) {
    if (e.item && e.item.props) {
      setWfInstance(e.item.props.data || {});
      formDataSet.setState('wfInstanceId', e.item.props.data.id);
      formDataSet.setState('wfInstanceName', e.item.props.data.name);
    }
  }

  function getMenu() {
    return (
      <Menu onClick={handleMenuClick}>
        {data.map((item => (<Menu.Item data={item} key={item.id}>{item.workflowName}</Menu.Item>)) )}
      </Menu>
    );
  }

  function renderFlow() {
    if (!wfInstance || !wfInstance.id) {
      return null;
    }
    if (wfInstance.workflowType === 'WITTY') {
      return (
        <Witty
          wfInstanceId={wfInstance.id}
          intl={intl}
          multipleFlag={data?.length > 1}
        />
      );
    }
    if (wfInstance.workflowType === 'SYSTEM') {
      return (
        <Chart
          wfInstanceId={wfInstance.id}
          intl={intl}
          predictionFlag={predictionFlag}
          onlyShowPredictionFlag={predictionMode === 'PREDICTION'}
          multipleFlag={data?.length > 1}
          formDataSet={formDataSet}
        />
      );
    }
    return null;
  }

  return (
    <div className={styles.wrapper}>
      <div className={styles.flex}>
        {data && data.length > 1
          ? (
            <Dropdown overlay={getMenu()} placement="bottomLeft">
              <div className={styles.switch}>
                <span>{wfInstance.workflowName || ''}</span>
                <Icon type="DownOne" theme="filled" />
              </div>
            </Dropdown>
          ) : null}
        {/* 系统流程并且开启预测显示 */}
        {predictionFlag && wfInstance.workflowType === 'SYSTEM' && data?.length
          ? (
            <SelectBox mode="button" value={predictionMode} onChange={setPredictionMode}>
              <Option value="COMPLETE">{intl.formatMessage({ id: 'lcr.renderer.desc.complete.flow', defaultMessage: '完整流程' })}</Option>
              <Option value="PREDICTION">{intl.formatMessage({ id: 'lcr.renderer.desc.prediction.flow', defaultMessage: '预测流程' })}</Option>
            </SelectBox>
          ) : null}
      </div>
      {!data || wfInstance.id
        ? renderFlow()
        : (
          <Empty
            description={intl.formatMessage({ id: 'zknow.common.model.noData', defaultMessage: '暂无数据' })}
            style={{ padding: '0px' }}
          />
        )}
    </div>
  );
});
