import { MarkerType } from 'react-flow-renderer';

const Y_STEP = 120; // 两个节点Y间距
const X_STEP = 240; // 两个节点X间距
const NODE_WIDTH = 140; // 节点宽度
const NODE_HEIGHT = 42; // 节点高度
const COLOR_MAP = {
  HISTORY: '#00C48C',
  SETTING: '#354767',
  CURRENT: '#FFB100',
  PREDICTION: '#2EA2FF',
};
const SHOW_POPOVER_NODE = ['APPROVE', 'CATALOG_TASK'];

function findNodeByType(elements, type) {
  return elements.find(element => {
    let data = { type: '' };
    try {
      data = JSON.parse(element.data);
    } catch (e) {
      data = { type: '' };
    }
    return data.type === type;
  });
}

function findNodeById(elements, id) {
  return elements.find(element => element.id === id);
}

function findSubNodes(elements, parent, lines, history, finishFlag, predictions, onlyShowPredictionFlag) {
  const subNodes = [] as Array<any>;
  // 寻找连线
  const subLines = elements.filter(element => element.mxCell.source === parent.id);
  // 上级节点是否撤回，撤回节点的后续预测不显示
  const withdrawFlag = history.find(h => h.id === parent.id && h.taskVariables?.result === 'withdraw');
  subLines.map(line => {
    const checkLine = findNodeById(lines, line.id);
    // 已计算的连线不再处理
    if (checkLine) {
      return line;
    }
    // 预测节点
    const predictionFlag = predictions && (predictions.includes(line.id) || predictions.includes(`_${line.id}`));
    // 如果开启预测并且仅显示预测节点
    if (predictions && !predictionFlag && onlyShowPredictionFlag) {
      return line;
    }
    const node = findNodeById(elements, line.mxCell.target);
    const lineHistory = findNodeById(history, line.id);
    const lineColor = lineHistory
      ? COLOR_MAP.HISTORY
      : (predictionFlag && !withdrawFlag ? COLOR_MAP.PREDICTION : COLOR_MAP.SETTING);
    lines.push({
      id: line.id,
      type: 'smoothstep',
      source: line.mxCell.source,
      target: line.mxCell.target,
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 18,
        height: 18,
        color: lineColor,
      },
      style: {
        // 开启预测的非预测节点显示为灰色
        stroke: lineColor,
        strokeOpacity: 1,
      },
      labelStyle: { fill: '#fff', fillOpacity: 1 },
      labelBgStyle: {
        fillOpacity: 1,
        fill: lineColor,
      },
      labelBgPadding: [8, 4],
      labelBgBorderRadius: 10,
      label: line.label,
      predictionFlag: !withdrawFlag && !lineHistory && predictionFlag,
      data: {
        style: {
          stroke: lineColor,
          strokeOpacity: 1,
        },
        labelStyle: { fill: '#fff', fillOpacity: 1 },
        labelBgStyle: {
          fillOpacity: 1,
          fill: lineColor,
        },
        labelBgPadding: [8, 4],
        labelBgBorderRadius: 10,
        label: line.label,
      },
    });
    subNodes.push({
      ...node,
      predictionFlag: !withdrawFlag && !lineHistory && predictionFlag,
    });
    return line;
  });
  return subNodes;
}

/**
 * 计算各个流程节点位置
 * @param elements
 * @param parent
 * @param params
 * @param nodes
 * @param lines
 * @param nodeMap
 * @param history
 * @param currentX
 * @param currentY
 * @param finishFlag 流程已完成
 * @param predictions 预测节点
 * @param onlyShowPredictionFlag 仅显示预测节点
 */
function sortSubNodes(elements, parent, params) {
  const {
    nodes, lines, nodeMap, history, assignees,
    currentX, currentY, finishFlag, predictions, onlyShowPredictionFlag,
  } = params;
  let data = { type: '', label: '', approvalMethod: 'NONE' };
  try {
    data = JSON.parse(parent.data);
  } catch (e) {
    data = { type: '', label: '', approvalMethod: 'NONE' };
  }
  if (!data.type) {
    return true;
  }
  const nodeConfig = nodeMap[data.type] || { name: '' };
  const checkNode = findNodeById(nodes, parent.id);
  if (checkNode) {
    // 如果节点有一条预测连线，则显示为SETTING
    if (parent.predictionFlag && checkNode.data.stage === 'SETTING') {
      checkNode.data.stage = 'PREDICTION';
    }
    if (checkNode.position.y < currentY) {
      checkNode.position.y = currentY;
    }
  } else {
    const activities = [] as Array<{
      userId: string,
      user: string,
      userImageUrl: string,
      action: string,
      startAt: string,
      endAt: string,
      remark: string,
      task: boolean, // 标识是否任务类节点
    }>;
    let activityTitle = '';
    let stage = parent.predictionFlag ? 'PREDICTION' : 'SETTING';
    let approvalMethod = 'JUSTONESIGN';
    // 审批历史
    const nodeHistories = history.filter(h => h.id === parent.id);
    // 审批预测
    const nodeAssignees = assignees && assignees[`_${parent.id}`];
    if (data.type === 'START') {
      stage = 'HISTORY';
    } else if (nodeHistories && nodeHistories.length) {
      stage = 'HISTORY';
      // 当前节点名称
      activityTitle = nodeHistories[0].name;
      // 审批类型
      approvalMethod = nodeHistories[0].action.approvalMethod || data.approvalMethod;
      nodeHistories.map((nodeHistory) => {
        // 无endAt说明是当前节点
        if (!nodeHistory.endAt) {
          stage = 'CURRENT';
        }
        // 目前只有审批节点才显示悬浮信息
        if (!SHOW_POPOVER_NODE.includes(data.type)) {
          return nodeHistory;
        }
        if (nodeHistory.action.approvalMethod === 'COUNTERSIGN' || nodeHistory.endAt) {
          // 会签 or 已审批的或签
          let comment = { fullMessage: '' };
          if (nodeHistory.comments && nodeHistory.comments.length) {
            // type为comment为当前节点审批的意见
            comment = nodeHistory.comments.find(comment => comment.type === 'comment') || { fullMessage: '' };
          }
          // 有endAt但无result说明是会签自动跳过
          let result = nodeHistory.taskVariables ? nodeHistory.taskVariables.result : '';
          // 产品设计：如果是任务/执行计划的交付任务节点，或者是工作流的目录任务节点，在流程图的节点详情上显示为处理完成
          if (nodeHistory.endAt && nodeHistory.action.type === 'CATALOG_TASK' && result === 'approved') {
            result = 'complete';
          }
          activities.push({
            userId: nodeHistory.assignee,
            user: nodeHistory.assigneeName,
            userImageUrl: nodeHistory.assigneeImageUrl,
            action: nodeHistory.endAt ? (result ? `approve_${result}` : 'approve_skipped') : '',
            startAt: nodeHistory.startAt,
            endAt: nodeHistory.endAt,
            remark: comment.fullMessage,
            task: nodeHistory.action.type === 'CATALOG_TASK',
          });
        } else if (nodeHistory.candidateUsers) {
          // 未审批的或签人员存在candidateUsers中
          nodeHistory.candidateUsers.map((user) => {
            activities.push({
              userId: user.id,
              user: user.name,
              userImageUrl: user.imageUrl,
              action: '',
              startAt: nodeHistory.startAt,
              endAt: nodeHistory.endAt,
              remark: '',
              task: nodeHistory.action.type === 'CATALOG_TASK',
            });
          });
        } else {
          activities.push({
            userId: nodeHistory.assignee,
            user: nodeHistory.assigneeName,
            userImageUrl: nodeHistory.assigneeImageUrl,
            action: '',
            startAt: nodeHistory.startAt,
            endAt: nodeHistory.endAt,
            remark: '',
            task: nodeHistory.action.type === 'CATALOG_TASK',
          });
        }
        return nodeHistory;
      });
    } else if (nodeAssignees && nodeAssignees.length) {
      // 预测数据
      activityTitle = data.label || nodeConfig.name;
      approvalMethod = data.approvalMethod;
      nodeAssignees.map((nodeAssigner) => {
        activities.push({
          userId: nodeAssigner.id,
          user: nodeAssigner.userName || nodeAssigner.groupName,
          userImageUrl: nodeAssigner.imageUrl,
          action: '',
          startAt: '',
          endAt: '',
          remark: '',
          task: false,
        });
      });
    }
    nodes.push({
      id: parent.id,
      type: 'status',
      position: {
        x: currentX,
        y: currentY,
      },
      data: {
        id: parent.id, // 连线接点会用
        name: data.label || nodeConfig.name,
        icon: nodeConfig ? nodeConfig.icon : '',
        stage,
        topTargetHandle: 0,
        rightTargetHandle: 0,
        bottomTargetHandle: 0,
        leftTargetHandle: 0,
        topSourceHandle: 0,
        rightSourceHandle: 0,
        bottomSourceHandle: 0,
        leftSourceHandle: 0,
        activities,
        activityTitle,
        activityMethod: data.type === 'APPROVE' && approvalMethod !== 'NONE' ?
          (approvalMethod === 'JUSTONESIGN' ? 'anyoneApproval' : 'parallelApproval') : '',
      },
    });
  }

  const subNodes = findSubNodes(elements, parent, lines, history, finishFlag, predictions, onlyShowPredictionFlag);

  subNodes.map(node => {
    sortSubNodes(elements, node, {
      nodes, lines, nodeMap, history, assignees, currentX, currentY: currentY + Y_STEP, finishFlag,
      predictions, onlyShowPredictionFlag,
    });
    return node;
  });
  return true;
}

function calculateLine(source, target) {
  const sourceX = source.position.x;
  const sourceY = source.position.y;
  const targetX = target.position.x;
  const targetY = target.position.y;

  let customFlag = false;
  let sourceDirection = 'bottom';
  let targetDirection = 'top';
  const spacingX = sourceX - targetX;
  const spacingY = sourceY - targetY;

  if (spacingX > NODE_WIDTH) {
    sourceDirection = 'left';
    targetDirection = 'right';
  } else if (-spacingX > NODE_WIDTH) {
    sourceDirection = 'right';
    targetDirection = 'left';
  } else if (spacingY > NODE_HEIGHT) {
    sourceDirection = 'top';
    targetDirection = 'bottom';
    if (spacingY > Y_STEP * 2) {
      sourceDirection = 'left';
      targetDirection = 'left';
      customFlag = true;
    } else if (spacingY === Y_STEP * 2) {
      sourceDirection = 'right';
      targetDirection = 'right';
      customFlag = true;
    }
  } else if (-spacingY > NODE_HEIGHT) {
    sourceDirection = 'bottom';
    targetDirection = 'top';
    if (-spacingY > Y_STEP * 2) {
      sourceDirection = 'left';
      targetDirection = 'left';
      customFlag = true;
    } else if (-spacingY === Y_STEP * 2) {
      sourceDirection = 'right';
      targetDirection = 'right';
      customFlag = true;
    }
  }

  return {
    sourceDirection,
    targetDirection,
    customFlag,
  };
}

/**
 * 计算连线点
 * @param lines
 * @param nodes
 */
function sortLines(lines, nodes) {
  lines.map((line) => {
    const source = findNodeById(nodes, line.source);
    const target = findNodeById(nodes, line.target);
    if (source && target) {
      const { sourceDirection, targetDirection, customFlag } = calculateLine(source, target);
      const sourceIndex = source.data[`${sourceDirection}SourceHandle`];
      const targetIndex = target.data[`${targetDirection}TargetHandle`];
      line.sourceHandle = `${sourceDirection}-source-${line.source}-${sourceIndex}`;
      line.targetHandle = `${targetDirection}-target-${line.target}-${targetIndex}`;
      if (customFlag) {
        line.type = 'custom';
      }
      source.data[`${sourceDirection}SourceHandle`] = sourceIndex + 1;
      target.data[`${targetDirection}TargetHandle`] = targetIndex + 1;
    }
    return line;
  });
}

/**
 * 计算字符串实际的渲染宽度
 * @param text
 * @param font
 */
function getTextWidth(text, font = '14px Arial') {
  // 创建一个临时的canvas元素
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  // 设置字体大小和字体
  context.font = font;
  // 使用measureText计算文本宽度
  const metrics = context.measureText(text);
  // 返回文本的实际宽度
  return metrics.width;
}

/**
 * 处理同一排有多个节点的情况
 * @param nodes
 * @param maxY
 */
function sortRowNodes(nodes, maxY) {
  const yNodes = nodes.filter(node => node.position.y === maxY);
  // 计算一行Node的起始坐标
  let minX = (yNodes.length - 1) / 2 * -(X_STEP + NODE_WIDTH);
  yNodes.map((node) => {
    // 为了居中对齐Node，需要计算Node的文字宽度（Node间宽度的差异就是在名字上）
    const nameWidth = getTextWidth(node.data.name);
    node.position.x = minX + (NODE_WIDTH - nameWidth) / 2;
    // 下一个Node的起始
    minX = minX + (X_STEP + NODE_WIDTH);
    return node;
  });
  if (maxY - Y_STEP >= 0) {
    sortRowNodes(nodes, maxY - Y_STEP)
  }
}

function sortDrawIoData(
  elements: Array<any>,
  nodeMap: any,
  history: Array<any>,
  assignees: any, // 未来节点的审批人 { nodeId: [] }
  predictions: Array<any>,
  onlyShowPredictionFlag: boolean,
) {
  const nodes = [] as Array<any>;
  const lines = [] as Array<any>;

  // 找到起始点
  const startNode = findNodeByType(elements, 'START');
  if (!startNode) {
    return false;
  }

  // 找到终点
  const finishActivity = history?.find(h => h.type === 'endEvent');
  const finishFlag = finishActivity && finishActivity.endAt;

  // 计算位置
  sortSubNodes(elements, startNode, {
    nodes, lines, nodeMap, history, assignees,
    currentX: 0, currentY: 0, finishFlag,
    predictions, onlyShowPredictionFlag,
  });

  // 处理同一行的节点
  let maxY = 0;
  nodes.map((node) => {
    if (node.position.y > maxY) {
      maxY = node.position.y;
    }
    return node;
  });
  sortRowNodes(nodes, maxY);

  // 计算连接点
  sortLines(lines, nodes);

  return {
    nodes,
    lines,
  };
}

/**
 * 将drawio原始数据转换为执行计划
 * @param data
 */
function conversionData(data) {
  const list = [] as Array<any>;
  if (data.root.mxCell) {
    // drawio的默认连线，可能会被连到其他地方
    list.push({
      mxCell: data.root.mxCell[data.root.mxCell.length - 1].$,
    });
  }
  if (data.root.object) {
    data.root.object.map((item) => {
      list.push({
        ...item.$,
        mxCell: {
          ...item.mxCell.$,
          ...(item.mxCell.mxGeometry?.$ || {}),
        },
      });
      return item;
    });
  }
  return list;
}

/**
 * 转化数据
 * @param drawIoJson 原始数据
 * @param nodeMap 节点的翻译和icon
 * @param data 完整流程数据
 * @param predictions 预测节点
 * @param onlyShowPredictionFlag 只展示预测节点
 */
function drawIo2Flow(drawIoJson: any, nodeMap: any, data: any, predictions: any, onlyShowPredictionFlag: boolean): any {
  const { historyActivities, configAssignees } = data;
  let json = drawIoJson;
  if (drawIoJson && drawIoJson.GraphModel) {
    json = (drawIoJson.GraphModel.root.object || []).reverse();
  } else if (drawIoJson && drawIoJson.mxGraphModel) {
    json = (conversionData(drawIoJson.mxGraphModel) || []).reverse();
  }
  return sortDrawIoData(json, nodeMap, historyActivities, configAssignees, predictions, onlyShowPredictionFlag) || { nodes: [], lines: [] };
}

export {
  drawIo2Flow,
};
