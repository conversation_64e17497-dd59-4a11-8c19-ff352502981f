import { inject } from 'mobx-react';
import React, { useMemo } from 'react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import WorkHour from './WorkHour';

export default inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })((props) => {
    const {
      instanceId,
      ticketId,
      viewDataSet,
    } = props;
    const { businessObjectCode, businessObjectId } = viewDataSet?.current?.toData() || {};

    return (
      <StoreProvider {...props} ticketId={instanceId || ticketId} businessObjectCode={businessObjectCode} businessObjectId={businessObjectId}>
        <WorkHour />
      </StoreProvider>
    );
  })
);

/* externalize: WorkHourRenderer */
