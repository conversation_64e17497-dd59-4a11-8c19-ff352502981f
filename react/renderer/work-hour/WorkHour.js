import { observer } from 'mobx-react-lite';
import React, { useContext, useState } from 'react';
import { Permission } from '@yqcloud/apps-master';
import { Button } from 'choerodon-ui/pro';
import { Empty } from '@zknow/components';
import WorkHourHeader from './components/header/workHourHeader';
import WorkHourSection from './components/section/WorkHourSection';
import WorkHourList from './components/list/WorkHourList';
import WorkHourChart from './components/chart/WorkHourChart';
import useTotalListData from './hooks/useTotalListData';
import Store from './stores';
import './WorkHour.less';

const WorkHour = () => {
  const {
    intl,
    intlPrefix,
    prefixCls,
    headerState,
    workHourDataSet,
    workLogsDataSet,
    addState,
    defaultData,
    formDataSet,
  } = useContext(Store);

  const { modes, mode } = headerState;

  const isList = mode.name === modes.listMode.name;
  // Section
  const sectionProps = { intl, intlPrefix, prefixCls };
  // 工时
  const workHourList = workHourDataSet?.current?.get('list');
  const { totalScheduledWorkTime, totalActualWorkTime } = useTotalListData({ workHourList });
  const [scheduledExpand, setScheduledExpand] = useState(true);
  const [actualExpand, setActualExpand] = useState(true);
  const [scheduledExpandMap, setScheduledExpandMap] = useState({});
  const [actualExpandMap, setActualExpandMap] = useState({});
  const actions = [
    {
      render: () => {
        if (!formDataSet?.getState('serviceSettingPermission')?.workingTime?.canAdd) return null;
        const { workingTimeSettingFlag, addWorkTimeFlag } = defaultData;
        if (!workingTimeSettingFlag) return null;
        const addButton = (
          <Button key="addWorkTimeHourButton" funcType="raised" color="primary" onClick={addState.openModal}>
            {intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' })}{' '}
          </Button>
        );
        if (workingTimeSettingFlag) {
          if (addWorkTimeFlag) {
            return addButton;
          }
          return null;
        }
        return <Permission service={['add_working_time']}>{addButton}</Permission>;
      },
    },
  ];

  const emptyComponent = (
    <Empty
      description={intl.formatMessage({ id: 'lcr.renderer.desc.empty.work.hour', defaultMessage: '暂无工时记录' })}
      style={{ padding: '25px 0 0 0' }}
      innerStyle={{ width: '80px', height: '80px' }}
      actions={actions}
    />
  );

  const scheduledWorkTime = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.plan', defaultMessage: '预估工时' });
  const actualWorkTime = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.real', defaultMessage: '实际工时' });

  const List = (
    <>
      {(!!totalScheduledWorkTime || totalScheduledWorkTime === 0) && (
        <WorkHourSection
          {...sectionProps}
          expand={scheduledExpand}
          setExpand={setScheduledExpand}
          title={scheduledWorkTime}
          totalWorkTime={totalScheduledWorkTime}
          totalWorkTimeTitle={intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.total.name', defaultMessage: '总计{name}' }, { name: scheduledWorkTime })}
        >
          {/* 预估工时 */}
          <WorkHourList
            isScheduled
            expandMap={scheduledExpandMap}
            setExpandMap={setScheduledExpandMap}
          />
        </WorkHourSection>
      )}
      {(!!totalActualWorkTime || totalActualWorkTime === 0) && (
        <WorkHourSection
          {...sectionProps}
          expand={actualExpand}
          setExpand={setActualExpand}
          title={actualWorkTime}
          totalWorkTime={totalActualWorkTime}
          totalWorkTimeTitle={intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.total.name', defaultMessage: '总计{name}' }, { name: actualWorkTime })}
        >
          {/* 实际工时工时 */}
          <WorkHourList expandMap={actualExpandMap} setExpandMap={setActualExpandMap} />
        </WorkHourSection>
      )}
      {!totalActualWorkTime && totalActualWorkTime !== 0 && !totalScheduledWorkTime && totalScheduledWorkTime !== 0 && emptyComponent}
    </>
  );

  const Chart = <WorkHourChart Empty={emptyComponent} />;

  return (
    <div id="lc-components-workHour-renderer" className={`${prefixCls}`}>
      <WorkHourHeader />
      {isList ? List : Chart}
    </div>
  );
};

export default observer(WorkHour);
