export default ({ intl, intlPrefix, tenantId, businessObjectCode, ticketId }) => {
  const urlPrefix = `itsm/v1/${tenantId}/incident_working_time/workspace/incident/id`;

  return {
    autoQuery: !!ticketId,
    transport: {
      read: {
        url: `${urlPrefix}?id=${ticketId}&businessObjectCode=${businessObjectCode}`,
        method: 'get',
      },
    },
    fields: [{ name: 'list' }, { name: 'totalWorkTime' }],
  };
};
