import React, { createContext, useMemo, useEffect, useState } from 'react';
import { DataSet, message } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import pick from 'lodash/pick';
import { observer } from 'mobx-react-lite';
import {
  WorkLogsDataSet,
  ReplyListDataSet,
  ActionListDataSet,
} from '../createButton/ButtonStores/DurationDataSet';
import WorkHourDataSet from './WorkHourDataSet';
import useHeader from '../hooks/useHeader';
import useSearch from '../hooks/useSearch';
import useAddWorkHour from '../hooks/useAddWorkHour';
import useEditWorkHour from '../hooks/useEditWorkHour';
import { durationToDays } from '@/utils';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState', 'HeaderStore')(observer((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId },
        userInfo,
      },
      AppState,
      HeaderStore: {
        getTenantConfig: { themeColor = '#2979ff' },
      },
      ticketId,
      businessObjectCode,
      businessObjectId,
      viewDataSet,
      tableDataSet,
      formDataSet,
    } = props;
    const intlPrefix = 'lc.components.workHour';
    const prefixCls = 'lc-components-workHour';
    const hasWorkHour = ['INCIDENT', 'PROBLEM', 'PROBLEM_TASK', 'CHANGE'].includes(
      businessObjectCode
    );
    const urlSuffix = `${businessObjectCode?.toLowerCase()}_working_time`;

    const [defaultData, setDefaultData] = useState({});
    const [workHourClientWidth, setWorkHourClientWidth] = useState();
    const MIN_CLIENT_WIDTH = 408; // 最小临界值，工时box宽度小于这个值后，列表视图，图表视图这个筛选器就变成icon

    const getDataWithCache = async () => {
      const key = businessObjectId;
      const cache = AppState?.customConfig[key];
      if (cache) {
        return cache;
      }
      if (!ticketId || !tenantId) return;
      const res = await axios.get(
        `/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`
      );
      AppState?.setCustomConfig(key, res);
      AppState?.setCustomConfig(`${businessObjectId}-${ticketId}`, res);
      return res;
    };

    useEffect(() => {
      if (formDataSet?.current?.get('id') && !formDataSet?.getState('serviceSettingPermission')) {
        getServiceSettingPermission();
      }
    }, [formDataSet?.current?.get('id'), formDataSet?.getState('serviceSettingPermission')]);

    // 获取服务配置中回复 和 工时的权限
    const getServiceSettingPermission = async () => {
      const res = await axios.get(
        `/itsm/v1/${tenantId}/service_settings/terminal_state_operation?id=${formDataSet?.current?.get('id')}&businessObjectCode=${businessObjectCode}`
      );
      if (res?.failed) {
        message.error(res?.message);
        return;
      }
      formDataSet?.setState('serviceSettingPermission', res);
    };

    const initConfig = async () => {
      const res = await getDataWithCache();
      setDefaultData(pick(res, ['workingTimeSettingFlag', 'workingTimeSettingVO', 'deleteWorkTimeFlag', 'addWorkTimeFlag', 'editWorkTimeFlag']));
    };

    useEffect(() => {
      if (ticketId && businessObjectId) {
        initConfig();
      }
    }, [ticketId, businessObjectId]);

    useEffect(() => {
      const ele = document.getElementById('lc-components-workHour-renderer');
      if (ele) {
        const width = ele.clientWidth || 0;
        setWorkHourClientWidth(width);
      }
    }, [document.getElementById('lc-components-workHour-renderer')]);

    // 工时弹窗
    const workLogsDataSet = useMemo(
      () => new DataSet(WorkLogsDataSet({ intl, tenantId, defaultData, instanceId: ticketId, intlPrefix })),
      [defaultData, ticketId, tenantId]
    );

    // 工时关联回复列表
    const replayListDataSet = useMemo(
      () => new DataSet(ReplyListDataSet({ intl, intlPrefix, tenantId, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );

    // 工时关联动作记录列表
    const actionListDataSet = useMemo(
      () => new DataSet(
        ActionListDataSet({ intl, intlPrefix, tenantId, ticketId, businessObjectCode })
      ),
      [ticketId, businessObjectCode]
    );

    // 工时展示
    const workHourDataSet = useMemo(
      () => new DataSet(WorkHourDataSet({ intl, intlPrefix, tenantId, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );

    // 把工时展示数据集挂在视图上，供其他组件调用或刷新数据。
    viewDataSet.setState('_workHourDataSet', workHourDataSet);

    // 工时表单必填应用逻辑
    useEffect(() => {
      const requiredFieldList = defaultData.workingTimeSettingVO?.required || [];
      if (requiredFieldList?.length > 0) {
        workLogsDataSet.setState('requiredFieldList', requiredFieldList);
      }
    }, [defaultData, workLogsDataSet]);

    const headerState = useHeader({ intl, intlPrefix, MIN_CLIENT_WIDTH, workHourClientWidth, defaultData });
    const searchState = useSearch({ workHourDataSet });
    const actionProp = {
      intl,
      intlPrefix,
      tenantId,
      ticketId,
      businessObjectCode,
      businessObjectId,
      workLogsDataSet, // 工时记录数据集
      workHourDataSet, // 新建工时表单数据集
      replayListDataSet,
      actionListDataSet,
      tableDataSet,
      viewDataSet,
      formDataSet,
      urlSuffix,
      defaultData,
      userInfo,
      AppState,
    };
    const addState = useAddWorkHour(actionProp);
    const openEditModal = useEditWorkHour(actionProp);
    const getWorkHour = (duration) => {
      // 工时可以为 0
      if (duration === 0) {
        return intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.totalMinutes', defaultMessage: '{minutes}分钟' }, { minutes: 0 });
      }
      const workHour = durationToDays(duration);
      const hours = workHour?.hours
        ? intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.totalHours', defaultMessage: '{hours}小时' }, workHour)
        : '';
      const minutes = workHour?.minutes
        ? intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.totalMinutes', defaultMessage: '{minutes}分钟' }, workHour)
        : '';
      return hours + minutes;
    };
    // lcr.renderer.desc.lc.components.work.hour.
    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      workLogsDataSet,
      replayListDataSet,
      actionListDataSet,
      workHourDataSet,
      hasWorkHour,
      urlSuffix,
      defaultData,
      themeColor,
      headerState,
      searchState,
      addState,
      getWorkHour,
      openEditModal,
      userInfo,
      workHourClientWidth,
      MIN_CLIENT_WIDTH,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  }))
);
