import React, { useState, useCallback } from 'react';
import { Modal } from 'choerodon-ui/pro';

import FormView from '../components/form/FormView';

const modalKey = Modal.key();
export default function useEditWorkHour({
  intl,
  intlPrefix,
  tenantId,
  ticketId,
  businessObjectCode,
  workLogsDataSet, // 工时记录数据集
  workHourDataSet, // 新建工时表单数据集
  replayListDataSet,
  actionListDataSet,
  tableDataSet,
  viewDataSet,
  formDataSet,
  urlSuffix,
  businessObjectId,

}) {
  const [adding, setAdding] = useState(false);

  /**
   * 打开工时表单
   */
  const openEditModal = useCallback(async (data) => {
    if (adding) return;
    setAdding(true);

    const _record = workLogsDataSet.create(data);
    // workLogsDataSet增加了create方法在新建后会选中第一个category，
    // 这里create的record用于更新所以重新set category
    _record.set('category', data.category);

    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.edit', defaultMessage: '编辑工时' }),
      children: (
        <FormView
          workHourId={_record?.get('id')}
          tenantId={tenantId}
          intl={intl}
          intlPrefix={intlPrefix}
          replayListDataSet={replayListDataSet}
          actionListDataSet={actionListDataSet}
          workHourTableDataSet={tableDataSet}
          viewDataSet={viewDataSet}
          ticketId={ticketId}
          businessObjectCode={businessObjectCode}
          urlSuffix={urlSuffix}
          formDataSet={formDataSet}
          businessObjectId={businessObjectId}
          isEdit
        />
      ),
      onClose: () => {
        workHourDataSet.setQueryParameter('param', '');
        workHourDataSet.query();
      },
      style: { width: 1000 },
    });
    setAdding(false);
  }, [workLogsDataSet]);

  return openEditModal;
}
