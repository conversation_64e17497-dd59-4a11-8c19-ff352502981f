import React, { useState, useMemo } from 'react';
import { TextField } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { debounce } from 'lodash';

export default function useSearch({ workHourDataSet }) {
  const [searching, setSearching] = useState(false);
  const [searchValue, setSearchValue] = useState(null);
  
  const handleSearch = debounce(async (value) => {
    workHourDataSet.setQueryParameter('param', value);
    const res = await workHourDataSet.query();
    if (value) {
      const _expandMap = {};
      res?.list.forEach(({ operatorId }) => {
        _expandMap[operatorId] = true;
      });
    }
  }, 500);

  const searchComponent = useMemo(() => {
    // 在失去焦点后如果没有值，关闭搜索
    const handleBlur = (e) => {
      const value = e.target.value;
      if (!value) {
        setSearching(false);
        handleSearch(null);
      }
    };
    // 点击清空按钮后关闭搜索
    const handleClear = () => {
      setSearching(false);
      handleSearch(null);
    };
    // 按下回车进行搜索
    const handleEnterDown = (e) => handleSearch(e.target.value);
    const handleChange = (value) => setSearchValue(value);
    
    return (
      <TextField
        style={{ width: '100%' }}
        prefix={<Icon type="icon-search" />}
        autoFocus
        clearButton
        onBlur={handleBlur}
        onClear={handleClear}
        onChange={handleChange}
        onEnterDown={handleEnterDown}
      />
    );
  }, []);

  return {
    searching, // 搜索中标识
    setSearching, // 设置搜索中标识
    searchComponent, // 搜索组件
    searchValue, // 当前搜索的值
  };
}
