import { useMemo } from 'react';
import _ from 'lodash';
import moment from 'moment';

export default function useListData({ intl, category, workHourList, workLogsDataSet }) {
  const typeMap = workLogsDataSet?.getField('type')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const serviceWayMap = workLogsDataSet?.getField('serviceWay')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const reply = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' });
  const ungrouped = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.ungrouped', defaultMessage: '未分组' });

  const getTotalHour = (items) => {
    const total = items.reduce((pre, current) => {
      return (current?.duration || 0) + pre;
    }, 0);
    // 转为小时，保留两位小数
    return Math.ceil((total / 60) * 100) / 100;
  };
  // 按维度处理数据
  const groupData = (temp) => {
    const result = [];
    // 处理人
    if (category.name === 'assignmentPerson') {
      const groupedData = _.groupBy(temp, 'operatorId');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = {
          key,
          title: items[0]?.personName || key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        result.push(group);
      });
    }
    // 处理组
    if (category.name === 'assignmentGroup') {
      const groupedData = _.groupBy(temp, 'operatorGroupId');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = {
          key,
          title: items[0]?.groupName || key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        result.push(group);
      });
    }
    // 工时类型
    if (category.name === 'type') {
      const groupedData = _.groupBy(temp, 'type');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];

        const group = {
          key,
          title: typeMap[key] || key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        result.push(group);
      });
    }
    // 服务方式
    if (category.name === 'serviceWay') {
      const groupedData = _.groupBy(temp, 'serviceWay');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = {
          key,
          title: serviceWayMap[key] || key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        result.push(group);
      });
    }
    // 发生时间
    if (category.name === 'enterDate') {
      const groupedData = _.groupBy(temp, 'entryDay');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = {
          key,
          title: key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        result.push(group);
      });
    }
    // 动作回复
    if (category.name === 'actionReply') {
      const groupedData = _.groupBy(temp, (v) => {
        if (v.actionName) {
          return v.actionName;
        }
        if (v.journalContent) {
          return reply;
        }
        return null;
      });
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = {
          key,
          title: key,
          scheduledTotal: getTotalHour(items.filter((v) => v.category === 'SCHEDULED_WORK_TIME')),
          actualTotal: getTotalHour(items.filter((v) => v.category !== 'SCHEDULED_WORK_TIME')),
        };
        if (key === reply) {
          result.unshift(group);
        } else {
          result.push(group);
        }
      });
    }
    return result.sort((a) => {
      if (a.title === 'null') return -1;
      return 1;
    }).map(v => {
      if (v.title === 'null') {
        v.title = ungrouped;
      }
      return v;
    });
  };

  const data = useMemo(() => {
    if (!workHourList || !category) {
      return null;
    }
    const temp = [];
    workHourList?.forEach((value) => {
      value.manHours.forEach((manHour) => {
        temp.push({
          ...manHour,
          ..._.omit(value, 'manHours'),
          entryDay: moment(manHour.entryDate).format('YYYY-MM-DD'),
        });
      });
    });
    return groupData(temp);
  }, [category, workHourList]);

  return data;
}
