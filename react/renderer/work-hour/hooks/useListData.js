import { useState, useEffect } from 'react';
import _ from 'lodash';
import moment from 'moment';

export default function useListData({ intl, category, workHourList, workLogsDataSet }) {
  const typeMap = workLogsDataSet?.getField('type')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const serviceWayMap = workLogsDataSet?.getField('serviceWay')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const reply = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' });
  const ungrouped = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.ungrouped', defaultMessage: '未分组' });

  const [scheduledWorkTime, setScheduledWorkTime] = useState([]);
  const [actualWorkTime, setActualWorkTime] = useState([]);
  // 按维度处理数据
  const groupData = (temp) => {
    const result = [];
    // 处理人
    if (category.name === 'assignmentPerson') {
      const groupedData = _.groupBy(temp, (v) => {
        if (v.actionName) {
          return `${v.operatorId}-$-${v.actionName}`;
        }
        if (v.journalContent || v.journalId) {
          return `${v.operatorId}-$-${reply}`;
        }
        return v.operatorId;
      });

      Object.keys(groupedData).forEach((key) => {
        const [operatorId, subTitle = false] = key.split('-$-');
        const items = groupedData[key];
        const group = {
          key,
          title: items[0]?.personName || operatorId,
          img: items[0]?.imageUrl,
          subTitle,
          items,
          operatorId,
        };
        result.push(group);
      });
    }
    // 处理组
    if (category.name === 'assignmentGroup') {
      const groupedData = _.groupBy(temp, 'operatorGroupId');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = { key, title: items[0]?.groupName || key, items };
        result.push(group);
      });
    }
    // 工时类型
    if (category.name === 'type') {
      const groupedData = _.groupBy(temp, 'type');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = { key, title: typeMap[key] || key, items };
        result.push(group);
      });
    }
    // 服务方式
    if (category.name === 'serviceWay') {
      const groupedData = _.groupBy(temp, 'serviceWay');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = { key, title: serviceWayMap[key] || key, items };
        result.push(group);
      });
    }
    // 发生时间
    if (category.name === 'enterDate') {
      const groupedData = _.groupBy(temp, 'entryDay');
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = { key, title: key, items };
        result.push(group);
      });
    }
    // 动作回复
    if (category.name === 'actionReply') {
      const groupedData = _.groupBy(temp, (v) => {
        if (v.actionName) {
          return v.actionName;
        }
        if (v.journalContent) {
          return reply;
        }
        return null;
      });
      Object.keys(groupedData).forEach((key) => {
        const items = groupedData[key];
        const group = { key, title: key, items };
        if (key === reply) {
          result.unshift(group);
        } else {
          result.push(group);
        }
      });
    }
    return result.sort((a) => {
      if (a.title === 'null') return 1;
      return -1;
    }).map(v => {
      if (v.title === 'null') {
        v.title = ungrouped;
      }
      return v;
    });
  };
  useEffect(() => {
    if (!workHourList || !category) {
      return;
    }
    const scheduledWorkTimeTemp = [];
    const actualWorkTimeTemp = [];
    workHourList?.forEach((value) => {
      value.manHours.forEach((manHour) => {
        if (manHour.category === 'SCHEDULED_WORK_TIME') {
          // 预估工时
          scheduledWorkTimeTemp.push({
            ...manHour,
            ..._.omit(value, 'manHours'),
            entryDay: moment(manHour.entryDate).format('YYYY-MM-DD'),
          });
        } else {
          actualWorkTimeTemp.push({
            ...manHour,
            ..._.omit(value, 'manHours'),
            entryDay: moment(manHour.entryDate).format('YYYY-MM-DD'),
          });
        }
      });
    });
    setActualWorkTime(groupData(actualWorkTimeTemp));
    setScheduledWorkTime(groupData(scheduledWorkTimeTemp));
  }, [category, workHourList]);

  return {
    scheduledWorkTime,
    actualWorkTime,
  };
}
