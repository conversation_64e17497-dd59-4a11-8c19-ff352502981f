import React, { useState, useMemo, useCallback } from 'react';
import { message, Tooltip, Modal } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import { axios, Permission } from '@yqcloud/apps-master';
import FormView from '../components/form/FormView';

const modalKey = Modal.key();
export default function useAddWorkHour({
  intl,
  intlPrefix,
  tenantId,
  ticketId,
  businessObjectCode,
  businessObjectId,
  workLogsDataSet, // 工时记录数据集
  workHourDataSet, // 新建工时表单数据集
  replayListDataSet,
  actionListDataSet,
  tableDataSet,
  viewDataSet,
  formDataSet,
  urlSuffix,
  defaultData,
  userInfo,
  AppState,
}) {
  const [adding, setAdding] = useState(false);
  /**
   * 初始化工时表单默认值
   */
  const initDefaultData = useCallback(
    async (_record) => {
      const { workingTimeSettingFlag, workingTimeSettingVO } = defaultData;
      // 默认时间
      _record?.set('entryDate', new Date());

      const data = formDataSet?.current?.toData() || {};

      const getPersonInGroupWithCache = async (userId, groupId) => {
        if (!userId || !groupId) return false;
        const key = `personInGroup-${userId}-${groupId}`;
        const cache = AppState?.customConfig[key];
        if (cache) {
          return cache;
        }
        const res = axios.get(
          `/iam/yqc/${tenantId}/userGroups/query/whether/in/group?userId=${userId}&groupId=${groupId}`
        );
        AppState?.setCustomConfig(key, res);
        return res;
      };
      // 默认操作人
      _record?.set('operatorId', {
        id: userInfo?.id,
        name: userInfo?.realName,
        realName: userInfo?.realName,
        real_name: userInfo?.realName,
      });
      // 默认操作组
      const inGroup = await getPersonInGroupWithCache(userInfo?.id, data?.assignment_group_id);
      if (inGroup) {
        _record?.set('operatorGroupId', {
          id: data.assignment_group_id,
          groupName: data['assignment_group_id:name'],
          group_name: data['assignment_group_id:name'],
          name: data['assignment_group_id:name'],
        });
      }

      if (workingTimeSettingFlag) {
        const { serviceMode, workHoursType, hours, minutes } = workingTimeSettingVO || {};
        // 工时类型
        workHoursType && _record?.set('type', workHoursType);
        // 服务方式
        serviceMode && _record?.set('serviceWay', serviceMode);
        // 时长默认值
        if (typeof hours === 'number') {
          _record.set('hours', hours);
        }
        if (typeof minutes === 'number') {
          _record.set('minutes', minutes);
        }
      }

      // 工时分类默认值
      const category = _record?.getField('category')?.options?.get(0)?.get('code');
      if (category) {
        _record?.set('category', category);
      }
    },
    [defaultData]
  );

  /**
   * 打开工时表单
   */
  const openModal = useCallback(async () => {
    if (adding) return;
    setAdding(true);

    // const _record = workLogsDataSet.create({});
    // await initDefaultData(_record);

    Modal.open({
      key: modalKey,
      title: intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' }),
      children: (
        <FormView
          tenantId={tenantId}
          intl={intl}
          intlPrefix={intlPrefix}
          replayListDataSet={replayListDataSet}
          actionListDataSet={actionListDataSet}
          workHourTableDataSet={tableDataSet}
          viewDataSet={viewDataSet}
          ticketId={ticketId}
          businessObjectCode={businessObjectCode}
          urlSuffix={urlSuffix}
          formDataSet={formDataSet}
          businessObjectId={businessObjectId}
        />
      ),
      onClose: () => {
        workHourDataSet.setQueryParameter('param', '');
        workHourDataSet.query();
      },
      style: { width: 1000 },
    });
    setAdding(false);
  }, [initDefaultData, workLogsDataSet]);

  const addComponent = useMemo(() => {
    if (!formDataSet?.getState('serviceSettingPermission')?.workingTime?.canAdd) return null;
    const { workingTimeSettingFlag, addWorkTimeFlag } = defaultData;
    const addIcon = (
      <Tooltip
        title={intl.formatMessage({ id: intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' }) })}
      >
        <Icon className="action action-active" type="add-one" size={16} onClick={openModal} />
      </Tooltip>
    );

    if (workingTimeSettingFlag) {
      if (addWorkTimeFlag) {
        return addIcon || null;
      }
      return null;
    }
    return <Permission service={['add_working_time']}>{addIcon || null}</Permission>;
  }, [defaultData, formDataSet?.getState('serviceSettingPermission')]);

  return {
    addComponent, // 添加工时组件
    openModal,
  };
}
