import React, { useState, useMemo } from 'react';
import { Icon } from '@zknow/components';
import { Menu, Dropdown, Button, Tooltip } from 'choerodon-ui/pro';
import SegmentedControl from '@/components/segmented-control';

export default function useHeader({ intl, intlPrefix, MIN_CLIENT_WIDTH, workHourClientWidth, defaultData }) {
  // 1. 统计方式有两种渲染方式：列表、图表
  const modes = {
    listMode: { name: 'listMode' },
    chartMode: { name: 'chartMode' },
  };
  const [mode, setMode] = useState(modes.listMode);

  // 2. 切换模式的组件
  const modeComponent = useMemo(() => {
    const options = [
      {
        label: intl.formatMessage({ id: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.mode.list', defaultMessage: '列表视图' }) }),
        value: 'listMode',
        icon: 'list-middle',
      },
      {
        label: intl.formatMessage({ id: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.mode.chart', defaultMessage: '图表视图' }) }),
        value: 'chartMode',
        icon: 'chart-histogram-two',
      },
    ];
    return (
      <SegmentedControl
        options={options}
        defaultValue="listMode"
        mode={workHourClientWidth > MIN_CLIENT_WIDTH ? 'text' : 'icon'}
        // mode="icon"
        onChange={(value) => {
          if (value === 'listMode') {
            setMode(modes.listMode);
          }
          if (value === 'chartMode') {
            setMode(modes.chartMode);
          }
        }}
      />
    );
  }, [mode, workHourClientWidth, MIN_CLIENT_WIDTH]);

  // 3. 统计维度有 6 种：处理人、处理组、工时类型、服务方式、发生时间、动作回复(先弃用)
  const categories = {
    assignmentPerson: {
      code: 'ASSIGNMENT_PERSON',
      name: 'assignmentPerson',
      label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.assignment.person', defaultMessage: '处理人' }),
    },
    assignmentGroup: {
      code: 'ASSIGNMENT_GROUP',
      name: 'assignmentGroup',
      label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.assignment.group', defaultMessage: '处理组' }),
    },
    type: {
      code: 'TYPE',
      name: 'type',
      label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.type', defaultMessage: '工时类型' }),
    },
    serviceWay: {
      code: 'SERVICE_WAY',
      name: 'serviceWay',
      label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.service.way', defaultMessage: '服务方式' }),
    },
    enterDate: {
      code: 'ENTRY_DATE',
      name: 'enterDate',
      label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.enter.date', defaultMessage: '发生时间' }),
    },
    // actionReply: {
    //   name: 'actionReply',
    //   label: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.action.reply', defaultMessage: '动作回复' }),
    // },
  };
  const [category, setCategory] = useState(categories.assignmentPerson);

  // 4. 切换维度的组件
  const categoryComponent = useMemo(() => {
    const menu = (
      <Menu selectedKeys={[category.name]} className="lc-components-workHour-header-switch">
        {Object.keys(categories).map((key) => {
          const val = categories[key];
          if (!defaultData?.workingTimeSettingVO?.displayDimensions?.includes(val.code)) return null;
          return (
            <Menu.Item key={key} onClick={() => setCategory(val)}>
              {val.label}
            </Menu.Item>
          );
        })}
      </Menu>
    );

    return (
      <Dropdown overlay={menu} key="categorySelect">
        <Tooltip
          title={intl.formatMessage({
            id: intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.category.switch', defaultMessage: '切换维度' }),
          })}
        >
          <Button className="action action-active-dropdown">
            {category?.label}
            <Icon type="DownOne" theme="filled" size={12} style={{ marginLeft: '4px' }} />
          </Button>
        </Tooltip>
      </Dropdown>
    );
  }, [category, defaultData]);

  return {
    modes, // 所有模式
    mode, // 当前模式
    setMode, // 设置当前模式
    modeComponent, // 切换模式的组件
    categories, // 所有分类
    category, // 当前分类
    setCategory, // 设置所有分类
    categoryComponent, // 切换分类的组件
  };
}
