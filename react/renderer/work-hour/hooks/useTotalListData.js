import { useState, useEffect } from 'react';

export default function useTotalListData({ workHourList }) {
  const [totalScheduledWorkTime, setTotalScheduledWorkTime] = useState(NaN);
  const [totalActualWorkTime, setTotalActualWorkTime] = useState(NaN);

  useEffect(() => {
    if (!workHourList) {
      return;
    }
    let _totalScheduledWorkTime = NaN;
    let _totalActualWorkTime = NaN;
    workHourList?.forEach((value) => {
      value.manHours.forEach((manHour) => {
        if (manHour.category === 'SCHEDULED_WORK_TIME') {
          if (Number.isNaN(_totalScheduledWorkTime)) {
            _totalScheduledWorkTime = manHour?.duration || 0;
          } else {
            _totalScheduledWorkTime += manHour?.duration || 0;
          }
        } else if (manHour.category === 'ACTUAL_WORK_TIME') {
          if (Number.isNaN(_totalActualWorkTime)) {
            _totalActualWorkTime = manHour?.duration || 0;
          } else {
            _totalActualWorkTime += manHour?.duration || 0;
          }
        }
      });
    });
    setTotalScheduledWorkTime(_totalScheduledWorkTime);
    setTotalActualWorkTime(_totalActualWorkTime);
  }, [workHourList]);

  return {
    totalScheduledWorkTime,
    totalActualWorkTime,
  };
}
