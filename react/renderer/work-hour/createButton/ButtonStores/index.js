import React, { createContext, useMemo, useState, useEffect } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import axios from 'axios';
import { WorkLogsDataSet, ReplyListDataSet, ActionListDataSet } from './DurationDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState')((props) => {
    const {
      intl,
      children,
      AppState: {
        currentMenuType: { tenantId }, userInfo,
      },
      AppState,
      ticketId,
      businessObjectCode,
      businessObjectId,
    } = props;
    const intlPrefix = 'lc.components.workHour';
    const prefixCls = 'lc-components-workHour';
    const hasWorkHour = ['INCIDENT', 'PROBLEM', 'PROBLEM_TASK', 'CHANGE'].includes(
      businessObjectCode
    );
    const urlSuffix = `${businessObjectCode?.toLowerCase()}_working_time`;
    const [defaultData, setDefaultData] = useState({});

    const getDataWithCache = async () => {
      const key = businessObjectId;
      const cache = AppState?.customConfig[key];
      if (cache) {
        return cache;
      }
      if (!ticketId || !tenantId) return;
      const res = await axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId}`);
      AppState?.setCustomConfig(key, res);
      AppState?.setCustomConfig(`${businessObjectId}-${ticketId}`, res);
      return res;
    };

    const initConfig = async () => {
      const res = await getDataWithCache();
      const { workingTimeSettingFlag, workingTimeSettingVO, deleteWorkTimeFlag, addWorkTimeFlag, editWorkTimeFlag } = res;
      setDefaultData({ workingTimeSettingFlag, workingTimeSettingVO, deleteWorkTimeFlag, addWorkTimeFlag, editWorkTimeFlag });
    };

    useEffect(() => {
      if (ticketId && businessObjectId) {
        initConfig();
      }
    }, [ticketId, businessObjectId]);

    // 工时弹窗
    const workLogsDataSet = useMemo(
      () => new DataSet(WorkLogsDataSet({ intl, tenantId, defaultData, instanceId: ticketId })),
      [defaultData, ticketId, tenantId]
    );

    // 工时关联回复列表
    const replayListDataSet = useMemo(
      () => new DataSet(ReplyListDataSet({ intl, intlPrefix, tenantId, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );

    // 工时关联动作记录列表
    const actionListDataSet = useMemo(
      () => new DataSet(ActionListDataSet({ intl, intlPrefix, tenantId, ticketId, businessObjectCode })),
      [ticketId, businessObjectCode]
    );

    // 工时表单必填应用逻辑
    useEffect(() => {
      const requiredFieldList = defaultData.workingTimeSettingVO?.required || [];
      if (requiredFieldList?.length > 0) {
        workLogsDataSet.setState('requiredFieldList', requiredFieldList);
      }
    }, [defaultData, workLogsDataSet]);

    const value = {
      ...props,
      intlPrefix,
      prefixCls,
      tenantId,
      workLogsDataSet,
      replayListDataSet,
      actionListDataSet,
      hasWorkHour,
      urlSuffix,
      defaultData,
      userInfo,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
