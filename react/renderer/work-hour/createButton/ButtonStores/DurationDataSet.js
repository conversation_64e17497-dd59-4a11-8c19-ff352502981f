import { DataSet } from 'choerodon-ui/pro';
import { getQueryParams } from '@zknow/utils';
import { getLovConfig, getLovQueryAxiosConfig } from '@/components/ui-action/stores/selfLovConfig';

export const WorkLogsDataSet = ({ intl, intlPrefix, tenantId, defaultData, instanceId }) => {
  const entryDate = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.enter.date', defaultMessage: '发生时间' });
  const category = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.category', defaultMessage: '工时类别' });
  const type = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.type', defaultMessage: '工时类型' });
  const timeSpent = intl.formatMessage({ id: 'zknow.common.model.duration', defaultMessage: '时长' });
  const note = intl.formatMessage({ id: 'zknow.common.model.remark', defaultMessage: '备注' });
  const assignmentPersonName = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.person.name', defaultMessage: '处理人' });
  const assignmentGroupName = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.group.name', defaultMessage: '处理组' });
  const serviceWay = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.service.way', defaultMessage: '服务方式' });
  const relate = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate', defaultMessage: '关联' });
  const reply = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' });
  const action = intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.action', defaultMessage: '动作' });
  const requiredList = defaultData?.workingTimeSettingVO?.required || [];

  let operatorField = {
    lovCode: 'USER',
    dynamicProps: {
      lovPara: ({ record }) => {
        const id = record?.get?.('operatorGroupId')?.id;
        if (id) {
          return { search_groupId: record.get('operatorGroupId')?.id };
        } else {
          return null;
        }
      },
    },
  };

  let operatorGroupField = {
    lovCode: 'GROUP',
    dynamicProps: {
      lovPara: ({ record }) => {
        const id = record?.get?.('operatorId')?.id;
        if (id) {
          return { search_userId: record.get('operatorId')?.id };
        } else {
          return null;
        }
      },
    },
  };

  // NOTE: 【服务配置-工时设置】中会修改工时的处理人/组的值列表，这边需要使用指定的值列表查询人和组
  //    以满足可以给外部人员填写工时的需求
  if (defaultData?.workingTimeSettingFlag) {
    const lovConfig = getLovConfig(tenantId, instanceId, intl);
    const userLovId = defaultData?.workingTimeSettingVO?.userLovId;
    const groupLovId = defaultData?.workingTimeSettingVO?.groupLovId;
    if (userLovId) {
      operatorField = {
        lovCode: userLovId,
        ...lovConfig,
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const groupId = record?.get('operatorGroupId');
            return groupId ? getLovQueryAxiosConfig({ id: groupId, type: 'USER', tenantId, ticketId: instanceId }) : getLovQueryAxiosConfig({ tenantId, ticketId: instanceId });
          },
        },
      };
    }
    if (groupLovId) {
      operatorGroupField = {
        lovCode: groupLovId,
        ...lovConfig,
        dynamicProps: {
          lovQueryAxiosConfig: ({ record }) => {
            const userId = record?.get('operatorId');
            return userId ? getLovQueryAxiosConfig({ id: userId, type: 'GROUP', tenantId, ticketId: instanceId }) : getLovQueryAxiosConfig({ tenantId, ticketId: instanceId });
          },
        },
      };
    }
  }

  const RelateOptions = new DataSet({
    data: [{ value: 'REPLY', text: reply }, { value: 'ACTION', text: action }],
  });
  return {
    autoCreate: true,
    autoQuery: false,
    autoLocateFirst: true,
    paging: false,
    fields: [
      // 处理人
      {
        name: 'operatorId',
        label: assignmentPersonName,
        required: true,
        type: 'object',
        transformRequest: (value, record) => {
          return value?.id;
        },
        ...operatorField,
      },
      {
        name: 'operatorGroupId',
        label: assignmentGroupName,
        type: 'object',
        required: requiredList.includes('operatorGroupId'),
        transformRequest: (value, record) => {
          return value?.id;
        },
        ...operatorGroupField,
      },
      // 时长
      {
        name: 'duration',
        label: timeSpent,
        required: true,
        defaultValue: 0,
        transformRequest: (value, record) => {
          const hours = record.get('hours') || 0;
          const minutes = record.get('minutes') || 0;
          return hours * 60 + minutes;
        },
      },
      // 发生时间
      {
        name: 'entryDate',
        label: entryDate,
        dynamicProps: {
          required: ({ dataSet, record }) => {
            const required = dataSet.getState('requiredFieldList') || [];
            return required.includes('entryDate') && record.get('category') === 'ACTUAL_WORK_TIME';
          },
        },
      },
      // 工时类别
      {
        name: 'category',
        label: category,
        lookupCode: 'WORK_TIME_CATEGORY',
      },
      // 工时类型
      {
        name: 'type',
        label: type,
        lookupCode: 'WORKLOG_TYPE',
        dynamicProps: {
          required: ({ dataSet }) => {
            const required = dataSet.getState('requiredFieldList') || [];
            return required.includes('type');
          },
        },
      },
      // 服务方式
      {
        name: 'serviceWay',
        label: serviceWay,
        lookupCode: 'SERVICE_WAY',
        dynamicProps: {
          required: ({ dataSet }) => {
            const required = dataSet.getState('requiredFieldList') || [];
            return required.includes('serviceWay');
          },
        },
      },
      // 备注
      { name: 'note', label: note },
      // 时
      {
        name: 'hours',
        required: true,
        validator: async (value, fieldName, record) => {
          if (value > 9999) {
            return intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.minutes.limit', defaultMessage: '超出限制数值' });
          }
          if (!value && value !== 0 && !record?.get('minutes')) {
            return intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.minutes.required', defaultMessage: '请填写工时' });
          }
        },
        transformResponse: (value, data) => {
          if (typeof data?.duration !== 'number') {
            return undefined;
          }
          return Math.floor((data?.duration || 0) / 60);
        },
        dynamicProps: {
          required: ({ record }) => record.get('minutes'),
        },
      },
      // 分
      {
        name: 'minutes',
        required: true,
        validator: async (value, fieldName, record) => {
          if (value > 9999) {
            return intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.minutes.limit', defaultMessage: '超出限制数值' });
          }
          if (!value && value !== 0 && !record?.get('hours')) {
            return intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.minutes.required', defaultMessage: '请填写工时' });
          }
        },
        transformResponse: (value, data) => {
          if (typeof data?.duration !== 'number') {
            return undefined;
          }
          return (data?.duration || 0) % 60;
        },
        dynamicProps: {
          required: ({ record }) => record.get('hours'),
        },
      },
      {
        name: 'relate',
        type: 'string',
        defaultValue: 'REPLY',
        label: relate,
        textField: 'text',
        valueField: 'value',
        options: RelateOptions,
      },
    ],
    events: {
      create: ({ record }) => {
        const defaultCategory = record?.getField('category')?.options?.get(0)?.get('code');
        if (defaultCategory) {
          record?.set('category', defaultCategory);
        }
      },
    },
  };
};

export const ReplyListDataSet = ({ intl, intlPrefix, tenantId, businessObjectCode, ticketId }) => {
  const urlPrefix = `itsm/v1/${tenantId}/journals/journals/workTime`;

  const content = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.content', defaultMessage: '内容' });
  const replyTime = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.reply.time', defaultMessage: '回复时间' });

  return {
    autoQuery: false,
    cacheSelection: true,
    primaryKey: 'id',
    transport: {
      read: ({ data }) => {
        return {
          url: businessObjectCode && ticketId ? `${urlPrefix}?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}&content=` : '',
          method: 'get',
          data: getQueryParams(data, ['ticketId', 'businessObjectCode']),
        };
      },
    },
    queryFields: [{ name: 'content', label: content }],
    fields: [
      { name: 'id' },
      { name: 'content', label: content },
      { name: 'replyTime', label: replyTime },
    ],
  };
};

export const ActionListDataSet = ({ intl, intlPrefix, tenantId, businessObjectCode, ticketId }) => {
  const urlPrefix = `itsm/v1/${tenantId}/journals/action/${ticketId}`;

  const name = intl.formatMessage({ id: 'zknow.common.model.name', defaultMessage: '名称' });
  const creationDate = intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.relate.action.create.time', defaultMessage: '操作时间' });

  return {
    autoQuery: false,
    cacheSelection: true,
    primaryKey: 'id',
    transport: {
      read: ({ data }) => {
        return {
          url: businessObjectCode && ticketId ? `${urlPrefix}?businessObjectCode=${businessObjectCode}` : '',
          method: 'get',
          data: getQueryParams(data, ['ticketId', 'businessObjectCode']),
        };
      },
    },
    queryFields: [{ name: 'name', label: name }],
    fields: [
      { name: 'id' },
      { name: 'name', label: name },
      { name: 'creationDate', label: creationDate },
    ],
  };
};
