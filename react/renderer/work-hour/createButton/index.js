/* eslint-disable no-shadow */
/*
 * @author: Yandif
 * @Description: 创建工时自定义按钮
 */
import { Modal } from 'choerodon-ui/pro';
import { Permission } from '@yqcloud/apps-master';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import React, { useContext, useMemo, useState } from 'react';
import axios from 'axios';
import { Button } from '@zknow/components';
import FormView from '../components/form/FormView';
import Store, { StoreProvider } from './ButtonStores';

const modalKey = Modal.key();

const CreateWorkHourButton = () => {
  const {
    intl,
    config,
    tenantId,
    intlPrefix,
    workLogsDataSet,
    replayListDataSet,
    actionListDataSet,
    businessObjectId,
    ticketId,
    businessObjectCode,
    hasWorkHour,
    urlSuffix,
    tableDataSet,
    viewDataSet,
    formDataSet,
    worklogRefresh = () => { }, // 后台单据刷新数据
    defaultData,
    userInfo,
    AppState,
  } = useContext(Store);
  const { workingTimeSettingFlag, workingTimeSettingVO, addWorkTimeFlag } = defaultData;

  const { icon, name, id, color = 'default' } = config || {};

  const formTitle = intl.formatMessage({ id: 'lcr.components.desc.lc.components.work.hour.create', defaultMessage: '添加工时' });

  const initDefaultData = async (_record) => {
    // 默认时间
    _record?.set('entryDate', new Date());

    const data = formDataSet?.current?.toData() || {};

    const getPersonInGroupWithCache = async (userId, groupId) => {
      if (!userId || !groupId) return false;
      const key = `personInGroup-${userId}-${groupId}`;
      const cache = AppState?.customConfig[key];
      if (cache) {
        return cache;
      }
      const res = axios.get(
        `/iam/yqc/${tenantId}/userGroups/query/whether/in/group?userId=${userId}&groupId=${groupId}`
      );
      AppState?.setCustomConfig(key, res);
      return res;
    };
    // 默认操作人
    _record?.set('operatorId', {
      id: userInfo?.id,
      name: userInfo?.realName,
      realName: userInfo?.realName,
    });
    // 默认操作组
    const inGroup = await getPersonInGroupWithCache(userInfo?.id, data?.assignment_group_id);
    if (inGroup) {
      _record?.set('operatorGroupId', {
        id: data.assignment_group_id,
        groupName: data['assignment_group_id:name'],
        name: data['assignment_group_id:name'],
      });
    }

    if (workingTimeSettingFlag) {
      const { serviceMode, workHoursType } = workingTimeSettingVO || {};
      // 工时类型
      workHoursType && _record?.set('type', workHoursType);
      // 服务方式
      serviceMode && _record?.set('serviceWay', serviceMode);
    }
  };
  const [isAdd, setIsAdd] = useState(false);
  async function openModal() {
    if (isAdd) return;
    setIsAdd(true);
    // const _record = workLogsDataSet.create({});
    // await initDefaultData(_record);
    Modal.open({
      key: modalKey,
      title: formTitle,
      children: (
        <FormView
          tenantId={tenantId}
          intl={intl}
          intlPrefix={intlPrefix}
          replayListDataSet={replayListDataSet}
          actionListDataSet={actionListDataSet}
          workHourTableDataSet={tableDataSet}
          viewDataSet={viewDataSet}
          ticketId={ticketId}
          businessObjectCode={businessObjectCode}
          urlSuffix={urlSuffix}
          businessObjectId={businessObjectId}
        />
      ),
      onClose: () => { worklogRefresh(); },
      style: { width: 800 },
      footer: null,
    });
    setIsAdd(false);
  }
  // 如果没有工时
  if (!hasWorkHour) {
    return null;
  }
  // 应用配置
  if (workingTimeSettingFlag && !addWorkTimeFlag) {
    return null;
  }
  return (
    <Permission service={['add_working_time']}>
      <Button key={id} onClick={openModal} funcType="raised" color={color} icon={icon}>
        {name}
      </Button>
    </Permission>
  );
};

export default inject('AppState')(
  observer((props) => {
    const {
      AppState: { currentLanguage: language },
      instanceId,
      ticketId,
      viewDataSet,
      businessObjectCode: defaultBusinessObjectCode,
      businessObjectId: defaultBusinessObjectId,
    } = props;
    const { businessObjectCode, businessObjectId } = viewDataSet?.current?.toData() || {};

    return (
      <StoreProvider
        {...props}
        ticketId={instanceId || ticketId}
        businessObjectCode={businessObjectCode || defaultBusinessObjectCode}
        businessObjectId={businessObjectId || defaultBusinessObjectId}
      >
        <CreateWorkHourButton />
      </StoreProvider>
    );
  })
);

/* externalize: CreateWorkHourButton */
