/* eslint-disable no-console */
/* eslint-disable react/no-danger */
import QuillDeltaToHtmlConverter from 'quill-delta-to-html';
import React from 'react';
import YqImgView from '@/components/yq-img-view';
import { getQueryString } from '@/utils';
/**
 * @description: 解析自定义富文本插件
 * @param {*} data delta数据格式
 * @return {*} 处理完之后的数据
 */
function parseCustomQuillParse(data) {
  try {
    data.forEach((delta) => {
      if (delta.insert && delta.insert['proc-user']) {
        delta.insert = delta.attributes?.innerHtml;
      }
      if (delta.insert && delta.insert['proc-knowledge']) {
        // delta.insert = delta.attributes?.innerHtml;
        delta.attributes.renderAsBlock = true;
        delta.insert = {
          'proc-knowledge': {
            id: delta.attributes?.atKnowledgeId,
            name: delta.attributes?.innerHtml,
          },
        };
      }
    });
  } catch (e) {
    //
  }
  return data;
}

/**
 * @description: 解析自定义富文本插件
 * @param {*} data delta数据格式
 * @return {*} 获取html
 */
function getHtml(data) {
  let htmlContent;
  try {
    if (Array.isArray(data)) {
      const jsonData = data;
      const converter = new QuillDeltaToHtmlConverter(parseCustomQuillParse(jsonData), {
        customTag: () => {},
      });
      converter.renderCustomWith((customOp, contextOp) => {
        if (customOp.insert.type === 'proc-knowledge') {
          const val = customOp.insert.value;
          const url = `${window.location.origin}/#/itsm/portal/knowledge?tenantId=${getQueryString('tenantId')}&menu=knowledge&knowledgeId=${val.id}`;
          return `<a href="${url}" target="__blank" id="${val.id}">${val.name}</a>`;
        } else {
          return '';
        }
      });
      htmlContent = converter.convert();
    } else {
      htmlContent = data;
    }
  } catch (e) {
    htmlContent = data;
  }
  return htmlContent;
}

// 过滤富文本注入
function escapeHtml(html) {
  return html?.replace(/<\/script/g, '<\\/script')?.replace(/<!--/g, '<\\!--');
}

// 转换delta为html
function deltaToHtml(data, _record) {
  let fieldValue = data;
  let attachmentsList = [];
  console.log('_record ===>', _record);
  try {
    if (typeof data === 'string') {
      fieldValue = JSON.parse(data);
    }
  } catch (e) {
    fieldValue = data;
  }
  try {
    attachmentsList = JSON.parse(_record.get('attachments')) || [];
  } catch (e) {
    attachmentsList = [];
  }
  try {
    if (fieldValue) {
      let resultValue = [];
      let html = '';
      if (typeof fieldValue === 'object') {
        // 2022年4月28日 富文本都是些什么格式？有的有ops  有的没有？？？？？？
        if (fieldValue.ops) {
          fieldValue = fieldValue.ops;
        }
        // 处理图片
        resultValue = fieldValue.map((i) => {
          if (i?.insert?.image) {
            // eslint-disable-next-line no-chinese/no-chinese
            i.insert = '[图片]';
          }
          if (typeof i?.insert === 'string') {
            i.insert = i.insert?.replace(new RegExp('\n', 'gm'), '');
            i.insert = i.insert?.replace(new RegExp('\\n', 'gm'), '');
          }
          return i;
        });
        html = getHtml(Array.from(resultValue));
      } else if (typeof fieldValue === 'string') {
        html = fieldValue;
      } else if (typeof fieldValue === 'number') {
        html = `${fieldValue}`;
      }

      return (
        <YqImgView>
          <span>
            <span
              className="quill-preview-table-line"
              dangerouslySetInnerHTML={{ __html: escapeHtml(html) }}
            />
            {attachmentsList.map((i) => <span>{`[${i.name}]`}</span>)}
          </span>
        </YqImgView>
      );
    } else {
      return attachmentsList.map((i) => <span>{`[${i.name}]`}</span>);
    }
  } catch (e) {
    console.log(e);
  }

  return '';
}

/**
 * 把整数分为指定份数。
 * @param {int} num
 * @param {int} length
 * @example
 * getAverageArr(10,3) //返回 [4,3,3]
 */
function getAverageArr(num, length) {
  const remainVal = num % length;
  const quotientVal = parseInt(num / length, 10);
  const arr = [];
  for (let i = 0; i < length; i++) {
    i < remainVal ? arr.push(quotientVal + 1) : arr.push(quotientVal);
  }
  return arr;
}

export { parseCustomQuillParse, getHtml, deltaToHtml, getAverageArr };
