import React, { useContext } from 'react';
import './workHourHeader.less';
import { observer } from 'mobx-react-lite';
import { Tooltip } from 'choerodon-ui/pro';
import { Icon } from '@zknow/components';
import Store from '../../stores';

function WorkHourHeader() {
  const { intl, intlPrefix, prefixCls, headerState, searchState, addState, defaultData } = useContext(Store);
  const { modeComponent, categoryComponent } = headerState;
  const { addComponent } = addState;
  const { searching, setSearching, searchComponent } = searchState;

  return (
    <div className={`${prefixCls}-header`}>
      <div className="header-top">
        <span className="header-top-title" style={{ display: searching ? 'none' : 'inline' }}>
          {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.title', defaultMessage: '工时' })}
        </span>
        <div className="header-top-icon" style={{ display: searching ? 'none' : 'flex' }}>
          {modeComponent}
          {categoryComponent}
          {defaultData?.workingTimeSettingFlag && addComponent}
          <Tooltip key="search" title={intl.formatMessage({ id: 'zknow.common.placeholder.search', defaultMessage: '搜索' })}>
            <Icon
              className="action action-active"
              type="icon-search"
              size={16}
              onClick={() => setSearching(true)}
            />
          </Tooltip>
        </div>

        {searching && searchComponent}
      </div>
    </div>
  );
}

export default observer(WorkHourHeader);
