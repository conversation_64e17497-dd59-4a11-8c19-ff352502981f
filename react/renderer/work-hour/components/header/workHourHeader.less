@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.lc-components-workHour-header {
  padding: 0 0.16rem;
  .flex-center {
    display: flex;
    align-items: center;
  }

  .header-top {
    justify-content: space-between;
    margin-bottom: 0.11rem;

    .flex-center;

    &-title {
      font-weight: 500;
      font-size: 0.16rem;
      line-height: 0.22rem;
      color: #12274d;
      white-space: nowrap;
    }
    &-icon {
      .flex-center;

      .action {
        width: 0.32rem;
        height: 0.32rem;
        margin-left: 8px;
        padding: 0.08rem;

        white-space: nowrap;

        background: @minor-color;
        border-radius: 0.04rem;
        cursor: pointer;
        opacity: 0.45;
      }
      .action-active-dropdown {
        width: unset;
        display: flex;
        align-items: center;
        color: @primary-color !important;
        border: none;
        background: @minor-color !important;
        opacity: 1;
        font-size: 400 !important;
        transition: all 0.3s;
        &:hover {
          color: @primary-1 !important;

          background-color: @primary-6 !important;
        }
      }

      .action-active {
        color: @primary-color;

        background: @minor-color;
        opacity: 1;
        line-height: 1;
        transition: all 0.3s;
        &:hover {
          color: @primary-1;

          background-color: @primary-6;
        }
      }
    }
  }

  .header-bottom {
    height: 0.22rem;

    font-weight: 500;
    font-size: 0.14rem;
    line-height: 0.22rem;

    .flex-center;
  }
  &-switch {
    .c7n-menu-item {
      color: #12274d;
      &:hover {
        background: #f7f7f7 !important;
      }
    }
  }
}
