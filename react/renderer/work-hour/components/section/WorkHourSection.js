import React, { useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import { Icon, Empty } from '@zknow/components';
import './WorkHourSection.less';
import { durationToDays } from '@/utils';

function WorkHourSection({
  intl,
  intlPrefix,
  prefixCls,
  title, // 标题
  totalWorkTime, // 总计工时
  totalWorkTimeTitle,
  expand,
  setExpand,
  children,
}) {
  const prefix = `${prefixCls}-WorkHourSection`;

  // 根据duration获取多少小时多少分钟
  const getWorkHour = (duration) => {
    const workHour = durationToDays(duration);
    const hours = workHour?.hours
      ? intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.totalHours', defaultMessage: '{hours}小时' }, workHour)
      : '';
    const minutes = workHour?.minutes
      ? intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.totalMinutes', defaultMessage: '{minutes}分钟' }, workHour)
      : '';
    return hours + minutes;
  };

  const totalWorkTimeLine = useMemo(() => {
    if (!totalWorkTime && totalWorkTime !== 0) return null;
    return (
      <div className={`${prefix}-totalWorkTime`}>
        {/* <Icon size={14} type="time" style={{ marginRight: 6 }} /> */}
        <span className={`${prefix}-totalWorkTime-title`}>{totalWorkTimeTitle}</span>
        <span className={`${prefix}-totalWorkTime-date`}>{getWorkHour(totalWorkTime)}</span>
      </div>
    );
  }, [totalWorkTime]);

  const workHourContent = useMemo(() => {
    return (
      <div className={`${prefix}-content`}>
        {(!totalWorkTime && totalWorkTime !== 0) && (
          <Empty
            description={intl.formatMessage({ id: 'zknow.common.model.noData', defaultMessage: '暂无数据' })}
            style={{ padding: '0px' }}
            innerStyle={{ width: '80px', height: '80px' }}
          />
        )}
        {children}
      </div>
    );
  }, [totalWorkTime]);

  return (
    <div className={prefix}>
      <div className={`${prefix}-header`}>
        <span className={`${prefix}-header-preLine`} />
        {title}
        <Icon
          size={16}
          type={expand ? 'DownOne' : 'RightOne'}
          theme="filled"
          onClick={() => {
            setExpand(!expand);
          }}
          style={{ marginLeft: 4, cursor: 'pointer' }}
        />
      </div>
      {expand && (
        <div>
          {totalWorkTimeLine}
          {workHourContent}
        </div>
      )}
    </div>
  );
}

export default observer(WorkHourSection);
