import React, { useEffect, useRef, useState } from 'react';
import axios from 'axios';
import { message } from 'choerodon-ui/pro';
import { observer } from 'mobx-react-lite';
import { inject } from 'mobx-react';
import isEmpty from 'lodash/isEmpty';
import './FormView.less';
import PageLoader from '@/components/page-loader';
import useServiceSetting from '../../../../hooks/useServiceSetting';

function DurationForm(props) {
  const {
    AppState,
    modal,
    tenantId,
    ticketId,
    businessObjectCode,
    intl,
    workHourTableDataSet,
    viewDataSet,
    isEdit,
    workHourId,
    businessObjectId,
    createFunction = () => {},
    replyRecord,
    workTimeRef,
    hiddenRelatedContent = false, // 回复和动作隐藏工时关联内容
    replayListDataSet,
  } = props;
  const [defaultMap, setDefaultMap] = useState(null);
  const url = `/itsm/v1/${tenantId}/working/time?businessObjectCode=${businessObjectCode}`;

  const serverSetting = useServiceSetting(businessObjectId, '', tenantId, AppState);

  const { detailViewId, createViewId } = serverSetting?.workingTimeSettingVO || {};

  const pageRef = useRef({ businessObjectCode });

  // 向工时关联内容组件传递需要的参数，即businessObjectCode和ticketId
  useEffect(() => {
    if (businessObjectCode) {
      pageRef.current = { ...pageRef.current, businessObjectCode, ticketId };
    }
  }, [businessObjectCode]);

  useEffect(() => {
    if (tenantId && createViewId && createViewId && ticketId) {
      queryDefaultData();
    }
  }, [tenantId, createViewId, createViewId, ticketId]);

  // 新添加工时
  const createWorkHour = async (data) => {
    const res = await axios.post(url, data);
    if (res?.failed) {
      // message.error(res?.message);
      return false;
    } else {
      if (res?.[0]._message) {
        message.success(res?.[0]._message);
      }
      // 更新回复上的工时
      viewDataSet?.getState('_replyDataSet')?.query();
      replayListDataSet.setState('defaultSelectId', '');
      await workHourTableDataSet?.query();
      return true;
    }
  };

  // 直接添加工时
  const editWorkHour = async (data) => {
    const res = await axios.post(url, data);
    if (res?.failed) {
      // message.error(res?.message);
      return false;
    }
    if (res?.[0]._message) {
      message.success(res?.[0]._message);
    } else {
      message.success(intl.formatMessage({ id: 'zknow.common.success.modify', defaultMessage: '修改成功' }));
    }
    await workHourTableDataSet?.query();
    viewDataSet?.getState('_replyDataSet')?.query();
    return true;
  };

  modal?.handleOk(async () => {
    const { formDataSet } = pageRef.current;
    const validate = await formDataSet?.current?.validate(true);
    if (!validate) {
      return false;
    }

    // FIXME: 只对【总是（非）必填】有效，无法适配设置条件的情况
    const durationField = pageRef?.current?.pageRecord?.get('jsonData')?.datasets?.[0]?.fields?.find(field => field.code === 'duration');
    const durationRequired = durationField?.requiredType === 'ALWAYS_REQUIRED';
    const durationMode = (durationField?.widgetConfig?.durationMode || []).slice(0);

    if (durationRequired) {
      // 工时必填，如果不填写应该报错，而不是默认为 0
      const duration = formDataSet?.current?.get('duration') || {};

      if (durationMode.map(d => duration[d]).every(item => typeof item !== 'number')) {
        message.error(intl.formatMessage({ id: 'lcr.renderer.desc.duration.required', defaultMessage: '请填写时长' }));
        return false;
      }
    }

    const formData = formDataSet?.current?.toData();
    const idList = formDataSet?.getState('tableList'); // 在工时关联内容组件上setState的
    let data = { ...formData, task_id: ticketId }; // 加上task_id字段
    if (isEdit) {
      data = { ...data, _status: 'update' }; // 对于更新视图，添加_status: 'update'
    }

    // duration 后端使用的是 int 类型，最大能表示的正整数 2 ** 31 -1
    if (typeof data.duration === 'number' && data.duration > (2 ** 31 - 1)) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.desc.duration.limit', defaultMessage: '工时超出最大值' }));
      return false;
    }

    if (idList?.length > 0 && formData?.relate) {
      // 根据和后端的约定，根据工时关联内容组件传来的idList，把data拆成若干份
      data = idList.map((id) => {
        if (formData.relate === 'REPLY') {
          return { ...data, journalId: id };
        } else if (formData.relate === 'ACTION') {
          return { ...data, actionHisId: id };
        }
        return { ...data };
      });
    } else {
      if (replayListDataSet.getState('defaultSelectId')) {
        data.journalId = replayListDataSet.getState('defaultSelectId');
      }
      data = [{ ...data }];
    }
    let resp = true;
    if (!isEdit) {
      /**
       * 特性 yq-21980 【人教社】工单填报工时支持批量添加
       *   由于多选组件使用的是中间表，所以在给后端数据的时候，需要将中间表的对象 id 带上
       *   本来应该带左表的 id，但是前端获取不到的，所以将中间表 id 给后端，后端自行查询
       */
      const multipleInfo = {};
      const sectionFields = pageRef.current?.pageRecord?.get?.('jsonData')?.sections || [];
      sectionFields.forEach((item) => {
        (item?.fields || []).forEach((field) => {
          if (field.widgetType === 'MultipleChoice' && field.widgetConfig?.multipleModelId) {
            multipleInfo[`${field.code}_config`] = {
              // 预留扩展
              multipleModelId: field.widgetConfig.multipleModelId,
            };
          }
        });
      });

      if (!isEmpty(multipleInfo)) {
        data = data.map(d => ({
          ...d,
          ...multipleInfo,
        }));
      }
      resp = await createWorkHour(data);
    } else if (isEdit) {
      resp = await editWorkHour(data);
    }
    return resp;
  });

  const queryDefaultData = async () => {
    const res = await loadExpression();
    setDefaultMap(res);
  };

  const loadExpression = async () => {
    const result = await axios.post(`lc/v1/engine/${tenantId}/dataset/${createViewId}/${createViewId}/calculate`, { task_id: ticketId });
    if (result && !result.failed) {
      return result;
    } else {
      return {};
    }
  };

  if (isEdit && detailViewId) {
    return (
      <PageLoader
        instanceId={workHourId}
        viewId={detailViewId}
        pageRef={pageRef}
        mode="MODIFY"
        openType="MIDDLE"
        hiddenRelatedContent={hiddenRelatedContent}
        autoFocusDisabled
      />
    );
  } else if (createViewId && defaultMap) {
    return (
      <PageLoader
        viewId={createViewId}
        pageRef={pageRef}
        mode="CREATE"
        openType="MIDDLE"
        formDataSetCreated={async (dataSet) => {
          try {
            if (replyRecord) {
              dataSet?.current?.set(replyRecord?.data);
            } else {
              dataSet?.current?.set('category', 'ACTUAL_WORK_TIME');
            }
            workTimeRef.current = { dataSet, pageRef };
          } catch (error) {
            //
          }
        }}
        defaultData={defaultMap}
        hiddenRelatedContent={hiddenRelatedContent}
        disDurationDefaultVal={!!replyRecord}
        autoFocusDisabled
      />
    );
  }
  return null;
}

export default inject('AppState')(observer(DurationForm));

/* externalize: CreateWorkHourRenderer */
