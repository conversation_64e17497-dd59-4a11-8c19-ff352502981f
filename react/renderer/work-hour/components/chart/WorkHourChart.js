/* eslint-disable no-chinese/no-chinese */
import React, { useContext, useMemo } from 'react';
import { observer } from 'mobx-react-lite';
import ReactECharts from 'echarts-for-react';
import Store from '../../stores';
import useChartData from '../../hooks/useChartData';

function WorkHourChart({ Empty = null }) {
  const context = useContext(Store);
  const {
    intl,
    intlPrefix,
    prefixCls,
    workLogsDataSet,
    workHourDataSet,
    headerState: { category, categories },
  } = context;
  const zeroText = intl.formatMessage({ id: `${intlPrefix}.totalMinutes` }, { minutes: 0 });
  // const scheduledWorkTime = workLogsDataSet
  //   ?.getField('category')
  //   ?.options?.find((r) => r?.get('code') === 'SCHEDULED_WORK_TIME')
  //   ?.get('meaning') || intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.plan', defaultMessage: '预估工时' });
  // const actualWorkTime = workLogsDataSet
  //   ?.getField('category')
  //   ?.options?.find((r) => r?.get('code') === 'ACTUAL_WORK_TIME')
  //   ?.get('meaning') || intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.real', defaultMessage: '实际工时' });
  const scheduledWorkTime = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.plan', defaultMessage: '预估工时' });
  const actualWorkTime = intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.real', defaultMessage: '实际工时' });

  const workHourList = workHourDataSet?.current?.get('list');
  const data = useChartData({
    intl,
    workLogsDataSet,
    category,
    categories,
    workHourList,
  });
  const intlHour = (value) => {
    return intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.total.hours', defaultMessage: '{hours}小时' }, { hours: value });
  };

  const seriesLabel = {
    show: false,
    fontSize: 10,
    position: 'right',
    color: 'rgba(18, 39, 77, 0.85)',
    formatter(value) {
      return intlHour(value.value);
    },
  };
  const option = useMemo(() => {
    if (!Array.isArray(data) || data.length === 0) return null;
    const yAxisData = [];
    const scheduledTotalData = [];
    const actualTotalData = [];
    data.forEach((v) => {
      yAxisData.push(v.title);
      scheduledTotalData.push(v.scheduledTotal || null);
      actualTotalData.push(v.actualTotal || null);
    });
    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter(value) {
          return value
            .map((v) => `${v.seriesName}: ${v.data ? `${intlHour(v.data)}` : '-'}`)
            .join('</br>');
        },
      },
      legend: {
        inactiveColor: 'rgba(18,39,77,0.15)',
        textStyle: { fontSize: 14, color: '#12274d' },
        itemGap: 16,
        y: 'bottom',
      },
      grid: {
        left: '16px',
        right: '32px',
        bottom: '36px',
        top: '16px',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        position: 'top',
        boundaryGap: [0, 0.01],
        axisLabel: {
          fontSize: 13,
          height: 18,
          color: 'rgba(18, 39, 77, 0.85)',
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(18, 39, 77, 0.25)',
          },
        },
      },
      yAxis: {
        type: 'category',
        axisTick: { show: false },
        axisLine: { show: true, lineStyle: { color: '#c3c8d2' } },
        data: yAxisData,
        axisLabel: {
          fontSize: 13,
          height: 18,
          color: 'rgba(18, 39, 77, 0.85)',
          formatter(value) {
            const length = intl?.locale === 'zh-CN' ? 10 : 20;
            if (value.length > length) {
              return `${value.slice(0, length)}...`;
            }
            return value;
          },
        },
      },
      series: [
        {
          name: scheduledWorkTime,
          type: 'bar',
          color: '#BFD4FF',
          data: scheduledTotalData,
          barWidth: 12,
          barGap: '10%',
          label: seriesLabel,
        },
        {
          name: actualWorkTime,
          type: 'bar',
          color: '#5B8FF9',
          barWidth: 12,
          data: actualTotalData,
          label: seriesLabel,
        },
      ],
    };
  }, [data]);

  return (
    <div className={`${prefixCls}-chart`}>
      {option ? (
        <ReactECharts
          key={category.name}
          style={{ minHeight: '300px', height: `${(data?.length || 0) * 66}px` }}
          option={option}
        />
      ) : (
        Empty
      )}
    </div>
  );
}

export default observer(WorkHourChart);
