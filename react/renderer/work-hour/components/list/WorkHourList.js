/* eslint-disable no-chinese/no-chinese */
import React, { useMemo, useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { YqAvatar, Icon } from '@zknow/components';
import _ from 'lodash';
import AvatarTooltip from '@/components/avatar-tooltip';
import Store from '../../stores';
import WorkHourListItem from './WorkHourListItem';
import useListData from '../../hooks/useListData';
import './WorkHourList.less';

function WorkHourList({ isScheduled, expandMap, setExpandMap }) {
  const context = useContext(Store);
  const {
    intl,
    workLogsDataSet,
    workHourDataSet,
    headerState: { category, categories },
  } = context;
  const workHourList = workHourDataSet?.current?.get('list');
  const { scheduledWorkTime, actualWorkTime } = useListData({
    intl,
    workLogsDataSet,
    category,
    categories,
    workHourList,
  });
  const data = isScheduled ? scheduledWorkTime : actualWorkTime;
  // 工时按照创建时间倒叙
  const dataAfterOrder = _.orderBy(data, (o) => o.items[o.items.length - 1].creationDate, 'desc');
  return dataAfterOrder?.map((v) => (
    <List key={v?.key} data={v} expandMap={expandMap} setExpandMap={setExpandMap} />
  ));
}

export default observer(WorkHourList);

const List = observer(({ data, expandMap, setExpandMap }) => {
  const {
    intl,
    prefixCls,
    getWorkHour,
    headerState: { category },
  } = useContext(Store);
  const { key, title, subTitle, img, items } = data;
  const defaultValue = expandMap[key] || false;
  const [expand, setExpanded] = useState(defaultValue);
  const total = items.reduce((pre, current) => {
    return (current?.duration || 0) + pre;
  }, 0);

  // 六种维度组件
  const avatars = {
    assignmentPerson: (
      <AvatarTooltip id={data?.operatorId}>
        <YqAvatar src={img} size={26}>
          {title}
        </YqAvatar>
      </AvatarTooltip>
    ),
    assignmentGroup: <Icon type="PeoplesTwo" size="16" fill="#fff" />,
    type: <Icon type="FinancingOne" size="16" fill="#fff" />,
    serviceWay: <Icon type="Delivery" size="16" fill="#fff" />,
    enterDate: <Icon type="CalendarThree" size="16" fill="#fff" />,
    actionReply: <Icon type="Delivery" size="16" fill="#fff" />,
  };
  const avatar = avatars[category.name];

  return (
    <div className={`${prefixCls}-list`}>
      <div className={`${prefixCls}-list-header`} style={{ marginBottom: expand ? '-4px' : '16px' }}>
        <div className={`${prefixCls}-list-header-avatar`}>{avatar}</div>
        <div className={`${prefixCls}-list-header-title`}>{title}</div>
        {subTitle && <div className={`${prefixCls}-list-header-subTitle`}>{subTitle}</div>}
        <div className={`${prefixCls}-list-header-time`}>{getWorkHour(total)}</div>
        <div
          className={`${prefixCls}-list-header-toggle`}
          onClick={() => {
            const flag = !expand;
            setExpanded(flag);
            setExpandMap({ ...expandMap, [key]: flag });
          }}
        >
          {expand ? intl.formatMessage({ id: 'zknow.common.button.up', defaultMessage: '收起' }) : intl.formatMessage({ id: 'zknow.common.button.expand', defaultMessage: '展开' })}
        </div>
      </div>
      <div className={`${prefixCls}-list-content`}>
        {expand && <WorkHourListItem items={items} category={category.name} />}
      </div>
    </div>
  );
});
