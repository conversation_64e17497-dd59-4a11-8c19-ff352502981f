/* eslint-disable no-chinese/no-chinese */
import { Permission } from '@yqcloud/apps-master';
import { Icon } from '@zknow/components';
import { Popconfirm } from 'choerodon-ui';
import { message } from 'choerodon-ui/pro';
import React, { useContext, useState } from 'react';
import { observer } from 'mobx-react-lite';
import axios from 'axios';
import Store from '../../stores';
import './WorkHourListItem.less';
import { deltaToHtml } from '../../util';

const WorkHourListItem = observer(({ item, category }) => {
  const {
    intl,
    intlPrefix,
    tenantId,
    businessObjectCode,
    workHourDataSet,
    viewDataSet,
    getWorkHour,
    defaultData,
    workLogsDataSet,
    AppState: { userInfo },
    openEditModal,
    formDataSet,
  } = useContext(Store);
  const typeMap = workLogsDataSet?.getField('type')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const serviceWayMap = workLogsDataSet?.getField('serviceWay')?.lookup?.reduce((pre, cur) => {
    pre[cur.code] = cur.meaning;
    return pre;
  }, {});
  const { workingTimeSettingFlag, deleteWorkTimeFlag, editWorkTimeFlag } = defaultData;
  const {
    id,
    duration,
    journalContent,
    actionName,
    creationDate,
    entryDate,
    type,
    serviceWay,
    note,
    objectVersionNumber,
    personName,
    groupName,
    operatorId,
    createdBy,
  } = item;

  const [visiable, setVisiable] = useState(false);
  const handleDelete = async () => {
    const res = await axios.put(
      `itsm/v1/${tenantId}/incident_working_time/workspace/incident/id?businessObjectCode=${businessObjectCode}`,
      {
        id,
        objectVersionNumber,
        enabledFlag: false,
      }
    );

    if (!res.failed) {
      message.success(intl.formatMessage({ id: 'zknow.common.success.delete', defaultMessage: '删除成功' }));
      workHourDataSet.query();
      viewDataSet?.getState('_replyDataSet')?.query();
    } else {
      message.error(res?.message);
    }
  };

  const deleteLine = () => {
    if (!formDataSet?.getState('serviceSettingPermission')?.workingTime?.canDelete) return null;
    const content = (
      <div className="main-item-right-actions-delete">
        <Popconfirm
          getPopupContainer={() => document.getElementById(`hour-${id}`)}
          visible={visiable}
          onVisibleChange={(v) => {
            setVisiable(v);
          }}
          title={intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.confirm', defaultMessage: '确认删除吗?' })}
          onConfirm={handleDelete}
        >
          <Icon type="delete" style={{ marginRight: 4 }} />
        </Popconfirm>
      </div>
    );
    // 如果操作人是当前用户，可以删除
    if (userInfo?.id === operatorId || userInfo?.id === createdBy) {
      return content;
    }
    if (workingTimeSettingFlag) {
      if (deleteWorkTimeFlag) {
        return content;
      }
      return null;
    }

    return <Permission service={['add_working_time']}>{content}</Permission>;
  };

  const openModal = () => {
    openEditModal({
      ...item,
      operatorGroupId: {
        id: item.operatorGroupId,
        group_name: groupName,
        name: groupName,
        groupName,
      },
      operatorId: {
        id: item.operatorId,
        realName: personName,
        real_name: personName,
        name: personName,
      },
    });
  };

  const editLine = () => {
    if (!formDataSet?.getState('serviceSettingPermission')?.workingTime?.canEdit) return null;
    if (!workingTimeSettingFlag) return null;
    const content = (
      <div className="main-item-right-actions-edit" onClick={openModal}>
        <Icon type="write" style={{ marginRight: 4 }} />
      </div>
    );
    // 如果操作人是当前用户，可以编辑
    if (userInfo?.id === operatorId || userInfo?.id === createdBy) {
      return content;
    }
    // 如果配置了服务配置，就走服务配置。
    if (workingTimeSettingFlag) {
      if (editWorkTimeFlag) {
        return content;
      }
      return null;
    }
    // 否则走permission code
    return <Permission service={['add_working_time']}>{content}</Permission>;
  };

  return (
    <div className="main-item">
      <div className="main-item-left">
        <div className="main-item-left-dot" />
        <div className="main-item-left-line" />
      </div>
      <div
        className="main-item-right"
        id={`hour-${id}`}
        onMouseLeave={() => {
          setVisiable(false);
        }}
      >
        <div className="main-item-right-actions">
          {deleteLine()} {editLine()}
        </div>
        <div className="main-item-right-duration">
          <Icon type="time" style={{ marginRight: 4 }} />
          {getWorkHour(duration || 0)}
        </div>
        {[
          journalContent && (
            <div className="main-item-right-reply">
              <div className="main-item-right-reply-title">
                <div className="reply-line" />
                {intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.reply', defaultMessage: '回复' })}：
              </div>
              {deltaToHtml(journalContent)}
            </div>
          ),
          category !== 'actionReply' && actionName && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.components.model.lc.components.work.hour.relate.action', defaultMessage: '动作' })}：{actionName}
            </div>
          ),
          category !== 'assignmentPerson' && category !== personName && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.person.name', defaultMessage: '处理人' })}：{personName}
            </div>
          ),
          category !== 'assignmentGroup' && groupName && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.assignment.group.name', defaultMessage: '处理组' })}：{groupName}
            </div>
          ),
          category !== 'serviceWay' && serviceWay && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.service.way', defaultMessage: '服务方式' })}：{serviceWayMap[serviceWay]}
            </div>
          ),
          category !== 'type' && type && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.type', defaultMessage: '工时类型' })}：{typeMap[type]}
            </div>
          ),
          creationDate && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.desc.lc.components.work.hour.creation.date', defaultMessage: '登记时间' })}：{creationDate}
            </div>
          ),
          category !== 'entryDate' && entryDate && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'lcr.renderer.model.lc.components.work.hour.enter.date', defaultMessage: '发生时间' })}：{entryDate}
            </div>
          ),
          note && (
            <div className="main-item-right-item">
              {intl.formatMessage({ id: 'zknow.common.model.remark', defaultMessage: '备注' })}：{deltaToHtml(note)}
            </div>
          ),
        ]}
      </div>
    </div>
  );
});

export default function WorkHourListItems({ items, category }) {
  return (
    <div className="lc-components-workHour-list-item">
      {items?.map((item) => {
        return <WorkHourListItem key={item.id} item={item} category={category} />;
      })}
    </div>
  );
}
