@import '~choerodon-ui/lib/style/themes/default';

.lc-components-workHour-list {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 27px;
    left: 13px;

    display: inline-block;
    width: 1px;
    height: 100%;

    background: rgba(203, 210, 220, 0.65);
  }
  &:last-child::before {
    display: none;
  }

  &-header {
    position: relative;

    display: flex;
    align-items: center;

    margin: 0.08rem -0.16rem 0.2rem -0.16rem;
    padding: 0.04rem 0.16rem;
    &:hover {
      background: rgba(203, 210, 220, 0.2);
      .lc-components-workHour-list-header-toggle {
        display: block;
      }
    }
    &-avatar {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 26px;
      height: 26px;
      margin-right: 0.04rem;

      background: #3e7afd;
      border-radius: 50%;
    }
    &-title {
      margin-right: 0.08rem;

      font-weight: 500;
      color: #12274d;
      white-space: nowrap;
    }
    &-subTitle {
      margin-right: 0.08rem;
      overflow: hidden;

      color: rgba(18, 39, 77, 0.65);
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &-time {
      color: @primary-color;
      white-space: nowrap;
    }
    &-toggle {
      display: none;
      position: absolute;
      right: 0.16rem;

      padding: 0 2px;

      color: @primary-color;
      white-space: nowrap;
      cursor: pointer;
    }
  }
  &-content {
    padding-left: 0.36rem;
  }
}
