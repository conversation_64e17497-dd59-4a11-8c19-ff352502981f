.lc-components-workHour-list-item {
  width: 100%;
  margin-top: 0.04rem;
  .main-item:last-child {
    .main-item-left-line {
      display: none;
    }
  }
  .main-item {
    display: flex;
    &-left {
      position: relative;
      &-dot {
        width: 0.09rem;
        height: 0.09rem;
        margin: 0.14rem 0.04rem 0 0.03rem;

        background: #fff;
        border: 0.02rem solid #3fd8d2;
        border-radius: 50%;
      }
      &-line {
        position: absolute;
        top: 0.23rem;
        right: 0.08rem;

        height: calc(100% - 0.1rem);

        border-left: 0.01rem dashed #d9d9d9;
      }
    }
    &-right {
      position: relative;

      width: 100%;
      padding: 0.08rem;
      &:hover {
        background: #f2f3f5;
        border-radius: 0.04rem;
        .main-item-right-reply {
          background: #f2f3f5;
        }
        .main-item-right-actions {
          display: flex;
        }
      }
      .c7n-popover-inner-content {
        min-width: 1.5rem;
      }

      &-actions {
        position: absolute;
        top: 0.08rem;
        right: 0.12rem;

        display: none;
        align-items: center;
        padding: 8px 0;
        &-delete {
          display: inline-block;
          display: flex;
          align-items: center;
          width: 16px;
          height: 16px;
          margin-right: 12px;

          color: rgba(18, 39, 77, 0.85);

          cursor: pointer;
        }
        &-edit {
          display: inline-block;
          display: flex;
          align-items: center;
          width: 16px;
          height: 16px;

          color: rgba(18, 39, 77, 0.85);

          cursor: pointer;
        }
      }

      &-duration {
        display: flex;
        align-items: center;
        margin-bottom: 0.04rem;

        font-weight: 500;
        color: #12274d;
      }

      &-reply {
        display: flex;
        // align-items: center;
        // height: 0.32rem;
        margin: 0 -0.08rem 0.04rem -0.08rem;
        padding: 0.06rem 0.08rem;
        // overflow: hidden;

        background: #f2f7ff;
        border-radius: 0.08rem;
        & > div:nth-child(2) {
          overflow: auto;
          max-height: 200px;
        }
        &-title {
          position: relative;

          padding-left: 0.06rem;

          font-size: 0.12rem;
          line-height: 0.2rem;
          color: rgba(18, 39, 77, 0.65);
          white-space: nowrap;
          .reply-line {
            position: absolute;
            top: 0.03rem;
            left: 0;

            display: inline-block;
            width: 0.02rem;
            height: 0.14rem;
            margin-right: 0.03rem;

            background: rgba(18, 39, 77, 0.45);
            border-radius: 0.01rem;
          }
        }
        br {
          // 也可以把html中的br元素替换掉
          display: none;
        }

        p {
          width: 100%;
          margin-bottom: 0;
          overflow: hidden;

          font-size: 0.12rem;
          line-height: 0.22rem;
          color: rgba(18, 39, 77, 0.65);
          text-overflow: ellipsis;
        }
        img {
          max-width: 100%;
          height: auto;
        }
      }

      &-item {
        margin-bottom: 0.04rem;

        font-size: 0.12rem;
        line-height: 0.2rem;
        color: rgba(18, 39, 77, 0.65);
        div {
          display: inline;
        }
      }
    }
  }
}
