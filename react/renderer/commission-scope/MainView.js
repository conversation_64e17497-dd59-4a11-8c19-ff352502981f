import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Select } from 'choerodon-ui/pro';
import Store from './stores';

function MainView() {
  const {
    formDataSet,
    principalOrgFlag,
  } = useContext(Store);

  // 租户开启批量委托才显示委托范围
  if (!principalOrgFlag) {
    return null;
  }

  // 视图需要拖入这个字段并隐藏，在这边显示
  return (
    <Form dataSet={formDataSet}>
      <Select name="principal_scope_type" />
    </Form>
  );
}

export default observer(MainView);
