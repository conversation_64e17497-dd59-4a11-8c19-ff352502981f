import { inject } from 'mobx-react';
import React, { createContext } from 'react';
import { injectIntl } from 'react-intl';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(
  inject('AppState', 'HeaderStore')((props) => {
    const {
      intl,
      children,
      HeaderStore: {
        getTenantConfig: { principalOrgFlag },
      },
      formDataSet,
    } = props;

    const value = {
      ...props,
      intl,
      formDataSet,
      principalOrgFlag,
    };

    return <Store.Provider value={value}>{children}</Store.Provider>;
  })
);
