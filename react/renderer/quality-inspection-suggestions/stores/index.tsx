import React, { createContext, useMemo } from 'react';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { DataSet } from 'choerodon-ui/pro';
import { FieldDataSet } from './DataSet';

type ServiceAgreementProps = {
  intl: any,
  history: any,
  id: string,
  prefixCls: string,
  tenantId?: string,
  path?: string,
  match?: any,
  formConfig?: any,
  children?: any,
  AppState: any,
  formDataSet: any,
  viewDataSet: any,
  goalsDataSet: any,
  ticketId: string,
  viewRecord: any,
  mode?: string,
  pageRef?: any,
  HeaderStore?: any,
  setRefreshShoppingCartCount?: any,
  shoppingCartFlag?: any,
  suggestionsField?: any,
  businessObjectCode?: any,
  promptTemplateId?: any,
  promptTemplateCode?: any,
};

const Store = createContext<ServiceAgreementProps>({
  intl: undefined,
  history: undefined,
  id: '',
  prefixCls: '',
  tenantId: '',
  AppState: '',
  formDataSet: '',
  viewDataSet: '',
  goalsDataSet: '',
  ticketId: '',
  viewRecord: '',
  HeaderStore: '',
  setRefreshShoppingCartCount: undefined,
  shoppingCartFlag: false,
  suggestionsField: [],
  businessObjectCode: '',
  promptTemplateId: '',
  promptTemplateCode: '',
});

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  observer((props: ServiceAgreementProps) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      HeaderStore: { getTenantConfig: { enableChatGptFlag, gptTenantFlag } },
      formConfig,
      formDataSet,
      viewDataSet,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
    } = props;
    const prefixCls = 'quality-inspection-suggestions';
    const ticketId = formDataSet && formDataSet.current && formDataSet.current.get('id');
    const { businessObjectCode, id: viewId } = (viewDataSet && viewDataSet.current && viewDataSet.current.toData()) || {};
    const suggestionsField = viewRecord && viewRecord.get('widgetConfig.suggestionsField') && JSON.parse(viewRecord.get('widgetConfig.suggestionsField'));
    const promptTemplateId = viewRecord && viewRecord.get('widgetConfig.promptTemplateId');
    const promptTemplateCode = viewRecord && viewRecord.get('widgetConfig.promptTemplateObj.code');
    const fieldDataSet = useMemo(() => new DataSet(FieldDataSet({ data: suggestionsField })), [suggestionsField]);

    const value = {
      ...props,
      formDataSet,
      viewDataSet,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      businessObjectCode,
      viewRecord,
      pageRef,
      HeaderStore,
      shoppingCartFlag,
      suggestionsField,
      promptTemplateId,
      fieldDataSet,
      viewId,
      enableChatGptFlag,
      gptTenantFlag,
      promptTemplateCode,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
