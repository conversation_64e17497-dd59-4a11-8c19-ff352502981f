import React, { useContext, useRef, useEffect, useState } from 'react';
// @ts-ignore
import { observer } from 'mobx-react-lite';
import lodashGet from 'lodash/get';
import { Permission } from '@yqcloud/apps-master';
import { Icon } from '@zknow/components';
import { message } from 'choerodon-ui/pro';
import Store from './stores';
import style from './index.module.less';
import { queryQualityInspectionContent, updateQualityInspectionContent, getAiSummaryAsync, getAiSummaryPoll } from '@/service';
import AiTag from '@/components/ai-tag';

const MainView = observer(() => {
  const context = useContext(Store);
  const {
    intl,
    prefixCls,
    ticketId,
    formDataSet,
    tenantId,
    businessObjectCode,
    promptTemplateId,
    fieldDataSet,
    viewId,
    enableChatGptFlag,
    gptTenantFlag,
    promptTemplateCode,
  } = context;

  const timer = useRef<any>();
  const count = useRef(30);
  const [suggestionLength, setSuggestionLength] = useState(0);
  const [hover, setHover] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setCacheSuggestions();
    return () => {
      clearInterval(timer.current);
      timer.current = null;
    };
  }, [fieldDataSet, ticketId]);

  const setCacheSuggestions = async () => {
    try {
      if (!fieldDataSet || !ticketId) return;
      const res = await queryQualityInspectionContent({ tenantId, ticketId });
      if (res && !res.failed) {
        const changedParams = JSON.parse(res.jsonData || '[]');
        const changedParamsArr = Object.keys(changedParams);
        changedParamsArr.forEach(v => {
          if (v === promptTemplateCode) {
            const data = JSON.parse(changedParams[v]);
            const dataArr = Object.keys(data);
            dataArr.forEach(i => {
              const index = fieldDataSet.findIndex(m => m.get('code') === i);
              if (index >= 0)fieldDataSet.get(index).set('suggestion', data[i]);
            });
            setSuggestionLength(dataArr.length);
          } else {
            const index = fieldDataSet.findIndex(m => m.get('code') === v);
            if (index >= 0)fieldDataSet.get(index).set('suggestion', changedParams[v]);
            setSuggestionLength(changedParamsArr.length);
          }
        });
      }
    } catch (error) {
      //
    }
  };

  const handleGetSuggestions = async () => {
    if (loading) return;
    setLoading(true);
    if (!promptTemplateId) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.template.empty', defaultMessage: '未配置模板，请联系管理员' }));
      return false;
    }
    getAiSummaryAsync({ tenantId, businessObjectCode, aiPromptId: promptTemplateId, ticketId }).then(result => {
      const uuid = typeof result === 'string' ? result : '';

      if (uuid) {
        timer.current = setInterval(() => {
          if (count.current === 0) {
            clearInterval(timer.current);
          } else {
            count.current -= 1;
            getAiSummaryPoll({ tenantId, uuid }).then(async resp => {
              if (resp && !resp?.failed) {
                let length = 0;
                const changedParams = lodashGet(resp, 'changedParams', {});
                const changedParamsArr = Object.keys(changedParams);
                changedParamsArr.forEach(v => {
                  if (v === promptTemplateCode) {
                    try {
                      const data = JSON.parse(changedParams[v]);
                      const dataArr = Object.keys(data);
                      dataArr.forEach(i => {
                        const index = fieldDataSet.findIndex(m => m.get('code') === i);
                        if (index >= 0)fieldDataSet.get(index).set('suggestion', data[i]);
                        length += 1;
                      });
                    } catch (error) {
                      //
                    }
                  } else {
                    const index = fieldDataSet.findIndex(m => m.get('code') === v);
                    if (index >= 0)fieldDataSet.get(index).set('suggestion', changedParams[v]);
                  }
                });
                setSuggestionLength(length);
                const data = {
                  taskId: ticketId,
                  jsonData: JSON.stringify(changedParams),
                  aiPromptId: promptTemplateId,
                  viewId,
                };
                setLoading(false);
                await updateQualityInspectionContent({ tenantId, data });
                clearInterval(timer.current);
              }
            });
          }
        }, 5 * 1000);
      } else {
        setLoading(false);
      }
    });
  };

  const handleWriteInForm = (r) => {
    const code = r.get('code');
    const suggestion = r.get('suggestion');
    formDataSet?.current?.set(code, suggestion);
    formDataSet?.current?.setState('qualityField', { [code]: suggestion });
    r.setState(`${code}Adopt`, true);
    message.success(intl.formatMessage({ id: 'lcr.renderer.qualityInspection.write.success' }));
  };

  const rendererField = (r) => {
    const code = r.get('code');
    const adoptFlag = r.getState(`${code}Adopt`);
    const fieldValue = formDataSet?.current?.get(code) === r.get('suggestion') || formDataSet?.current?.get(code)?.includes?.(r.get('suggestion'));
    return <div className={style.field}>
      <div className={style.fieldName}>{r?.get('name')}</div>
      {r.get('suggestion') && <div className={style.fieldSuggestions}>
        <span>{intl.formatMessage({ id: 'lcr.renderer.qualityInspection.label' })}</span>
        <span className={style.fieldSuggestionsText}>{r.get('suggestion')}</span>
      </div>}
      {r.get('suggestion') && !adoptFlag && <Permission service={['yqcloud-ticket.ticket-check.saveSuggestions']}>
        <div className={style.write} onClick={() => handleWriteInForm(r)}>{intl.formatMessage({ id: 'lcr.renderer.qualityInspection.write' })}</div>
      </Permission>}
      {r.get('suggestion') && adoptFlag && fieldValue && <div className={style.adopt}><Icon type="check-one" theme="filled" className={style.adoptIcon} /><span>{intl.formatMessage({ id: 'lcr.renderer.qualityInspection.adopt' })}</span></div>}
    </div>;
  };

  const getTagName = () => {
    if (loading) {
      return intl.formatMessage({ id: 'lcr.renderer.qualityInspection.creating' });
    } else if (hover || suggestionLength === 0) {
      return intl.formatMessage({ id: 'lcr.renderer.qualityInspection.create' });
    } else {
      return `${suggestionLength} ${intl.formatMessage({ id: 'lcr.renderer.qualityInspection.number' })}`;
    }
  };

  const renderMain = () => {
    return (
      <div className={style[`${prefixCls}`]}>
        <div className={style[`${prefixCls}-header`]}>
          <span className={style[`${prefixCls}-header-left`]}>{intl.formatMessage({ id: 'lcr.renderer.qualityInspection.suggestions' })}</span>
        </div>
        <div className={style[`${prefixCls}-content`]}>
          <div className={style[`${prefixCls}-content-title`]}>
            <div className={style[`${prefixCls}-content-title-left`]}>{intl.formatMessage({ id: 'lcr.renderer.qualityInspection.ticket' })}</div>
            {promptTemplateId && enableChatGptFlag && gptTenantFlag && <Permission service={['yqcloud-ticket.ticket-check.saveSuggestions']}>
              <div onMouseEnter={() => { setHover(true); }} onMouseLeave={() => { setHover(false); }}>
                <AiTag name={getTagName()} onClick={handleGetSuggestions} />
              </div>
            </Permission>}
          </div>
          <div className={style[`${prefixCls}-content-form`]}>
            {fieldDataSet?.map(i => rendererField(i))}
          </div>
        </div>
      </div>
    );
  };

  return renderMain();
});

export default MainView;
