import React, { useContext } from 'react';
import { observer } from 'mobx-react-lite';
import { Form, Lov } from 'choerodon-ui/pro';
import classnames from 'classnames';
import Store from './stores';
import styles from './index.module.less';

const CartAuth = observer(() => {
  const { principalDataSet, widgetConfig, serviceItemId, labelWidth } = useContext(Store);
  const { editableFlag, visible } = widgetConfig || {};

  if (!serviceItemId) {
    return null;
  }

  return (
    <Form
      dataSet={principalDataSet}
      disabled={!editableFlag}
      labelWidth={labelWidth}
      className={classnames({ [styles.hidden]: !visible })}
    >
      <Lov name="_principal_person_id" />
    </Form>
  );
});

export default CartAuth;

/* externalize: ShoppingCartPrincipalRenderer */
