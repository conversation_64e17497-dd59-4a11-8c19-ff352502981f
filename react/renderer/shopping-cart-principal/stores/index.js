import React, { createContext, useMemo, useEffect, useState } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import qs from 'qs';
import axios from 'axios';
import PrincipalDataSet from './principalDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  observer((props) => {
    const {
      intl,
      children,
      AppState: { currentMenuType: { organizationId: tenantId }, userInfo: { personId } },
      formDataSet,
      pageRef,
      name,
      widgetConfig = {},
    } = props;
    const prefixCls = 'multiple-choice-renderer';
    const serviceItemDetailDataSet = pageRef?.current?.serviceItemDetailDataSet;
    const urlObj = qs.parse(window.location.href.split('?')[1]) || {};
    const { assignedUserId } = urlObj;
    const serviceItemId = serviceItemDetailDataSet?.current?.get('id');
    const { required } = widgetConfig;

    const principalDataSet = useMemo(() => new DataSet(PrincipalDataSet({
      tenantId,
      intl,
      serviceItemDetailDataSet: pageRef?.current?.serviceItemDetailDataSet,
      pageRef,
      name,
      required,
      formDataSet,
    })), [serviceItemId]);

    async function getPrincipalPermission() {
      if (!pageRef?.current?.serviceItemDetailDataSet?.current?.get('id')) {
        return;
      }
      const principalPersonId = assignedUserId || personId;
      const res = await axios.get(`/itsm/v1/${tenantId}/principalConfig/authCommitPersonList?principalPersonId=${principalPersonId}&itemId=${pageRef?.current?.serviceItemDetailDataSet?.current?.get('id')}`);
      if (res && !res.failed) {
        const { id, realName } = res.content[0] || {};
        principalDataSet?.current.set('_principal_person_id', id ? { id, realName } : null);
        serviceItemDetailDataSet?.current.setState('_principal_person_id', id);
        if (required) {
          serviceItemDetailDataSet?.current.setState('principalDataSet', principalDataSet);
        }
      }
    }

    useEffect(() => {
      getPrincipalPermission();
    }, [assignedUserId, personId, serviceItemId]);

    const value = {
      ...props,
      formDataSet,
      prefixCls,
      principalDataSet,
      serviceItemId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  }),
));
