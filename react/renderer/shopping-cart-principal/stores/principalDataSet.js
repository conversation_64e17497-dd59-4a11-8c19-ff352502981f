export default ({ intl, serviceItemDetailDataSet, name: label, required, formDataSet, pageRef }) => {
  return {
    autoQuery: false,
    selection: false,
    paging: false,
    autoCreate: true,
    transport: {
    },
    fields: [{
      name: '_principal_person_id',
      type: 'object',
      lovCode: 'PRINCIPAL_PERSON_LIST',
      label: label || intl.formatMessage({ id: 'lcr.renderer.model.cart.principal.principal', defaultMessage: '委托人' }),
      dynamicProps: {
        disabled: () => !pageRef?.current?.serviceItemDetailDataSet?.current?.get('id'),
        lovPara: () => ({
          itemId: pageRef?.current?.serviceItemDetailDataSet?.current?.get('id'),
          cartFlag: true,
        }),
      },
      required,
    }],
    events: {
      update: ({ name, value }) => {
        if (name === '_principal_person_id') {
          pageRef?.current?.serviceItemDetailDataSet?.current.setState('_principal_person_id', value?.id);
          // 西门子有个需要，需要使用 submitted_by 这个固定字段，做 UI 规则
          formDataSet?.current?.set?.('submitted_by', value?.id);
        }
      },
    },
  };
};
