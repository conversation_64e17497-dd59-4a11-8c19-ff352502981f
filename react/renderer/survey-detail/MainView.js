/*
 * @Author: xiaoreya
 * @Date: 2022-01-24 19:41:53
 * @Description:
 */
import React, { useState, useEffect, useContext, useMemo, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Modal, message } from 'choerodon-ui/pro';
import { Button, Empty } from '@zknow/components';
import CardWrapper from '@/components/card-wrapper';
import DetailView from '@/renderer/survey-list/components/survey-card/DetailView';
import Store from './stores';
import './index.less';

const modalKey = Modal.key();

const SurveyDetail = observer(() => {
  const context = useContext(Store);
  const {
    formDataSet,
    intl,
    viewRecord,
    prefixCls,
    surveyInstantcesDataSet,
    ticketId,
    configDataSet,
    surveyDetailDataSet,
    displayMode = 'CARD',
    viewId,
  } = context;
  const [show, setShow] = useState(true);
  const surveyRef = useRef(null);

  useEffect(() => {
    if (formDataSet?.status === 'ready' && !!ticketId) {
      const readFlag = viewRecord.get('widgetConfig.surveyViewFlag');
      const relatedPersonField = viewRecord.get('widgetConfig.respondent')?.[0]?.id;
      const relatedPersonFieldPath = viewRecord.get('widgetConfig.respondent')?.[0]?.path;
      surveyInstantcesDataSet.setQueryParameter('relatedPersonField', relatedPersonField);
      surveyInstantcesDataSet.setQueryParameter('relatedPersonFieldPath', relatedPersonFieldPath);
      surveyInstantcesDataSet.setQueryParameter('readFlag', readFlag);
      surveyInstantcesDataSet.query();
    }
  }, [formDataSet?.status, ticketId]);

  function refresh(id) {
    configDataSet.setQueryParameter('id', id);
    configDataSet.query().then(() => {
      configDataSet.current = configDataSet.get(0);
    });
    surveyDetailDataSet.setQueryParameter('id', id);
    surveyDetailDataSet.query().then(() => {
      surveyDetailDataSet.current = surveyDetailDataSet.get(0);
      setShow(false);
      setShow(true);
    });
  }

  useEffect(() => {
    const id = surveyInstantcesDataSet?.current?.get('id');
    if (id) {
      refresh(id);
      // 由于视图和动作集的执行是分开的，所以动作中的调查提交后无法更新调查详情的页面，所以需要向外抛出一个刷新的页面
      //   FIXME： 这个办法只能适用于视图中的【调查详情】唯一的情况，如果有多个，则只会更新最后挂载的一个
      if (formDataSet) {
        formDataSet.setState('surveyDetailRefresh', () => {
          refresh(id);
        });
      }
    }
  }, [surveyInstantcesDataSet?.current?.get('id')]);

  function renderEmpty() {
    return (
      <div className={`${prefixCls}-header-top`}>
        <div className={`${prefixCls}-header-top-title`}>{viewRecord?.get('name')}</div>
        <Empty
          description={intl.formatMessage({ id: 'zknow.common.model.noData' })}
          style={{ padding: '0px', paddingTop: '36px' }}
          type="empty"
        />
      </div>
    );
  }

  const buttons = useMemo(() => {
    return (
      <>
        <Button
          key="submit"
          funcType="raised"
          color="primary"
          onClick={async () => {
            const res = await surveyRef?.current.submit();
            if (res === 'validate') {
              message.error(intl.formatMessage({ id: 'zknow.common.validate.failed' }));
              return false;
            } else if (res === false) {
              message.error(intl.formatMessage({ id: 'lcr.renderer.survey.save.failed' }));
              return false;
            }
            const id = surveyInstantcesDataSet?.current?.get('id');
            if (id) {
              refresh(id);
            }
          }}
        >
          {intl.formatMessage({ id: 'zknow.common.button.save' })}
        </Button>
        <Button
          key="save"
          funcType="raised"
          onClick={async () => {
            const res = await surveyRef?.current.save();
            if (res === 'validate') {
              message.error(intl.formatMessage({ id: 'zknow.common.validate.failed' }));
              return false;
            } else if (res === false) {
              message.error(intl.formatMessage({ id: 'lcr.renderer.survey.save.failed' }));
              return false;
            }
            const id = surveyInstantcesDataSet?.current?.get('id');
            if (id) {
              refresh(id);
            }
          }}
        >
          {intl.formatMessage({ id: 'zknow.common.button.save' })}
        </Button>
      </>
    );
  }, []);

  function getEditFlag() {
    const record = surveyInstantcesDataSet?.current;
    const editFlag = record?.get('editFlag');
    if (!editFlag) {
      return false;
    } else if (configDataSet?.current?.get('state') === 'PENDING') {
      return true;
    }
    return false;
  }

  // 底部操作按钮
  const renderButtons = () => {
    return getEditFlag()
      ? (
        <div className="survey-list-detail-footer">
          <div className="button-group">
            {buttons}
          </div>
        </div>
      ) : null;
  };

  const renderSurveyMain = () => {
    if (!show) return renderEmpty();
    const _config = {
      id: surveyInstantcesDataSet?.current?.get('id'),
      dataSet: surveyInstantcesDataSet,
      record: surveyInstantcesDataSet?.current,
      ...context,
      resetFlag: false,
      surveyHasAnchorComponentParentViewId: viewId,
    };

    function handleOpenModal() {
      Modal.open({
        key: modalKey,
        okFirst: true,
        style: { width: '800px' },
        children: (
          <DetailView
            {..._config}
            ref={surveyRef}
          />
        ),
        destroyOnClose: true,
        footer: getEditFlag() ? () => {
          return <>{buttons}</>;
        } : null,
      });
    }
    if (displayMode === 'CARD') {
      return (
        <CardWrapper
          onClickEdit={getEditFlag() ? handleOpenModal : null}
          title={surveyInstantcesDataSet?.current?.get('surveyName')}
          description={surveyInstantcesDataSet?.current?.get('description')}
        >
          <DetailView {..._config} readOnly titleFlag={false} />
        </CardWrapper>
      );
    }
    return <DetailView {..._config} ref={surveyRef} />;
  };

  const renderMain = () => {
    if (!formDataSet?.current) return null;
    if (!surveyInstantcesDataSet?.current?.get('id')) return renderEmpty();
    return (
      <div className={`${prefixCls}`}>
        <div className={`${prefixCls}-main`}>
          {renderSurveyMain()}
          <div className={`${prefixCls}-footer`}>
            {displayMode !== 'CARD' && renderButtons()}
          </div>
        </div>
      </div>
    );
  };

  return renderMain();
});

const SurveyDetailRenderer = injectIntl((props) => {
  return <SurveyDetail {...props} />;
});

export default SurveyDetailRenderer;
