import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import SurveyDetailDataSet from '@/renderer/survey-list/stores/SurveyDetailDataSet';
import SurveyConfigDataSet from '@/renderer/survey-list/stores/SurveyConfigDataSet';
import SurveyInstantcesDataSet from './SurveyInstantcesDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState')(
  (props) => {
    const {
      children,
      AppState: { currentMenuType: { organizationId: tenantId } },
      ticketId,
      formDataSet,
      viewDataSet,
    } = props;
    const prefixCls = 'survey-detail-renderer';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];

    const surveyInstantcesDataSet = useMemo(() => new DataSet(SurveyInstantcesDataSet({
      tenantId,
      ticketId,
      businessObjectId,
    })), [ticketId, businessObjectId]);

    const surveyDetailDataSet = useMemo(() => new DataSet(SurveyDetailDataSet({ tenantId })), []);

    const configDataSet = useMemo(() => new DataSet(SurveyConfigDataSet({ tenantId })), []);

    const value = {
      ...props,
      formDataSet,
      dsFieldList,
      prefixCls,
      formConfig,
      tenantId,
      ticketId,
      surveyDetailDataSet,
      configDataSet,
      surveyInstantcesDataSet,
      viewId,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
