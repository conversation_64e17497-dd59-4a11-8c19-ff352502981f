export default ({ tenantId, ticketId, businessObjectId }) => ({
  autoQuery: false,
  selection: false,
  autoCreate: false,
  paging: false,
  transport: {
    read: ({ data }) => {
      if (!ticketId) return null;
      return {
        url: `/asmt/v1/${tenantId}/assessment_instances/query_related_instance/${ticketId}?businessObjectId=${businessObjectId}`,
        method: 'get',
      };
    },
  },
  events: {

  },
});
