import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';

import MainView from './MainView';

export default inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })((props) => (
    <StoreProvider {...props}>
      <MainView />
    </StoreProvider>
  ))
);

/* externalize: SurveyDetailRenderer */
