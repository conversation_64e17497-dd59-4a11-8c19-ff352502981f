@import '~choerodon-ui/lib/style/themes/default';
.lc-page-loader-tab > .lc-page-loader-tab-right {
  transform: scale(1); // 仅做 fixed 定位相对容器
}

.survey-detail-renderer {
  padding: 16px;

  &-header-top {
    padding: 0.16rem;
    &-title {
      font-size: 0.16rem;
      font-weight: 500;
      color: #12274d;
      line-height: 0.22rem;
    }
  }

  &-main {
    .lc-page-loader-section-layout {
      // margin-left: -14px;
    }
    .lc-form-page-loader-title {
      font-size: 0.16rem;
      font-weight: 500;
      color: #12274d;
      line-height: 0.22rem;
      text-align: left;
      margin-bottom: 0.1rem;
    }
    // 前言样式
    .lc-form-page-loader-description {
      font-size: 0.14rem;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.65);
      line-height: 0.22rem;
      text-align: left;
      margin-top: 0.06rem;
      margin-bottom: 0.14rem;
    }
    .lc-form-page-loader-content {
      .lc-form-viewer-section-header {
        .lc-title {
          margin-left: 0;
        }
      }
      .lc-form-page-loader-column {
        table {
          label.@{c7n-pro-prefix}-field-label.@{c7n-pro-prefix}-field-label-vertical {
            font-size: 0.14rem;
            font-weight: 400;
            color: #12274d;
            line-height: 0.22rem;
            margin-bottom: 4px;
            width: 100%;
            word-break: break-word;
            white-space: normal;
          }
          label.@{c7n-pro-prefix}-field-label.@{c7n-pro-prefix}-field-label-vertical:not(.@{c7n-pro-prefix}-field-required) {
            // 非必填项对齐
            padding-left: 14px;
          }
          .@{c7n-pro-prefix}-field-wrapper {
            margin-bottom: 12px;
          }
        }
      }
    }
  }
  &-footer {
    display: flex;
    text-align: right;
    height: 0.66rem;
    border-top: 0.01rem solid rgba(203, 210, 220, 0.5);
    width: 100%;
    align-items: center;
    justify-content: flex-end;
  }
}
