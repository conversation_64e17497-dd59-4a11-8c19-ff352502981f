import React, { useContext, useEffect } from 'react';
import { observer, useLocalStore } from 'mobx-react-lite';
import { MobXProviderContext } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { useSize } from 'ahooks';
// import { PortalReact } from '@zknow/components';
import { getEnv, getCookieToken } from '@zknow/utils';
import queryString from 'query-string';
import axios from 'axios';
import { Skeleton } from 'choerodon-ui/pro';
import styles from './TicketDiagnosis.module.less';

function TicketDiagnosis(props) {
  const { intl, ticketId, formDataSet, viewDataSet, context, currentTabRef } = props;
  const {
    AppState: { currentMenuType: { organizationId: tenantId }, currentLanguage: language },
    HeaderStore: { getTenantConfig: { enableChatGptFlag, gptTenantFlag } },
  } = useContext(MobXProviderContext);

  const size = useSize(context.pageDOMRefs?.current);

  const store = useLocalStore(() => ({
    botId: '', // 预置的机器人，详情 @国凯
    iframeLoad: true,
    get getBotId() {
      return store.botId;
    },
    setBotId(id) {
      store.botId = id;
    },
    get getIframeLoad() {
      return store.iframeLoad;
    },
    setIframeLoad(flag) {
      store.iframeLoad = flag;
    },
  }));

  useEffect(() => {
    (async () => {
      const res = await axios.get(`/intelligent/v1/${tenantId}/intelligent/ticket_analyser`).catch(e => {
        // eslint-disable-next-line no-console
        console.error('ERROR: bot load failed', e);
      });
      if (res?.id) {
        store.setBotId(res.id);
      }
      store.setIframeLoad(false);
    })();
  }, [tenantId]);

  const botId = store.getBotId;
  const botAssistantOptions = {
    yqLanguage: language,
    yqAccessToken: getCookieToken(),
    ticketId,
    hiddenHeader: true,
    noUploadFile: true,
    embed: true,
    yqTenantId: tenantId,
    ticketOptions: JSON.stringify({
      type: 'ticket_analyse',
      id: ticketId,
      short_description: formDataSet?.current?.get('short_description') || formDataSet?.current?.get('shortDescription') || '',
      description: formDataSet?.current?.get('description') || '',
    }),
  };

  const rootBounding = context.pageDOMRefs?.current?.current?.getBoundingClientRect();

  const specStyle = currentTabRef?.current ? {
    style: {
      height: size?.height || rootBounding?.height || '100%',
    },
  } : null;

  const renderContent = () => {
    if (!enableChatGptFlag || !gptTenantFlag) {
      return <div className={styles.error}>{intl.formatMessage({ id: 'lcr.renderer.desc.diagnosis.gpt.error', defaultMessage: 'GPT 能力不可用，请联系管理员' })}</div>;
    }
    return (
      <div className={styles.wrap} {...specStyle}>
        <Skeleton
          active
          loading={store.getIframeLoad}
          skeletonTitle={false}
          paragraph={{ rows: 5 }}
        >
          {botId ? (
            <iframe
              className={styles.iframe}
              title="bot-embed"
              name="BOT_ASSISTANT_EMBED"
              src={`${getEnv('BOTPRESS_DOMAIN')}/bot/#/lite/${botId}?${queryString.stringify(botAssistantOptions)}`}
              frameBorder="0"
              onLoad={() => {
                store.setIframeLoad(false);
              }}
            />
          ) : <div className={styles.error}>{intl.formatMessage({ id: 'lcr.renderer.desc.diagnosis.assistant.error', defaultMessage: '智能助理不可用，请联系管理员' })}</div>}
        </Skeleton>
      </div>
    );
  };

  return renderContent();
}

export default injectIntl(observer(TicketDiagnosis));
