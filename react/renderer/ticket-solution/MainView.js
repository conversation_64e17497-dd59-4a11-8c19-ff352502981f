import React, { useState, useContext, useEffect, useRef, useMemo } from 'react';
import { Icon, ExternalComponent, TranslateArea, TRANSLATE_FIELDS, TRANSLATE_RESULT, TRANSLATE_STATE } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import classnames from 'classnames';
import axios from 'axios';
import { useSize } from 'ahooks';
import lodashGet from 'lodash/get';
import uniq from 'lodash/uniq';
import isEmpty from 'lodash/isEmpty';
import { Popover, Menu } from 'choerodon-ui';
import { Modal, Tooltip, message } from 'choerodon-ui/pro';
import { getObjectFields, getLookUpValueByCode } from '@/service/lowcodeMap';
import { getHtml, quillNullContent, getRichTextIsNull, isInIframe, transTextToHTML } from '@/utils';
import { getActionFormDataSet, getRichJson } from '../utils/utils.js';
import styles from './TicketSolution.module.less';
import RichTextPreview from '@/renderer/rich-text-preview';
import FileItem from '../ticket-header/components/file-item';
import AiTag from '@/components/ai-tag';
import Store from './stores';
import { getAiSummaryPoll, getAiSummaryAsync, getServiceSetting, queryTranslateNormal, queryTranslateNormalAsync } from '@/service';
import FormView from './FormView';

const AI_GENERATE_SOLUTION = 'aiSolution';
const modalKey = Modal.key();
const MAX_HEIGHT = 260;
const SubMenu = Menu.SubMenu;
const intelligentOption = ['reply', 'similar', 'best', 'knowledge'];

function TicketSolution(props) {
  const {
    formDataSet,
    intl,
    viewRecord,
    tenantId,
    prefixCls,
    dsFieldList,
    ticketId,
    businessObjectCode,
    businessObjectId,
    businessObjectFieldDataSet,
    formConfigRecord,
    viewCode,
    sectionDisplayMode,
    udmFlag, // 共享服务项生成的单据无法编辑解决方案
    widgetConfig,
    HeaderStore,
    AppState,
    downstreamDataSet,
    udmEnableFlag,
    AppState: { currentLanguage: language },
  } = useContext(Store);
  const [isEditable, setIsEditable] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [resolutionValue, setResolutionValue] = useState(null);
  const [lastUpdateBy, setLastUpdateBy] = useState(null);
  const [expanded, setExpanded] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  const [translateSetting, setTranslateSetting] = React.useState(null);
  const [translateFlag, setTranslateFlag] = React.useState(false);
  const [translateLoading, setTranslateLoading] = React.useState(false);
  const [translateResult, setTranslateResult] = React.useState('');
  const [translateError, setTranslateError] = React.useState(false);
  const [translateVisible, setTranslateVisible] = React.useState(false);

  const timer = useRef();
  const count = useRef(30);
  const modalRef = useRef();
  const [exampleData, setExampleData] = useState({});
  const record = props?.record || formDataSet?.current;
  const ticketSolutionFieldName = viewRecord?.get('name');
  const ticketSolutionFieldCode = viewRecord?.get('widgetConfig.ticketSolutionFieldCode');
  const fieldCodeList = [ticketSolutionFieldCode];
  const { genSolutionTemplateId } = widgetConfig || {};
  const genSolutionTitle = widgetConfig.aiSolutionPromptTitle || intl.formatMessage({ id: 'lcr.renderer.ticketSolution.popover.generate', defaultMessages: '智能生成方案' });

  const contentRef = useRef(null);
  const contentSize = useSize(contentRef);
  const countRef = React.useRef(30); // 翻译的轮询

  React.useEffect(() => {
    return () => {
      countRef.current = -1;
    };
  }, []);

  const transformDataSet = useMemo(() => getActionFormDataSet({
    fieldList: [...fieldCodeList, 'qaFlag', 'syncUdm'],
    formDataSet,
    dsFieldList,
    businessObjectFieldDataSet,
    formConfigRecord,
    tenantId,
    intl,
  }), [formDataSet?.current]);

  useEffect(() => {
    return () => {
      clearInterval(timer.current);
      timer.current = null;
    };
  }, []);

  useEffect(() => {
    const resolutionType = record?.get('resolution_type');
    if (resolutionType) {
      getResolutionValue(resolutionType);
    }
  }, [record?.get('resolution_type')]);

  useEffect(() => {
    (async () => {
      if (businessObjectId) {
        const cache = await AppState.customConfig?.[businessObjectId];
        if (cache && !cache.failed) {
          setTranslateSetting(cache.ticketTranslationSettingVO);
          setTranslateFlag(cache.ticketTranslationFlag);
        } else {
          const res = await getServiceSetting(tenantId, businessObjectId);
          if (res && !res.failed) {
            AppState.setCustomConfig(businessObjectId, res);
            setTranslateSetting(res.ticketTranslationSettingVO);
            setTranslateFlag(res.ticketTranslationFlag);
          }
        }
      }
    })();
  }, [businessObjectId]);

  // 校验当前用户是否可以编辑解决方案
  //   TODO: 这里还兼具更新人和更新时间的查询（之前的人设计的）
  //      后续需要将其分离
  const checkPermission = async () => {
    try {
      const editablePerson = viewRecord?.get('widgetConfig.editablePerson') || [];
      const editableRole = JSON.parse(viewRecord?.get('widgetConfig.editableRole')) || [];

      const data = {
        specialList: editablePerson?.map(v => ({
          receiverFieldId: v?.id,
          receiverFieldFullId: v?.fullId,
          receiverField: v?.path,
        })),
        roleIdList: editableRole?.map(v => v?.id),
      };

      // TODO DANGER 由于功能调整 hasResolutionEditPermission 已经没有作用，后续会删除
      // NOTE：添加这个自定义状态目的：跨组件传递解决方案编辑权限给回复转解决方案时使用，避免重复调用
      formDataSet.setState(`hasResolutionEditPermission-${ticketSolutionFieldCode || 'resolution'}`, false);
      const res = await axios.post(`/itsm/v1/${tenantId}/resolution/${businessObjectCode}/${ticketId}/check?ticketSolutionFieldCode=${ticketSolutionFieldCode}`, data);
      if (res?.failed) {
        setIsEditable(false);
      } else {
        res && businessObjectFieldDataSet.query();
        setIsEditable(res?.editFlag);
        setLastUpdateTime(res?.resolutionLastUpdateDate);
        setLastUpdateBy(res?.resolutionLastUpdateBy);
        formDataSet.setState(`hasResolutionEditPermission-${ticketSolutionFieldCode || 'resolution'}`, res?.editFlag);
        formDataSet.setState('hasResolutionEdit-syncResolutionFlag', res?.syncResolutionFlag);
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      setIsEditable(false);
    }
  };

  useEffect(() => {
    if (ticketId && businessObjectCode) {
      checkPermission();
    }
  }, [ticketId, businessObjectCode, tenantId, ticketSolutionFieldCode]);

  async function getResolutionValue(resolutionType) {
    const objectFields = await getObjectFields(tenantId, businessObjectId, { search_code: 'resolution_type' });
    const lookupTypeCode = objectFields?.find((i) => i?.code === 'resolution_type')?.widgetConfig?.lookupCode;
    const LookUpValue = await getLookUpValueByCode(tenantId, { lookupTypeCode });
    const value = LookUpValue?.find((i) => i?.code === resolutionType)?.value;
    setResolutionValue(value);
    return value;
  }

  // 回复生成解决方案
  function aiGenerateResolution() {
    transformDataSet.setState('loading', true);
    getAiSummaryAsync({ tenantId, businessObjectId, aiPromptId: genSolutionTemplateId, ticketId, isNewUrl: true, data: widgetConfig }).then(result => {
      const uuid = typeof result === 'string' ? result : '';
      if (uuid) {
        timer.current = setInterval(() => {
          if (count.current === 0) {
            transformDataSet.setState('loading', false);
            clearInterval(timer.current);
          } else {
            count.current -= 1;
            getAiSummaryPoll({ tenantId, uuid, isNewUrl: true }).then(resp => {
              if (resp && !resp?.failed) {
                const changedParams = lodashGet(resp, 'changedParams', {});
                const fieldName = ticketSolutionFieldCode || 'resolution';
                Object.keys(changedParams).forEach(v => {
                  const htmlStr = transTextToHTML(changedParams[v], 'p');
                  transformDataSet?.current.set(fieldName, htmlStr);
                });
                transformDataSet.setState('loading', false);
                clearInterval(timer.current);
              }
            });
          }
        }, 5 * 1000);
      } else {
        transformDataSet.setState('loading', false);
      }
    });
    setExampleData({});
  }

  /**
   * 生成解决方案
   * @param type 为 true 表示是在修改解决方案的弹窗头的操作入口；为 false 表示直接点击的生成解决方案
   * @returns {Promise<void>}
   */
  async function handleAiPrompt(type) {
    if (type) {
      aiGenerateResolution();
    } else if (widgetConfig.aiSolutionPromptFlag) {
      handleEdit(null, true);
    }
  }

  const modalProps = {
    fieldCodeList,
    formDataSet,
    dsFieldList,
    businessObjectFieldDataSet,
    formConfigRecord,
    tenantId,
    viewCode,
    businessObjectCode,
    intl,
    ticketId,
    widgetConfig,
    AppState,
    modalLoading,
    setModalLoading,
    transformDataSet,
    aiGenerateResolution,
    exampleData,
    udmEnableFlag,
    downstreamId: downstreamDataSet?.current?.get('id'),
    handleAsync,
  };

  useEffect(() => {
    if (modalRef?.current) {
      modalRef.current.update({
        title: (
          <div className={styles['modal-title']}>
            <span>{intl.formatMessage({ id: 'zknow.common.button.modify' })}{ticketSolutionFieldName}</span>
            {HeaderStore?.getTenantConfig?.gptTenantFlag && widgetConfig?.aiSolutionPromptFlag && renderPopoverBtn()}
          </div>
        ),
        children: (
          <FormView
            submitSuccess={checkPermission}
            {...modalProps}
          />
        ),
      });
    }
  }, [transformDataSet?.getState('loading')]);

  function handleEdit(e, aiMode = false) {
    if (!formDataSet.current) {
      return false;
    }
    modalRef.current = Modal.open({
      title: (
        <div className={styles['modal-title']}>
          <span>{intl.formatMessage({ id: 'zknow.common.button.modify' })}{language === 'en_US' ? ` ${ticketSolutionFieldName}` : ticketSolutionFieldName}</span>
          {HeaderStore?.getTenantConfig?.gptTenantFlag && widgetConfig?.aiSolutionPromptFlag && renderPopoverBtn()}
        </div>
      ),
      children: (
        <FormView
          submitSuccess={checkPermission}
          aiMode={aiMode}
          {...modalProps}
        />
      ),
      key: modalKey,
      drawer: false,
      style: { width: '800px' },
      className: `${prefixCls}-modal`,
      destroyOnClose: true,
      onCancel: () => {
        formDataSet.reset();
        return true;
      },
    });
  }

  async function handleAsync() {
    const content = formDataSet?.current?.get(ticketSolutionFieldCode || 'resolution');
    const textIsNul = getRichTextIsNull(content || '');
    if (textIsNul) {
      message.error(intl.formatMessage({ id: 'lcr.renderer.ticketSolution.upgrade.async.null', defaultMessage: '当前解决方案为空，下游单据将不会同步更新' }));
      return false;
    }

    const res = await axios.put(`/itsm/v1/${tenantId}/global/tickets/downstream/${businessObjectId}/${ticketId}/sync/resolution`);
    if (res && !res.failed) {
      message.success(intl.formatMessage({ id: 'lcr.renderer.ticketSolution.upgrade.async.success', defaultMessage: '提交成功！当前解决方案已同步至下游单据' }));
    }
  }

  // 渲染新的富文本编辑器
  function renderWysiwyg() {
    if (!record) return null;
    // FIX1: 2022年4月28日 如果视图上配置了关联字段，不管有没有值，都取关联字段展示，如果没配置，就去解决方案。
    // FIX2: 发布单 INC00026205，导入数据给是问题，最好还是先转为普通 js 再操作，防止出现 mobx 的可监听格式
    const currentData = record.toData();
    const resolution = currentData[ticketSolutionFieldCode || 'resolution'] || '';

    let htmlContent;
    try {
      const jsonData = JSON.parse(resolution);
      htmlContent = getHtml(jsonData);
    } catch (e) {
      htmlContent = resolution;
    }
    if (quillNullContent?.includes(htmlContent)) return null;
    // 数据如：<p>啊？？？？？<span class="yqmention" data-yqmention="531468825176444928" origin-name="真诚的道歉.pdf" yqmention-type="knowledge" tenantid="207965165382135808" fromtenantid="undefined">＠真诚的道歉.pdf</span>&nbsp;</p>
    // 需要取出 tenantid
    const regex = /tenantid="(\d+)"/;
    const tenantIdInContent = JSON.stringify(resolution || '').match(regex)?.[1];

    return (
      <RichTextPreview
        ocrFlag
        data={htmlContent}
        htmlData={resolution?.includes('<p data-json=') ? resolution : ''}
        preview
        minHeight={1}
        ticketId={ticketId}
        tenantId={tenantIdInContent || tenantId}
      />
    );
  }

  // 渲染音频文件
  function renderAudioArea() {
    try {
      const descriptionExtraInfo = getExtraInfo();
      const { audios = [] } = descriptionExtraInfo || {};
      if (audios?.length === 0) return null;
      return (
        <div className={styles.audio}>
          {audios.map((i) => (
            <ExternalComponent
              system={{ scope: 'itsm', module: 'YqAudio' }}
              fileInfo={i}
            />
          ))}
        </div>
      );
    } catch {
      //
    }
  }

  // 获取附加信息
  function getExtraInfo() {
    try {
      const content = record?.get(ticketSolutionFieldCode || 'resolution');
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  }

  // 渲染附件
  function renderAttachmentsArea() {
    const descriptionExtraInfo = getExtraInfo();
    const { attachments = [] } = descriptionExtraInfo || {};
    if (attachments?.length === 0) return null;
    return attachments?.map((i, index) => {
      return (
        <FileItem
          data={i}
          isLast={attachments.length - 1 === index}
          tenantId={tenantId}
          intl={intl}
          prefixCls="ticket-header-renderer-file"
        />
      );
    });
  }

  function renderInfo() {
    if (!record) return null;
    const resolutionType = record.get('resolution_type');
    const _resolutionValue = (record.getField('resolution_type')?.lookup || [])?.find((i) => i.code === resolutionType)?.meaning || resolutionValue || '';

    return (
      <>
        <span>{_resolutionValue}</span>
        {_resolutionValue && (lastUpdateBy || lastUpdateTime) ? <span className={styles.splitDot}>·</span> : null}
        <span>
          {lastUpdateBy && <span>{lastUpdateBy}</span>}
          {lastUpdateTime && <span>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.updatedAt' })} {lastUpdateTime}</span>}
          {formDataSet.getState('hasResolutionEdit-syncResolutionFlag') && <span className={styles.sync}>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.syncResolutionFlag', defaultMessage: '（已同步至下游）' })}</span>}
        </span>
      </>
    );
  }

  function renderToggle() {
    if (contentSize?.height > MAX_HEIGHT) {
      return (
        <div
          className={classnames(styles.toggle, {
            [styles.aiBack]: HeaderStore?.getTenantConfig?.gptTenantFlag && widgetConfig?.aiSolutionPromptFlag,
          })}
          onClick={() => {
            setExpanded(prevState => !prevState);
          }}
        >
          {expanded ? intl.formatMessage({ id: 'zknow.common.button.up' }) : intl.formatMessage({ id: 'lcr.renderer.ticketSolution.expandMore' })}
          <span className={classnames(styles.toggleIcon, { [styles.iconHidden]: expanded })}><Icon type="DoubleDown" /></span>
          <span className={classnames(styles.toggleIcon, { [styles.iconHidden]: !expanded })}><Icon type="DoubleUp" /></span>
        </div>
      );
    }
  }

  function renderContent() {
    const fieldName = ticketSolutionFieldCode || 'resolution';
    const wysiwyg = renderWysiwyg();
    const audio = renderAudioArea();
    const attachments = renderAttachmentsArea();

    // widgetConfig?.solutionIntelligentTranslation 视图可以单独控制该组件是否显示翻译
    //   解决方案组件不一定使用的标准字段， 可能服务配置选择了解决方案字段，但不是标准字段，所以视图增加单独控制
    const showTranslateArea = widgetConfig?.solutionIntelligentTranslation
      && (translateLoading || translateResult || translateError
      || (formDataSet.getState(TRANSLATE_STATE)
      && formDataSet.getState(TRANSLATE_FIELDS)?.includes(fieldName)
      && formDataSet.current?.get(fieldName)
      && formDataSet.getState(TRANSLATE_RESULT)?.[fieldName]))
      && (!isInIframe());

    const extraClass = showTranslateArea ? {
      [styles.contentMore]: true,
    } : {
      [styles.contentMore]: expanded,
      [styles.hasPadding]: contentSize?.height > MAX_HEIGHT,
    };

    return wysiwyg || attachments || audio ? (
      <div
        ref={contentRef}
        className={classnames(styles.content, extraClass)}
      >
        {wysiwyg}
        {audio}
        {attachments}
        {!(isInIframe()) && <TranslateArea
          intl={intl}
          formDataSet={formDataSet}
          name={fieldName}
          loading={translateLoading}
          error={translateError}
          value={translateResult}
          message={intl.formatMessage({ id: 'itsm.common.translate.progress' })}
        />}
        <div className={styles.bottom}>
          {renderInfo()}
        </div>
        {showTranslateArea ? null : renderToggle()}
      </div>
    ) : (
      <div className={styles.empty}>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.noContent' })}</div>
    );
  }

  function renderPopoverBtn() {
    const { generationPromptTemplateFlag, similarPromptTemplateFlag, bestPromptTemplateFlag, knowledgePromptTemplateFlag } = widgetConfig || {};

    if (transformDataSet?.getState('loading') || (!generationPromptTemplateFlag && !similarPromptTemplateFlag && !bestPromptTemplateFlag && !knowledgePromptTemplateFlag)) {
      return null;
    }

    return (
      <AiTag
        className={styles.aiTag}
        name={genSolutionTitle}
        onClick={aiGenerateResolution}
      />
    );
  }

  /**
   * 【解决方案】标题显示
   * @returns {Element}
   */
  function renderTitle() {
    return (
      <div
        className={classnames(styles.title, {
          [styles.titleAI]: isEditable && HeaderStore?.getTenantConfig?.gptTenantFlag && widgetConfig?.aiSolutionPromptFlag,
        })}
      >
        {HeaderStore?.getTenantConfig?.gptTenantFlag && widgetConfig?.aiSolutionPromptFlag
          && <span className={styles.titleIcon}><Icon type="Tips" size={16} /></span>}
        <span className={styles.titleText}>
          {ticketSolutionFieldName || intl.formatMessage({ id: 'lcr.renderer.ticketSolution.solution' })}
          <span className={styles.titleBorder} />
        </span>
      </div>
    );
  }

  /**
   * 同步解决方案按钮
   * @returns {React.JSX.Element|null}
   */
  // function renderSync() {
  //   return udmEnableFlag && widgetConfig?.syncUdmFlag && downstreamDataSet?.current?.get('id') ? (
  //     <Tooltip title={intl.formatMessage({ id: 'lcr.renderer.ticketSolution.tip.async' })}>
  //       <div className={styles.edit}>
  //         <Icon type="afferent-two" size={16} onClick={handleAsync} id="yq-async-solution" />
  //       </div>
  //     </Tooltip>
  //   ) : null;
  // }

  /**
   * 编辑按钮
   * @returns {Element}
   */
  function renderEdit() {
    return isEditable && !udmFlag ? (
      <Tooltip title={intl.formatMessage({ id: 'zknow.common.button.edit' })}>
        <div className={styles.edit}>
          <Icon type="write" size={16} onClick={handleEdit} id="yq-write-solution" />
        </div>
      </Tooltip>
    ) : null;
  }

  const getTranslateResult = async (uuid) => {
    const res = await queryTranslateNormal(tenantId, uuid);
    if (countRef.current === -1) {
      setTranslateError(true);
      setTranslateLoading(false);
      return false;
    }
    if (res?.failed) {
      setTranslateError(true);
      setTranslateLoading(false);
    } else if (res) {
      if (!isEmpty(res)) {
        setTranslateResult(res);
      } else {
        setTranslateError(true);
      }
      setTranslateLoading(false);
    } else if (countRef.current > 0) {
      setTimeout(() => {
        getTranslateResult(uuid);
      }, 3000);
    }
  };

  const handleIntelligentMenuClick = async (e) => {
    const { key } = e;
    // 智能生成解决方案
    if (key === AI_GENERATE_SOLUTION) {
      handleAiPrompt(false);
    } else {
      // 翻译
      const resolution = record.get(ticketSolutionFieldCode || 'resolution') || '';
      if (!resolution) {
        // 解决方案为空
        return false;
      }
      setTranslateLoading(true);
      setTranslateError(false);
      try {
        const res = await queryTranslateNormalAsync({ tenantId, businessObjectId, targetLang: key, message: resolution });
        if (typeof res === 'string') {
          getTranslateResult(res);
        } else {
          setTranslateError(true);
          setTranslateLoading(false);
        }
      } catch (err) {
        setTranslateError(true);
        setTranslateLoading(false);
      }
    }
    setTranslateVisible(false);
  };

  /**
   * 鼠标 hover 在菜单项的时候，需要保持入口依然显示
   */
  const menuItemMouseEnter = () => {
    const node = document.querySelector(`#solution-top-${record?.get('id')}`);
    if (node) {
      node.classList.add('hover-effect');
    }
  };

  const getPopoverContent = () => {
    let translateMenu = null;
    let solutionMenu = null;
    const aiTranslateFlag = translateFlag && translateSetting?.targetLanguageList?.length && widgetConfig?.solutionIntelligentTranslation && !(isInIframe());
    const aiSolutionFlag = isEditable && widgetConfig?.aiSolutionPromptFlag;
    /**
     * 当前工单：即时引用当前工单数据
     * 历史案例：借鉴历史相似工单经验
     * 算法推荐：利用算法推荐最佳解决方案
     * 企业知识库：根据选择的知识空间
     */
    const { generationPromptTemplateFlag, similarPromptTemplateFlag, bestPromptTemplateFlag, knowledgePromptTemplateFlag } = widgetConfig || {};
    const intelligentOptionFlag = {
      reply: generationPromptTemplateFlag,
      similar: similarPromptTemplateFlag,
      best: bestPromptTemplateFlag,
      knowledge: knowledgePromptTemplateFlag,
    };
    const intelligentFlag = intelligentOption.some(item => intelligentOptionFlag[item]);

    // 生成翻译菜单选项
    const translate = uniq(translateSetting?.targetLanguageList).map(item => (
      <Menu.Item key={item} onMouseEnter={menuItemMouseEnter}>
        {(translateSetting?.languageValueList || []).find(({ code }) => code === item)?.value || item}
      </Menu.Item>
    ));

    if (aiTranslateFlag) {
      translateMenu = aiSolutionFlag && intelligentFlag ? (
        <SubMenu
          key="translateAI"
          onMouseEnter={menuItemMouseEnter}
          title={intl.formatMessage({ id: 'itsm.common.translateAI' })}
          className={styles.submenu}
        >
          {translate}
        </SubMenu>
      ) : translate;
    } else {
      // 如果没有【翻译】，就没必要再显示一个下拉菜单
      return AI_GENERATE_SOLUTION;
    }

    if (aiSolutionFlag && intelligentFlag) {
      solutionMenu = (
        <Menu.Item
          key={AI_GENERATE_SOLUTION}
          onMouseEnter={menuItemMouseEnter}
        >
          {genSolutionTitle}
        </Menu.Item>
      );
    }

    return solutionMenu || translateMenu ? (
      <Menu
        onClick={handleIntelligentMenuClick}
        className={styles.menu}
        mode="vertical"
        selectedKeys={[]}
      >
        {solutionMenu}
        {translateMenu}
      </Menu>
    ) : null;
  };

  const popoverHidden = (visible) => {
    if (!visible) {
      const node = document.querySelector(`#solution-top-${record?.get('id')}`);
      if (node) {
        node.classList.remove('hover-effect');
      }
    }
    setTranslateVisible(visible);
  };

  /**
   * 智能操作：生成解决方案、参考相似问题、参考最佳解决方案、AI 翻译
   * @returns {*|null}
   */
  function renderIntelligent() {
    // 开启翻译
    const aiTranslateFlag = translateFlag && translateSetting?.targetLanguageList?.length && widgetConfig?.solutionIntelligentTranslation && !(isInIframe());
    // 开启智能生成解决方案
    const aiSolutionFlag = isEditable && widgetConfig?.aiSolutionPromptFlag;
    // 租户开启 AIGC
    if (!HeaderStore?.getTenantConfig?.gptTenantFlag || (!aiSolutionFlag && !aiTranslateFlag)) {
      return null;
    }

    const popoverContent = getPopoverContent();

    if (!popoverContent) {
      return null;
    }
    // 仅有一个解决方案操作
    if (popoverContent === AI_GENERATE_SOLUTION) {
      return (
        <Tooltip title={genSolutionTitle}>
          <div
            className={styles.edit}
            onClick={() => handleIntelligentMenuClick({ key: AI_GENERATE_SOLUTION })}
          >
            <Icon type="icon-yan-copilot-icon" size={18} />
          </div>
        </Tooltip>
      );
    }

    const tooltipTitle = !aiSolutionFlag && aiTranslateFlag ? intl.formatMessage({ id: 'itsm.common.translateAI' }) : intl.formatMessage({ id: 'lcr.renderer.ticketSolution.tip.intelligent' });
    return (
      <Tooltip title={tooltipTitle}>
        <Popover
          content={popoverContent}
          trigger="hover"
          placement="bottomRight"
          autoAdjustOverflow={false}
          overlayClassName={styles.intelPopover}
          onVisibleChange={popoverHidden}
          visible={translateVisible}
        >
          <div className={styles.edit}>
            <Icon type="icon-yan-copilot-icon" size={18} />
          </div>
        </Popover>
      </Tooltip>
    );
  }

  return (
    <div
      className={classnames(styles.wrap, {
        [styles.ai]: widgetConfig?.aiSolutionPromptFlag,
        [styles.card]: sectionDisplayMode === 'CARD', // 似乎已经没啥用了，最初的需求也未找到
      })}
      id={`solution-top-${record?.get('id')}`}
    >
      <div className={styles.top}>
        {renderTitle()}
        <div className={styles.right}>
          {renderIntelligent()}
          {renderEdit()}
        </div>
      </div>
      {renderContent()}
    </div>
  );
}

export default injectIntl(observer(TicketSolution));
