import React from 'react';
import { formatterCollections } from '@zknow/utils';
import { inject } from 'mobx-react';
import { StoreProvider } from './stores';

import MainView from './MainView';

export default inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'itsm.common', 'lcr.renderer'],
  })((props) => (
    <StoreProvider {...props}>
      <MainView />
    </StoreProvider>
  ))
);

/* externalize: SolutionRenderer */
