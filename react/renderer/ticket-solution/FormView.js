import React, { useEffect, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import { Modal, Form, CheckBox } from 'choerodon-ui/pro';
import { renderField, runAction } from '@/renderer/utils/utils';
import TransQAModal from './main-view/trans2qa';
import AiTag from '@/components/ai-tag';
import styles from '@/renderer/ticket-solution/TicketSolution.module.less';
import GptLoading from '@/components/gpt-loading';
import AiImg from '@/assets/images/yan-ai-white-icon.svg';

const modalKey = Modal.key();

const FormView = ({ modal, fieldCodeList, formDataSet,
  dsFieldList, businessObjectFieldDataSet,
  tenantId,
  viewCode,
  businessObjectCode,
  intl,
  ticketId,
  submitSuccess,
  AppState,
  widgetConfig = {},
  transformDataSet,
  aiGenerateResolution,
  exampleData,
  udmEnableFlag,
  downstreamId,
  handleAsync,
  aiMode,
}) => {
  const { QAPromptTemplateId, issuesPromptTemplateId, aiQAPromptTitle, aiQAPromptFlag } = widgetConfig;
  const issuesPromptTemplateCode = widgetConfig?.issuesPromptTemplateObj?.code;
  const QAModalRef = useRef();

  modal.handleOk(async () => {
    if (transformDataSet.getState('loading')) {
      return false;
    }
    if (transformDataSet?.current?.get('qaFlag') && aiQAPromptFlag) {
      openQAModal();
    }
    return runAction({
      transformDataSet,
      formDataSet,
      fieldCodeList,
      intl,
      submitSuccess: () => {
        // 必须延迟请求，不然后端还没执行到，返回的还是上次更新时间
        //   有时间还是把这个逻辑改一下吧
        setTimeout(() => {
          submitSuccess();
        }, 2000);
        if (udmEnableFlag && widgetConfig?.syncUdmFlag && downstreamId && typeof (handleAsync) === 'function') {
          handleAsync();
        }
      },
    });
  });

  useEffect(() => {
    if (QAModalRef.current && formDataSet?.getState('queryAiQA')) {
      QAModalRef.current.update({
        children: (
          <TransQAModal
            intl={intl}
            tenantId={tenantId}
            currentUser={AppState?.getUserInfo || {}}
            question={formDataSet?.current?.get('short_description')}
            businessObjectCode={businessObjectCode}
            promptTemplateId={QAPromptTemplateId}
            instanceId={ticketId}
            answer={formDataSet?.current?.get('resolution')}
            aiPromptFlag={aiQAPromptFlag}
            aiPromptTitle={aiQAPromptTitle}
            similarityPromptTemplateId={issuesPromptTemplateId}
            similarityPromptTemplateCode={issuesPromptTemplateCode}
            formDataSet={formDataSet}
          />
        ),
      });
    }
  }, [formDataSet?.getState('queryAiQA')]);

  const openQAModal = () => {
    QAModalRef.current = Modal.open({
      title: (
        <div>
          <span>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.trans2qa' })}</span>
          {aiQAPromptFlag && <AiTag name={aiQAPromptTitle} onClick={() => { formDataSet?.setState('queryAiQA', true); }} className={styles.richTextAi} />}
        </div>
      ),
      style: { width: '8rem' },
      key: modalKey,
      children: (
        <TransQAModal
          intl={intl}
          tenantId={tenantId}
          currentUser={AppState?.getUserInfo || {}}
          question={formDataSet?.current?.get('short_description')}
          businessObjectCode={businessObjectCode}
          promptTemplateId={QAPromptTemplateId}
          instanceId={ticketId}
          answer={formDataSet?.current?.get('resolution')}
          aiPromptFlag={aiQAPromptFlag}
          aiPromptTitle={aiQAPromptTitle}
          similarityPromptTemplateId={issuesPromptTemplateId}
          similarityPromptTemplateCode={issuesPromptTemplateCode}
          formDataSet={formDataSet}
        />
      ),
    });
  };

  useEffect(() => {
    if (!transformDataSet?.getState('loading') && aiMode) {
      aiGenerateResolution();
    }
  }, [aiMode]);

  useEffect(() => {
    if (widgetConfig?.syncUdmReplyDefaultFlag) {
      transformDataSet?.current?.set('syncUdm', true);
    }
  }, [widgetConfig?.syncUdmReplyDefaultFlag]);

  return transformDataSet.getState('loading') ? (
    <GptLoading
      title={intl.formatMessage({ id: 'lcr.renderer.gptLoading.title' })}
      description={intl.formatMessage({ id: 'lcr.renderer.gptLoading.description' })}
    />
  ) : (
    <>
      {exampleData?.title && (
        <div className={styles.example}>
          <img src={AiImg} alt="" className={styles.exampleImg} />
          <span className={styles.exampleTitle}>{intl.formatMessage({ id: 'lcr.renderer.ticketSolution.example' })}</span>
          <span className={styles.exampleText}>{exampleData.number} {exampleData.title}</span>
        </div>
      )}
      <Form
        record={transformDataSet?.current}
        labelWidth="auto"
        className="lc-model-detail-form"
      >
        {fieldCodeList.map((i, index) => renderField({
          fieldItem: { field: i },
          formDs: transformDataSet,
          formDataSet,
          dsFieldList,
          intl,
          businessObjectFieldDataSet,
          viewCode,
          businessObjectCode,
          autoFocus: index === 0,
          tenantId,
        }))}
        {udmEnableFlag && widgetConfig?.syncUdmFlag && downstreamId && <CheckBox label={intl.formatMessage({ id: 'lcr.renderer.ticketSolution.udm', defaultMessage: '同步至下游单据' })} name="syncUdm" defaultValue={widgetConfig?.syncUdmReplyDefaultFlag} />}
        {aiQAPromptFlag && <CheckBox label={intl.formatMessage({ id: 'lcr.renderer.ticketSolution.generate.QA' })} name="qaFlag" />}
      </Form>
    </>
  );
};

export default observer(FormView);
