@import '~choerodon-ui/lib/style/themes/default';
@import '~@yqcloud/apps-master/lib/containers/yqcloud-ui/variables';

.wrap {
  position: relative;
  margin: 0 16px 16px;
  padding: 0 16px;
  width: calc(100% - 32px);
  background: rgba(41, 121, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.25);

  &.ai {
    background: linear-gradient(135deg, #fafffd 0%, #f5fcff 52%, #fbf7ff 100%);
  }

  &.card {
    margin: 0;
    border: 0;
    width: 100%;
  }

  .top {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    height: 46px;
    justify-content: space-between;

    .title {
      display: inline-flex;
      align-items: center;
      height: 100%;
      width: 100%;

      &Icon {
        display: flex;
        align-items: center;
        flex: 0 0 20px;
        width: 20px;
        height: 20px;
        margin-right: 4px;
      }

      &Text {
        position: relative;
        display: block;
        font-size: 16px;
        font-weight: 500;
        color: #12274d;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &Border {
        position: absolute;
        bottom: 2px;
        left: 0;
        display: block;
        width: 40px;
        height: 4px;
        background: linear-gradient(270deg, rgba(83, 177, 255, 0.1) 0%, #2979ff 100%);
      }

      &.titleAI {
        width: unset;
        max-width: 50%;

        .titleText {
          display: inline-block;
          max-width: calc(100% - 24px);
        }
      }
    }

    .aiTag {
      // max-width: calc(50% - 58px);
      margin-bottom: 0 !important;
    }

    .right {
      display: flex;
      align-items: center;
    }

    .edit {
      // position: absolute;
      z-index: 9;
      // top: 7px;
      // right: 0;
      width: 22px;
      height: 22px;
      display: none;
      cursor: pointer;
      margin-left: 4px;
      align-items: center;
      justify-content: center;

      &:hover {
        color: @primary-color;
        background-color: @yq-primary-color-10;
        border-radius: 2px;
      }
    }
  }

  .content {
    font-size: 14px;
    font-weight: 400;
    color: #2b2d38;
    line-height: 22px;
    word-break: break-all;
    max-height: 300px;
    overflow: hidden;

    img {
      width: 100%;
      max-width: 500px;
      max-height: 450px;
      display: block;
    }

    table,
    tr,
    td,
    th {
      border: 1px #bfbfbf solid;
      border-collapse: collapse;
    }

    table td {
      min-width: 2em;
      padding: 0.4em;
    }

    .audio {
      margin-top: 4px;
    }

    &.contentMore {
      max-height: unset !important;
    }

    &.hasPadding {
      padding-bottom: 40px;
    }

    .toggle {
      position: absolute;
      left: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 40px;
      background: linear-gradient(180deg, rgba(249, 251, 255, 0.85) 0%, rgba(248, 251, 255, 0.94) 20%, #f4f8ff 100%);
      font-size: 14px;
      font-weight: 400;
      color: rgba(18, 39, 77, 0.45);
      cursor: pointer;

      &Icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 100%;

        &.iconHidden {
          display: none;
        }
      }

      &:hover {
        color: @primary-color;
      }

      &.aiBack {
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.85) 0%, rgba(255, 255, 255, 0.92) 20%, rgba(255, 255, 255, 0.9) 100%);
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(18, 39, 77, 0.45);
    line-height: 20px;

    .splitDot {
      margin-right: 6px;
    }
    .sync {
      margin-left: 4px;
    }
  }

  &:global(.hover-effect),
  &:hover {
    .edit {
      display: flex;
    }
  }
}

.alwaysShow {
  display: flex !important;
}

.empty {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40px;
  font-size: 14px;
  font-weight: 400;
  color: rgba(18, 39, 77, 0.45);
}

.popover {
  :global {
    .c7n-popover-inner-content {
      padding: 0 !important;
    }
  }

  &Item {
    display: flex;
    align-items: center;
    padding: 5px 12px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 400;
  }
}

.solutionItem {
  display: flex;
  align-items: center;
}

.help {
  color: rgba(18, 39, 77, 0.45);
  margin-left: 4px;
}

.menu {
  width: 100%;
  max-height: 400px;
  overflow-y: auto;
}

.submenu {
  :global(.c7n-menu-sub) {
    max-height: 400px;
    overflow-y: auto;
  }
}

.intelPopover {
  padding-top: 0 !important;

  :global {
    .c7n-popover-arrow {
      display: none !important;
    }

    .c7n-popover-inner-content {
      padding: 0 !important;
    }
  }
}

.example {
  display: flex;
  background: linear-gradient(135deg, #fafffd 0%, #f5fcff 52%, #fbf7ff 100%);
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.25);
  padding: 12px;
  align-items: center;
  margin-bottom: 16px;

  .exampleImg {
    width: 24px;
    height: 24px;
  }

  .exampleTitle {
    margin: 0 12px;
    font-weight: 500;
    font-size: 14px;
  }

  .exampleText {
    color: rgba(18, 39, 77, 0.85);
  }
}

.modal-title {
  display: flex;
  align-items: center;

  .aiTag {
    margin-bottom: 0 !important;
  }
}
