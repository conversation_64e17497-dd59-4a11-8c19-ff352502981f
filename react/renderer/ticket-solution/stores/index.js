import React, { createContext, useMemo } from 'react';
import { DataSet } from 'choerodon-ui/pro';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import SolutionDataSet from './SolutionDataSet';
import DownstreamDataSet from './DownstreamDataSet';
import BusinessObjectFieldDataSet from '../../../components/ui-action/stores/BusinessObjectFieldDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = injectIntl(inject('AppState', 'HeaderStore')(
  (props) => {
    const {
      children,
      AppState,
      AppState: { currentMenuType: { organizationId: tenantId } },
      HeaderStore: { getTenantConfig: { enableChatGptFlag, udmEnableFlag } }, // 用于解决方案转问答时GPT的控制
      ticketId,
      formDataSet,
      viewDataSet,
      viewRecord,
    } = props;
    const prefixCls = 'solution-renderer';
    const formConfig = viewDataSet?.current?.toData() || {};
    const { id: viewId, jsonData, businessObjectCode, businessObjectId } = formConfig;
    const dsFieldList = jsonData?.datasets?.find(ds => ds.id === viewId)?.fields || [];
    const widgetConfig = viewRecord?.get('widgetConfig');

    const solutionDataSet = useMemo(() => new DataSet(SolutionDataSet()), []);

    const downstreamDataSet = useMemo(() => new DataSet(DownstreamDataSet({ tenantId, ticketId, udmEnableFlag })), [udmEnableFlag]);

    const businessObjectFieldDataSet = useMemo(() => new DataSet(BusinessObjectFieldDataSet({
      tenantId,
      businessObjectId,
    })), [tenantId, businessObjectId]);

    const value = {
      ...props,
      solutionDataSet,
      formDataSet,
      dsFieldList,
      prefixCls,
      formConfig,
      tenantId,
      businessObjectCode,
      businessObjectId,
      businessObjectFieldDataSet,
      formConfigRecord: viewDataSet?.current,
      enableChatGptFlag,
      widgetConfig,
      AppState,
      downstreamDataSet,
      udmEnableFlag,
    };

    return (
      <Store.Provider value={value}>
        {children}
      </Store.Provider>
    );
  },
));
