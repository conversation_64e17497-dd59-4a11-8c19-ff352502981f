// @ts-nocheck
import React, { useContext, useEffect, useState, useCallback, useRef } from 'react';
import { observer } from 'mobx-react-lite';
import {
  message,
  Form,
  Lov,
  Output,
  TextField,
  Select,
  Switch,
  Skeleton,
} from 'choerodon-ui/pro';
import { Upload } from 'choerodon-ui';
import lodashGet from 'lodash/get';
import axios from 'axios';
import { getAccessToken } from '@zknow/utils';
import { Button, Icon } from '@zknow/components';
import RichText from '../../../../components/wysiwyg';
import stores from './stores';
import styles from './Trans2qa.module.less';
import AiTag from '@/components/ai-tag';
import { getAiSummaryPoll, getAiSummaryAsync } from '../../../../service';
import GptLoading from '@/components/gpt-loading';

const SelectContent = (props) => {
  const {
    children,
    dataSet,
    textField,
    valueField,
    tenantId,
    intl,
    originDataSet,
    tagGroupId,
  } = props;
  const [value, setValue] = useState('');
  const [flag, setFlag] = useState(false);

  // eslint-disable-next-line no-shadow
  async function handleAddLabel(value) {
    if (!value) {
      return;
    }
    const postObj = {
      name: value,
      tagGroupId,
      tenantId,
    };
    const label = await axios.post(`lc/v1/${tenantId}/tag`, postObj);
    if (label && !label.failed) {
      dataSet.create({ name: value, [valueField]: label.id }, 0);
      const qaLibraryItemLabelAssignList = originDataSet.current.get('qaLibraryItemLabelAssignList') || [];
      originDataSet.current.set('qaLibraryItemLabelAssignList', [...qaLibraryItemLabelAssignList, {
        name: value,
        [valueField]: label.id,
        labelName: value,
        labelId: label.id,
        meaning: value,
      }]);
    } else {
      message.error(label.message);
    }
  }

  const handleAdd = useCallback(() => {
    handleAddLabel(value);
    setValue('');
    setFlag(false);
  }, [dataSet, textField, valueField, value]);

  const handleClose = () => {
    setFlag(false);
  };

  const handleOpen = () => {
    setFlag(true);
  };

  return (
    <>
      {children}
      <div style={{ width: '100%' }}>
        <div style={{ display: flag ? 'flex' : 'none', marginBottom: 12 }}>
          <TextField
            value={value}
            onChange={setValue}
            style={{ padding: '0 12px', flex: 1 }}
            placeholder={intl.formatMessage({ id: 'lcr.renderer.trans2qa.tag.input' })}
          />
          <div style={{ display: 'flex' }}>
            <Button
              style={{ marginRight: '.02rem' }}
              color="primary"
              funcType="raised"
              onClick={handleAdd}
            >{intl.formatMessage({ id: 'zknow.common.button.submit' })}</Button>
            <Button
              style={{ marginRight: '.12rem' }}
              onClick={handleClose}
            >{intl.formatMessage({ id: 'zknow.common.button.cancel' })}</Button>
          </div>
        </div>
        <div className={styles.addTag} onClick={handleOpen} style={{ display: !flag ? 'flex' : 'none' }}>
          <Icon type="plus" />
          <span style={{ marginLeft: 8 }}>{intl.formatMessage({ id: 'lcr.renderer.trans2qa.tag.add' })}</span>
        </div>
      </div>

    </>
  );
};

const TransQAModal = () => {
  const {
    intl,
    labelDataSet,
    qaDataSet,
    tenantId,
    modal,
    question,
    answer,
    aiPromptFlag,
    businessObjectCode,
    promptTemplateId,
    instanceId,
    similarityPromptTemplateId,
    similarityPromptTemplateCode,
    formDataSet,
  } = useContext(stores);
  const [tagGroupId, setTagGroupId] = useState<string>('');
  const [gptResult, setGptResult] = useState<boolean>(aiPromptFlag);
  const [similarData, setSimilarData] = useState([]);
  const [loadingSimilar, setLoadingSimilar] = useState<boolean>(aiPromptFlag);

  const timer = useRef();
  const count = useRef(30);
  const similarTimer = useRef();
  const similarCount = useRef(30);

  useEffect(() => {
    handleGenerateQA();
    return () => {
      clearInterval(timer.current);
    };
  }, [aiPromptFlag, tenantId, businessObjectCode, promptTemplateId, instanceId]);

  useEffect(() => {
    (async () => {
      const res = await axios.get(`lc/v1/${tenantId}/tag_group?page=0&size=10&search_code=QA_LIBRARY_ITEM_DEFAULT`);
      if (res && !res.failed) {
        const resId = res?.content?.[0]?.id || '';
        if (resId) {
          labelDataSet.setQueryParameter('tagGroupId', resId);
          labelDataSet.query();
          setTagGroupId(resId);
        }
      }
    })();
  }, [tenantId]);

  useEffect(() => {
    if (formDataSet?.getState('queryAiQA') && !gptResult) {
      handleGenerateQA();
    }
  }, [formDataSet?.getState('queryAiQA')]);

  // 智能生成问答
  function handleGenerateQA() {
    if (aiPromptFlag && promptTemplateId && instanceId) {
      setGptResult(true);
      clearInterval(timer.current);
      getAiSummaryAsync({ tenantId, businessObjectCode, aiPromptId: promptTemplateId, ticketId: instanceId }).then(res => {
        const key = typeof res === 'string' ? res : '';
        if (key) {
          timer.current = setInterval(() => {
            if (count.current === 0) {
              clearInterval(timer.current);
              setGptResult(false);
            } else {
              count.current -= 1;
              getAiSummaryPoll({ tenantId, uuid: key }).then(resp => {
                if (resp && !resp?.failed && qaDataSet.current) {
                  // TODO: 将图片提取出，等纯文本分析结束后，将图片拼接在后面
                  if (resp !== 'NULL') {
                    qaDataSet.current.set('question', lodashGet(resp, 'changedParams.short_description', question));
                    qaDataSet.current.set('answer', `<p>${lodashGet(resp, 'changedParams.resolution', answer)}</p>`);
                  }
                  clearInterval(timer.current);
                  setGptResult(false);
                  formDataSet?.setState('queryAiQA', false);
                }
              });
            }
          }, 5 * 1000);
        } else {
          setGptResult(false);
          formDataSet?.setState('queryAiQA', false);
        }
      }).catch(e => {
        setGptResult(false);
        clearInterval(timer.current);
        formDataSet?.setState('queryAiQA', false);
      });
    } else {
      setGptResult(false);
      formDataSet?.setState('queryAiQA', false);
    }
  }

  // 智能生成相似问题
  function handleGenerateSimilar() {
    if (aiPromptFlag && similarityPromptTemplateId && instanceId) {
      setLoadingSimilar(true);

      clearInterval(similarTimer.current);
      getAiSummaryAsync({ tenantId, businessObjectCode, aiPromptId: similarityPromptTemplateId, ticketId: instanceId, data: { customParams: { originQuestion: qaDataSet.current?.get('question') } } }).then(res => {
        const key = typeof res === 'string' ? res : '';
        if (key) {
          similarTimer.current = setInterval(() => {
            if (similarCount.current === 0) {
              clearInterval(similarTimer.current);
              setLoadingSimilar(false);
            } else {
              similarCount.current -= 1;
              getAiSummaryPoll({ tenantId, uuid: key }).then(resp => {
                if (resp && !resp?.failed && qaDataSet.current) {
                  if (resp !== 'NULL') {
                    const data = lodashGet(resp, `changedParams.${similarityPromptTemplateCode}`, similarData);
                    setSimilarData(JSON.parse(data));
                  }
                  clearInterval(similarTimer.current);
                  setLoadingSimilar(false);
                }
              });
            }
          }, 5 * 1000);
        } else {
          setLoadingSimilar(false);
        }
      }).catch(e => {
        setLoadingSimilar(false);
        clearInterval(similarTimer.current);
      });
    } else {
      setLoadingSimilar(false);
    }
  }

  modal.handleOk(async () => {
    if (gptResult) return false;
    if (similarData.length && qaDataSet.current?.get('similarProblems')) {
      const qaLibrarySimilarQuestionList = similarData.map(i => { return { name: i }; });
      qaDataSet?.current.set('qaLibrarySimilarQuestionList', qaLibrarySimilarQuestionList);
    }
    const res = await qaDataSet.submit();
    if (res === false) {
      return false;
    }
  });

  const onAddTag = async (value) => {
    const postObj = {
      name: value,
    };
    const label = await axios.post(`/intelligent/v1/${tenantId}/qaItem/label`, postObj);
    if (label && !label.failed) {
      labelDataSet.create({ name: value, id: label.id }, 0);
      const qaLibraryItemLabelAssignList = qaDataSet.current.get('qaLibraryItemLabelAssignList') || [];
      qaDataSet.current.set('qaLibraryItemLabelAssignList', [...qaLibraryItemLabelAssignList, {
        name: value,
        id: label.id,
        labelName: value,
        labelId: label.id,
        meaning: value,
      }]);
    } else {
      message.error(label.message);
    }
  };

  const renderPopupContent = useCallback(({ content, dataSet: currentDataSet, textField, valueField, setPopup, children }) => (
    <SelectContent
      dataSet={currentDataSet}
      originDataSet={qaDataSet}
      textField={textField}
      valueField={valueField}
      setPopup={setPopup}
      tenantId={tenantId}
      intl={intl}
      tagGroupId={tagGroupId}
    >
      {content}
    </SelectContent>
  ), [qaDataSet, tagGroupId]);

  const richTextRender = useCallback(() => (
    <RichText
      data={qaDataSet?.current?.get('answer') || ''}
      onChange={(event, editor) => {
        qaDataSet?.current?.set('answer', editor?.getData());
      }}
    />
  ), [gptResult]);

  function handleChangeSimilarFlag(value) {
    if (value)handleGenerateSimilar();
  }

  function handleChangeSimilarQuestion(value, index) {
    const arr = [...similarData];
    arr[index] = value;
    setSimilarData(arr);
  }

  function handleDeleteSimilarQuestion(index) {
    const arr = [...similarData];
    arr.splice(index, 1);
    setSimilarData(arr);
  }

  function handleAddSimilar() {
    const arr = [...similarData, ''];
    setSimilarData(arr);
  }

  return gptResult ? (
    <GptLoading
      title={intl.formatMessage({ id: 'lcr.renderer.gptLoading.title' })}
      description={intl.formatMessage({ id: 'lcr.renderer.gptLoading.description' })}
    />
  ) : (
    <>
      <Form
        dataSet={qaDataSet}
        labelLayout="horizontal"
        className={styles.form}
        labelTooltip="always"
        labelWidth="auto"
      >
        <TextField name="question" autoFocus />
        <Output
          label={(<div><span className={styles.richTextLabel}>*</span>{intl.formatMessage({ id: 'lcr.renderer.trans2qa.answer' })}</div>)}
          name="answer"
          renderer={richTextRender}
        />
        {qaDataSet.current ? (
          <Upload
            label={intl.formatMessage({ id: 'lcr.renderer.trans2qa.attachments' })}
            name="file"
            multiple
            listType="text-down"
            action={`${window._env_.API_HOST}/hfle/yqc/v1/${tenantId}/files/secret-multipart`}
            headers={{
              'Access-Control-Allow-Origin': '*',
              Authorization: getAccessToken(),
              'x-tenant-id': tenantId,
            }}
            accept="*"
            defaultFileList={qaDataSet.current.get('itemFiles') || []}
            beforeUpload={(file) => {
            }}
            onChange={({ file }) => {
              const { status, response } = file;
              if (status === 'done') {
                const fileObj = {
                  uid: file?.uid,
                  name: file?.name,
                  status: file?.status,
                  fileKey: file?.response?.fileKey,
                  md5: file?.response?.md5,
                  size: file?.size,
                };
                if (file?.response.failed) {
                  message.error(file?.response.message);
                  return;
                }
                const result = [...(qaDataSet.current.get('itemFiles') || []), fileObj];
              qaDataSet.current?.set('itemFiles', result);
              } else if (status === 'removed') {
                const removedAfterAttachments = qaDataSet.current.get('itemFiles').filter((e) => e.uid !== file.uid);
              qaDataSet?.current?.set('itemFiles', removedAfterAttachments);
              } else if (status === 'error') {
                // message.error('附件上传失败');
              }
            }}
          >
            <Button
              funcType="raised"
              icon="upload"
            >{intl.formatMessage({ id: 'lcr.renderer.trans2qa.upload' })}</Button>
          </Upload>
        ) : null}
        <Lov name="itemDirectorList" />
        <Select
          inputable
          searchable
          name="qaLibraryItemLabelAssignList"
          placeholder={intl.formatMessage({ id: 'lcr.renderer.trans2qa.select' })}
          popupContent={renderPopupContent}
          tabIntoPopupContent
          onBeforeEnterAdd={onAddTag}
        />
        <Lov name="qaId" />
        {aiPromptFlag && <Switch name="similarProblems" onChange={handleChangeSimilarFlag} />}
      </Form>
      {qaDataSet.current?.get('similarProblems') && (
        <div className={styles.similarArea}>
          <div className={styles.areaHeader}>
            <div className={styles.title}>{intl.formatMessage({ id: 'lcr.renderer.trans2qa.similarQuestion' })}</div>
            {!loadingSimilar && <AiTag name={intl.formatMessage({ id: 'lcr.renderer.trans2qa.retry' })} onClick={handleGenerateSimilar} />}
          </div>
          <Skeleton
            active
            loading={loadingSimilar}
            skeletonTitle={false}
            paragraph={{ rows: 3 }}
          >
            {
                similarData?.map((i, index) => {
                  return <div className={styles.question}>
                    <TextField value={i} className={styles.input} onChange={(value) => handleChangeSimilarQuestion(value, index)} />
                    <Icon type="close" className={styles.close} size={12} onClick={() => handleDeleteSimilarQuestion(index)} />
                  </div>;
                })
            }
            <div className={styles.add} onClick={handleAddSimilar}>+</div>
          </Skeleton>
        </div>
      )}
    </>
  );
};

export default observer(TransQAModal);
