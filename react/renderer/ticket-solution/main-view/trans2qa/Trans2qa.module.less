@import '~choerodon-ui/lib/style/themes/default';

.richTextLabel {
  display: inline-block;
  margin-right: 0.04rem;
  color: #d50000;
  font-size: 0.14rem;
  line-height: 1;
}

.richTextAi {
  margin: 10px 0 0 0 !important;
}

.addTag {
  color: #2979ff;
  align-items: center;
  padding: 8px;
  margin-left: 6px;
  cursor: pointer;
}

.waitFor {
  min-height: 150px;
  padding-top: 5px;
  color: #939aaa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #d7e2ec;
}

.form {
  width: 100%;

  :global {
    .c7n-upload-list-item-span {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}

.similarArea {
  background: #f7f9fc;
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.5);
  padding: 12px;

  .areaHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      font-weight: 500;
      font-size: 14px;
      color: #12274d;
      line-height: 22px;
    }
  }

  .question {
    display: flex;
    align-items: center;
    margin: 8px 0;

    .input {
      flex: 1;
    }

    .close {
      margin-left: 8px;
      cursor: pointer;
    }
  }

  .add {
    height: 32px;
    width: 32px;
    font-size: 16px;
    color: @primary-color;
    background-color: @primary-1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #dbe9fd
  }
}
