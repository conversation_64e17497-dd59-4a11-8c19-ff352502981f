// @ts-nocheck
import React, { createContext, useEffect, useMemo } from 'react';
import uuidv4 from 'uuid/v4';
import { DataSet } from 'choerodon-ui/pro';
import LabelDataSet from './LabelDataSet';
import QaDataSet from './QaItemDetailDataSet';

const Store = createContext();

export default Store;

export const StoreProvider = (props) => {
  const {
    intl,
    tenantId,
    children,
    currentUser,
    question,
    answer,
    atts,
    aiPromptFlag,
  } = props;

  const labelDataSet = useMemo(() => new DataSet(LabelDataSet({ tenantId })), [tenantId]);
  const qaDataSet = useMemo(() => new DataSet(QaDataSet({
    intl,
    tenantId,
    labelDataSet,
    currentUser,
  })), [tenantId, question]);
  useEffect(() => {
    const attachments = atts?.attachments || [];
    const files = Array.isArray(attachments) ? attachments.map(item => ({ ...item, status: 'done', uid: item.uid || uuidv4() })) : [];
    qaDataSet.create({
      question,
      answer: aiPromptFlag ? '' : answer,
      itemFiles: files,
    });
  }, [qaDataSet, question, answer, aiPromptFlag]);

  const value = {
    ...props,
    tenantId,
    labelDataSet,
    qaDataSet,
  };
  return (
    <Store.Provider value={value}>
      {children}
    </Store.Provider>
  );
};
