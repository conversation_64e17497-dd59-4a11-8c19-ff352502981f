export default ({ tenantId }) => {
  return {
    autoQuery: false,
    pageSize: 9999,
    transport: {
      read: ({ data: { tagGroupId } }) => {
        const url = `lc/v1/${tenantId}/tag/${tagGroupId}`;
        return ({
          url,
          method: 'get',
        });
      },
    },
    fields: [
      { name: 'meaning', type: 'string', transformResponse: (value, object) => object.name },
      { name: 'value', type: 'string', transformResponse: (value, object) => object.id },
    ],
  };
};
