// @ts-nocheck
import uuidv4 from 'uuid/v4';

export default ({ intl, tenantId, currentUser, labelDataSet }) => {
  const question = intl.formatMessage({ id: 'lcr.renderer.trans2qa.question' });
  const answer = intl.formatMessage({ id: 'lcr.renderer.trans2qa.answer' });
  const tag = intl.formatMessage({ id: 'lcr.renderer.trans2qa.tag' });
  const charge = intl.formatMessage({ id: 'lcr.renderer.trans2qa.charge' });
  const lib = intl.formatMessage({ id: 'lcr.renderer.trans2qa.lib' });
  const similarProblems = intl.formatMessage({ id: 'lcr.renderer.trans2qa.similar' });

  return {
    autoCrate: false,
    transport: {
      create: ({ data: [data] }) => {
        data.qaLibraryItemLabelAssignList = data.qaLibraryItemLabelAssignList?.map(r => ({
          labelId: r.id,
          labelName: r.name,
        }));
        return ({
          url: `intelligent/v1/${tenantId}/qaItem`,
          method: 'post',
          data,
        });
      },
    },
    fields: [
      { name: 'answer', type: 'string', label: answer, required: true },
      {
        name: 'question',
        type: 'string',
        label: question,
        required: true,
      },
      {
        name: 'qaId',
        label: lib,
        required: true,
        type: 'object',
        lovCode: 'QA_LIBRARY',
        transformRequest: (value) => {
          return value?.id || value;
        },
      },
      {
        name: 'itemDirectorList',
        type: 'object',
        label: charge,
        lovCode: 'USER',
        multiple: true,
        defaultValue: [{
          id: currentUser.id,
          realName: currentUser.realName,
        }],
        transformResponse: (value, data) => data?.itemDirectorList?.map((item => ({ id: item?.id, realName: item?.realName }))),
      },
      { name: 'qaLibraryItemLabelAssignList',
        options: labelDataSet,
        type: 'object',
        label: tag,
        multiple: true,
        valueField: 'id',
        textField: 'meaning',
        transformResponse: (value, object) => {
          return object.qaLibraryItemLabelAssignList?.map(r => ({ meaning: r.labelName, ...r, id: r.labelId }));
        },
      },
      {
        name: 'itemFiles',
        transformRequest: (value) => {
          if (typeof value === 'string') {
            return value;
          } else if (Array.isArray(value)) {
            return JSON.stringify(value.map(item => ({ ...item, status: 'done', uid: item.uid || uuidv4() })));
          }
          return '[]';
        },
      },
      {
        name: 'similarProblems',
        type: 'boolean',
        label: similarProblems,
      },
    ],
  };
};
