import React from 'react';
import { inject } from 'mobx-react';
import { formatterCollections } from '@zknow/utils';
import { StoreProvider } from './stores';
import TransQAModal from './TransQAModal';

export default inject('AppState')(
  formatterCollections({
    code: ['zknow.common', 'lcr.renderer'],
  })((props) => (
    <StoreProvider {...props}>
      <TransQAModal />
    </StoreProvider>
  ))
);
