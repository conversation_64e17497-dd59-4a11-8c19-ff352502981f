/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable react/no-danger */
import React, { useState, useCallback, useContext, useEffect, useRef } from 'react';
import { Icon, ExternalComponent } from '@zknow/components';
import { observer } from 'mobx-react-lite';
import { injectIntl } from 'react-intl';
import { Modal, Form, Tooltip, Spin } from 'choerodon-ui/pro';
import classnames from 'classnames';
import axios from 'axios';
import { getObjectFields, getLookUpValueByCode } from '@/service/lowcodeMap';
import { getHtml, quillNullContent } from '@/utils';
import { renderField, getActionFormDataSet, runAction, getRichJson } from '@/renderer/utils/utils.js';
import RichTextPreview from '@/renderer/rich-text-preview';
import FileItem from '../../ticket-header/components/file-item';
import Store from '../stores';
import './index.less';
import AiImg from '@/assets/images/yan-ai-white-icon.svg';

const modalKey = Modal.key();

const Solution = observer((props) => {
  const context = useContext(Store);
  const {
    formDataSet,
    intl,
    viewRecord,
    tenantId,
    prefixCls,
    intlPrefixCls,
    dsFieldList,
    ticketId,
    businessObjectCode,
    businessObjectId,
    businessObjectFieldDataSet,
    formConfigRecord,
    viewCode,
    sectionDisplayMode,
    udmFlag, // 共享服务项生成的单据无法编辑解决方案
    widgetConfig,
  } = context;
  const [isEditable, setIsEditable] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const [resolutionValue, setResolutionValue] = useState(null);
  const [lastUpdateBy, setLastUpdateBy] = useState(null);
  const [loading, setLoading] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const record = props?.record || formDataSet?.current;
  const ticketSolutionFieldName = viewRecord?.get('name');
  const ticketSolutionFieldCode = viewRecord?.get('widgetConfig.ticketSolutionFieldCode');
  const fieldCodeList = [
    ticketSolutionFieldCode,
  ];
  const timer = useRef();
  const count = useRef(30);

  useEffect(() => {
    const resolutionType = record?.toData()?.resolution_type;
    resolutionType && getResolutionValue(resolutionType);
  }, [record?.toData()?.resolution_type]);

  async function handleAiPrompt() {
    if (widgetConfig.promptTemplateId) {
      setLoading(true);
      handleEdit();
      const result = await axios.post(`/ai/v1/${tenantId}/callPromptTemplate/run/async/${widgetConfig.promptTemplateId}?ticketId=${ticketId}&businessObjectCode=${businessObjectCode}`);
      const uuid = typeof result === 'string' ? result : '';
      if (uuid) {
        timer.current = setInterval(() => {
          if (count.current === 0) {
            setLoading(false);
            clearInterval(timer.current);
          } else {
            count.current -= 1;
            axios.get(`/ai/v1/${tenantId}/callPromptTemplate/run/async/poll/${uuid}`).then(resp => {
              if (resp && !resp?.failed && resp?.status !== 204) {
                const { changedParams } = resp;
                if (formDataSet?.current && !editModalOpen) {
                  Object.keys(changedParams).forEach(v => {
                    formDataSet?.current.set(v, changedParams[v]);
                  });
                }
                setLoading(false);
                clearInterval(timer.current);
              }
            });
          }
        }, 5 * 1000);
      } else {
        setLoading(false);
      }
    }
  }

  const check = async () => {
    try {
      const editablePerson = viewRecord?.get('widgetConfig.editablePerson') || [];
      const editableRole = JSON.parse(viewRecord?.get('widgetConfig.editableRole')) || [];

      const data = {
        specialList: editablePerson?.map(v => ({
          receiverFieldId: v?.id,
          receiverFieldFullId: v?.fullId,
          receiverField: v?.path,
        })),
        roleIdList: editableRole?.map(v => v?.id),
      };

      // NOTE：添加这个自定义状态目的：跨组件传递解决方案编辑权限给回复转解决方案时使用，避免重复调用
      formDataSet.setState(`hasResolutionEditPermission-${ticketSolutionFieldCode || 'resolution'}`, false);
      const res = await axios.post(`/itsm/v1/${tenantId}/resolution/${businessObjectCode}/${ticketId}/check?ticketSolutionFieldCode=${ticketSolutionFieldCode}`, data);
      if (res?.failed) {
        setIsEditable(false);
      } else {
        res && businessObjectFieldDataSet.query();
        setIsEditable(res?.editFlag);
        setLastUpdateTime(res?.resolutionLastUpdateDate);
        setLastUpdateBy(res?.resolutionLastUpdateBy);
        formDataSet.setState(`hasResolutionEditPermission-${ticketSolutionFieldCode || 'resolution'}`, res?.editFlag);
      }
    } catch (e) {
      // eslint-disable-next-line no-console
      console.log(e);
      setIsEditable(false);
    }
  };

  useEffect(() => {
    if (ticketId && businessObjectCode) {
      check();
    }
  }, [tenantId, businessObjectCode, ticketId]);

  useEffect(() => {
    if (formDataSet?.getState('solutionEidtModal')) {
      formDataSet?.getState('solutionEidtModal').update({
        children: FieldFormView,
        onOk: async () => {
          setEditModalOpen(false);
          setLoading(false);
          return runAction({
            transformDataSet,
            formDataSet,
            fieldCodeList,
            intl,
          });
        },
      });
    }
  }, [loading, formDataSet?.getState('solutionEidtModal')]);

  const transformDataSet = formDataSet?.current && getActionFormDataSet({
    fieldList: fieldCodeList,
    formDataSet,
    dsFieldList,
    businessObjectFieldDataSet,
    formConfigRecord,
    tenantId,
    intl,
  });

  // 动作字段的表单
  const FieldFormView = formDataSet?.current && (
    <Spin spinning={loading}>
      <Form record={transformDataSet?.current} labelWidth="auto" className="lc-model-detail-form">
        {fieldCodeList.map((i, index) => renderField({
          fieldItem: { field: i },
          formDs: transformDataSet,
          formDataSet,
          dsFieldList,
          intl,
          businessObjectFieldDataSet,
          viewCode,
          businessObjectCode,
          autoFocus: index === 0,
          tenantId,
        }))}
      </Form>
    </Spin>

  );

  function handleEdit() {
    setEditModalOpen(true);
    const editRef = Modal.open({
      title: intl.formatMessage({ id: 'zknow.common.button.modify', defaultMessage: '修改' }) + ticketSolutionFieldName,
      children: FieldFormView,
      key: modalKey,
      drawer: false,
      style: { width: '800px' },
      className: `${prefixCls}-modal`,
      destroyOnClose: true,
      onOk: async () => {
        setEditModalOpen(false);
        setLoading(false);
        return runAction({
          transformDataSet,
          formDataSet,
          fieldCodeList,
          intl,
        });
      },
      onCancel: () => {
        formDataSet.reset();
        setEditModalOpen(false);
        setLoading(false);
        return true;
      },
    });
    formDataSet.setState('solutionEidtModal', editRef);
  }

  // 渲染音频文件
  function renderAudioArea() {
    try {
      const descriptionExtraInfo = getExtraInfo();
      const { audios = [] } = descriptionExtraInfo || {};
      if (audios?.length === 0) return null;
      return <div className="reply-audio">{audios.map((i) => <ExternalComponent system={{ scope: 'itsm', module: 'YqAudio' }} fileInfo={i} />)}</div>;
    } catch {
      //
    }
  }

  // 渲染附件
  function renderAttachmentsArea() {
    const descriptionExtraInfo = getExtraInfo();
    const { attachments = [] } = descriptionExtraInfo || {};
    if (attachments?.length === 0) return null;
    return attachments?.map((i, index) => {
      return (
        <FileItem
          data={i}
          isLast={attachments.length - 1 === index}
          tenantId={tenantId}
          intl={intl}
          prefixCls="ticket-header-renderer-file"
        />
      );
    });
  }

  // 渲染新的富文本编辑器
  function renderWysiwyg(value) {
    if (quillNullContent?.includes(value)) return null;
    const content = record?.toData()[ticketSolutionFieldCode || 'resolution'];
    return (
      <RichTextPreview
        ocrFlag
        data={value}
        htmlData={content?.includes('<p data-json=') ? content : ''}
        preview
        minHeight={1}
        ticketId={ticketId}
      />
    );
  }

  // 获取附加信息
  function getExtraInfo() {
    try {
      const content = record?.toData()[ticketSolutionFieldCode || 'resolution'];
      // 移动端富文本
      if (content?.includes('<p data-json=')) {
        const htmlObj = getRichJson(content) || {};
        const { audios = [], attachments = [] } = htmlObj;
        return {
          audios,
          attachments,
        };
      }
      return { audios: [], attachments: [] };
    } catch {
      return { audios: [], attachments: [] };
    }
  }

  async function getResolutionValue(resolutionType) {
    const objectFields = await getObjectFields(tenantId, businessObjectId, { search_code: 'resolution_type' });
    const lookupTypeCode = objectFields?.find((i) => i?.code === 'resolution_type')?.widgetConfig?.lookupCode;
    const LookUpValue = await getLookUpValueByCode(tenantId, { lookupTypeCode });
    const value = LookUpValue?.find((i) => i?.code === resolutionType)?.value;
    setResolutionValue(value);
    return value;
  }

  const renderMain = useCallback(() => {
    if (!record) return null;
    let resolution = '';
    // FIX: 2022年4月28日 如果视图上配置了关联字段，不管有没有值，都取关联字段展示，如果没配置，就去解决方案。
    if (ticketSolutionFieldCode) {
      resolution = record?.toData()[ticketSolutionFieldCode];
    } else {
      resolution = record?.toData()?.resolution;
    }
    const resolutionType = record?.toData()?.resolution_type;
    const _resolutionValue = (record.getField('resolution_type')?.lookup || [])?.find((i) => i.code === resolutionType)?.meaning || resolutionValue;
    let htmlContent;
    try {
      const jsonData = JSON.parse(resolution);
      htmlContent = getHtml(jsonData);
    } catch (e) {
      htmlContent = resolution;
    }
    return (
      <div className={classnames(prefixCls, {
        [`${prefixCls}-isCard`]: sectionDisplayMode === 'CARD',
      })}
      >
        {isEditable && !udmFlag && (
          <Tooltip title={intl.formatMessage({ id: `${intlPrefixCls}.solution.tip.edit` })}>
            <div className={`${prefixCls}-edit`}>
              <Icon type="write" size={16} onClick={() => handleEdit()} id="yq-write-solution" />
            </div>
          </Tooltip>
        )}
        <div className={`${prefixCls}-main`}>
          <div className={`${prefixCls}-main-top`}>
            <span className="top-title">
              {ticketSolutionFieldName || intl.formatMessage({ id: `${intlPrefixCls}.solution` })}
            </span>
            <span className="top-type">{_resolutionValue}</span>
            {widgetConfig && widgetConfig.aiPromptFlag
            && <div
              className={`${prefixCls}-main-top-ai-title`}
              onClick={() => handleAiPrompt()}
            >
              <img src={AiImg} alt="" className="ai-icon" />
              <span className="ai-name">{widgetConfig.aiPromptTitle}</span>
            </div>}
            <span className="top-update-info">
              {lastUpdateBy && <span>{lastUpdateBy}</span>}
              {lastUpdateTime && <span>{intl.formatMessage({ id: 'lcr.renderer.desc.updated.at', defaultMessage: '更新于' })} {lastUpdateTime}</span>}
            </span>
          </div>
          <div className={`${prefixCls}-main-bottom`}>
            {renderWysiwyg(htmlContent)}
            {renderAudioArea()}
            {renderAttachmentsArea()}
          </div>
        </div>
      </div>
    );
  }, [record?.toData()?.resolution_type, record?.toData()?.resolution, isEditable, lastUpdateTime, lastUpdateBy, resolutionValue]);

  return renderMain();
});

const SolutionRenderer = injectIntl((props) => {
  return <Solution {...props} />;
});

export default SolutionRenderer;
