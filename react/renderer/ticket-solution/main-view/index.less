@import '~choerodon-ui/lib/style/themes/default';

.solution-renderer {
  background: #fff;
  margin: 0 16px 16px 16px;
  border-radius: 4px;
  border: 1px solid rgba(203, 210, 220, 0.5);

  &-isCard {
    margin: 0;
    border: 0;
  }

  &-edit, &-trans {
    height: 0;
    float: right;
    position: relative;
    z-index: 9;
    top: 7px;
    right: 15px;
    display: none;

    .i-icon,
    .yqcloud-icon-park-wrapper {
      padding: 8px;
      cursor: pointer;
      background-color: rgb(229, 239, 255);

      &:hover {
        color: @primary-color;
      }
    }
  }

  &-trans {
    right: 45px;

    &-gpt {
      display: inline-flex;
      align-items: center;
      margin-left: 8px;
      padding: 3px 4px;
      height: 22px;
      font-size: 12px;
      color: #d438cb;
      background: #ffe8fb;
      border-radius: 2px;

      &-icon {
        display: flex;
        align-items: center;
        height: 100%;
        color: #d438cb;
        margin-right: 4px;
        filter: invert(49%) sepia(61%) saturate(7003%) hue-rotate(281deg) brightness(90%) contrast(92%);
      }
    }
  }

  &:hover {
    .solution-renderer {
      &-edit, &-trans {
        display: block;
      }
    }
  }

  &-main {
    &-top {
      position: relative;
      display: flex;
      align-items: center;
      background: rgba(41, 121, 255, 0.12);
      border-radius: 4px 4px 0 0;
      height: 46px;
      padding: 12px 16px;
      box-shadow: 0 1px 6px 0 rgba(31, 35, 41, 0.04);

      .top-icon {
        margin-right: 8px;

        &-img {
          height: 24px;
          width: 20px;
        }
      }

      .top-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 22px;
        margin-right: 12px;
      }

      .top-type {
        font-size: 12px;
        font-weight: 400;
        color: #12274d;
        line-height: 20px;
      }

      .top-update-info {
        position: absolute;
        right: 16px;
        height: 22px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(18, 39, 77, 0.54);
        line-height: 22px;
      }
      &-ai-title {
        display: inline-flex;
        cursor: pointer;
        font-size: 0.12rem;
        color: #fff;
        font-family: DingTalk-JinBuTi,DingTalk;
        font-weight: normal;
        height: 0.24rem;
        background: linear-gradient(135deg, #53FFC6, #439CFF, #BB4BFF);
        border-radius: 0.12rem;
        padding: 0 0.08rem 0 0.02rem;
        vertical-align: middle;
        align-items: center;
        margin-left: 12px;
        &:hover {
          background-size: 200% 200%;
          animation: flowColors 1s infinite;
        }
      
        .ai-icon {
          width: 0.2rem;
          height: 0.2rem;
          margin: 0.02rem 0;
        }
      
        .ai-name {
          margin-left: 4px;
        }
      }
      
      @keyframes flowColors {
        0% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0% 50%;
        }
      }
    }

    &-bottom {
      font-size: 14px;
      font-weight: 400;
      color: #2b2d38;
      line-height: 22px;
      word-break: break-all;
      padding: 16px;

      img {
        // max-width: 400px;
        // max-height: 400px;
        width: 100%;
        max-width: 500px;
        max-height: 450px;
        display: block;
      }
    }

    table,
    tr,
    td,
    th {
      border: 1px #bfbfbf solid;
      border-collapse: collapse;
    }

    table td {
      min-width: 2em;
      padding: 0.4em;
    }
  }
}
