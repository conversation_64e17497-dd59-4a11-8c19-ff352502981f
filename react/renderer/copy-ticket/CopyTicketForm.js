/* eslint-disable camelcase */
import axios from 'axios';
import { Spin } from 'choerodon-ui/pro';
import { ExternalComponent } from '@zknow/components';
import React, { useEffect, useState, useMemo, useRef } from 'react';

export default function CopyTicketForm({
  modal,
  tenantId,
  createTicketCode,
  currentTableCode,
  record,
  businessObjectCode,
  formDataSet,
  businessObjectId,
}) {
  const [viewId, setViewId] = useState(null);
  const [ticketData, setTicketData] = useState(null);
  const [loading, setLoading] = useState(true);

  const createViewPageRef = useRef();

  const submitSuccess = async ({ data }) => {
    modal.close();
    await setRelationTicket(data);
    try {
      window._gotoListView();
    } catch {
      /* */
    }
  };

  async function setRelationTicket(data) {
    const newTicketId = data.id || data._id; // 复制过后新生成的单据id
    const oldTicketId = record?.get('id'); // 被复制的单据id
    const url = `/lc/v1/${tenantId}/svs_ticket_links/${businessObjectCode}/clone/relation`;
    await axios.post(url, JSON.stringify({
      sourceTicketId: oldTicketId,
      targetTicketId: newTicketId,
    }));
    formDataSet?.current?.setState('relationTicketRefreshCount', Math.round(Math.random() * 10000));
  }

  // 获取默认值
  const loadExpression = async (createViewId, createTicketData) => {
    const fieldMap = {
    };
    const res = await axios.post(`lc/v1/engine/${tenantId}/dataset/${createViewId}/${createViewId}/calculate`, JSON.stringify(fieldMap));
    if (res?.failed) {
      //
    } else {
      const _id = res?._id;
      const __parent_id = record?.get('id');
      const data = {
        ...createTicketData,
        // NOTE: 复制的单据不是源单据的子级，所以这里不应该有 parent_id
        parent_id: {
          id: record?.get('id'),
          name: record?.get('number'),
          number: record?.get('number'),
          short_description: record?.get('short_description'),
          shortDescription: record?.get('short_description'),
        },
        _id,
        __parent_id,
      };
      setViewId(createViewId);
      setTicketData(data);
      setLoading(false);
    }
  };

  // 用于处理单据复制数据格式问题
  const widgetTypeProcessMap = {};

  async function loadData() {
    try {
      let createViewId;
      // 1. 根据表格视图配置查询新建视图id
      const tabelView = await axios.get(
        `/lc/v1/${tenantId}/views/form/code/${currentTableCode}`
      );
      const id = JSON.parse(tabelView.jsonData)
        ?.sections[0]?.fields?.find((field) => field?.widgetType === 'Table')
        ?.widgetConfig?.buttons?.find((button) => button?.type === 'CREATE')
        ?.viewId;
      if (id) {
        createViewId = id;
      }
      // 2. 根据新建视图id或code查询新建视图数据
      let newView;
      if (createViewId) {
        newView = await axios.get(
          `/lc/v1/${tenantId}/views/form/${createViewId}`
        );
      } else {
        newView = await axios.get(
          `/lc/v1/${tenantId}/views/form/code/${createTicketCode}`
        );
      }
      createViewId = newView.id;

      const createTicketData = record?.data ? JSON.parse(JSON.stringify(record?.data)) : {};

      // 3. 获取新建视图所有字段
      const fields = JSON.parse(newView.jsonData)?.datasets[0]?.fields?.map(
        (field) => {
          const { code, widgetType, widgetConfig } = field;
          const relationLovNameFieldCode = widgetConfig?.relationLovNameFieldCode;
          return {
            code,
            widgetType,
            key: relationLovNameFieldCode,
          };
        }
      );

      // 4. 把表单上没有的字段去掉
      const nomatchFields = []; // 新视图中没有的字段
      const keys = fields?.map((field) => field?.code);
      Object.keys(createTicketData).forEach((key) => {
        // NOTE: 比如 新视图中有 example_id,  父级单据中只要包含这个值的都保留，example_id, example_id:name, example_id:color等
        if (!(keys.join(',')?.indexOf(key) > -1) && key !== 'id') {
          nomatchFields.push(key);
        }
      });

      // 5. 把特殊字段去掉
      //   NOTE: 新视图里没有的字段也不要传 null，会影响分单功能，可见生产工单 INC00025323
      [
        'id',
        '_token',
        'number',
        'active',
        'state_id',
        'created_by',
        'creation_date',
        'last_updated_by',
        'last_update_date',
        'object_version_number',
        ...nomatchFields,
      ].forEach((key) => {
        // 说是要把这些字段直接不传，不然会报错
        delete createTicketData[key];
      });

      // 6. 对关联类型数据进行修复
      fields
        ?.filter((field) => {
          return !!field?.key;
        })
        ?.forEach(({ code, key }) => {
          try {
            const data = createTicketData[code];
            const shortKey = key?.split(':')?.pop();
            const value = createTicketData[`${code}:${shortKey}`];
            if (data && value) {
              if (typeof data === 'string') {
                createTicketData[code] = { id: data };
              }
              createTicketData[code][key] = value;
            }
          } catch {
            //
          }
        });

      loadExpression(createViewId, createTicketData);
    } catch (e) {
      modal.close();
      // eslint-disable-next-line no-console
      console.error(e);
    }
  }

  // 设置父级单据展示字段
  async function setParentFieldValue() {
    const fDs = createViewPageRef?.current?.formDataSet;
    const fieldCode = fDs?.getField('parent_id')?.props?.textField;
    if (fieldCode && ticketData.parent_id) {
      const searchParam = `fields=${fieldCode}&businessObjectId=${businessObjectId}`;
      const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/searchByParamsForFields?${searchParam}`, JSON.stringify({
        id: record?.get('id'),
      }));
      const value = res?.[0]?.[fieldCode];
      ticketData.parent_id[fieldCode] = value;
      fDs?.current?.set('parent_id', ticketData.parent_id);
    }
  }

  useEffect(() => {
    loadData();
  }, [tenantId, currentTableCode]);

  useEffect(() => {
    setParentFieldValue();
  }, [createViewPageRef?.current?.formDataSet?.current?.get('_id')]);

  const Page = useMemo(() => {
    if (viewId && ticketData) {
      return (
        <ExternalComponent
          system={{
            scope: 'lcr',
            module: 'PageLoader',
          }}
          viewId={viewId}
          defaultData={ticketData}
          events={{
            submitSuccess,
          }}
          mode="CREATE"
          ticketFlag
          disableACL
          modal={modal}
          pageRef={createViewPageRef}
          submissionChannel="COPY_FROM_INCIDENT"
        />
      );
    }
    return null;
  }, [viewId, ticketData, createViewPageRef?.current?.formDataSet?.current?.get('_id')]);

  if (loading) return <div style={{ height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}><Spin /></div>;
  return Page;
}
