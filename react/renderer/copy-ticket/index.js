/*
 * @Author: xiaoreya
 * @Date: 2021-12-07 17:09:36
 * @Description: 复制单据
 */
import React, { useMemo, useEffect, useRef } from 'react';
import { inject } from 'mobx-react';
import { injectIntl } from 'react-intl';
import { observer } from 'mobx-react-lite';
import { formatterCollections } from '@zknow/utils';
import { Modal, Menu } from 'choerodon-ui/pro';
import { Permission } from '@yqcloud/apps-master';
import { Button, Icon } from '@zknow/components';
import CopyTicketForm from './CopyTicketForm';
import './index.less';

const modalKey = Modal.key();

const HiddenParent = () => {
  const ref = useRef();
  useEffect(() => {
    try {
      ref.current.parentElement.hidden = true;
    } catch {
      /** */
    }
  }, []);
  return <div ref={ref} />;
};

const MainView = injectIntl(observer((props) => {
  const {
    intl,
    tenantId,
    formDataSet: dataSet,
    viewDataSet,
    record: defaultRecord,
    viewCode,
    feature,
    config,
  } = props;
  const intlPrefixCls = 'lc.components';
  const businessObjectId = viewDataSet?.get(0)?.get('businessObjectId');

  // 可以进行单据复制的类型,和默认创建
  const createCodeMap = {
    INCIDENT: 'INCIDENT_NEW',
    CHANGE: 'CHANGE_NEW',
    CHANGE_TASK: 'CTASK_NEW',
    PROBLEM: 'PROBLEM_NEW',
    PROBLEM_TASK: 'PTASK_NEW',
  };
  // 表格Code 用于查找创建视图id
  const tableCodeMap = {
    INCIDENT: 'INCIDENT_TABLE',
    CHANGE: 'CHANGE_TABLE',
    CHANGE_TASK: 'CHANGE_TASK_TABLE',
    PROBLEM: 'PROBLEM_TABLE',
    PROBLEM_TASK: 'PROBLEM_TASK_TABLE',
  };

  const ticketCode = viewCode || viewDataSet?.get(0)?.get('businessObjectCode');
  const createTicketCode = createCodeMap[ticketCode];
  const currentTableCode = tableCodeMap[ticketCode];

  async function handleCopyTicket() {
    try {
      const record = defaultRecord || dataSet?.current || dataSet?.get(0);
      Modal.open({
        className: 'itsm-copy-ticket-modal',
        key: modalKey,
        style: { width: 1200, top: 55 },
        title: intl.formatMessage({ id: 'lcr.renderer.desc.copyTicket', defaultMessage: '复制单据' }),
        children: (
          <CopyTicketForm
            tenantId={tenantId}
            createTicketCode={createTicketCode}
            currentTableCode={currentTableCode}
            record={record}
            businessObjectCode={ticketCode}
            formDataSet={dataSet}
            businessObjectId={businessObjectId}
          />
        ),
        footer: null,
        destroyOnClose: true,
      });
    } catch (e) {
      return null;
    }
  }

  const isPortal = useMemo(
    () => {
      const protalPath = window._env_.PORTAL_PATH;
      const href = window.location.href;
      return href.includes(protalPath);
    },
    []
  );

  const hidden = !Object.keys(createCodeMap).includes(ticketCode) || isPortal;

  if (feature === 'table-action') {
    if (hidden) {
      return <HiddenParent />;
    }

    return <Permission service={['itsm_ticket_replication']} onClick={handleCopyTicket} noAccessChildren={<HiddenParent />}>
      <Menu.Item key="copy">
        <div className="copy-btn-wrapper">
          <Icon className="icon" size="16" type={config?.icon || 'copy-one'} style={{ marginRight: '8px' }} />
          <span>{intl.formatMessage({ id: 'lcr.renderer.desc.copyTicket', defaultMessage: '复制单据' })}</span>
        </div>
      </Menu.Item>
    </Permission>;
  }

  if (hidden) return null;

  return (
    <Permission service={['itsm_ticket_replication']} onClick={handleCopyTicket}>
      <Button key="copy" onClick={handleCopyTicket}>
        <Icon className="icon" size="16" type={config?.icon || 'copy-one'} style={{ marginRight: '8px' }} />
        {intl.formatMessage({ id: 'lcr.renderer.desc.copyTicket', defaultMessage: '复制单据' })}
      </Button>
    </Permission>
  );
}));

export default inject('AppState')(
  formatterCollections({
    code: 'lcr.renderer',
  })((props) => <MainView {...props} />)
);

/* externalize: CopyTicket */
