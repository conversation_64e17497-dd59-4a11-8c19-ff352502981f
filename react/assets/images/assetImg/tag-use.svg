<?xml version="1.0" encoding="UTF-8"?>
<svg width="76px" height="27px" viewBox="0 0 76 27" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>tag-use</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#FF9100" offset="0%"></stop>
            <stop stop-color="#FFB600" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="资产中心" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="门户-关联资产-1" transform="translate(-902.000000, -704.000000)">
            <g id="tag-use" transform="translate(902.000000, 704.000000)">
                <g id="编组-9" transform="translate(38.000000, 13.500000) scale(-1, 1) translate(-38.000000, -13.500000) ">
                    <path d="M6,0 L66,0 L66,0 L66,21 C66,24.3137085 63.3137085,27 60,27 L0,27 L0,27 L0,6 C-4.05812251e-16,2.6862915 2.6862915,6.08718376e-16 6,0 Z" id="矩形" fill="url(#linearGradient-1)"></path>
                    <path d="M66,0 L70,0 C72.209139,-4.05812251e-16 74,1.790861 74,4 L74,4 L74,4 L66,4 L66,0 Z" id="矩形" fill="#FFB600"></path>
                    <path d="M66,0 L76,0 L70,0 C67.790861,4.05812251e-16 66,1.790861 66,4 L66,4 L66,4 L66,0 Z" id="矩形" fill="#FF9100"></path>
                </g>
                <text id="使用中" font-family="AlibabaPuHuiTiM, Alibaba PuHuiTi" font-size="13" font-weight="400" fill="#FFFFFF">
                    <tspan x="25" y="18">使用中</tspan>
                </text>
            </g>
        </g>
    </g>
</svg>