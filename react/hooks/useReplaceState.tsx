/* eslint-disable prefer-rest-params */
import { useEffect } from 'react';

/**
 * 类工作台的多tab页使用的 H5 的 history.replaceState 进行url切换，直接监听 replaceState 是无效的
 * 通过代理触发想要触发的事件
 * 组件销毁后会恢复原生事件
 * @param callback
 */
function useReplaceState(callback: () => void) {
  useEffect(() => {
    const replaceState = window.history.replaceState;
    window.history.replaceState = replaceStateProxy(callback);
    window.addEventListener('replaceState', callback);
    return () => {
      window.removeEventListener('replaceState', callback);
      window.history.replaceState = replaceState;
    };
  }, []);

  return null;
}

export default useReplaceState;

function replaceStateProxy(trigger) {
  return new Proxy(window.history.pushState, {
    apply: (target, thisArg, argArray) => {
      const result = target.apply(thisArg, argArray);
      trigger();
      return result;
    },
  });
}
// 如果要支持IE
// function bindEventListenerReplaceState() {
//   const replaceState = window.history.replaceState;
//   return function newReplaceState() {
//     const newEvent = replaceState.apply(this, arguments);
//     const e = new Event('replaceState');
//     e.arguments = arguments;
//     window.dispatchEvent(e);
//     return newEvent;
//   };
// }
