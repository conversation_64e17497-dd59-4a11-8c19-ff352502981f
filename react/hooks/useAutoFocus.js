import { useEffect, useRef } from 'react';

export default function useAutoFocus(props = [], type) {
  const focusRef = useRef();
  useEffect(() => {
    if (focusRef?.current) {
      switch (type) {
        case 'lov':
          setTimeout(() => focusRef?.current?.element.focus(), 100);
          break;
        default:
          focusRef?.current?.element.focus();
      }
    }
  }, [focusRef?.current, ...props]);
  return focusRef;
}
