import React, { useRef } from 'react';
import {
  TRANSLATE_RESULT,
  TRANSLATE_STATE,
} from '@zknow/components';
import { message } from 'choerodon-ui/pro';
import isEmpty from 'lodash/isEmpty';
import { queryTranslate, queryTranslateAsync } from '../service';

/**
 * 复用翻译逻辑，非通用逻辑
 *   TicketAITranslationButton 中手动调用
 *   page-loader 中符合条件时自动调用
 * @param intl
 * @param mainStore
 * @param tenantId
 * @param formDataSet
 * @param setShowPopover 控制popover显示隐藏
 * @param businessObjectId
 * @param ticketId
 * @returns {(function(*): Promise<boolean|undefined>)|*}
 */
export default function useTranslate({
  intl,
  mainStore,
  tenantId,
  formDataSet,
  setShowPopover = () => {},
  businessObjectId,
  ticketId,
}) {
  const countRef = useRef(30);

  React.useEffect(() => {
    return () => {
      countRef.current = -1;
    };
  }, []);

  const getTranslateResult = async (uuid) => {
    if (mainStore.getTranslateManualCancel || countRef.current === -1) {
      mainStore.setTranslateLoading(false);
      return false;
    }
    const res = await queryTranslate(tenantId, uuid);
    if (mainStore.getTranslateManualCancel || countRef.current === -1) {
      mainStore.setTranslateLoading(false);
      return false;
    }
    if (res?.failed) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' }));
      mainStore.setTranslateLoading(false);
    } else if (res) {
      if (!isEmpty(res)) {
        formDataSet?.setState?.({
          [TRANSLATE_RESULT]: res,
          [TRANSLATE_STATE]: true,
          __TIMESTAMP: Date.now(),
        });
      } else {
        message.error(intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' }));
      }
      setShowPopover(false);
      mainStore.setTranslateLoading(false);
    } else if (countRef.current > 0) {
      countRef.current -= 1;
      setTimeout(() => {
        getTranslateResult(uuid);
      }, 3000);
    } else {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' }));
      mainStore.setTranslateLoading(true);
    }
  };

  const handleTranslate = async (targetLang) => {
    if (!formDataSet) {
      return false;
    }
    try {
      mainStore.setTranslateLoading(true);
      mainStore.setTranslateManualCancel(false);
      const translateRes = await queryTranslateAsync({ tenantId, businessObjectId, ticketId, targetLang });
      if (typeof translateRes === 'string') {
        getTranslateResult(translateRes);
      } else {
        // TODO: 需要区分错误类型，从后端返回信息中获取
        message.error(intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' }));
        setShowPopover(false);
      }
    } catch (e) {
      message.error(intl.formatMessage({ id: 'lcr.components.desc.translate.error', defaultMessage: 'AI翻译软件调取错误' }));
    }
  };

  return handleTranslate;
}
