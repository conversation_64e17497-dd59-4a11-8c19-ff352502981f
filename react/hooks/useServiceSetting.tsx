import { useEffect, useState } from 'react';
import axios from 'axios';
import isInOutward from '../utils/isInOutward';

export const useChatGptFlag = (
  businessObjectId: string,
  ticketId: string,
  tenantId: string,
  enableChatGptFlag: boolean,
  AppState: any,
) => {
  const [setting, setSetting] = useState<any>(null);
  useEffect(() => {
    (async () => {
      if (!enableChatGptFlag) return;
      const st = await getServiceSetting(businessObjectId, ticketId, tenantId, AppState);
      setSetting(st);
    })();
  }, [businessObjectId, ticketId, tenantId]);
  return setting;
};

export default function useServiceSetting(businessObjectId: string, ticketId: string, tenantId: string, AppState: any) {
  const [setting, setSetting] = useState<any>(null);
  useEffect(() => {
    (async () => {
      const st = await getServiceSetting(businessObjectId, ticketId, tenantId, AppState);
      setSetting(st);
    })();
  }, [businessObjectId, ticketId, tenantId]);
  return setting;
}

export async function getServiceSetting(businessObjectId: string, ticketId: string, tenantId: string, AppState: any) {
  const cache = await AppState?.customConfig[businessObjectId];
  if (cache) {
    return cache;
  }
  const outward = isInOutward(window.location.hash);
  // TODO 服务配置理论上不应该和单据有关，应该只和业务对象有关，实际情况是混杂的
  if (!businessObjectId || outward || !tenantId) {
    return false;
  }

  const res = await axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=${ticketId || 1}`);
  AppState?.setCustomConfig(businessObjectId, res);
  if (ticketId) {
    AppState?.setCustomConfig(`${businessObjectId}-${ticketId}`, res);
  }
  return res;
}
