import axios from 'axios';

/**
 * 发起翻译请求，由于接口时间较长，所以不会直接返回结果，而是返回一个 uuid
 * @param tenantId
 * @param businessObjectId
 * @param ticketId
 * @param targetLang
 * @param onlyReply 是否只查询所有的回复，否则查询工单上所有可翻译字段
 */
const queryTranslateAsync = async ({ tenantId, businessObjectId, ticketId, targetLang, onlyReply }) => {
  return axios.post(`/ai/v1/${tenantId}/itsmScene/translate/${businessObjectId}/${ticketId}/async?language=${targetLang}${onlyReply ? '&type=REPLY' : ''}`);
};

/**
 * 通过 uuid 获取实际结果，由于是异步，所以需要轮询此请求
 * @param tenantId
 * @param uuid
 */
const queryTranslate = async (tenantId, uuid) => {
  return axios.get(`/ai/v1/${tenantId}/itsmScene/ticketTranslate/poll/${uuid} `);
};

const queryTranslateNormalAsync = async ({ tenantId, businessObjectId, targetLang, message }) => {
  return axios.post(`/ai/v1/${tenantId}/itsmScene/translate/${businessObjectId}/async?language=${targetLang}`, message);
};

const queryTranslateNormal = async (tenantId, uuid) => {
  return axios.get(`/ai/v1/${tenantId}/itsmScene/translate/poll/${uuid} `);
};

export {
  queryTranslateAsync,
  queryTranslate,
  queryTranslateNormalAsync,
  queryTranslateNormal,
};
