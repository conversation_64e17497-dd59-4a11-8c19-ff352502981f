import axios from 'axios';

const queryServiceItemInfo = async (params: { tenantId: string; itemId: string }) => {
  const res = await axios.get(`itsm/v1/${params.tenantId}/service_items/${params.itemId}`);
  return res;
};
const queryAssetInfo = async (params: { tenantId: string; assetId: string; assetType: string }) => {
  const res = await axios.get(
    `/asset/v1/${params.tenantId}/itam-asset/getAssetDetail/${params.assetId}?assetType=${params.assetType}`
  );
  return res;
};
export { queryServiceItemInfo, queryAssetInfo };
