import axios from 'axios';

const relatedRepeatTicket = async (params: { tenantId: string; ticketId: string, data:any }) => {
  const res = await axios.post(`/itsm/v1/${params.tenantId}/ticket/repeat/link/${params.ticketId}`, params.data);
  return res;
};

const cancelRelatedRepeatTicket = async (params: { tenantId: string; linkId: string}) => {
  const res = await axios.delete(
    `/lc/v1/${params.tenantId}/svs_ticket_links?linkId=${params.linkId}`
  );
  return res;
};
export { relatedRepeatTicket, cancelRelatedRepeatTicket };
