import axios from 'axios';
import querystring from 'query-string';
import _ from 'lodash';

const handleDeleteTicketLink = async (params: { tenantId: string; linkId: string }) => {
  const res = await axios.delete(
    `lc/v1/${params.tenantId}/svs_ticket_links?linkId=${params.linkId}`
  );
  return res;
};

const getFiltersByViewId = async (params: { tenantId: string; viewId: string }) => {
  const res = await axios.get(`lc/v1/${params.tenantId}/formFilters?viewId=${params.viewId}`);
  return res;
};

const handleC7NDelink = async (params: {
  tenantId: string;
  openAppId: string;
  ticketId: string;

  instanceId: number; // 猪齿鱼工作项ID
  openInstanceId: string; // 燕千云工单ID
  projectId: number; // 猪齿鱼项目ID
  instanceType: string; // 猪齿鱼工作项类型
}) => {
  const res = await axios.post(
    `/itsm/v1/${params.tenantId}/c7n_ticket/operation/${params.openAppId}/${params.ticketId}`,
    JSON.stringify([
      {
        ..._.pick(params, ['instanceId', 'openInstanceId', 'projectId', 'instanceType']),
      },
    ])
  );
  return res;
};

const handleImportData = async (params: { tenantId: string; postData: {} }) => {
  const res = await axios.post(
    `/data/v1/${params.tenantId}/import`,
    JSON.stringify(params.postData)
  );
  return res;
};
/**
 * 获取对象字段
 * @param {string} tenantId 租户id
 * @param {string} businessObjectId 业务对象id
 * @param {string} params 查询参数对象
 */
const getObjectFields = (tenantId, businessObjectId, params) => {
  return axios.get(
    `/lc/v1/${tenantId}/object_fields/all/${businessObjectId}?${querystring.stringify(params)}`
  );
};

/**
 * 通过参数获取快码值
 * @param {string} tenantId 租户id
 * @param {string} params 查询参数对象
 */
const getLookUpValueByCode = (tenantId, params) => {
  return axios.get(`/hpfm/v1/${tenantId}/lookup/queryByCode?${querystring.stringify(params)}`);
};

// 自定义按钮生成动态记录
const generateDynamicRecord = async (params: {
  tenantId: string;
  businessObjectCode: string;
  postData: {};
}) => {
  const res = await axios.post(
    `/itsm/v1/${params.tenantId}/activity/${params.businessObjectCode}/customAction`,
    JSON.stringify(params.postData)
  );
  return res;
};

/**
 * 修复sla
 * @param {string} tenantId 租户id
 * @param {string} businessObjectCode 业务对象Code
 * @param {Array} ids 单据id的集合
 */
const repairSLA = (tenantId, businessObjectCode, ids = []) => {
  return axios.post(
    `/itsm/v1/${tenantId}/incident_task_sla/sla/repair?businessObjectCode=${businessObjectCode}`,
    JSON.stringify(ids)
  );
};

/**
 * 修复sla
 * @param {string} tenantId 租户id
 * @param {string} businessObjectCode 业务对象Code
 * @param {Array} ids 单据id的集合
 */
const repairServiceAgreement = (tenantId, businessObjectCode, ids = []) => {
  return axios.post(
    `/itsm/v1/${tenantId}/tasks/sla/repair?businessObjectCode=${businessObjectCode}`,
    JSON.stringify(ids)
  );
};

/**
 * 查看租户是否启用了sla
 * @param {string} tenantId 租户id
 * @param {Array} ids 单据id的集合
 */
const getCurrentTenantIsUseServiceAgreement = (tenantId) => {
  return axios.get(`/itsm/v1/${tenantId}/tasks/slas/useTenant`);
};

/**
 * 加入购物车
 * @param {string} tenantId 租户id
 * @param {Object} data 购物车信息
 */
const addToShoppingCart = (tenantId, data) => {
  return axios.post(`/cart/v1/${tenantId}/cart/add`, JSON.stringify(data));
};

/**
 * 直接购买
 * @param {string} tenantId 租户id
 * @param {Array} data 购物车信息
 */
const buyNowWithShoppingCart = (tenantId, data) => {
  return axios.post(`/cart/v1/${tenantId}/cart/settlement`, JSON.stringify(data));
};

const getRequestContent = (params: { tenantId: string; id: string }) => {
  return axios.get(`/ticket/v1/${params.tenantId}/task/sc_req_item/${params.id}`);
};

const updateRequestContent = (params: { tenantId: string; id: string; data: any }) => {
  return axios.post(
    `/ticket/v1/${params.tenantId}/task/sc_req_item/${params.id}`,
    JSON.stringify(params.data)
  );
};

const getRequestProcess = (params: { tenantId: string; id: string }) => {
  return axios.get(`/ticket/v1/${params.tenantId}/task/sc_req_item/workflow/${params.id}`);
};

const getWfInstances = (params: { tenantId: string; id: string }) => {
  return axios.get(`/workflow/v1/workflowInstances/${params.tenantId}/queryByBusiness/${params.id}`);
};

const getWfProcess = (params: { wfInstanceId: string, predictionFlag: boolean }) => {
  return axios.get(`/workflow/v1/workflowInstances/${params.wfInstanceId}?predictCondition=${params.predictionFlag}`);
};

const getApprovalInfo = (params: { tenantId: string; instanceId: string }) => {
  return axios.get(`/workflow/v1/organizations/${params.tenantId}/workflows/${params.instanceId}/list-approval`);
};

const updateTicketHeader = (params: {
  tenantId: string;
  id: string;
  data: any;
  businessObjectCode: string;
}) => {
  const { tenantId, id, data, businessObjectCode } = params;
  return axios.post(
    `/itsm/v1/${tenantId}/task/saveTicketHeaderInfo/${id}?businessObjectCode=${businessObjectCode}`,
    JSON.stringify(data)
  );
};
/**
 * 查看租户是否启用了某个功能点
 * @param {string} tenantId 租户id
 * @param {string} appCode 模块编码
 */
const getIsEnabledApps = (tenantId, appCode) => {
  return axios.get(`/app/v1/${tenantId}/apps/exist/app?appCode=${appCode}`);
};

const getMemoTransfer = (tenantId) => {
  return axios.get(`/lc/v1/${tenantId}/lc_actions/last/transfer`);
};

/**
 * 单据关联知识
 * @param {string} tenantId 租户id
 * @param {string} businessObjectCode 业务对象code
 */
const associationTicketWithKnowledge = (tenantId, ticketId, businessObjectCode, ids) => {
  return axios.post(
    `/itsm/v1/${tenantId}/ticket/element/related/${ticketId}?businessObjectCode=${businessObjectCode}`,
    JSON.stringify(ids)
  );
};

/**
 * 取消单据关联知识
 * @param {string} tenantId 租户id
 * @param {string} businessObjectCode 业务对象code
 * @param {string} knowledgeId 知识id
 */
const disassociateTicketWithKnowledge = (tenantId, businessObjectCode, knowledgeId) => {
  return axios.post(
    `/itsm/v1/${tenantId}/ticket/element/disassociate/${knowledgeId}?businessObjectCode=${businessObjectCode}`
  );
};

/**
 * 获取当前单据上关联的知识
 * @param {string} tenantId 租户id
 * @param {string} businessObjectCode 业务对象code
 * @param {string} ticketId 单据id
 * @param {string} relationType 类型是引用的还是生成的
 * @param {string} page 分页
 * @param {string} size 分页大小
 */
const getAssociateTicketWithKnowledge = (
  tenantId,
  businessObjectCode,
  ticketId,
  relationType,
  page = 0,
  size = 10
) => {
  return axios.get(
    `/itsm/v1/${tenantId}/ticket/element/${ticketId}?businessObjectCode=${businessObjectCode}&relationType=${relationType}&page=${page}&size=${size}`
  );
};

/**
 * 解决方案转问答，获取gpt分析后结果
 * @param tenantId
 * @param content
 */
const getGptResultForQA = (tenantId, content) => {
  return axios.post(`/itsm/v1/${tenantId}/resolution/gpt`, content);
};

/**
 * 获取还能加入购物车或者购买的数量
 * @param {string} tenantId 租户id
 * @param {string} itemId 服务项id
 */
const getPurchasedQuantity = (tenantId, itemId) => {
  return axios.get(`/cart/v1/${tenantId}/item/sale/properties/surplus/${itemId}`);
};

const getReqTaskSla = ({ tenantId, id }) => {
  return axios.get(`/itsm/v1/${tenantId}/tasks/${id}/slaGoals?businessObjectCode=WF_TASK_INSTANCE&activeFlag=true`);
};

/**
 * 查询当前业务对象服务配置
 *    &ticketId=1 说明和单据无关的服务配置
 * @param {string} tenantId 租户id
 * @param {string} businessObjectId 业务对象id
 */
const getServiceSetting = (tenantId, businessObjectId) => {
  if (!tenantId) {
    return false;
  }
  return axios.get(`/itsm/v1/${tenantId}/service_settings/apply?businessObjectId=${businessObjectId}&ticketId=1`);
};

const getAiSummary = ({ tenantId, aiPromptId, businessObjectCode, ticketId }) => {
  return axios.post(`/ai/v1/${tenantId}/callPromptTemplate/run/${aiPromptId}?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}`);
};

/**
 * 异步查询ai总结，不会返回结果
 * @param tenantId
 * @param aiPromptId
 * @param businessObjectCode
 * @param ticketId
 * @param isNewUrl
 * @param params
 */
const getAiSummaryAsync = ({ tenantId, aiPromptId, businessObjectCode, businessObjectId, ticketId, data, isNewUrl = false }) => {
  const url = isNewUrl
    ? `/ai/v1/${tenantId}/itsmScene/resolution/${businessObjectId}/async?ticketId=${ticketId}`
    : `/ai/v1/${tenantId}/callPromptTemplate/run/async/${aiPromptId}?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}`;
  return axios.post(url, data);
};

/**
 * 轮训结果，会返回结果
 * @param tenantId
 * @param uuid
 * @param isNewUrl
 */
const getAiSummaryPoll = async ({ tenantId, uuid, isNewUrl = false }) => {
  try {
    const url = isNewUrl ? `/ai/v1/${tenantId}/itsmScene/resolution/poll/${uuid}` : `/ai/v1/${tenantId}/callPromptTemplate/run/async/poll/${uuid}`;
    const res = await axios.get(url);
    if (res?.status !== 204) {
      return res;
    }
  } catch (e) {
    return null;
  }
};

/**
 * 查询重复问题
 * @param tenantId
 * @param ticketId
 * @param shortDescription
 */
const getSimilarQuestion = ({ tenantId, shortDescription, ticketId }) => {
  return axios.get(`/itsm/v1/${tenantId}/ticket/repeat/${ticketId}?maxValue=1&shortDescription=${shortDescription}`);
};

/**
 * 查询推荐解决方案
 * @param tenantId
 * @param ticketId
 * @param shortDescription
 */
const getBestQuestion = ({ tenantId, data }) => {
  return axios.post(`/bss/v1/${tenantId}/openSearch?draftFlag=false&page=0&size=10`, data);
};

const getPageViewId = async ({ tenantId, expression, params }) => {
  const res = await axios.post(`/lc/v1/engine/${tenantId}/dataset/execute-expression`, { expression, params });
  return res;
};

/**
 * 存储某个文本字段的常用输入
 * @param {string} tenantId 租户id
 * @param {string} phrasesComponentId 组件id&编码
 * @param {string} value 文本内容
 */
const setComponentPhrases = (tenantId, phrasesComponentId, value) => {
  return axios.post(`/lc/v1/${tenantId}/common_phrases/use`, {
    [phrasesComponentId]: value,
  });
};

export {
  handleDeleteTicketLink,
  getFiltersByViewId,
  handleC7NDelink,
  handleImportData,
  getObjectFields,
  getLookUpValueByCode,
  generateDynamicRecord,
  repairSLA,
  repairServiceAgreement,
  getCurrentTenantIsUseServiceAgreement,
  addToShoppingCart,
  buyNowWithShoppingCart,
  getRequestContent,
  updateRequestContent,
  getRequestProcess,
  getWfInstances,
  getWfProcess,
  getApprovalInfo,
  getIsEnabledApps,
  associationTicketWithKnowledge,
  disassociateTicketWithKnowledge,
  getAssociateTicketWithKnowledge,
  updateTicketHeader,
  getGptResultForQA,
  getPurchasedQuantity,
  getReqTaskSla,
  getServiceSetting,
  getAiSummary,
  getAiSummaryAsync,
  getAiSummaryPoll,
  getMemoTransfer,
  getSimilarQuestion,
  getBestQuestion,
  getPageViewId,
  setComponentPhrases,
};
