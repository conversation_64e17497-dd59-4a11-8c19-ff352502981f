import axios from 'axios';

const scoreQualityInspection = ({ tenantId, ticketId, data }) => {
  return axios.post(`/ticket/v1/${tenantId}/ticket/checks/score?ticketId=${ticketId}`, data);
};

const queryQualityInspectionContent = ({ tenantId, ticketId }) => {
  return axios.get(`/ticket/v1/${tenantId}/ticket/checks/suggestions?ticketId=${ticketId}`);
};

const updateQualityInspectionContent = ({ tenantId, data }) => {
  return axios.post(`/ticket/v1/${tenantId}/ticket/checks/suggestions`, data);
};

const checkQualityInspectionPermissions = ({ tenantId, data }) => {
  return axios.post(`/iam/yqc/v1/${tenantId}/menus/check-permissions`, data);
};

const getQualityAiSummaryAsync = ({ tenantId, aiTemplateId, solutionId, ticketId }) => {
  return axios.post(`/ticket/v1/${tenantId}/ticket/checks/intelligence?ticketId=${ticketId}&solutionId=${solutionId}&aiTemplateId=${aiTemplateId}`);
};

export {
  scoreQualityInspection,
  queryQualityInspectionContent,
  updateQualityInspectionContent,
  checkQualityInspectionPermissions,
  getQualityAiSummaryAsync,
};
