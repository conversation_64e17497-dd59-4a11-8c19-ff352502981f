import { message, DataSet } from 'choerodon-ui/pro';
import * as REQUESTS from './lowcodeMap';

type RequestConfig = {
  dataSet?: DataSet, // 传入dataSet 在请求处理结束后查询
  successCallback?: () => void, // 请求结束后自定义回调
  successTip?: string, // 请求成功message
  failedTip?: string, // 请求失败message
  params?: {}, // 接口需要的参数
}

const handleCommonRequest = async (key: string, config?: RequestConfig) => {
  if (config) {
    try {
      const res = await REQUESTS[key](config.params || {});
      if (res.failed) {
        message.error(config.failedTip || res.message);
      } else {
        if (config.successCallback) {
          config.successCallback();
        } else {
          config.dataSet && config.dataSet.query();
        }
        if (config.successTip) {
          message.success(config.successTip);
        }
      }
    } catch (error) {
      // console.error(error);
    }
  } else {
    const res = await REQUESTS[key]();
    return res;
  }
};

export default handleCommonRequest;
export * from './lowcodeMap';
export * from './assetMap';
export * from './duplicateProblemMap';
export * from './thirdPartyApplication';
export * from './upgradeOrder';
export * from './qualityInspection';
export * from './ticketTranslate';
export * from './ticketSummary';
export * from './ticketReminderBanner';
