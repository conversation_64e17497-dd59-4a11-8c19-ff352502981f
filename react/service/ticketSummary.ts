import axios from 'axios';

const SUMMARY_TEMPLATE_CODE = 'INTELLIGENT_TICKET_SUMMARY';

/**
 * 发起工单总结，由于接口时间较长，所以不会直接返回结果，而是返回一个 uuid
 * @param tenantId
 * @param businessObjectId
 * @param ticketId
 */
const querySummaryAsync = async ({ tenantId, businessObjectCode, ticketId }) => {
  return axios.post(`/ai/v1/${tenantId}/callPromptTemplate/run/async?templateCode=${SUMMARY_TEMPLATE_CODE}&ticketId=${ticketId}&businessObjectCode=${businessObjectCode}`);
};

/**
 * 通过 uuid 获取实际结果，由于是异步，所以需要轮询此请求
 * @param tenantId
 * @param uuid
 */
const querySummaryResult = async (tenantId, uuid) => {
  return axios.get(`/ai/v1/${tenantId}/callPromptTemplate/run/async/poll/${uuid}`);
};

const querySummaryInfo = async ({ tenantId, businessObjectCode, ticketId }) => {
  return axios.get(`/itsm/v1/${tenantId}/activity/last/update/info?businessObjectCode=${businessObjectCode}&ticketId=${ticketId}&fieldCode=summary`);
};

export {
  SUMMARY_TEMPLATE_CODE,
  querySummaryAsync,
  querySummaryResult,
  querySummaryInfo,
};
