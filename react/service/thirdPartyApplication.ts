import axios from 'axios';

/**
 * 获取关联猪齿鱼映射信息
 * @param {string} tenantId 租户id
 * @param {string} businessObjectId 业务对象id
 * @param {string} params 查询参数对象
 */
const queryAppFieldMappingConfig = async (params: { tenantId: string; thirdPartyApplicationId: string; ticketId: string, businessObjectCode: string }) => {
  const res = await axios.get(`/itsm/v1/${params.tenantId}/c7n_ticket/mapping/${params.thirdPartyApplicationId}/${params.ticketId}?businessObjectCode=${params.businessObjectCode}`);
  return res;
};
export { queryAppFieldMappingConfig };
