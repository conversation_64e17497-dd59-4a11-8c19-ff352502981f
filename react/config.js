const path = require('path');

const config = {
  server: 'http://api.staging.saas.test.com',
  fileServer: 'http://minio.staging.saas.test.com',
  projectType: 'yqcloud',
  buildType: 'single',
  master: '@yqcloud/apps-master',
  port: 9110,
  theme: {
  },
  sharedModules: {
    '@yqcloud/ckeditor5': {
      singleton: true,
      requiredVersion: false,
    },
  },
  devServerConfig: {
    allowedHosts: 'all',
    headers: [
      {
        key: 'Access-Control-Allow-Origin',
        value: '*',
      },
    ],
  },
  webpackConfig(webpackConfig) {
    webpackConfig.resolve.alias = {
      ...webpackConfig.resolve.alias,
      '@': path.resolve('./react'),
    };
    return webpackConfig;
  },
  dashboard: {},
  modules: [
    '.',
  ],
  outward: '',
};

module.exports = config;
